// 定义公共的 namespace 划分.
// 公共 namespace 的注释统一写在这里

import { DialogProps, HighlightElementConfig } from './types';

/** 小程序接口合集 */
export declare namespace IDP {
    /** 云设计工具主应用级别接口 */
    namespace Platform {
    }
    /**
     * UI控制相关接口
     */
    namespace UI {
        /**
         * dialog弹窗
         * @internal
         */
        function dialogConfirm(props: DialogProps): void;
        /**
         * 显示全屏遮罩：非 main 挂载点的小程序视图会被遮罩覆盖，main 挂载点下高显示优先级的视图会显示在遮罩上层
         * @internal
         */
        function showFullscreenMask(): void
        /**
         * 关闭全屏遮罩
         * @internal
         */
        function hideFullscreenMask(): void
    }

    /** 方案信息相关接口 */
    namespace Design {
    }

    /** 用户信息相关接口 */
    namespace User {
    }

    /** 数据相关接口 */
    namespace DB {
        namespace Types {
        }
    }

    /** 用户交互行为相关接口 */
    namespace Interaction {
        /**
         * 获取当前选中的对象列表
         */
        function getSelectedElements(): IDP.DB.Types.ElementId[];
        /**
         * 设置当前选中的对象列表
         * 目前支持下面几种 ElementType:Wardrobe、WardrobeCopy、Cabinet、CabinetCopy、DoorWindow、DoorWindowCopy、CustomGroup;
         * @param option
         * @returns 返回已选中的对象列表，没有选中的对象则返回[];
         */
        function setSelectedElements(option: IDP.DB.Types.ElementId[]): IDP.DB.Types.ElementId[];

        /**
         * 高亮指定的对象
         * @internal
         */
        function setHighlightedElements(highlighedElements: HighlightElementConfig[]): void;
    }

    /** 小程序可监听的事件集合 */
    // eslint-disable-next-line @typescript-eslint/no-empty-interface
    interface EventTypes {
    }
    /** 小程序事件相关接口 */
    namespace Events {
    }
}

interface MiniappUploadDataOption {
    miniappId: string
    data: string
}

interface MiniappUploadDataResult {
    uniqueId: string
}

type Locale = 'zh_CN' | 'en_US' | 'de_DE' | 'fr_FR' | 'ja_JP' | 'zh_TW' | 'ru_RU' | 'ko_KR' | 'es_ES' | 'ar_EG' | 'en_CN' | 'vi' | 'it_IT' | 'th_TH' | 'id_ID' | 'pl_PL';

/**
 * @internal
 */
interface PubInjection {
    /**
     * 配置包
     */
    config?: any;
    /**
     * 多语言
     */
    language?: any;
    /**
     * 分桶
     */
    bucket?: any;
    /**
     * model api 提供的数据
     */
    modelApiData?: any;
}

export declare namespace IDP {

    namespace Platform {
        /**
         * 获取当前页面的 URL 参数
         *
         * @vm-type IDP_Platform_getURLSearchParams
         * @internal
         */
        function getURLSearchParams(): { [key: string]: string };

        /**
         * 应用模式标识
         */
        enum AppMode {
            /**
             * 云图模式
             */
            DefaultMode = 'DefaultMode',
            /**
             * 户型模式
             */
            SuperFloorplanMode = 'SuperFloorplanMode',
            /**
             * 定制模式
             */
            CustomMode = 'CustomMode',
            /**
             * 其余模式统称
             */
            UnknownMode = 'UnknownMode',
        }

        /**
         * 获取当前应用模式
         */
        function getAppMode(): AppMode;

        /**
         * 获取当前语言和地区设置
         */
        function getLocale(): Locale;

        /**
         * 页面跳转
         *
         * @internal
         */
        function redirect(url: string): void;

        /**
         * 长度单位类型
         */
        enum LengthUnitType {
            /** 毫米 */
            mm,
            /** 米 */
            m,
            /** 英尺 */
            ft,
            /** 英寸 */
            in
        }

        /**
         * 面积单位类型
         *
         * @internal
         */
        enum AreaUnitType {
            /** 平方毫米 */
            sqMm,
            /** 平方米 */
            sqM,
            /** 平方英尺 */
            sqFt,
            /** 平方英寸 */
            sqIn,
            /** 畳 */
            mats,
            /** 坪 */
            tsubo
        }

        /**
         * 获取全局默认长度单位类型
         */
        function getGlobalLengthUnit(): LengthUnitType;

        /**
         * 获取全局默认长度展示精度
         *
         * 展示精度有两类，小数和分数：
         * - 小数类，比如 '0.001'，1.23456 在该展示精度下应展示为 '1.235'
         * - 分数类，比如 '1/4'，1.23456 在该展示精度下应展示为 '1 1/4'
         *
         * @returns {string}
         */
        function getGlobalLengthPrecision(): string;

        /**
         * 获取全局默认面积单位类型
         *
         * @internal
         */
        function getGlobalAreaUnit(): AreaUnitType;

        /**
         * 获取全局默认面积展示精度
         *
         * 精度表示格式同 `getGlobalLengthPrecision`
         *
         * @internal
         */
        function getGlobalAreaPrecision(): string;

        /**
         * 设置 2D 鼠标样式（低优先级）
         *
         * 显示规则：
         * 1. 原生功能的设置会覆盖当前设置
         * 2. 同一 id 的样式多次设置，仅第一次生效，重新设置需要先调用`unSetCursorStyle`清除之前的设置
         * 3. 不同 id 的样式多次设置，后设置的覆盖先设置的
         *
         * @internal
         *
         * @param {string} id 标识
         * @param {string} cursor https://developer.mozilla.org/en-US/docs/Web/CSS/cursor
         */
        function setCursorStyle2D(id: string, cursor: string): void;

        /**
         * 清除 2D 鼠标样式设置（低优先级）
         *
         * @internal
         *
         * @param {string} id 标识
         */
        function unSetCursorStyle2D(id: string): void;

        /**
         * 设置 3D 鼠标样式（低优先级）
         *
         * 显示规则：
         * 1. 原生功能的设置会覆盖当前设置
         * 2. 同一 id 的样式多次设置，仅第一次生效，重新设置需要先调用`unSetCursorStyle`清除之前的设置
         * 3. 不同 id 的样式多次设置，后设置的覆盖先设置的
         *
         * @internal
         *
         * @param {string} id 标识
         * @param {string} cursor https://developer.mozilla.org/en-US/docs/Web/CSS/cursor
         */
        function setCursorStyle3D(id: string, cursor: string): void;

        /**
         * 清除 3D 鼠标样式设置（低优先级）
         *
         * @internal
         *
         * @param {string} id 标识
         */
        function unSetCursorStyle3D(id: string): void;
        /**
         * 通过 pub 部署的二方小程序可通过该接口获取 pub 注入的变量
         *
         * @internal
         */
        function getPubInjectionAsync(): Promise<PubInjection>;
    }

    namespace Events {
        /**
         * 添加小程序事件监听
         *
         * @vm-type IDP_Events_on
         */
        function on<T extends keyof IDP.EventTypes>(eventType: T, callback: (data: IDP.EventTypes[T]) => void): void;
        /**
         * 添加仅一次的小程序事件监听
         *
         * @vm-type IDP_Events_once
         */
        function once<T extends keyof IDP.EventTypes>(eventType: T, callback: (data: IDP.EventTypes[T]) => void): void;
        /**
         * 移除小程序事件监听
         *
         * @vm-type IDP_Events_off
         */
        function off<T extends keyof IDP.EventTypes>(eventType: T, callback: (data: IDP.EventTypes[T]) => void): void;
    }
    namespace Design {
        interface Level {
            // 楼层的id
            id: string;

            // 楼层的名称
            name: string;

            // 楼层位置。1,2,3表示地上一层二层三层；-1,-2,-3表示地下一层二层三层；其他以此类推
            index: number;
        }

        /**
         * 获取当前方案id
         */
        function getDesignId(): string | undefined;

        /**
         * 获取方案名称
         */
        function getName(): string | undefined;

        /**
         * 获取方案全部楼层的信息
         *
         * @returns 方案全部楼层，按照从低到高排序，如果是新方案会返回空数组
         */
        function getAllLevels(): Level[]

        /**
         * 获取方案当前所在楼层的信息
         *
         * @returns 当前所在楼层的信息，如果是新方案会返回undefined
         */
        function getCurrentLevel(): Level | undefined;

        /**
         * 切换方案当前的楼层
         *
         * @param levelId
         * @returns 切换楼层完成后promise resolve，切换失败reject
         */
        function changeCurrentLevelAsync(levelId: string): Promise<void>

        /**
         * 通过DesignId重载方案
         *
         * @internal
         */
        function changeCurrentDesignAsync(designId: string): Promise<void>
    }

    namespace User {
        /**
         * 获取当前用户id
         */
        function getUserId(): string;
    }

    namespace DB {
        namespace Types {
            /**
             * 设计对象类型
             */
            enum ElementType {
                /** 软装家具 */
                Furniture = 'Furniture',
                /**
                 * 软装组合家具
                 * @internal
                 */
                FurnitureGroup = 'FurnitureGroup',
                /** 全屋定制家具 */
                Wardrobe = 'Wardrobe',
                /** 全屋定制家具副本 */
                WardrobeCopy = 'WardrobeCopy',
                /** 厨卫 */
                Cabinet = 'Cabinet',
                /** 厨卫副本 */
                CabinetCopy = 'CabinetCopy',
                /** 门窗 */
                DoorWindow = 'DoorWindow',
                /** 门窗副本 */
                DoorWindowCopy = 'DoorWindowCopy',
                /** 踢脚线 */
                Skirting = 'Skirting',
                /** 硬装铺贴 */
                Paving = 'Paving',
                /**
                 * 硬装线条
                 * @internal
                 */
                DecoMolding = 'DecoMolding',
                /**
                 * 硬装造型(墙面/地面/顶面)
                 * @internal
                 */
                PlanarModelingDesign = 'PlanarModelingDesign',
                /**
                 * 硬装灯带
                 * @internal
                 */
                StripLight = 'StripLight',
                /**
                 * 硬装自由造型
                 * @internal
                 */
                DecoFreeStyleModel = 'DecoFreeStyleModel',
                /**
                 * 硬装灯槽
                 * @internal
                 */
                StripLightPlaster = 'StripLightPlaster',
                /**
                 * 参数化吊顶
                 * @internal
                 */
                ParamCeiling = 'ParamCeiling',
                /** 定制组合 */
                CustomGroup = 'CustomGroup',
                /** 模型门窗 */
                ModelDoorWindow = 'ModelDoorWindow',
                /** 模型踢脚线 */
                ModelMolding = 'ModelMolding',
                /** 混组 */
                MixGroup = 'MixGroup',

                /**
                 * 户型-墙
                 */
                Wall = 'Wall',
                /**
                 * 户型-洞
                 * @internal
                 */
                Opening = 'Opening',
                /**
                 * 户型-房间
                 */
                Room = 'Room',
                /**
                 * 户型-方柱
                 * @internal
                 * */
                SquareColumn = 'SquareColumn',
                /**
                 * 户型-楼板
                 * @internal
                 * */
                Floor = 'Floor',
                /**
                 * 户型-梁
                 * @internal
                 * */
                Beam = 'Beam',
                /**
                 * 户型-门洞
                 * @internal
                 * */
                DoorOpening = 'DoorOpening',
                /**
                 * 户型-窗洞
                 * @internal
                 * */
                WindowOpening = 'WindowOpening',
                /**
                 * 户型-房间分割线
                 * @internal
                 * */
                RoomSeparator = 'RoomSeparator',
                /**
                 * 户型-楼板洞
                 * @internal
                 * */
                FloorOpening = 'FloorOpening',
                /** 家具图例 */
                FurnitureLegend = 'FurnitureLegend',
                /** 参数化图例 */
                ParamLegend = 'ParamLegend',
                /** 图例组合 */
                LegendGroup = 'LegendGroup',
                /**
                 * 区域
                 * @internal
                */
                Zone = 'Zone',
                /** 厨卫引用模型 */
                CabinetRef = 'CabinetRef',
                /** 厨卫副本引用模型 */
                CabinetCopyRef = 'CabinetCopyRef',
                /** 全屋引用模型 */
                WardrobeRef = 'WardrobeRef',
                /** 全屋副本引用模型 */
                WardrobeCopyRef = 'WardrobeCopyRef',
                /** 厨卫装配模型 */
                CabinetSnapper = 'CabinetSnapper',
                /** 厨卫副本装配模型 */
                CabinetCopySnapper = 'CabinetCopySnapper',
                /** 全屋装配模型 */
                WardrobeSnapper = 'WardrobeSnapper',
                /** 全屋副本装配模型 */
                WardrobeCopySnapper = 'WardrobeCopySnapper',
                /** 厨卫装配体 */
                CabinetSnapperGroup = 'CabinetSnapperGroup',
                /** 厨卫副本装配体 */
                CabinetCopySnapperGroup = 'CabinetCopySnapperGroup',
                /** 全屋装配体 */
                WardrobeSnapperGroup = 'WardrobeSnapperGroup',
                /** 全屋副本装配体 */
                WardrobeCopySnapperGroup = 'WardrobeCopySnapperGroup',
                /**
                 * 通用设计对象
                 * @internal
                 * */
                GenericModel = 'GenericModel',
                /** 冻结模型 */
                FrozenModel = 'FrozenModel',
                /** 办公：走线面 */
                RoutingFace = 'RoutingFace',
                /** 办公：走线组 */
                RoutingFaceGroup = 'RoutingFaceGroup',
                /** 办公：走线体 */
                RoutingCube = 'RoutingCube',
                /** 办公：走线插口 */
                RoutingSocket = 'RoutingSocket',
            }

            /**
             * 设计对象 ID
             */
            interface ElementId {
                /** 实例 ID */
                id: string;
                /** 类型 */
                type: ElementType;
            }
        }
    }

    interface EventTypes {
        /** 监听当前选中的对象列表的变更 */
        'IDP.Interaction.SelectedElementsChange': IDP.DB.Types.ElementId[];
        /**
         * 监听定制模型新增
         */
        'IDP.Custom.Design.CustomModel.Add': IDP.DB.Types.ElementId[];
        /**
         * 监听定制模型删除
         */
        'IDP.Custom.Design.CustomModel.Delete': IDP.DB.Types.ElementId[];
        /**
         * 监听模型新增
         */
        'IDP.DB.Element.Add': IDP.DB.Types.ElementId[];
        /**
         * 监听模型删除
         */
        'IDP.DB.Element.Delete': IDP.DB.Types.ElementId[];
        /**
         * 监听模型更新
         * @internal
         */
        'IDP.DB.Element.Update': IDP.DB.Types.ElementId[];
        /**
         * 监听是否触发保存
         */
        'IDP.Design.Save': void;
        /**
         * 监听方案加载完成
         * @internal
         */
        'IDP.Design.Loaded': { isReload: boolean };
        /**
         * 监听定制数据加载完成
         */
        'IDP.Custom.Design.Loaded': void;
        /**
         * 监听 **云设计工具页面** 文档视图（窗口）大小调整
         */
        'IDP.UI.Layout.WindowResize': void;
        /**
         * 监听 **窗口模式下的小程序容器** 的最小化状态更新
         * @derepcated 请使用 `IDP.Miniapp.View.ContainerMinimizedChange`
         * @internal
         */
        'IDP.UI.MiniappContainer.Main.MinimizedChange': { isMinimized: boolean };
        /**
         * 监听小程序frame容器的最小化状态更新
         */
        'IDP.Miniapp.View.ContainerMinimizedChange': { frameId: string, isMinimized: boolean };
        /**
         * 监听保存成功
         */
        'IDP.Design.Saved': void;
    }

    namespace Miniapp {
        /**
         * 获取通过 uploadDataAsync 上传的临时存储数据
         *
         * 需要将 uploadDataAsync 返回的 **uniqueId** 作为 URL 参数 **miniappUploadId** 传入
         *
         * https://yun.kujiale.com/cloud/tool/h5/bim?designid=${designId}&miniappUploadId=${uniqueId}
         */
        function getUploadedDataAsync(): Promise<any>;

        /**
         * 临时存储数据，24 小时后自动清理
         *
         * 返回值中的 **uniqueId** 是获取临时存储数据的媒介
         */
        function uploadDataAsync(option: MiniappUploadDataOption): Promise<MiniappUploadDataResult | undefined>;

        /**
         * 激活另一个小程序
         *
         * **注意：将会导致当前小程序关闭！**
         *
         * @param {string} miniappId 小程序 id
         * @param {*} [options] 启动参数（可通过 IDP.Miniapp.getLaunchOptions 获取）
         *
         * @internal
         */
        function launchMiniappById(miniappId: string, options?: any): void;
    }
}

export { };
