> 小程序目前包含内网包和外网包及两套发布流程，发布周期也不相同，以下发布要求和流程供接口开发者和使用者参考

## 发布周期
- 内网包测试包随时修改`prerelease`，如`1.0.1-alpha.0`，手动执行ci任务发布
- 内网包正式包按需升级小版本发布，如`1.0.10`(如果没有破坏性改动的)，合并至`master`后拉取`tag`后自动刚发布
- 公网包每两周锁定至一个中版本如`1.1.0`，当灰度完成是发布至外网，注意：发布时会同步所有子包

## 公网发布
公网发布采用二方包管理平台进行，需要特定权限，发布目标地址: [idp-sdk](http://npm.qunhequnhe.com/repo/detail?id=25)

注意：
- 公网只发布中版本包即`1.x.0`的包
- 正式版发布会伴随三方接口正式上线(灰度100%)，接口正式上线前原则上不进行发布
- 公网发布只会发布`@manycore/idp-sdk`这个包(内容等价于`@qunhe/idp-sdk`)，公网发布后撤销期很短，基本无法撤销，注意不要引入敏感信息
- 发布权限问题咨询 @daodao, @wufan, @nanxue
