# 本地 SDK 开发环境配置文件
# 可以根据需要修改这些配置

# Docker 镜像配置
DOCKER_IMAGE=registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest

# 动态服务发现配置
# 脚本会自动扫描 openapi/ 目录下的所有子目录
# 跳过的公共目录（不作为服务处理）
SKIP_DIRECTORIES="common shared"

# 必需文件（服务目录必须包含这些文件才被识别为有效服务）
REQUIRED_FILES="restapi.yaml config-java.yaml"

# 服务配置模板
# 每个服务的配置都会遵循以下模式：
# {SERVICE_NAME}_RESTAPI=openapi/{service_name}/restapi.yaml
# {SERVICE_NAME}_CONFIG_JAVA=openapi/{service_name}/config-java.yaml  
# {SERVICE_NAME}_OUTPUT_JAVA=output/{service_name}/java

# Maven 配置
MAVEN_SETTINGS=ci-settings.xml

# OpenAPI Generator 配置
OPENAPI_GENERATOR_JAR=/opt/openapi-generator/openapi-generator-cli-7.12.0.jar

# 其他配置
OUTPUT_BASE_DIR=output
LOG_LEVEL=INFO

# 部署配置（可选）
# MAVEN_REPO_URL=your-maven-repo-url
# MAVEN_USERNAME=ozKgCmTX
# MAVEN_PASSWORD=5F1Q0mjjWnLd1cqQMBoWsoZwrm/BLK/7w9tVHoU8W5zn

# 示例：当前发现的服务
# 以下是基于当前目录结构自动发现的服务示例
# doorwindow: 门窗服务
# furniture: 家具设计服务
# 新增服务时，只需在 openapi/ 目录下创建相应的服务目录
# 并包含 restapi.yaml 和 config-java.yaml 文件即可 

# ManyCore SDK 发布配置
# 此文件包含发布所需的认证信息

# =================
# GPG 签名配置
# =================

# GPG 密钥 ID
GPG_KEY_NAME="0EA283C0"

# GPG 密钥密码
GPG_PASSPHRASE="Manycore666"

# GPG 私钥 (Base64编码)
# 请将你的私钥Base64字符串粘贴到下面
GPG_PRIVATE_KEY="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"

# =================
# Maven Central 配置
# =================

# Maven Central Portal 用户名
CENTRAL_USERNAME="ozKgCmTX"

# Maven Central Portal 令牌
CENTRAL_PASSWORD="5F1Q0mjjWnLd1cqQMBoWsoZwrm/BLK/7w9tVHoU8W5zn"

# =================
# 使用方法
# =================

# 1. 导出你的GPG私钥: gpg --armor --export-secret-keys 0EA283C0 | base64 -w 0
# 2. 将输出的Base64字符串填入上面的 GPG_PRIVATE_KEY
# 3. 加载配置: source scripts/local/config.env
# 4. 发布SDK: scripts/local/local-sdk-manager.sh deploy-rel doorwindow 