var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_Miniapp = injections.types["Miniapp"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Platform = {};
    var var_Host = {};
    var var_postMessage = {};
    var var_unknownType = {};
    var var_undefinedType = {};
    var var_onMessageReceive = {};
    var var_onMessageReceive_callback_function = {};
    var var_UI = {};
    var var_onThemeChange = {};
    var var_onThemeChange_fn_function = {};
    var var_stringType = {};
    var var_hideAll = {};
    var var_computeLayout = {};
    var var_Layout = {};
    var var_numberType = {};
    var var_ClientRect = {};
    var var_Toast = {};
    var var_Toast_info = {};
    var var_Toast_warn = {};
    var var_Toast_error = {};
    var var_Toast_success = {};
    var var_Design = {};
    var var_save = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_Promise_then_onreject = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_Scene3D = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_Miniapp, exportName: "Miniapp" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Platform": var_Platform,
        "Host": var_Host,
        "Miniapp": var_injection_Miniapp,
        "UI": var_UI,
        "Design": var_Design,
        "Scene3D": var_Scene3D,
    };
    var_Platform.type = BasicType.Object;
    var_Platform.properties = {
    };
    var_Host.type = BasicType.Object;
    var_Host.properties = {
        "postMessage": var_postMessage,
        "onMessageReceive": var_onMessageReceive,
    };
    var_postMessage.type = BasicType.Function;
    var_postMessage.name = "postMessage";
    var_postMessage.varying = false;
    var_postMessage.keepArgsHandle = false;
    var_postMessage.args = [var_unknownType];
    var_postMessage.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_undefinedType.type = BasicType.Undefined;
    var_onMessageReceive.type = BasicType.Function;
    var_onMessageReceive.name = "onMessageReceive";
    var_onMessageReceive.varying = false;
    var_onMessageReceive.keepArgsHandle = false;
    var_onMessageReceive.args = [var_onMessageReceive_callback_function];
    var_onMessageReceive.return = var_undefinedType;
    var_onMessageReceive_callback_function.type = BasicType.Function;
    var_onMessageReceive_callback_function.name = "";
    var_onMessageReceive_callback_function.varying = false;
    var_onMessageReceive_callback_function.keepArgsHandle = false;
    var_onMessageReceive_callback_function.args = [var_unknownType];
    var_onMessageReceive_callback_function.return = var_undefinedType;
    var_UI.type = BasicType.Object;
    var_UI.properties = {
        "onThemeChange": var_onThemeChange,
        "hideAll": var_hideAll,
        "computeLayout": var_computeLayout,
        "toast": var_Toast,
        "theme": var_stringType,
    };
    var_onThemeChange.type = BasicType.Function;
    var_onThemeChange.name = "onThemeChange";
    var_onThemeChange.varying = false;
    var_onThemeChange.keepArgsHandle = true;
    var_onThemeChange.args = [var_onThemeChange_fn_function];
    var_onThemeChange.return = var_undefinedType;
    var_onThemeChange_fn_function.type = BasicType.Function;
    var_onThemeChange_fn_function.name = "";
    var_onThemeChange_fn_function.varying = false;
    var_onThemeChange_fn_function.keepArgsHandle = false;
    var_onThemeChange_fn_function.args = [var_stringType];
    var_onThemeChange_fn_function.return = var_undefinedType;
    var_stringType.type = BasicType.String;
    var_hideAll.type = BasicType.Function;
    var_hideAll.name = "hideAll";
    var_hideAll.varying = false;
    var_hideAll.keepArgsHandle = false;
    var_hideAll.args = [];
    var_hideAll.return = var_undefinedType;
    var_computeLayout.type = BasicType.Function;
    var_computeLayout.name = "computeLayout";
    var_computeLayout.varying = false;
    var_computeLayout.keepArgsHandle = false;
    var_computeLayout.args = [];
    var_computeLayout.return = var_Layout;
    var_Layout.type = BasicType.Object;
    var_Layout.properties = {
        "windowWidth": var_numberType,
        "windowHeight": var_numberType,
        "topBar": var_ClientRect,
        "bottomBar": var_ClientRect,
        "leftPanel": var_ClientRect,
    };
    var_numberType.type = BasicType.Number;
    var_ClientRect.type = BasicType.Object;
    var_ClientRect.properties = {
        "top": var_numberType,
        "left": var_numberType,
        "bottom": var_numberType,
        "right": var_numberType,
        "width": var_numberType,
        "height": var_numberType,
    };
    var_Toast.type = BasicType.Object;
    var_Toast.properties = {
        "info": var_Toast_info,
        "warn": var_Toast_warn,
        "error": var_Toast_error,
        "success": var_Toast_success,
    };
    var_Toast_info.type = BasicType.Function;
    var_Toast_info.name = "info";
    var_Toast_info.varying = false;
    var_Toast_info.keepArgsHandle = false;
    var_Toast_info.args = [var_stringType];
    var_Toast_info.return = var_undefinedType;
    var_Toast_warn.type = BasicType.Function;
    var_Toast_warn.name = "warn";
    var_Toast_warn.varying = false;
    var_Toast_warn.keepArgsHandle = false;
    var_Toast_warn.args = [var_stringType];
    var_Toast_warn.return = var_undefinedType;
    var_Toast_error.type = BasicType.Function;
    var_Toast_error.name = "error";
    var_Toast_error.varying = false;
    var_Toast_error.keepArgsHandle = false;
    var_Toast_error.args = [var_stringType];
    var_Toast_error.return = var_undefinedType;
    var_Toast_success.type = BasicType.Function;
    var_Toast_success.name = "success";
    var_Toast_success.varying = false;
    var_Toast_success.keepArgsHandle = false;
    var_Toast_success.args = [var_stringType];
    var_Toast_success.return = var_undefinedType;
    var_Design.type = BasicType.Object;
    var_Design.properties = {
        "save": var_save,
    };
    var_save.type = BasicType.Function;
    var_save.name = "save";
    var_save.varying = false;
    var_save.keepArgsHandle = false;
    var_save.args = [];
    var_save.return = var_undefinedType_Promise;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_Scene3D.type = BasicType.Object;
    var_Scene3D.properties = {
    };
    
    return var_sourceFile;
};
