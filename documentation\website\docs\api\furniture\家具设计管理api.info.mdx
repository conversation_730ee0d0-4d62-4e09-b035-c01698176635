---
id: 家具设计管理api
title: "家具设计管理API"
description: "家具设计服务REST API"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import ApiLogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: 1.0.0"}
>
</span>

<Export
  url={"/specifications/services/furniture/openapi.yaml"}
  proxy={undefined}
>
  
</Export>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"家具设计管理API"}
>
</Heading>



家具设计服务REST API

为群核旗下设计工具提供家具数据的完整生命周期管理接口，支持家具模型的创建、编辑、查询和管理。提供单个和批量操作，支持不同数据视图以优化性能，适用于各种家具设计和管理场景。

**核心功能：**
- 家具CRUD操作：创建、获取、更新、删除家具实例
- 批量操作支持：高效处理大量家具数据的批量操作
- 多视图支持：BASIC和FULL视图，针对不同场景优化性能
- 分页查询：支持大量家具数据的分页展示和管理
- 产品组合：支持通过组合商品批量创建相关家具
- 异步处理：批量操作采用异步机制，保证系统响应性能
- 幂等性控制：通过requestId确保操作的幂等性

**业务应用场景：**
- 室内设计软件的家具管理
- 家具产品的三维展示和编辑
- 场景模板和套装家具的快速应用
- 家具数据的批量导入和同步
- 个性化家具定制和配置
- 空间布局优化和家具摆放

**技术特性：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：基于设计方案的权限验证
- 异步支持：长时间批量操作的异步处理机制

<div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    群核科技开发团队: [<EMAIL>](mailto:<EMAIL>)
  </span><span>
    URL: [https://wiki.manycore.com/furniture-design](https://wiki.manycore.com/furniture-design)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://manycore.com/license"}
  >
    群核科技专有许可证
  </a>
</div>
      