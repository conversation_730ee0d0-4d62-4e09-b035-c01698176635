import { BasicType } from '@qunhe/kls-abstraction';
import { getPromiseType } from '../helper';

/**
 * 异步函数类型
 */
export const AsyncFunctionType = {
    name: 'AsyncFunctionType',
    varying: false,
    keepArgsHandle: true,
    args: [
        {
            type: BasicType.Unknown,
        },
        {
            type: BasicType.Unknown,
        },
        {
            type: BasicType.Unknown,
        }
    ],
    type: BasicType.Function,
    return: getPromiseType({
        type: BasicType.Unknown,
    }),
}
