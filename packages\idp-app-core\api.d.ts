import { KPoint3d, KVector3d } from '@qunhe/math-apaas-api';
import { FrameHost } from '@qunhe/miniapp-types';

declare namespace IDP {
    namespace Platform {
        /**
         * 暂停云设计工具内置的场景视图更新，通常用于执行重度操作时提升性能使用
         * 在结束后需要调用 {@link IDP.Platform.resumeActivities} 恢复场景视图更新
         *
         * @internal
         */
        function pauseActivities(): void;
        /**
         * 恢复云设计工具内置的场景视图更新
         *
         * @internal
         */
        function resumeActivities(): void;
    }
}

declare namespace IDP {
    /**
     * 小程序与酷家乐页面承载方(以iframe形式承载酷家乐)通讯相关接口
     */
    namespace Host {
        /**
         * 发送消息至承载方，会验证承载方的origin是否满足要求，如果没有承载方或者不满足要求，则不会发送
         * @param data 发送的数据
         */
        function postMessage(data: any): void;

        /**
        * 监听来自酷家乐承载方的消息，会验证承载方的origin是否满足要求，如果没有承载方或者不满足要求，则不会触发
        * @param callback 消息回调
        */
        function onMessageReceive(callback: (data: any) => void): void;
    }
}

interface MountPoints {
    /**
     * 主挂载点，一个悬浮的小窗口
     */
    main: number,
    /**
     * 左侧素材栏挂载点
     */
    leftPanel: number,
    /**
     * 无外框全屏窗口挂载点
     *
     * @internal
     */
    fullscreen: number,
    /**
     * 后台挂载点
     *
     * @internal
     */
    background: number,

    /**
     * 右侧属性面板挂载点
     *
     * @internal
     */
    functionPanel: number,
}

/**
 * 主挂载点可配置项
 */
interface MainMountPointOptions {
    /**
     * 窗体显示模式, 不传入则不修改, 默认值`'windowed'`
     *
     * @remarks
     *
     * `'windowed'`: 窗口模式，标准显优先级。
     *
     * `'topWindowed'`: 窗口模式，高显示优先级。
     *
     * `'modal'`: 模态框模式，始终居中显示，将会存在模态遮罩，位置不可修改，将会忽略`resizable`、`draggable`、`position`，高显示优先级。
     *
     * `'fullscreen'`: 全屏模式，自动占满整个网页，将会忽略`resizable`和`draggable`，始终不可拖拽移动和缩放，并且不会接受任何尺寸修改，切换至此模式会修改iframe大小并锁定，高显示优先级。
     *
     * **注意**:
     * - 全屏模式仅为网页全屏，顶部标题边框不会移除。
     * - 窗口模式在不同优先级的显示模式之间切换时会导致`iframe`重载
     *
     * @defaultValue `'windowed'`
     */
    windowMode?: 'windowed' | 'topWindowed' | 'fullscreen' | 'modal';
    /**
     * 窗体左上角位置
     */
    position?: { x: number, y: number };
    /**
     * 容器是否可以被拖拽移动
     */
    draggable?: boolean;
    /**
     * 容器是否可以被用户拖拽缩放
     *
     * @remarks
     * - true: 均可改，此时 4 个 edge 和底部 2 个 corner 可拖拽；
     * - false: 均不可改，均不可拖拽；
     * - 'width': 宽度可改，仅左右两侧 edge 可拖拽；
     * - 'height'：高度可改，仅上下两侧 edge 可拖拽；
     */
    resizable?: boolean | 'width' | 'height';
    /**
     * 容器最小宽度，不传入则不修改，传入`undefined`则重置为默认配置，默认值`240`
     *
     * 若传入值小于默认值，则会被重置为默认值
     *
     * @defaultValue `240`
     */
    minWidth?: number | undefined;
    /**
     * 容器最大宽度，不传入则不修改，传入`undefined`则重置为默认配置，默认不限制
     *
     * @defaultValue `undefined`
     */
    maxWidth?: number | undefined;
    /**
     * 容器最小高度，不传入则不修改，传入`undefined`则重置为默认配置，默认值`120`
     *
     * 若传入值小于默认值，则会被重置为默认值
     *
     * @defaultValue `120`
     */
    minHeight?: number | undefined;
    /**
     * 容器最大高度，不传入则不修改，传入`undefined`则重置为默认配置，默认不限制
     *
     * @defaultValue `undefined`
     */
    maxHeight?: number | undefined;
    /**
     * **仅在窗口模式时生效**，默认值`false`
     *
     * 缩小浏览器：
     * 优先适配位置，移动至极限位置处，再适配面板宽度，跟随浏览器边缘自适应缩小，达到最小尺寸后保持不变
     *
     * 拉大浏览器：
     * 优先适配面板宽度，跟随浏览器边缘自适应拉大，达到原尺寸后保持不变，再适配位置，移动至原位置后保持不变
     *
     * 若默认适配规则不满足需求，可基于`IDP.UI.Layout.WindowResize`事件，搭配`UI`、`resize`等接口自主实现
     */
    widthResponsive?: boolean;
    /**
     * **仅在窗口模式时生效**，默认值`false`
     *
     * 缩小浏览器：
     * 优先适配位置，移动至极限位置处，再适配面板高度，跟随浏览器边缘自适应缩小，达到最小尺寸后保持不变
     *
     * 拉大浏览器：
     * 优先适配面板高度，跟随浏览器边缘自适应拉大，达到原尺寸后保持不变，再适配位置，移动至原位置后保持不变
     *
     * 若默认适配规则不满足需求，可基于`IDP.UI.Layout.WindowResize`事件，搭配`UI`、`resize`等接口自主实现
     */
    heightResponsive?: boolean;
    /**
     * **仅在窗口模式时生效**
     *
     * 窗口模式下的顶、底部尺寸规格
     *
     * @defaultValue `middle`
     */
    windowBarSize?: 'small' | 'middle' | 'large';
    /**
     * **仅在窗口模式时生效**
     *
     * 容器是否可以最小化
     *
     * @defaultValue false
     */
    minimizable?: boolean;
    /**
     * **仅在窗口模式和minimizable设置为true时生效**
     *
     * 容器是否是最小化状态
     *
     * @defaultValue false
     */
    isMinimized?: boolean;
}

/**
 * 左侧栏挂载点可配置项
 */
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface LeftPanelMountPointOptions {
    // 暂时无可配置项
}

/**
 * 小程序视图实例，用于控制iframe挂载，创建等
 */
interface View {
    /**
     * 可用的挂载点
     */
    readonly mountPoints: MountPoints;

    /**
     * 默认的iframe
     */
    readonly defaultFrame: FrameHost;

    /**
    * 创建一个iframe
    * @param srcName 视图资源名称
    * @returns 创建完成的iframe
    */
    createFrame(srcName: string): FrameHost;

    /**
     * 配置iframe容器选项，如果iframe未挂载，则不会产生响应
     * @param frame 目标iframe
     * @param options 可配置项，视挂载点而定，挂载点的可配置项参考实际配置类型
     */
    setContainerOptions(frame: FrameHost, options: MainMountPointOptions | LeftPanelMountPointOptions): void;
}

// miniapp
declare namespace IDP {
    /**
     * 小程序基础接口，包括退出等
     *
     * @vm-type Miniapp
     */
    namespace Miniapp {
        const view: View;

        /**
         * 获取启动参数
         *
         * @internal
         */
        function getLaunchOptions(): any;

        /**
         * 向所有小程序广播消息
         *
         * @internal
         */
        function broadcast(message: any): void;

        /**
         * 退出小程序
         */
        function exit(): void;
    }
}

interface Toast {
    /**
     * 显示一个info形式的toast
     * @param message 消息
     */
    info(message: string): void;
    /**
    * 显示一个warn形式的toast
    * @param message 消息
    */
    warn(message: string): void;
    /**
     * 显示一个error形式的toast
     * @param message 消息
     */
    error(message: string): void;
    /**
     * 显示一个success形式的toast
     * @param message 消息
     */
    success(message: string): void;

}

type Theme = 'light-theme' | 'dark-theme';

// ui
declare namespace IDP {
    /**
     * UI控制相关接口
     */
    namespace UI {

        /**
         * 布局信息, 字段含义等同于`getBoundingClientRect`，建议参考： https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect
         */
        interface ClientRect {
            top: number;
            left: number;
            bottom: number;
            right: number;
            width: number;
            height: number;
        }

        /**
         * 页面布局尺寸信息
         */
        interface Layout {
            /**
             * 窗口宽度，等同于`window.innerWidth`
             */
            windowWidth: number;
            /**
             * 窗口高度，等同于`window.innerHeight`
             */
            windowHeight: number;
            /**
             * 顶部栏布局信息
             */
            topBar: ClientRect;
            /**
             * 底部栏布局信息
             */
            bottomBar: ClientRect;
            /**
             * 左侧栏布局信息
             */
            leftPanel: ClientRect;
        }

        /**
         * 注册一个回调当主题发生变更的时候触发
         * @param fn callback
         *
         * @vm-function-keepArgsHandle
         */
        function onThemeChange(fn: (theme: Theme) => void): void;
        /**
         * 隐藏除了场景之外的所有ui元素，包括左侧栏，顶部栏等
         */
        function hideAll(): void;
        /**
         * 获取计算后的布局信息
         */
        function computeLayout(): Layout;
        /**
         * toast实例
         */
        const toast: Toast;
        /**
         * 当前的主题
         */
        const theme: Theme;
    }
}

// design
declare namespace IDP {
    namespace Design {
        /**
         * 保存方案
         */
        export function save(): Promise<void>;

        /**
         * 重新加载方案
         *
         * @internal
         */
        export function reloadAsync(): Promise<void>;
    }
}

/**
 * @internal
 */
interface CommonCameraAttributes {
    near: number;
    far: number;
    position: KPoint3d;
    lookAt: KPoint3d;
    up: KVector3d;
    minNear: number;
    maxNear: number;
}

/**
 * @internal
 */
interface OrthCameraAttrs extends CommonCameraAttributes {
    frustumSize: number;
}

/**
 * @internal
 */
interface PerspectiveCameraAttrs extends CommonCameraAttributes {
    fov: number;
}

/**
 * @internal
 */
interface CameraAttributes extends OrthCameraAttrs, PerspectiveCameraAttrs { }

/**
 * 相机类型
 * @internal
 */
declare enum CameraType {
    Perspective = 'Perspective',
    Orthographic = 'Orthographic'
}

declare namespace IDP {
    namespace Scene3D {
        /**
         * 更新相机类型
         * @internal
         */
        function updateCameraType(cameraType: CameraType, keepAttributesOfPrevCamera?: boolean): void;

        /**
         * 更新相机参数
         * @internal
         */
        function updateCameraAttributes(attributes: Partial<CameraAttributes>): void;

        /**
         * 获取相机参数
         * @internal
         */
        function getCameraAttributes(): CameraAttributes;
    }
}

export { IDP };

    export { };

