import { IDP } from './api';

/** @internal */
export interface HighlightElementConfig extends IDP.DB.Types.ElementId {
    /**
     * 高亮颜色，默认为 0x00eeff
     */
    color?: number;
}

/**
 * @internal
 */
type ConfirmType = 'info' | 'warn' | 'error';

/**
 * @internal
 */
type Size = 'small' | 'medium' | 'large' | 'extra-large';

/**
 * Dialog参数
 * @internal
 */
export interface DialogProps {
    /**
     * 类型
     */
    type: ConfirmType;
    /**
     * 标题
     */
    title: string;
    /**
     * Dialog内容
     */
    description?: string;
    /**
     * 内容
     */
    size?: Size;
    /**
     * “不再提醒”文本，当传入该值时会在左下角渲染一个 checkbox，并在 onOk 和 onCancel 回调中传出其选中状态
     */
    notRemindAgainText?: string;
    /**
     * 是否显示右上角的关闭按钮，传入title时默认为true，未传入title时默认为false
     */
    showCloseButton?: boolean;
    /**
     * 取消按钮文字，若无则无取消按钮
     */
    cancelText?: string;
    /**
     * 确认按钮文字，若无则无确认按钮
     */
    okText?: string;
    /**
     * 点击确定回调
     */
    onOk?: () => void;
    /**
     * 点击取消回调
     */
    onCancel?: () => void;
}
