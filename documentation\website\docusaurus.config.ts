import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';
import type * as OpenApiPlugin from "docusaurus-plugin-openapi-docs";
import * as fs from 'node:fs';
import * as path from 'node:path';

/**
 * 将连字符分隔的字符串转换为驼峰命名（用于 JavaScript 变量名）
 */
function toCamelCase(str: string): string {
  return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * 动态生成 OpenAPI 配置
 */
function generateOpenApiConfig(): Record<string, any> {
  const configPath = path.join(__dirname, 'openapi-config.json');
  
  // 如果配置文件存在，读取它
  if (fs.existsSync(configPath)) {
    try {
      const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      console.log('✅ 使用动态生成的 OpenAPI 配置');
      return configData.openApiConfig || {};
    } catch (error) {
      console.warn('⚠️ 无法读取 OpenAPI 配置文件，使用默认配置');
    }
  }
  
  // 回退到手动扫描
  console.log('🔍 动态扫描 OpenAPI 文件...');
  return scanOpenApiFiles();
}

/**
 * 动态生成导航配置
 */
function generateNavbarItems() {
  const navbarConfigPath = path.join(__dirname, 'navbar-config.json');
  
  // 默认导航配置 - 移除 apiSidebar 引用
  const defaultItems = [
    {
      type: 'html',
      value: '<hr style="margin: 0.5rem 0;">',
    },
  ];

  // 如果存在动态配置文件，使用它
  if (fs.existsSync(navbarConfigPath)) {
    try {
      const navbarConfig = JSON.parse(fs.readFileSync(navbarConfigPath, 'utf8'));
      console.log('✅ 使用动态生成的导航配置');
      
      // 组合默认配置和动态配置
      const dynamicItems = navbarConfig.services.map((service: any) => ({
        type: 'docSidebar',
        sidebarId: `${toCamelCase(service.id)}Sidebar`,
        label: `${service.icon} ${service.name}`,
      }));

      return [...defaultItems, ...dynamicItems];
    } catch (error) {
      console.warn('⚠️ 无法读取导航配置文件，使用默认配置');
    }
  }

  // 回退到默认配置
  console.log('🔍 使用默认导航配置...');
  return [
    ...defaultItems,
    {
      type: 'docSidebar',
      sidebarId: 'diymodeldwSidebar',
      label: '🏠 DIY模型数据仓库',
    },
    {
      type: 'docSidebar',
      sidebarId: 'furnitureDesignSidebar',
      label: '🪑 家具设计服务',
    },
    {
      type: 'docSidebar',
      sidebarId: 'designinfoserviceSidebar',
      label: '📐 设计信息服务',
    },
  ];
}

/**
 * 手动扫描 OpenAPI 文件（回退方案）
 */
function scanOpenApiFiles(): Record<string, any> {
  const openApiDir = path.join(__dirname, '..', 'openapi');
  const config: Record<string, any> = {};
  
  if (!fs.existsSync(openApiDir)) {
    console.warn('⚠️ openapi 文件夹不存在');
    return {};
  }
  
  try {
    const subdirs = fs.readdirSync(openApiDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
      .filter(name => !['common', 'shared'].includes(name));
    
    for (const serviceDir of subdirs) {
      const restapiPath = path.join(openApiDir, serviceDir, 'restapi.yaml');
      
      if (fs.existsSync(restapiPath)) {
        const configId = serviceDir;
        config[configId] = {
          specPath: `../openapi/${serviceDir}/restapi.yaml`,
          outputDir: `docs/api/${serviceDir}`,
          sidebarOptions: {
            groupPathsBy: "tag",
            categoryLinkSource: "tag",
          },
          downloadUrl: `/openapi/${serviceDir}/restapi.yaml`,
          hideSendButton: false,
          showSchemas: false,
          expandSchemas: false,
          // 防止复杂schema内联展开的额外配置
          schemaInFile: false,
          proxy: undefined,
          template: "api.mustache", 
          expandResponsesBy: "none",
          expandRequestsBy: "none",
          showExtensions: false,
          showCommonSchemas: false,
          // 过滤abstract和internal类型
          schemaFilter: (schema: any) => {
            return !schema['x-abstract'] && !schema['x-internal'];
          },
          // 限制schema展开深度
          maxDisplayedEnumValues: 10,
          downloadFileName: `${serviceDir}-api.yaml`,
        };
      }
    }
  } catch (error) {
    console.error('❌ 扫描 OpenAPI 文件失败:', error);
  }
  
  return config;
}

// 获取动态配置
const openApiConfig = generateOpenApiConfig();
const navbarItems = generateNavbarItems();

const config: Config = {
  title: '群核科技 API 文档中心',
  tagline: '全栈家居云设计平台 API 参考',
  favicon: 'img/logo.svg',

  // 生产部署配置
  url: 'https://manual.qunhequnhe.com/',
  baseUrl: '/manycoreapi-demo/0.0.4/',

  // GitLab Pages 配置
  organizationName: 'your-organization',
  projectName: 'backend-api-sdk',

  onBrokenLinks: 'ignore',
  onBrokenMarkdownLinks: 'ignore',

  // 构建性能优化配置
  future: {
    experimental_faster: false,
    v4: true
  },

  // 自定义插件配置
  plugins: [
    // Webpack优化插件
    require.resolve('./plugins/webpack-optimization-plugin.js'),
  ],



  markdown: {
    mermaid: true,
  },




  i18n: {
    defaultLocale: 'zh-Hans',
    locales: ['zh-Hans'],
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: './sidebars.ts',
          docItemComponent: "@theme/ApiItem",
          // 移除编辑链接
          editUrl: undefined,
          // 优化大型文档的处理
          showLastUpdateTime: false,
          showLastUpdateAuthor: false,
          // 减少内存使用和加快构建速度
          exclude: [
            '**/_*.{js,jsx,ts,tsx,md,mdx}',
            '**/_*/**',
            '**/*.test.{js,jsx,ts,tsx}',
            '**/__tests__/**',
          ],
          // 启用增量构建
          includeCurrentVersion: true,
          // 禁用不必要的功能以提高性能
          breadcrumbs: false,
          // 优化搜索索引
          remarkPlugins: [],
          rehypePlugins: [],
        },
        // 禁用博客功能
        blog: false,
        theme: {
          customCss: './src/css/custom.css',
        },
        // 禁用一些不必要的功能以提高构建速度
        gtag: undefined,
        googleAnalytics: undefined,
        googleTagManager: undefined,

      } satisfies Preset.Options,
    ],
  ],

  plugins: [
    [
      'docusaurus-plugin-openapi-docs',
      {
        id: "api",
        docsPluginId: "classic",
        config: openApiConfig as OpenApiPlugin.Options,
      },
    ],

  ],

  themes: ["docusaurus-theme-openapi-docs"],

  themeConfig: {
    image: 'img/docusaurus-social-card.jpg',
    navbar: {
      title: '群核科技 API',
      logo: {
        alt: '群核科技 Logo',
        src: 'img/logo.svg',
      },
      hideOnScroll: true,
      style: 'primary',
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: '开发指南',
        },
        {
          type: 'dropdown',
          label: 'API 参考',
          position: 'left',
          items: navbarItems,
        },
        {to: '/blog', label: '📝 更新日志', position: 'left'},
        {
          type: 'search',
          position: 'right',
        },
        {
          type: 'localeDropdown',
          position: 'right',
        },
        {
          href: 'https://developers.qunheco.com',
          label: '开发者控制台',
          position: 'right',
          className: 'header-github-link',
        },
        {
          href: 'https://gitlab.com/your-org/backend-api-sdk',
          label: 'GitLab',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      logo: {
        alt: '群核科技 Logo',
        src: 'img/logo.svg',
        href: 'https://www.kujiale.com',
        width: 32,
        height: 32,
      },
      links: [
        {
          title: '📚 产品文档',
          items: [
            {
              label: '快速开始',
              to: '/docs/intro',
            },
            {
              label: 'API 概览',
              to: '/docs/api',
            },
            {
              label: '最佳实践',
              to: '/docs/guides/best-practices',
            },
            {
              label: 'SDK 开发包',
              to: '/docs/guides/sdks',
            },
          ],
        },
        {
          title: '🔗 API 服务',
          items: [
            {
              label: '门窗服务',
              to: '/docs/api/diymodeldw-service',
            },
            {
              label: '家具设计服务',
              to: '/docs/api/furniture-design-service',
            },
            {
              label: '设计信息服务',
              to: '/docs/api/designinfoservice',
            },
          ],
        },
        {
          title: '🌐 开发者社区',
          items: [
            {
              label: '开发者控制台',
              href: 'https://developers.qunheco.com',
            },
            {
              label: '技术博客',
              href: 'https://tech.qunheco.com',
            },
            {
              label: '开发者论坛',
              href: 'https://forum.qunheco.com',
            },
            {
              label: 'GitHub',
              href: 'https://github.com/qunhe',
            },
          ],
        },
        {
          title: '📞 支持与帮助',
          items: [
            {
              label: '更新日志',
              to: '/blog',
            },
            {
              label: '服务状态',
              href: 'https://status.qunheco.com',
            },
            {
              label: '技术支持',
              href: 'mailto:<EMAIL>',
            },
            {
              label: 'GitLab 仓库',
              href: 'https://gitlab.com/your-org/backend-api-sdk',
            },
          ],
        },
      ],
      copyright: `
        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #334155;">
          <p>Copyright © ${new Date().getFullYear()} 群核科技有限公司. All rights reserved.</p>
          <p style="font-size: 0.875rem; opacity: 0.8;">
            Built with ❤️ by Qunhe Technology Developer Team | Powered by Docusaurus
          </p>
        </div>
      `,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
      additionalLanguages: ['bash', 'diff', 'json'],
    },
    algolia: {
      // 当你准备好时添加 Algolia 搜索配置
      appId: 'YOUR_APP_ID',
      apiKey: 'YOUR_SEARCH_API_KEY',
      indexName: 'YOUR_INDEX_NAME',
      contextualSearch: true,
    },
  } satisfies Preset.ThemeConfig,
};

export default config; 