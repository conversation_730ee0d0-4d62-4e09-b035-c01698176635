.idea

.vscode

temp_templates

build

output

examples

**/node_modules
ui/**/dist

# Docusaurus 相关自动生成文件
docs-site/build/
docs-site/.docusaurus/
docs-site/node_modules/
docs-site/navbar-config.json
docs-site/openapi-config.json
docs-site/sidebars.ts

# OpenAPI 插件自动生成的文档文件
# API 接口文档
docs-site/docs/api/*/*.api.mdx
# OpenAPI 定义信息文档
docs-site/docs/api/*/*.info.mdx
# API 标签文档
docs-site/docs/api/*/*.tag.mdx
# Schema 定义文档
docs-site/docs/api/*/schemas/*.schema.mdx
# 自动生成的侧边栏配置
docs-site/docs/api/*/sidebar.ts

# 构建和缓存文件
docs-site/package-lock.json
*.log
*.tgz

/test-results

docs-site/docs/api/

# 本地发布配置文件（包含敏感信息）
scripts/local/config.env
scripts/local/.env
.manycore-sdk-config
**/.env.local

public/