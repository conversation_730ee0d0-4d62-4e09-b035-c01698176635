import { BasicType } from '@qunhe/kls-abstraction';
import { UnknownType } from '../basic/base-types';

/**
 * create an promise type for transform to vm
 * @param value promise value type
 */
export function getPromiseType(value: any) {
    const secondArgOfThenType = {
        type: BasicType.Function,
        keepArgsHandle: false,
        name: '',
        varying: false,
        args: [UnknownType],
        return: {
            type: BasicType.Undefined
        },
    };

    const catchType = {
        type: BasicType.Function,
        keepArgsHandle: false,
        name: '',
        varying: false,
        args: [{
            type: BasicType.Function,
            keepArgsHandle: false,
            name: '',
            varying: false,
            args: [UnknownType],
            return: {
                type: BasicType.Undefined
            },
        }],
        return: {
            type: BasicType.Undefined
        },
    };

    return {
        type: BasicType.Object,
        extends: [],
        properties: {
            then: {
                type: BasicType.Function,
                keepArgsHandle: true,
                name: '',
                varying: false,
                args: [{
                    type: BasicType.Function,
                    keepArgsHandle: false,
                    name: '',
                    varying: false,
                    args: [value],
                    return: {
                        type: BasicType.Undefined
                    },
                }, secondArgOfThenType],
                return: {
                    type: BasicType.Undefined
                }
            },
            catch: catchType,
        }
    };
}
