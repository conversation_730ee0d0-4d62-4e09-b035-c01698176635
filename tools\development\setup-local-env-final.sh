#!/bin/bash

# API 评审流程本地环境设置脚本 (最终修复版本)
# 支持内部 npm registry 环境

set -e

echo "🚀 开始设置 API 评审流程本地环境..."

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    echo "📥 下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js 已安装: $(node --version)"

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ npm 已安装: $(npm --version)"

# 检查当前 npm registry
CURRENT_REGISTRY=$(npm config get registry)
echo "🔍 当前 npm registry: $CURRENT_REGISTRY"

# 检查是否需要切换 registry
NEED_SWITCH_REGISTRY=false
if [[ $CURRENT_REGISTRY != *"npmjs.org"* ]]; then
    echo "⚠️  检测到您使用的是内部 npm registry"
    echo "   某些包可能不在内部仓库中，需要从官方 registry 安装"
    
    read -p "🔄 是否临时切换到官方 npm registry 进行安装？ (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        NEED_SWITCH_REGISTRY=true
        echo "📦 临时切换到官方 npm registry..."
        npm config set registry https://registry.npmjs.org/
        echo "✅ 已切换到官方 registry"
    else
        echo "⚠️  将尝试使用当前 registry，某些包可能无法安装"
    fi
fi

# 安装必要的 CLI 工具
echo "📦 安装必要的 CLI 工具..."

# 安装 Spectral CLI
echo "  🔧 安装 Spectral CLI (OpenAPI 规范校验)..."
if npm install -g @stoplight/spectral-cli --quiet; then
    echo "  ✅ Spectral CLI 安装成功"
else
    echo "  ❌ Spectral CLI 安装失败"
    if [ "$NEED_SWITCH_REGISTRY" = false ]; then
        echo "  💡 建议切换到官方 npm registry 重试"
    fi
fi

# 安装文档生成工具
echo "  🔧 安装文档生成工具..."
echo "  📝 使用 Redoc CLI 作为默认文档生成工具 (简单稳定)..."

# 尝试安装 Redoc CLI (默认推荐)
if npm install -g redoc-cli --quiet; then
    echo "  ✅ Redoc CLI 安装成功"
    ELEMENTS_INSTALLED=true
else
    echo "  ❌ Redoc CLI 安装失败"
    echo "  🔄 尝试安装社区 Elements CLI 作为替代..."
    
    if npm install -g @skriptfabrik/elements-cli --legacy-peer-deps --quiet; then
        echo "  ✅ Elements CLI (社区版本) 安装成功"
        ELEMENTS_INSTALLED=true
    else
        echo "  ❌ Elements CLI (社区版本) 安装失败"
        echo "  🔄 尝试安装 Swagger UI CLI 作为第三替代..."
        
        if npm install -g swagger-ui-cli --quiet; then
            echo "  ✅ Swagger UI CLI 安装成功"
            ELEMENTS_INSTALLED=true
        else
            echo "  ❌ 所有 CLI 工具都安装失败"
            ELEMENTS_INSTALLED=false
        fi
    fi
fi

# 恢复原 registry 设置
if [ "$NEED_SWITCH_REGISTRY" = true ]; then
    echo "🔄 恢复原 npm registry 设置..."
    npm config set registry "$CURRENT_REGISTRY"
    echo "✅ 已恢复原 registry: $CURRENT_REGISTRY"
fi

# 验证安装
echo "🔍 验证工具安装..."

SPECTRAL_INSTALLED=false
if command -v spectral &> /dev/null; then
    echo "  ✅ Spectral CLI: $(spectral --version)"
    SPECTRAL_INSTALLED=true
else
    echo "  ❌ Spectral CLI: 未安装"
fi

if command -v redoc-cli &> /dev/null; then
    echo "  ✅ Redoc CLI (默认): $(redoc-cli --version 2>/dev/null || echo 'version unknown')"
    ELEMENTS_INSTALLED=true
elif command -v elements &> /dev/null; then
    echo "  ✅ Elements CLI (社区版): $(elements --version 2>/dev/null || echo 'version unknown')"
    ELEMENTS_INSTALLED=true
elif command -v swagger-ui-serve &> /dev/null; then
    echo "  ✅ Swagger UI CLI: $(swagger-ui-serve --version 2>/dev/null || echo 'version unknown')"
    ELEMENTS_INSTALLED=true
else
    echo "  ❌ 文档生成工具: 未安装"
    ELEMENTS_INSTALLED=false
fi

# 如果没有文档生成工具，提供替代方案
if [ "$ELEMENTS_INSTALLED" = false ]; then
    echo ""
    echo "🔧 没有文档生成工具，提供替代方案："
    echo ""
    echo "方案1: 使用在线工具"
    echo "  - Swagger Editor: https://editor.swagger.io/"
    echo "  - Redoc 在线版: https://redocly.github.io/redoc/"
    echo "  - Stoplight Studio: https://stoplight.io/studio/"
    echo ""
    echo "方案2: 手动安装 (如果网络允许)"
    echo "  npm config set registry https://registry.npmjs.org/"
    echo "  npm install -g redoc-cli  # 推荐：简单稳定"
    echo "  # 或者"
    echo "  npm install -g @skriptfabrik/elements-cli --legacy-peer-deps"
    echo ""
    echo "方案3: 本地 HTML 生成"
    echo "  # 创建简单的 HTML 文件使用 Stoplight Elements Web 组件"
    echo ""
fi

# 设置 Git hooks（可选）
if [ "$SPECTRAL_INSTALLED" = true ]; then
    read -p "📎 是否设置 Git pre-commit hook 以自动校验 OpenAPI 文件？ (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -d ".git" ]; then
            echo "🔗 设置 Git pre-commit hook..."
            
            # 创建 hooks 目录（如果不存在）
            mkdir -p .git/hooks
            
            # 复制 pre-commit hook 脚本
            if [ -f "scripts/pre-commit-hook.sh" ]; then
                cp scripts/pre-commit-hook.sh .git/hooks/pre-commit
                chmod +x .git/hooks/pre-commit
                echo "✅ Pre-commit hook 已设置"
                echo "💡 Hook 只需要 Spectral CLI（必需），文档生成工具是可选的"
            else
                echo "❌ Pre-commit hook 脚本不存在"
            fi
        else
            echo "❌ 当前目录不是 Git 仓库"
        fi
    fi
else
    echo "⚠️  由于 Spectral CLI 未安装，建议先安装后再设置 Git hooks"
    echo "   Pre-commit hook 需要 Spectral CLI 进行 OpenAPI 规范校验"
fi

# 测试本地校验功能
if [ "$SPECTRAL_INSTALLED" = true ]; then
    echo "🧪 测试本地校验功能..."
    
    # 查找现有的 OpenAPI 文件进行测试
    SAMPLE_FILE=$(find openapi -name "*.yaml" | head -1)
    
    if [ -n "$SAMPLE_FILE" ]; then
        echo "📄 使用示例文件测试: $SAMPLE_FILE"
        
        echo "  🔍 测试 Spectral 校验..."
        if spectral lint "$SAMPLE_FILE"; then
            echo "  ✅ Spectral 校验测试通过"
        else
            echo "  ⚠️  Spectral 校验发现问题（这是正常的，可能需要修复规范文件）"
        fi
        
        if [ "$ELEMENTS_INSTALLED" = true ]; then
            echo "  📖 测试文档生成..."
            temp_doc="/tmp/test-api-doc.html"
            
            # 优先使用 redoc-cli，然后是 elements，最后是 swagger-ui-cli
            if command -v redoc-cli &> /dev/null; then
                if redoc-cli build "$SAMPLE_FILE" --output "$temp_doc" > /dev/null 2>&1; then
                    echo "  ✅ Redoc 文档生成测试通过"
                    rm -f "$temp_doc"
                fi
            elif command -v elements &> /dev/null; then
                if elements export "$SAMPLE_FILE" > "$temp_doc" 2>/dev/null; then
                    echo "  ✅ Elements 文档生成测试通过"
                    rm -f "$temp_doc"
                fi
            elif command -v swagger-ui-serve &> /dev/null; then
                echo "  ✅ Swagger UI CLI 可用 (需要手动运行生成文档)"
            fi
        fi
    else
        echo "📄 未找到 OpenAPI 文件，跳过测试"
    fi
else
    echo "⚠️  由于 Spectral CLI 未安装，跳过校验功能测试"
fi

echo ""
echo "🎉 本地环境设置完成！"
echo ""

# 根据安装情况提供不同的指导
if [ "$SPECTRAL_INSTALLED" = true ] && [ "$ELEMENTS_INSTALLED" = true ]; then
    echo "✅ 所有工具已成功安装"
elif [ "$SPECTRAL_INSTALLED" = true ]; then
    echo "⚠️  部分工具安装成功，文档生成功能需要手动配置"
else
    echo "⚠️  关键工具安装失败，请检查网络和 npm 配置"
fi

echo ""
echo "📚 下一步操作："
echo "  1. 阅读文档: docs/quick-start-guide.md"
echo "  2. 了解评审流程: docs/api-review-process.md"
if [ "$SPECTRAL_INSTALLED" = true ]; then
    echo "  3. 运行演示: bash scripts/demo-api-change.sh"
fi
echo ""
echo "🔧 常用命令："
if [ "$SPECTRAL_INSTALLED" = true ]; then
    echo "  - 校验 OpenAPI 文件: spectral lint openapi/your-service/restapi.yaml"
fi
if command -v redoc-cli &> /dev/null; then
    echo "  - 生成文档预览: redoc-cli build openapi/your-service/restapi.yaml --output preview.html"
elif command -v elements &> /dev/null; then
    echo "  - 生成文档预览: elements export openapi/your-service/restapi.yaml > preview.html"
elif command -v swagger-ui-serve &> /dev/null; then
    echo "  - 生成文档预览: swagger-ui-serve openapi/your-service/restapi.yaml"
fi
echo ""
echo "❓ 如有问题，请查看文档或联系团队支持" 