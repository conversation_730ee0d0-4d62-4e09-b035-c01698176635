{"id": "api/camera/camera-infrastructure", "title": "Camera Infrastructure", "description": "Camera Infrastructure", "source": "@site/docs/api/camera/camera-infrastructure.tag.mdx", "sourceDirName": "api/camera", "slug": "/api/camera/camera-infrastructure", "permalink": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "frontMatter": {"id": "camera-infrastructure", "title": "Camera Infrastructure", "description": "Camera Infrastructure", "custom_edit_url": null}, "sidebar": "cameraSidebar", "previous": {"title": "Introduction", "permalink": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api"}, "next": {"title": "获取相机列表", "permalink": "/manycoreapi-demo/0.0.4/docs/api/camera/get-cameras"}}