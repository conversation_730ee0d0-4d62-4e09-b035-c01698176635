import { Type } from '@qunhe/kls-abstraction';
import * as APIType from '@qunhe/idp-yundesign/api';
export declare function createVMBindingType(typeInjections: {
    types: {
        "StartDragProductPromiseResult": Type;
        "PromiseResultWithUuid": Type;
        "PromiseResult": Type;
    };
    packages: {
        "@qunhe/math-apaas-api": {
            "KPoint3d": Type;
            "KVector3d": Type;
            "KEuler": Type;
        };
    };
}): Type & { VALUE_TYPE: typeof APIType; };
