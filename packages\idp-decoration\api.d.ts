import { IDP as IDPCommon } from '@qunhe/idp-common';
import { KBoundedCurve3d, KSurface } from '@qunhe/math-apaas-api';

declare namespace IDP {

    namespace DB {
        namespace Types {
            /**
             * 硬装3d面数据
             * @internal
             */
            interface DecorationFace3d {
                id: string;
                contour: KBoundedCurve3d[];
                holes: KBoundedCurve3d[][];
                surface: KSurface;
            }

            /**
             * 硬装造型类型
             * @internal
             */
            enum PlanarModelingDesignType {
                Floor = 'Floor', // 地台
                Ceiling = 'Ceiling', // 吊顶
            }
        }

        namespace Methods {
            /**
             * 通过 taskId 请求后端以更新硬装数据
             * @param taskId 由后端生成的id
             * @returns true 表示应用成功 false 表示应用失败
             * @internal
             */
            function updateDecorationByTaskIdAsync(taskId: string): Promise<boolean>;

            /**
             * 根据id清理硬装线条（含铺贴踢脚线）
             * @param 删除线条的 id
             * @internal
             */
            function deleteMoldingById(id: IDPCommon.DB.Types.ElementId): boolean;

            /**
            * 判断硬装商品是否在方案中
            * @param productId 商品id
            * @returns true 表示存在 false 表示不存在
            * @internal
            */
            function isProductInDecorationDesignAsync(productId: string): Promise<boolean>;

            /**
             * 替换硬装商品
             * @param originProductId 原商品id，targetProductId 目标商品id
             * @returns true 表示是替换成功 false 表示替换失败
             * @internal
             */
            function replaceDecorationProductAsync(originProductId: string, targetProductId: string): Promise<boolean>;

            /**
             * 判断一个商品是否为硬装商品
             * @param ProductId 商品id
             * @returns true 表示是硬装商品 false 表示不是硬装商品
             * @internal
             */
            function isDecorationProductAsync(productId: string): Promise<boolean>;

            /**
             * 获取硬装地台造型中所有凸起造型的顶面，包含凸起为0的造型，不包含造型侧面
             * 没有硬装地台造型数据时，返回空数组
             * @internal
             * @deprecated 请使用 getPlanarModelingDesignExtrusionFaces
             */
            function getFloorModelingExtrusionFaces(): IDP.DB.Types.DecorationFace3d[];

            /**
             * 获取硬装造型中所有的凸起面，包含凸起为0的面，不包含侧面
             * 没有造型数据时，返回空数组
             * @param type 需要的造型类型
             * @internal
             */
            function getPlanarModelingDesignExtrusionFaces(type: IDP.DB.Types.PlanarModelingDesignType): IDP.DB.Types.DecorationFace3d[];
        }
    }
}

export { IDP };
