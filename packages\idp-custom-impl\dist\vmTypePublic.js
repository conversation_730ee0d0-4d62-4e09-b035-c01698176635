var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_CustomModel = injections.types["CustomModel"];
    var var_injection_ISelectionEvtType = injections.types["ISelectionEvtType"];
    var var_injection_UnknownType = injections.types["UnknownType"];
    var var_injection_NewCustomModelByCategoryAsync = injections.types["NewCustomModelByCategoryAsync"];
    var var_injection_NewCustomModelByProductIdAsync = injections.types["NewCustomModelByProductIdAsync"];
    var var_injection_GetCustomModelByModelIdAsync = injections.types["GetCustomModelByModelIdAsync"];
    var var_injection_FindTopModelsAsync = injections.types["FindTopModelsAsync"];
    var var_injection_UpdateCustomModelAsync = injections.types["UpdateCustomModelAsync"];
    var var_injection_DeleteTopModelsAsync = injections.types["DeleteTopModelsAsync"];
    var var_injection_DragCustomProductPromiseResult = injections.types["DragCustomProductPromiseResult"];
    var var_injection_AsyncFunctionType = injections.types["AsyncFunctionType"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Design = {};
    var var_copyAsync = {};
    var var_copyAsync_objectLiteral_Promise = {};
    var var_copyAsync_objectLiteral_Promise_then = {};
    var var_copyAsync_objectLiteral_Promise_then_onresolve = {};
    var var_copyAsync_objectLiteral = {};
    var var_stringType = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_Custom = {};
    var var_Common = {};
    var var_openTerminalAsync = {};
    var var_OpenOrCloseOption = {};
    var var_numberType = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_closeTerminalAsync = {};
    var var_getCurrentToolType = {};
    var var_getDesignType = {};
    var var_getOrderDesignId = {};
    var var_isDesignLoaded = {};
    var var_booleanType = {};
    var var_EToolType = {};
    var var_EDesignType = {};
    var var_InnerSpace = {};
    var var_getSelected = {};
    var var_IInnerSpaceData = {};
    var var_Number3 = {};
    var var_Number2 = {};
    var var_EFace = {};
    var var_DoorCoveringHardware = {};
    var var_getOption = {};
    var var_IDoorCoveringHardwareMode = {};
    var var_setOption = {};
    var var_setProductId = {};
    var var_SetOption = {};
    var var_getSelectedElements = {};
    var var_ElementId_Array = {};
    var var_ElementId = {};
    var var_Mode = {};
    var var_current = {};
    var var_enter = {};
    var var_enterAsync = {};
    var var_exit = {};
    var var_ECustomAppMode = {};
    var var_Design_1 = {};
    var var_splitDesignByElementsAsync = {};
    var var_ISplitDesignParams = {};
    var var_ElementId_Array_1 = {};
    var var_ElementId_1 = {};
    var var_ISplitDesignResponse_Promise = {};
    var var_ISplitDesignResponse_Promise_then = {};
    var var_ISplitDesignResponse_Promise_then_onresolve = {};
    var var_ISplitDesignResponse = {};
    var var_Export = {};
    var var_getDesignJsonAsync = {};
    var var_IDesignExportDataParams = {};
    var var_unknownType_Promise = {};
    var var_unknownType_Promise_then = {};
    var var_unknownType_Promise_then_onresolve = {};
    var var_getDesignFullJsonAsync = {};
    var var_getModelJsonAsync = {};
    var var_IGetModelJsonOption = {};
    var var_ModelJson_Promise = {};
    var var_ModelJson_Promise_then = {};
    var var_ModelJson_Promise_then_onresolve = {};
    var var_ModelJson = {};
    var var_unknownType_Array = {};
    var var_getDesignJsonUrlAsync = {};
    var var_getDesignFullJsonUrlAsync = {};
    var var_getDesignXmlUrlAsync = {};
    var var_IDesignExportXmlDataParams = {};
    var var_getQuotation = {};
    var var_IQuotationOption = {};
    var var_IQuotationResult = {};
    var var_getQuotationTemplateAsync = {};
    var var_IQuotationReportTemplateParam = {};
    var var_numberType_Array = {};
    var var_getQuotationTemplateAsync_objectLiteral_Promise = {};
    var var_getQuotationTemplateAsync_objectLiteral_Promise_then = {};
    var var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getQuotationTemplateAsync_objectLiteral = {};
    var var_IQuotationReportTemplateItem_Array = {};
    var var_IQuotationReportTemplateItem = {};
    var var_getModelJsonAsyncV2 = {};
    var var_GetModelJsonOptionV2 = {};
    var var_getFittingDataFromAuxiliaryAsync = {};
    var var_IGetFittingDataByAuxiliaryOption = {};
    var var_IGetFittingDataByAuxiliaryBaseOption = {};
    var var_injection_UnknownType_Array_Promise = {};
    var var_injection_UnknownType_Array_Promise_then = {};
    var var_injection_UnknownType_Array_Promise_then_onresolve = {};
    var var_injection_UnknownType_Array = {};
    var var_getFittingDatasFromAuxiliaryAsync = {};
    var var_IBatchGetFittingDataByAuxiliaryOption = {};
    var var_stringType_Array = {};
    var var_injection_UnknownType_Array_Array_Promise = {};
    var var_injection_UnknownType_Array_Array_Promise_then = {};
    var var_injection_UnknownType_Array_Array_Promise_then_onresolve = {};
    var var_injection_UnknownType_Array_Array = {};
    var var_getIntersectedDataAsync = {};
    var var_IGetIntersectedOption = {};
    var var_IGetIntersectedOption_products_objectLiteral_Array = {};
    var var_IGetIntersectedOption_products_objectLiteral = {};
    var var_IGetIntersectedOption_primitiveMatch_objectLiteral = {};
    var var_IGetIntersectedOption_productDirectionConfig_objectLiteral_Array = {};
    var var_IGetIntersectedOption_productDirectionConfig_objectLiteral = {};
    var var_IIntersectedData_Promise = {};
    var var_IIntersectedData_Promise_then = {};
    var var_IIntersectedData_Promise_then_onresolve = {};
    var var_IIntersectedData = {};
    var var_IIntersectedGroup_Array = {};
    var var_IIntersectedGroup = {};
    var var_EDesignExportCode = {};
    var var_EPointType = {};
    var var_EClockWise = {};
    var var_ELineType = {};
    var var_EIntersectModelType = {};
    var var_EProductDirection = {};
    var var_EIntersectedInfoType = {};
    var var_CustomModel = {};
    var var_getPreviewImgAsync = {};
    var var_IGetPreviewImgOption = {};
    var var_IPreviewImgInfo_Promise = {};
    var var_IPreviewImgInfo_Promise_then = {};
    var var_IPreviewImgInfo_Promise_then_onresolve = {};
    var var_IPreviewImgInfo = {};
    var var_getTopModelsLiteInfoAsync = {};
    var var_IGetTopModelsLiteInfoOption = {};
    var var_getTopModelsLiteInfoAsync_objectLiteral_Promise = {};
    var var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then = {};
    var var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getTopModelsLiteInfoAsync_objectLiteral = {};
    var var_ICustomModelLiteInfo_Array = {};
    var var_ICustomModelLiteInfo = {};
    var var_IRoomInfo = {};
    var var_highlightModels = {};
    var var_highlightModels_option_objectLiteral = {};
    var var_findTopModelsSimpleAsync = {};
    var var_FindTopModelsSimpleOption = {};
    var var_CustomModelSimpleData_Array_Promise = {};
    var var_CustomModelSimpleData_Array_Promise_then = {};
    var var_CustomModelSimpleData_Array_Promise_then_onresolve = {};
    var var_CustomModelSimpleData_Array = {};
    var var_CustomModelSimpleData = {};
    var var_lockModel = {};
    var var_IModelLockOption = {};
    var var_unlockModel = {};
    var var_computeInnerSpacesAsync = {};
    var var_IInnerSpaceWithBorders_Array_Promise = {};
    var var_IInnerSpaceWithBorders_Array_Promise_then = {};
    var var_IInnerSpaceWithBorders_Array_Promise_then_onresolve = {};
    var var_IInnerSpaceWithBorders_Array = {};
    var var_IInnerSpaceWithBorders = {};
    var var_EParamType = {};
    var var_EParamModelType = {};
    var var_EBzPropertyType = {};
    var var_Product = {};
    var var_startDragProductAsync = {};
    var var_startDragProductAsync_option_objectLiteral = {};
    var var_injection_DragCustomProductPromiseResult_Promise = {};
    var var_injection_DragCustomProductPromiseResult_Promise_then = {};
    var var_injection_DragCustomProductPromiseResult_Promise_then_onresolve = {};
    var var_isEnterpriseLibraryModel = {};
    var var_importProductAsync = {};
    var var_IImportModel = {};
    var var_IParameter_Array = {};
    var var_IParameter = {};
    var var_IParameterOption_Array = {};
    var var_IParameterOption = {};
    var var_IImportModelResult_Promise = {};
    var var_IImportModelResult_Promise_then = {};
    var var_IImportModelResult_Promise_then_onresolve = {};
    var var_IImportModelResult = {};
    var var_Drawing = {};
    var var_getConstructionDrawingByElementsAsync = {};
    var var_IDrawingExportResponse_Array_Promise = {};
    var var_IDrawingExportResponse_Array_Promise_then = {};
    var var_IDrawingExportResponse_Array_Promise_then_onresolve = {};
    var var_IDrawingExportResponse_Array = {};
    var var_IDrawingExportResponse = {};
    var var_enterCustomDrawingAsync = {};
    var var_EDivisionType = {};
    var var_EDrawingFileType = {};
    var var_FittingDesign = {};
    var var_getIntersectedDataAsync_1 = {};
    var var_getDesignDataAsync = {};
    var var_IFittingDesignOption = {};
    var var_IFittingDesignData_Promise = {};
    var var_IFittingDesignData_Promise_then = {};
    var var_IFittingDesignData_Promise_then_onresolve = {};
    var var_IFittingDesignData = {};
    var var_saveDesignDataAsync = {};
    var var_ISaveFittingDesignOption = {};
    var var_deleteDesignDataAsync = {};
    var var_findDesignDataAsync = {};
    var var_IFittingDesignData_Array_Promise = {};
    var var_IFittingDesignData_Array_Promise_then = {};
    var var_IFittingDesignData_Array_Promise_then_onresolve = {};
    var var_IFittingDesignData_Array = {};
    var var_saveDesignDatasAsync = {};
    var var_ISaveFittingDesignOption_Array = {};
    var var_deleteDesignDatasAsync = {};
    var var_FittingDesignV2 = {};
    var var_putDesignDataAsync = {};
    var var_findDesignDataAsync_1 = {};
    var var_deleteDesignDataAsync_1 = {};
    var var_Detection = {};
    var var_checkIntersectionAsync = {};
    var var_IIntersectCheckOption = {};
    var var_IIntersectCheckResult_Array_Promise = {};
    var var_IIntersectCheckResult_Array_Promise_then = {};
    var var_IIntersectCheckResult_Array_Promise_then_onresolve = {};
    var var_IIntersectCheckResult_Array = {};
    var var_IIntersectCheckResult = {};
    var var_detectAsync = {};
    var var_IDetectOption = {};
    var var_detectByModelIdAsync = {};
    var var_getDetectConfig = {};
    var var_EDetectType = {};
    var var_Order = {};
    var var_getRelatedOrderAsync = {};
    var var_IRelatedOrderResult_Promise = {};
    var var_IRelatedOrderResult_Promise_then = {};
    var var_IRelatedOrderResult_Promise_then_onresolve = {};
    var var_IRelatedOrderResult = {};
    var var_getCustomerOrderAsync = {};
    var var_ICustomerOrderDetail_Promise = {};
    var var_ICustomerOrderDetail_Promise_then = {};
    var var_ICustomerOrderDetail_Promise_then_onresolve = {};
    var var_ICustomerOrderDetail = {};
    var var_getAuditOrderAsync = {};
    var var_IAuditOrderResult_Promise = {};
    var var_IAuditOrderResult_Promise_then = {};
    var var_IAuditOrderResult_Promise_then_onresolve = {};
    var var_IAuditOrderResult = {};
    var var_openOrderDetailPanel = {};
    var var_Group = {};
    var var_getGroupDataAsync = {};
    var var_IGroupData_Promise = {};
    var var_IGroupData_Promise_then = {};
    var var_IGroupData_Promise_then_onresolve = {};
    var var_IGroupData = {};
    var var_IGroupData_rooms_objectLiteral_Array = {};
    var var_IGroupData_rooms_objectLiteral = {};
    var var_IGroupData_rooms_groups_objectLiteral_Array = {};
    var var_IGroupData_rooms_groups_objectLiteral = {};
    var var_IGroupData_rooms_groups_wallFaces_objectLiteral_Array = {};
    var var_IGroupData_rooms_groups_wallFaces_objectLiteral = {};
    var var_IGroupData_rooms_groups_models_objectLiteral_Array = {};
    var var_IGroupData_rooms_groups_models_objectLiteral = {};
    var var_updateGroupDataAsync = {};
    var var_InstallCode = {};
    var var_attachInstallDataAsync = {};
    var var_IInstallData = {};
    var var_IInstallData_rooms_objectLiteral_Array = {};
    var var_IInstallData_rooms_objectLiteral = {};
    var var_IInstallData_models_objectLiteral_Array = {};
    var var_IInstallData_models_objectLiteral = {};
    var var_LeftPanel = {};
    var var_BoolModeling = {};
    var var_triggerBoolEffectAsync = {};
    var var_ITriggerBoolEffectResult_Promise = {};
    var var_ITriggerBoolEffectResult_Promise_then = {};
    var var_ITriggerBoolEffectResult_Promise_then_onresolve = {};
    var var_ITriggerBoolEffectResult = {};
    var var_UI = {};
    var var_openStyleExtensionAsync = {};
    var var_ILeftPanelStyleExtensionOptions = {};
    var var_Product_1 = {};
    var var_getProductAsync = {};
    var var_IProduct_Promise = {};
    var var_IProduct_Promise_then = {};
    var var_IProduct_Promise_then_onresolve = {};
    var var_IProduct = {};
    var var_IProductCustomField_Array = {};
    var var_IProductCustomField = {};
    var var_findCustomFoldersAsync = {};
    var var_IFindCustomFoldersOptions = {};
    var var_IFolder_Array_Promise = {};
    var var_IFolder_Array_Promise_then = {};
    var var_IFolder_Array_Promise_then_onresolve = {};
    var var_IFolder_Array = {};
    var var_IFolder = {};
    var var_findCustomProductsAsync = {};
    var var_IFindCustomProductsOptions = {};
    var var_findCustomProductsAsync_objectLiteral_Promise = {};
    var var_findCustomProductsAsync_objectLiteral_Promise_then = {};
    var var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve = {};
    var var_findCustomProductsAsync_objectLiteral = {};
    var var_IProduct_Array = {};
    var var_getProductDefaultBuildAsync = {};
    var var_IGetProductDefaultBuildOption = {};
    var var_stringType_Promise = {};
    var var_stringType_Promise_then = {};
    var var_stringType_Promise_then_onresolve = {};
    var var_DB = {};
    var var_Types = {};
    var var_Methods = {};
    var var_getCustomGroup = {};
    var var_CustomGroup = {};
    var var_createCabinetAsync = {};
    var var_ICreateCustomModelSimpleOption = {};
    var var_ICustomModelSimpleChangeable = {};
    var var_ElementId_Promise = {};
    var var_ElementId_Promise_then = {};
    var var_ElementId_Promise_then_onresolve = {};
    var var_updateCabinetAsync = {};
    var var_IUpdateCustomModelSimpleOption = {};
    var var_deleteCabinetAsync = {};
    var var_booleanType_Promise = {};
    var var_booleanType_Promise_then = {};
    var var_booleanType_Promise_then_onresolve = {};
    var var_getCabinetAsync = {};
    var var_injection_UnknownType_Promise = {};
    var var_injection_UnknownType_Promise_then = {};
    var var_injection_UnknownType_Promise_then_onresolve = {};
    var var_findCabinetListAsync = {};
    var var_createCabinetCopyAsync = {};
    var var_updateCabinetCopyAsync = {};
    var var_deleteCabinetCopyAsync = {};
    var var_getCabinetCopyAsync = {};
    var var_findCabinetCopyListAsync = {};
    var var_createWardrobeAsync = {};
    var var_updateWardrobeAsync = {};
    var var_deleteWardrobeAsync = {};
    var var_getWardrobeAsync = {};
    var var_findWardrobeListAsync = {};
    var var_createWardrobeCopyAsync = {};
    var var_updateWardrobeCopyAsync = {};
    var var_deleteWardrobeCopyAsync = {};
    var var_getWardrobeCopyAsync = {};
    var var_findWardrobeCopyListAsync = {};
    var var_Integration = {};
    var var_FOP = {};
    var var_createOrderAsync = {};
    var var_ICreateOrderOption = {};
    var var_IDesignAttachments_Array = {};
    var var_IDesignAttachments = {};
    var var_ISubModel_Array = {};
    var var_ISubModel = {};
    var var_ICreateOrderResult_Promise = {};
    var var_ICreateOrderResult_Promise_then = {};
    var var_ICreateOrderResult_Promise_then_onresolve = {};
    var var_ICreateOrderResult = {};
    var var_getOrderAsync = {};
    var var_IOrderData_Promise = {};
    var var_IOrderData_Promise_then = {};
    var var_IOrderData_Promise_then_onresolve = {};
    var var_IOrderData = {};
    var var_IOrderRemarks_Array = {};
    var var_IOrderRemarks = {};
    var var_IGetOrderRemarksResult = {};
    var var_IOrderAttachment_Array = {};
    var var_IOrderAttachment = {};
    var var_IOrderAttachments_Array = {};
    var var_IOrderAttachments = {};
    var var_IAuditOrderRemarks_Array = {};
    var var_IAuditOrderRemarks = {};
    var var_IOrderEvent_Array = {};
    var var_IOrderEvent = {};
    var var_IOrderAction_Array = {};
    var var_IOrderAction = {};
    var var_IOrderJumpActionExtra = {};
    var var_IOrderJumpActionExtra_info_objectLiteral = {};
    var var_IOrderBaseInfo = {};
    var var_ICustomerBaseInfo = {};
    var var_deleteRemarkAsync = {};
    var var_IDeleteOrderRemarkOption = {};
    var var_getModelAssociatedOrderAsync = {};
    var var_IModelIsAssociatedOrderOption = {};
    var var_IModelIsAssociatedOrderResult_Array_Promise = {};
    var var_IModelIsAssociatedOrderResult_Array_Promise_then = {};
    var var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve = {};
    var var_IModelIsAssociatedOrderResult_Array = {};
    var var_IModelIsAssociatedOrderResult = {};
    var var_executeOrderOperationAsync = {};
    var var_IExecuteOrderOperationOption = {};
    var var_checkOperatePermissionAsync = {};
    var var_ICheckOperatePermissionOption = {};
    var var_createCustomerAsync = {};
    var var_ICreateCustomerOption = {};
    var var_ICreateCustomerResult_Promise = {};
    var var_ICreateCustomerResult_Promise_then = {};
    var var_ICreateCustomerResult_Promise_then_onresolve = {};
    var var_ICreateCustomerResult = {};
    var var_findCustomerListAsync = {};
    var var_IGetCustomerListOption = {};
    var var_IPaginationQuery = {};
    var var_getStoreAsync = {};
    var var_IGetStoreInfoResult_Promise = {};
    var var_IGetStoreInfoResult_Promise_then = {};
    var var_IGetStoreInfoResult_Promise_then_onresolve = {};
    var var_IGetStoreInfoResult = {};
    var var_createAfterSaleOrderFromDesignAsync = {};
    var var_findAuditedModelAsync = {};
    var var_findAuditedModelAsync_option_objectLiteral = {};
    var var_IFindAuditedModelResult_Promise = {};
    var var_IFindAuditedModelResult_Promise_then = {};
    var var_IFindAuditedModelResult_Promise_then_onresolve = {};
    var var_IFindAuditedModelResult = {};
    var var_getStoresAsync = {};
    var var_IStoreInfo_Array_Promise = {};
    var var_IStoreInfo_Array_Promise_then = {};
    var var_IStoreInfo_Array_Promise_then_onresolve = {};
    var var_IStoreInfo_Array = {};
    var var_IStoreInfo = {};
    var var_getStatesAsync = {};
    var var_getStatesAsync_objectLiteral_Promise = {};
    var var_getStatesAsync_objectLiteral_Promise_then = {};
    var var_getStatesAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getStatesAsync_objectLiteral = {};
    var var_IOrderStateData_Array = {};
    var var_IOrderStateData = {};
    var var_getOperationsAsync = {};
    var var_getOperationsAsync_objectLiteral_Promise = {};
    var var_getOperationsAsync_objectLiteral_Promise_then = {};
    var var_getOperationsAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getOperationsAsync_objectLiteral = {};
    var var_IOrderOperationData_Array = {};
    var var_IOrderOperationData = {};
    var var_updateOrderModelsAsync = {};
    var var_IUpdateOrderModel = {};
    var var_IOrderModel = {};
    var var_findOrderModelsAsync = {};
    var var_findOrderModelsAsync_option_objectLiteral = {};
    var var_IOrderModel_Promise = {};
    var var_IOrderModel_Promise_then = {};
    var var_IOrderModel_Promise_then_onresolve = {};
    var var_getReportFileAsync = {};
    var var_getReportFileAsync_objectLiteral_Promise = {};
    var var_getReportFileAsync_objectLiteral_Promise_then = {};
    var var_getReportFileAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getReportFileAsync_objectLiteral = {};
    var var_getAllPlankDrawingAsync = {};
    var var_getAllPlankDrawingAsync_objectLiteral_Promise = {};
    var var_getAllPlankDrawingAsync_objectLiteral_Promise_then = {};
    var var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getAllPlankDrawingAsync_objectLiteral = {};
    var var_getPlankDrawingAsync = {};
    var var_getPlankDrawingAsync_option_objectLiteral = {};
    var var_getPlankDrawingAsync_objectLiteral_Promise = {};
    var var_getPlankDrawingAsync_objectLiteral_Promise_then = {};
    var var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve = {};
    var var_getPlankDrawingAsync_objectLiteral = {};
    var var_getPlankDrawingsAsync = {};
    var var_getPlankDrawingsAsync_option_objectLiteral = {};
    var var_getPlankDrawingsAsync_objectLiteral_Array_Promise = {};
    var var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then = {};
    var var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve = {};
    var var_getPlankDrawingsAsync_objectLiteral_Array = {};
    var var_getPlankDrawingsAsync_objectLiteral = {};
    var var_updateOrderAsync = {};
    var var_IUpdateOrderOption = {};
    var var_updateCustomerAsync = {};
    var var_IUpdateCustomerOption = {};
    var var_findAfterSaleOrderListAsync = {};
    var var_findAfterSaleOrderListAsync_option_objectLiteral = {};
    var var_createAfterSaleOrderFromBomAsync = {};
    var var_ICreateAfterSaleOrderFromBomOption = {};
    var var_deleteOrderAsync = {};
    var var_deleteOrderAsync_option_objectLiteral = {};
    var var_ECustomerSource = {};
    var var_ECustomerQueryType = {};
    var var_OrderMode = {};
    var var_getEditable = {};
    var var_setEditable = {};
    var var_getOrderId = {};
    var var_pauseAutoUpdateOrderModel = {};
    var var_resumeAutoUpdateOrderModel = {};
    var var_Config = {};
    var var_getEnumAsync = {};
    var var_getEnumAsync_option_objectLiteral = {};
    var var_IFieldEnumResult_Array_Promise = {};
    var var_IFieldEnumResult_Array_Promise_then = {};
    var var_IFieldEnumResult_Array_Promise_then_onresolve = {};
    var var_IFieldEnumResult_Array = {};
    var var_IFieldEnumResult = {};
    var var_EFieldEnum = {};
    var var_InstallationSharing = {};
    var var_createShareDataAsync = {};
    var var_createShareDataAsync_option_objectLiteral = {};
    var var_createShareDataAsync_objectLiteral_Promise = {};
    var var_createShareDataAsync_objectLiteral_Promise_then = {};
    var var_createShareDataAsync_objectLiteral_Promise_then_onresolve = {};
    var var_createShareDataAsync_objectLiteral = {};
    var var_Upload = {};
    var var_EngravingMachineCutting = {};
    var var_createTaskAsync = {};
    var var_IEngravingMachineCuttingTaskCreate = {};
    var var_IEngravingMachineCuttingTaskItem_Array = {};
    var var_IEngravingMachineCuttingTaskItem = {};
    var var_IEngravingMachineCuttingTaskItemObject_Array = {};
    var var_IEngravingMachineCuttingTaskItemObject = {};
    var var_unknownType_Array_Array = {};
    var var_IEngravingMachineCuttingPlate_Array = {};
    var var_IEngravingMachineCuttingPlate = {};
    var var_findTaskAsync = {};
    var var_IEngravingMachineCuttingResponse_Promise = {};
    var var_IEngravingMachineCuttingResponse_Promise_then = {};
    var var_IEngravingMachineCuttingResponse_Promise_then_onresolve = {};
    var var_IEngravingMachineCuttingResponse = {};
    var var_IEngravingMachineCuttingResultResponse = {};
    var var_IEngravingMachineCuttingResultResponseObjects_Array = {};
    var var_IEngravingMachineCuttingResultResponseObjects = {};
    var var_IIEngravingMachineCuttingResultResponseNesting_Array = {};
    var var_IIEngravingMachineCuttingResultResponseNesting = {};
    var var_IIEngravingMachineCuttingResultResponseNestingItem_Array = {};
    var var_IIEngravingMachineCuttingResultResponseNestingItem = {};
    var var_deleteTaskAsync = {};
    var var_Bom = {};
    var var_createMaterialsAsync = {};
    var var_CreateMaterialsResult_Promise = {};
    var var_CreateMaterialsResult_Promise_then = {};
    var var_CreateMaterialsResult_Promise_then_onresolve = {};
    var var_CreateMaterialsResult = {};
    var var_deleteMaterialsAsync = {};
    var var_DeleteMaterialsOption = {};
    var var_DeleteMaterialsResult_Promise = {};
    var var_DeleteMaterialsResult_Promise_then = {};
    var var_DeleteMaterialsResult_Promise_then_onresolve = {};
    var var_DeleteMaterialsResult = {};
    var var_DeleteMaterialsResult_productMaterials_objectLiteral_Array = {};
    var var_DeleteMaterialsResult_productMaterials_objectLiteral = {};
    var var_deletePlankAsync = {};
    var var_deletePlanksAsync = {};
    var var_deletePlanksAsync_option_objectLiteral = {};
    var var_deletePlanksAsync_objectLiteral_Promise = {};
    var var_deletePlanksAsync_objectLiteral_Promise_then = {};
    var var_deletePlanksAsync_objectLiteral_Promise_then_onresolve = {};
    var var_deletePlanksAsync_objectLiteral = {};
    var var_deletePlanksByProductIdsAsync = {};
    var var_deletePlanksByProductIdsAsync_option_objectLiteral = {};
    var var_deletePlanksByProductIdsAsync_objectLiteral_Promise = {};
    var var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then = {};
    var var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve = {};
    var var_deletePlanksByProductIdsAsync_objectLiteral = {};
    var var_deletePlanksByOrderIdsAsync = {};
    var var_deletePlanksByOrderIdsAsync_option_objectLiteral = {};
    var var_deletePlanksByOrderIdsAsync_objectLiteral_Promise = {};
    var var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then = {};
    var var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve = {};
    var var_deletePlanksByOrderIdsAsync_objectLiteral = {};
    var var_deleteGroupsByProductIdAsync = {};
    var var_BomGroupDeleteByProductIdsOption = {};
    var var_BomGroupDeleteByProductIdsResult_Promise = {};
    var var_BomGroupDeleteByProductIdsResult_Promise_then = {};
    var var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve = {};
    var var_BomGroupDeleteByProductIdsResult = {};
    var var_BomGroupDeleteByProductIdsResult_groups_objectLiteral_Array = {};
    var var_BomGroupDeleteByProductIdsResult_groups_objectLiteral = {};
    var var_generateGroupRelationsAsync = {};
    var var_generateGroupRelationsAsync_option_objectLiteral = {};
    var var_updateRawPlankProcessingAttributesAsync = {};
    var var_updateRawPlankProcessingAttributesAsync_option_objectLiteral = {};
    var var_IBomPlankLayoutProcessAttribute_Array = {};
    var var_IBomPlankLayoutProcessAttribute = {};
    var var_IBomRawPlankMeta = {};
    var var_findCategoryAttrsAsync = {};
    var var_FindCategoryAttrOption = {};
    var var_FindCategoryAttrResult_Promise = {};
    var var_FindCategoryAttrResult_Promise_then = {};
    var var_FindCategoryAttrResult_Promise_then_onresolve = {};
    var var_FindCategoryAttrResult = {};
    var var_FindCategoryAttrResult_catAttrs_objectLiteral_Array = {};
    var var_FindCategoryAttrResult_catAttrs_objectLiteral = {};
    var var_ICategoryAttribute_Array = {};
    var var_ICategoryAttribute = {};
    var var_ICategoryAttributeRule_Array = {};
    var var_ICategoryAttributeRule = {};
    var var_ICategory = {};
    var var_clearMaterialPropertiesAsync = {};
    var var_clearGroupPropertiesAsync = {};
    var var_refreshProductCodeInOrderAsync = {};
    var var_refreshProductCodeInOrderAsync_option_objectLiteral = {};
    var var_ProductCodeInOrder_Promise = {};
    var var_ProductCodeInOrder_Promise_then = {};
    var var_ProductCodeInOrder_Promise_then_onresolve = {};
    var var_ProductCodeInOrder = {};
    var var_IProductCodeInOrderInfo_Array = {};
    var var_IProductCodeInOrderInfo = {};
    var var_updateProductsAsync = {};
    var var_UpdateProductsOption = {};
    var var_refreshCodeAsync = {};
    var var_refreshCodeAsync_option_objectLiteral = {};
    var var_findProductsAsync = {};
    var var_findProductsAsync_objectLiteral_Array_Promise = {};
    var var_findProductsAsync_objectLiteral_Array_Promise_then = {};
    var var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve = {};
    var var_findProductsAsync_objectLiteral_Array = {};
    var var_findProductsAsync_objectLiteral = {};
    var var_previewMaterialDrawingsAsync = {};
    var var_IPreviewMaterialDrawingsOption = {};
    var var_IPreviewMaterialDrawingsResult_Promise = {};
    var var_IPreviewMaterialDrawingsResult_Promise_then = {};
    var var_IPreviewMaterialDrawingsResult_Promise_then_onresolve = {};
    var var_IPreviewMaterialDrawingsResult = {};
    var var_IPreviewMaterialDrawing_Array = {};
    var var_IPreviewMaterialDrawing = {};
    var var_EBomPlankType = {};
    var var_EBomCurveType = {};
    var var_EBomPlankHoleType = {};
    var var_Plank = {};
    var var_Detection_1 = {};
    var var_Report = {};
    var var_generateReportPageAsync = {};
    var var_GenerateReportPageOption = {};
    var var_User = {};
    var var_getAppUidAsync = {};
    var var_IGetAppUidResult_Promise = {};
    var var_IGetAppUidResult_Promise_then = {};
    var var_IGetAppUidResult_Promise_then_onresolve = {};
    var var_IGetAppUidResult = {};
    var var_getUserDetailsAsync = {};
    var var_IUserDetails_Promise = {};
    var var_IUserDetails_Promise_then = {};
    var var_IUserDetails_Promise_then_onresolve = {};
    var var_IUserDetails = {};
    var var_IUserDetails_company_objectLiteral = {};
    var var_Storage = {};
    var var_Common_1 = {};
    var var_putItemAsync = {};
    var var_IStorageItem = {};
    var var_getItemAsync = {};
    var var_getItemListAsync = {};
    var var_IStorageItem_Array_Promise = {};
    var var_IStorageItem_Array_Promise_then = {};
    var var_IStorageItem_Array_Promise_then_onresolve = {};
    var var_IStorageItem_Array = {};
    var var_deleteItemAsync = {};
    var var_getKeysAsync = {};
    var var_IStorageGetKeysOption = {};
    var var_IStorageGetKeysResult_Promise = {};
    var var_IStorageGetKeysResult_Promise_then = {};
    var var_IStorageGetKeysResult_Promise_then_onresolve = {};
    var var_IStorageGetKeysResult = {};
    var var_Design_2 = {};
    var var_putItemAsync_1 = {};
    var var_getItemAsync_1 = {};
    var var_getItemListAsync_1 = {};
    var var_deleteItemAsync_1 = {};
    var var_getKeysAsync_1 = {};
    var var_DesignV2 = {};
    var var_putItemAsync_2 = {};
    var var_getItemAsync_2 = {};
    var var_deleteItemAsync_2 = {};
    var var_Enterprise = {};
    var var_putItemAsync_3 = {};
    var var_getItemAsync_3 = {};
    var var_getItemListAsync_2 = {};
    var var_deleteItemAsync_3 = {};
    var var_getKeysAsync_2 = {};
    var var_Interaction = {};
    var var_getSelectedElements_1 = {};
    var var_setSelectedElements = {};
    var var_getSelectedRooms = {};
    var var_getSelectedRooms_objectLiteral_Array = {};
    var var_getSelectedRooms_objectLiteral = {};
    var var_Platform = {};
    var var_enterCustomModeAsync = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_CustomModel, exportName: "CustomModel" },
        { value: var_injection_ISelectionEvtType, exportName: "ISelectionEvtType" },
        { value: var_injection_UnknownType, exportName: "UnknownType" },
        { value: var_injection_NewCustomModelByCategoryAsync, exportName: "NewCustomModelByCategoryAsync" },
        { value: var_injection_NewCustomModelByProductIdAsync, exportName: "NewCustomModelByProductIdAsync" },
        { value: var_injection_GetCustomModelByModelIdAsync, exportName: "GetCustomModelByModelIdAsync" },
        { value: var_injection_FindTopModelsAsync, exportName: "FindTopModelsAsync" },
        { value: var_injection_UpdateCustomModelAsync, exportName: "UpdateCustomModelAsync" },
        { value: var_injection_DeleteTopModelsAsync, exportName: "DeleteTopModelsAsync" },
        { value: var_injection_DragCustomProductPromiseResult, exportName: "DragCustomProductPromiseResult" },
        { value: var_injection_AsyncFunctionType, exportName: "AsyncFunctionType" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Design": var_Design,
        "Custom": var_Custom,
        "Product": var_Product_1,
        "DB": var_DB,
        "Integration": var_Integration,
        "User": var_User,
        "Storage": var_Storage,
        "Interaction": var_Interaction,
        "Platform": var_Platform,
    };
    var_Design.type = BasicType.Object;
    var_Design.properties = {
        "copyAsync": var_copyAsync,
    };
    var_copyAsync.type = BasicType.Function;
    var_copyAsync.name = "copyAsync";
    var_copyAsync.varying = false;
    var_copyAsync.keepArgsHandle = false;
    var_copyAsync.args = [];
    var_copyAsync.return = var_copyAsync_objectLiteral_Promise;
    var_copyAsync_objectLiteral_Promise.type = BasicType.Object;
    var_copyAsync_objectLiteral_Promise.properties = {
        "then": var_copyAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_copyAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_copyAsync_objectLiteral_Promise_then.name = "";
    var_copyAsync_objectLiteral_Promise_then.varying = false;
    var_copyAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_copyAsync_objectLiteral_Promise_then.args = [var_copyAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_copyAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_copyAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_copyAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_copyAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_copyAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_copyAsync_objectLiteral_Promise_then_onresolve.args = [var_copyAsync_objectLiteral];
    var_copyAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_copyAsync_objectLiteral.type = BasicType.Object;
    var_copyAsync_objectLiteral.properties = {
        "designId": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_Custom.type = BasicType.Object;
    var_Custom.properties = {
        "Common": var_Common,
        "InnerSpace": var_InnerSpace,
        "DoorCoveringHardware": var_DoorCoveringHardware,
        "Mode": var_Mode,
        "Design": var_Design_1,
        "Product": var_Product,
        "Drawing": var_Drawing,
        "FittingDesign": var_FittingDesign,
        "FittingDesignV2": var_FittingDesignV2,
        "Detection": var_Detection,
        "Order": var_Order,
        "Group": var_Group,
        "InstallCode": var_InstallCode,
        "LeftPanel": var_LeftPanel,
        "BoolModeling": var_BoolModeling,
        "UI": var_UI,
    };
    var_Common.type = BasicType.Object;
    var_Common.properties = {
        "openTerminalAsync": var_openTerminalAsync,
        "closeTerminalAsync": var_closeTerminalAsync,
        "getCurrentToolType": var_getCurrentToolType,
        "getDesignType": var_getDesignType,
        "getOrderDesignId": var_getOrderDesignId,
        "isDesignLoaded": var_isDesignLoaded,
        "ToolType": var_EToolType,
        "DesignType": var_EDesignType,
    };
    var_openTerminalAsync.type = BasicType.Function;
    var_openTerminalAsync.name = "openTerminalAsync";
    var_openTerminalAsync.varying = false;
    var_openTerminalAsync.keepArgsHandle = true;
    var_openTerminalAsync.args = [var_OpenOrCloseOption];
    var_openTerminalAsync.return = var_undefinedType_Promise;
    var_OpenOrCloseOption.type = BasicType.Object;
    var_OpenOrCloseOption.properties = {
        "clientId": var_stringType,
        "timeout": var_numberType,
    };
    var_numberType.type = BasicType.Number;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_closeTerminalAsync.type = BasicType.Function;
    var_closeTerminalAsync.name = "closeTerminalAsync";
    var_closeTerminalAsync.varying = false;
    var_closeTerminalAsync.keepArgsHandle = true;
    var_closeTerminalAsync.args = [var_OpenOrCloseOption];
    var_closeTerminalAsync.return = var_undefinedType_Promise;
    var_getCurrentToolType.type = BasicType.Function;
    var_getCurrentToolType.name = "getCurrentToolType";
    var_getCurrentToolType.varying = false;
    var_getCurrentToolType.keepArgsHandle = false;
    var_getCurrentToolType.args = [];
    var_getCurrentToolType.return = var_stringType;
    var_getDesignType.type = BasicType.Function;
    var_getDesignType.name = "getDesignType";
    var_getDesignType.varying = false;
    var_getDesignType.keepArgsHandle = false;
    var_getDesignType.args = [];
    var_getDesignType.return = var_stringType;
    var_getOrderDesignId.type = BasicType.Function;
    var_getOrderDesignId.name = "getOrderDesignId";
    var_getOrderDesignId.varying = false;
    var_getOrderDesignId.keepArgsHandle = false;
    var_getOrderDesignId.args = [];
    var_getOrderDesignId.return = var_stringType;
    var_isDesignLoaded.type = BasicType.Function;
    var_isDesignLoaded.name = "isDesignLoaded";
    var_isDesignLoaded.varying = false;
    var_isDesignLoaded.keepArgsHandle = false;
    var_isDesignLoaded.args = [];
    var_isDesignLoaded.return = var_booleanType;
    var_booleanType.type = BasicType.Boolean;
    var_EToolType.type = BasicType.Object;
    var_EToolType.properties = {
        "Cabinet": var_stringType,
        "Wardrobe": var_stringType,
        "DoorWindow": var_stringType,
        "CabinetCopy": var_stringType,
        "WardrobeCopy": var_stringType,
        "DoorWindowCopy": var_stringType,
    };
    var_EDesignType.type = BasicType.Object;
    var_EDesignType.properties = {
        "Design": var_stringType,
        "Audit": var_stringType,
        "Production": var_stringType,
    };
    var_InnerSpace.type = BasicType.Object;
    var_InnerSpace.properties = {
        "getSelected": var_getSelected,
        "Face": var_EFace,
        "selection": var_injection_ISelectionEvtType,
    };
    var_getSelected.type = BasicType.Function;
    var_getSelected.name = "getSelected";
    var_getSelected.varying = false;
    var_getSelected.keepArgsHandle = false;
    var_getSelected.args = [];
    var_getSelected.return = var_IInnerSpaceData;
    var_IInnerSpaceData.type = BasicType.Object;
    var_IInnerSpaceData.properties = {
        "size": var_Number3,
        "position": var_Number3,
        "rotate": var_Number3,
        "rotation": var_Number3,
        "face": var_stringType,
        "parent": var_injection_CustomModel,
    };
    var_Number3.type = BasicType.Object;
    var_Number3.properties = {
        "z": var_numberType,
    };
    var_Number2.type = BasicType.Object;
    var_Number2.properties = {
        "x": var_numberType,
        "y": var_numberType,
    };
    var_EFace.type = BasicType.Object;
    var_EFace.properties = {
        "LEFT": var_stringType,
        "RIGHT": var_stringType,
        "BOTTOM": var_stringType,
        "TOP": var_stringType,
        "BACK": var_stringType,
        "FRONT": var_stringType,
    };
    var_DoorCoveringHardware.type = BasicType.Object;
    var_DoorCoveringHardware.properties = {
        "getOption": var_getOption,
        "setOption": var_setOption,
        "setProductId": var_setProductId,
        "getSelectedElements": var_getSelectedElements,
    };
    var_getOption.type = BasicType.Function;
    var_getOption.name = "getOption";
    var_getOption.varying = false;
    var_getOption.keepArgsHandle = false;
    var_getOption.args = [];
    var_getOption.return = var_IDoorCoveringHardwareMode;
    var_IDoorCoveringHardwareMode.type = BasicType.Object;
    var_IDoorCoveringHardwareMode.properties = {
        "hardwareModel": var_stringType,
        "selectTarget": var_stringType,
    };
    var_setOption.type = BasicType.Function;
    var_setOption.name = "setOption";
    var_setOption.varying = false;
    var_setOption.keepArgsHandle = true;
    var_setOption.args = [var_IDoorCoveringHardwareMode];
    var_setOption.return = var_undefinedType;
    var_setProductId.type = BasicType.Function;
    var_setProductId.name = "setProductId";
    var_setProductId.varying = false;
    var_setProductId.keepArgsHandle = true;
    var_setProductId.args = [var_SetOption];
    var_setProductId.return = var_undefinedType;
    var_SetOption.type = BasicType.Object;
    var_SetOption.properties = {
        "productId": var_stringType,
    };
    var_getSelectedElements.type = BasicType.Function;
    var_getSelectedElements.name = "getSelectedElements";
    var_getSelectedElements.varying = false;
    var_getSelectedElements.keepArgsHandle = false;
    var_getSelectedElements.args = [];
    var_getSelectedElements.return = var_ElementId_Array;
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_Mode.type = BasicType.Object;
    var_Mode.properties = {
        "current": var_current,
        "enter": var_enter,
        "enterAsync": var_enterAsync,
        "exit": var_exit,
        "AppMode": var_ECustomAppMode,
    };
    var_current.type = BasicType.Function;
    var_current.name = "current";
    var_current.varying = false;
    var_current.keepArgsHandle = false;
    var_current.args = [];
    var_current.return = var_stringType;
    var_enter.type = BasicType.Function;
    var_enter.name = "enter";
    var_enter.varying = false;
    var_enter.keepArgsHandle = true;
    var_enter.args = [var_unknownType];
    var_enter.return = var_undefinedType;
    var_enterAsync.type = BasicType.Function;
    var_enterAsync.name = "enterAsync";
    var_enterAsync.varying = false;
    var_enterAsync.keepArgsHandle = true;
    var_enterAsync.args = [var_unknownType];
    var_enterAsync.return = var_undefinedType_Promise;
    var_exit.type = BasicType.Function;
    var_exit.name = "exit";
    var_exit.varying = false;
    var_exit.keepArgsHandle = false;
    var_exit.args = [];
    var_exit.return = var_undefinedType;
    var_ECustomAppMode.type = BasicType.Object;
    var_ECustomAppMode.properties = {
        "INNER_SPACE": var_stringType,
        "PARTITION_TOOL": var_stringType,
        "ORDER_AUDIT": var_stringType,
        "FOP_ORDER": var_stringType,
        "DOOR_COVERING_HARDWARE": var_stringType,
    };
    var_Design_1.type = BasicType.Object;
    var_Design_1.properties = {
        "splitDesignByElementsAsync": var_splitDesignByElementsAsync,
        "Export": var_Export,
        "CustomModel": var_CustomModel,
    };
    var_splitDesignByElementsAsync.type = BasicType.Function;
    var_splitDesignByElementsAsync.name = "splitDesignByElementsAsync";
    var_splitDesignByElementsAsync.varying = false;
    var_splitDesignByElementsAsync.keepArgsHandle = true;
    var_splitDesignByElementsAsync.args = [var_ISplitDesignParams];
    var_splitDesignByElementsAsync.return = var_ISplitDesignResponse_Promise;
    var_ISplitDesignParams.type = BasicType.Object;
    var_ISplitDesignParams.properties = {
        "elements": var_ElementId_Array_1,
        "name": var_stringType,
        "lock": var_booleanType,
    };
    var_ElementId_Array_1.type = BasicType.Array;
    var_ElementId_Array_1.value = var_ElementId_1;
    var_ElementId_1.type = BasicType.Object;
    var_ElementId_1.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_ISplitDesignResponse_Promise.type = BasicType.Object;
    var_ISplitDesignResponse_Promise.properties = {
        "then": var_ISplitDesignResponse_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ISplitDesignResponse_Promise_then.type = BasicType.Function;
    var_ISplitDesignResponse_Promise_then.name = "";
    var_ISplitDesignResponse_Promise_then.varying = false;
    var_ISplitDesignResponse_Promise_then.keepArgsHandle = true;
    var_ISplitDesignResponse_Promise_then.args = [var_ISplitDesignResponse_Promise_then_onresolve, var_Promise_then_onreject];
    var_ISplitDesignResponse_Promise_then.return = var_undefinedType;
    var_ISplitDesignResponse_Promise_then_onresolve.type = BasicType.Function;
    var_ISplitDesignResponse_Promise_then_onresolve.name = "";
    var_ISplitDesignResponse_Promise_then_onresolve.varying = false;
    var_ISplitDesignResponse_Promise_then_onresolve.keepArgsHandle = false;
    var_ISplitDesignResponse_Promise_then_onresolve.args = [var_ISplitDesignResponse];
    var_ISplitDesignResponse_Promise_then_onresolve.return = var_undefinedType;
    var_ISplitDesignResponse.type = BasicType.Object;
    var_ISplitDesignResponse.properties = {
        "designId": var_stringType,
    };
    var_Export.type = BasicType.Object;
    var_Export.properties = {
        "getDesignJsonAsync": var_getDesignJsonAsync,
        "getDesignFullJsonAsync": var_getDesignFullJsonAsync,
        "getModelJsonAsync": var_getModelJsonAsync,
        "getDesignJsonUrlAsync": var_getDesignJsonUrlAsync,
        "getDesignFullJsonUrlAsync": var_getDesignFullJsonUrlAsync,
        "getDesignXmlUrlAsync": var_getDesignXmlUrlAsync,
        "getQuotation": var_getQuotation,
        "getQuotationTemplateAsync": var_getQuotationTemplateAsync,
        "getModelJsonAsyncV2": var_getModelJsonAsyncV2,
        "getFittingDataFromAuxiliaryAsync": var_getFittingDataFromAuxiliaryAsync,
        "getFittingDatasFromAuxiliaryAsync": var_getFittingDatasFromAuxiliaryAsync,
        "getIntersectedDataAsync": var_getIntersectedDataAsync,
        "DesignExportCode": var_EDesignExportCode,
        "PointType": var_EPointType,
        "ClockWise": var_EClockWise,
        "LineType": var_ELineType,
        "IntersectModel": var_EIntersectModelType,
        "ProductDirection": var_EProductDirection,
        "IntersectedInfoType": var_EIntersectedInfoType,
    };
    var_getDesignJsonAsync.type = BasicType.Function;
    var_getDesignJsonAsync.name = "getDesignJsonAsync";
    var_getDesignJsonAsync.varying = false;
    var_getDesignJsonAsync.keepArgsHandle = true;
    var_getDesignJsonAsync.args = [var_IDesignExportDataParams];
    var_getDesignJsonAsync.return = var_unknownType_Promise;
    var_IDesignExportDataParams.type = BasicType.Object;
    var_IDesignExportDataParams.properties = {
        "levelId": var_stringType,
        "toolType": var_stringType,
        "roomId": var_stringType,
        "timeout": var_numberType,
        "templateId": var_stringType,
        "modelIds": var_unknownType,
    };
    var_unknownType_Promise.type = BasicType.Object;
    var_unknownType_Promise.properties = {
        "then": var_unknownType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_unknownType_Promise_then.type = BasicType.Function;
    var_unknownType_Promise_then.name = "";
    var_unknownType_Promise_then.varying = false;
    var_unknownType_Promise_then.keepArgsHandle = true;
    var_unknownType_Promise_then.args = [var_unknownType_Promise_then_onresolve, var_Promise_then_onreject];
    var_unknownType_Promise_then.return = var_undefinedType;
    var_unknownType_Promise_then_onresolve.type = BasicType.Function;
    var_unknownType_Promise_then_onresolve.name = "";
    var_unknownType_Promise_then_onresolve.varying = false;
    var_unknownType_Promise_then_onresolve.keepArgsHandle = false;
    var_unknownType_Promise_then_onresolve.args = [var_unknownType];
    var_unknownType_Promise_then_onresolve.return = var_undefinedType;
    var_getDesignFullJsonAsync.type = BasicType.Function;
    var_getDesignFullJsonAsync.name = "getDesignFullJsonAsync";
    var_getDesignFullJsonAsync.varying = false;
    var_getDesignFullJsonAsync.keepArgsHandle = true;
    var_getDesignFullJsonAsync.args = [var_IDesignExportDataParams];
    var_getDesignFullJsonAsync.return = var_unknownType_Promise;
    var_getModelJsonAsync.type = BasicType.Function;
    var_getModelJsonAsync.name = "getModelJsonAsync";
    var_getModelJsonAsync.varying = false;
    var_getModelJsonAsync.keepArgsHandle = true;
    var_getModelJsonAsync.args = [var_IGetModelJsonOption];
    var_getModelJsonAsync.return = var_ModelJson_Promise;
    var_IGetModelJsonOption.type = BasicType.Object;
    var_IGetModelJsonOption.properties = {
        "modelId": var_stringType,
        "templateId": var_stringType,
        "force": var_booleanType,
    };
    var_ModelJson_Promise.type = BasicType.Object;
    var_ModelJson_Promise.properties = {
        "then": var_ModelJson_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ModelJson_Promise_then.type = BasicType.Function;
    var_ModelJson_Promise_then.name = "";
    var_ModelJson_Promise_then.varying = false;
    var_ModelJson_Promise_then.keepArgsHandle = true;
    var_ModelJson_Promise_then.args = [var_ModelJson_Promise_then_onresolve, var_Promise_then_onreject];
    var_ModelJson_Promise_then.return = var_undefinedType;
    var_ModelJson_Promise_then_onresolve.type = BasicType.Function;
    var_ModelJson_Promise_then_onresolve.name = "";
    var_ModelJson_Promise_then_onresolve.varying = false;
    var_ModelJson_Promise_then_onresolve.keepArgsHandle = false;
    var_ModelJson_Promise_then_onresolve.args = [var_ModelJson];
    var_ModelJson_Promise_then_onresolve.return = var_undefinedType;
    var_ModelJson.type = BasicType.Object;
    var_ModelJson.properties = {
        "paramModel": var_unknownType_Array,
    };
    var_unknownType_Array.type = BasicType.Array;
    var_unknownType_Array.value = var_unknownType;
    var_getDesignJsonUrlAsync.type = BasicType.Function;
    var_getDesignJsonUrlAsync.name = "getDesignJsonUrlAsync";
    var_getDesignJsonUrlAsync.varying = false;
    var_getDesignJsonUrlAsync.keepArgsHandle = true;
    var_getDesignJsonUrlAsync.args = [var_IDesignExportDataParams];
    var_getDesignJsonUrlAsync.return = var_unknownType_Promise;
    var_getDesignFullJsonUrlAsync.type = BasicType.Function;
    var_getDesignFullJsonUrlAsync.name = "getDesignFullJsonUrlAsync";
    var_getDesignFullJsonUrlAsync.varying = false;
    var_getDesignFullJsonUrlAsync.keepArgsHandle = true;
    var_getDesignFullJsonUrlAsync.args = [var_IDesignExportDataParams];
    var_getDesignFullJsonUrlAsync.return = var_unknownType_Promise;
    var_getDesignXmlUrlAsync.type = BasicType.Function;
    var_getDesignXmlUrlAsync.name = "getDesignXmlUrlAsync";
    var_getDesignXmlUrlAsync.varying = false;
    var_getDesignXmlUrlAsync.keepArgsHandle = true;
    var_getDesignXmlUrlAsync.args = [var_IDesignExportXmlDataParams];
    var_getDesignXmlUrlAsync.return = var_unknownType_Promise;
    var_IDesignExportXmlDataParams.type = BasicType.Object;
    var_IDesignExportXmlDataParams.properties = {
        "elements": var_ElementId_Array_1,
        "levelId": var_stringType,
        "toolType": var_stringType,
        "timeout": var_numberType,
    };
    var_getQuotation.type = BasicType.Function;
    var_getQuotation.name = "getQuotation";
    var_getQuotation.varying = false;
    var_getQuotation.keepArgsHandle = true;
    var_getQuotation.args = [var_IQuotationOption];
    var_getQuotation.return = var_IQuotationResult;
    var_IQuotationOption.type = BasicType.Object;
    var_IQuotationOption.properties = {
        "uploadFileKey": var_stringType,
        "hlkTemplateId": var_stringType,
    };
    var_IQuotationResult.type = BasicType.Object;
    var_IQuotationResult.properties = {
        "url": var_stringType,
    };
    var_getQuotationTemplateAsync.type = BasicType.Function;
    var_getQuotationTemplateAsync.name = "getQuotationTemplateAsync";
    var_getQuotationTemplateAsync.varying = false;
    var_getQuotationTemplateAsync.keepArgsHandle = true;
    var_getQuotationTemplateAsync.args = [var_IQuotationReportTemplateParam];
    var_getQuotationTemplateAsync.return = var_getQuotationTemplateAsync_objectLiteral_Promise;
    var_IQuotationReportTemplateParam.type = BasicType.Object;
    var_IQuotationReportTemplateParam.properties = {
        "activeToolType": var_stringType,
        "libraryType": var_numberType_Array,
    };
    var_numberType_Array.type = BasicType.Array;
    var_numberType_Array.value = var_numberType;
    var_getQuotationTemplateAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getQuotationTemplateAsync_objectLiteral_Promise.properties = {
        "then": var_getQuotationTemplateAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getQuotationTemplateAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then.name = "";
    var_getQuotationTemplateAsync_objectLiteral_Promise_then.varying = false;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then.args = [var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getQuotationTemplateAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve.args = [var_getQuotationTemplateAsync_objectLiteral];
    var_getQuotationTemplateAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getQuotationTemplateAsync_objectLiteral.type = BasicType.Object;
    var_getQuotationTemplateAsync_objectLiteral.properties = {
        "result": var_IQuotationReportTemplateItem_Array,
    };
    var_IQuotationReportTemplateItem_Array.type = BasicType.Array;
    var_IQuotationReportTemplateItem_Array.value = var_IQuotationReportTemplateItem;
    var_IQuotationReportTemplateItem.type = BasicType.Object;
    var_IQuotationReportTemplateItem.properties = {
        "templateId": var_stringType,
        "name": var_stringType,
    };
    var_getModelJsonAsyncV2.type = BasicType.Function;
    var_getModelJsonAsyncV2.name = "getModelJsonAsyncV2";
    var_getModelJsonAsyncV2.varying = false;
    var_getModelJsonAsyncV2.keepArgsHandle = true;
    var_getModelJsonAsyncV2.args = [var_GetModelJsonOptionV2];
    var_getModelJsonAsyncV2.return = var_unknownType_Promise;
    var_GetModelJsonOptionV2.type = BasicType.Object;
    var_GetModelJsonOptionV2.properties = {
        "modelIds": var_unknownType,
        "levelId": var_stringType,
        "timeout": var_numberType,
        "templateId": var_stringType,
    };
    var_getFittingDataFromAuxiliaryAsync.type = BasicType.Function;
    var_getFittingDataFromAuxiliaryAsync.name = "getFittingDataFromAuxiliaryAsync";
    var_getFittingDataFromAuxiliaryAsync.varying = false;
    var_getFittingDataFromAuxiliaryAsync.keepArgsHandle = true;
    var_getFittingDataFromAuxiliaryAsync.args = [var_IGetFittingDataByAuxiliaryOption];
    var_getFittingDataFromAuxiliaryAsync.return = var_injection_UnknownType_Array_Promise;
    var_IGetFittingDataByAuxiliaryOption.type = BasicType.Object;
    var_IGetFittingDataByAuxiliaryOption.properties = {
        "modelId": var_stringType,
    };
    var_IGetFittingDataByAuxiliaryBaseOption.type = BasicType.Object;
    var_IGetFittingDataByAuxiliaryBaseOption.properties = {
        "distanceTol": var_numberType,
    };
    var_injection_UnknownType_Array_Promise.type = BasicType.Object;
    var_injection_UnknownType_Array_Promise.properties = {
        "then": var_injection_UnknownType_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_UnknownType_Array_Promise_then.type = BasicType.Function;
    var_injection_UnknownType_Array_Promise_then.name = "";
    var_injection_UnknownType_Array_Promise_then.varying = false;
    var_injection_UnknownType_Array_Promise_then.keepArgsHandle = true;
    var_injection_UnknownType_Array_Promise_then.args = [var_injection_UnknownType_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_UnknownType_Array_Promise_then.return = var_undefinedType;
    var_injection_UnknownType_Array_Promise_then_onresolve.type = BasicType.Function;
    var_injection_UnknownType_Array_Promise_then_onresolve.name = "";
    var_injection_UnknownType_Array_Promise_then_onresolve.varying = false;
    var_injection_UnknownType_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_UnknownType_Array_Promise_then_onresolve.args = [var_injection_UnknownType_Array];
    var_injection_UnknownType_Array_Promise_then_onresolve.return = var_undefinedType;
    var_injection_UnknownType_Array.type = BasicType.Array;
    var_injection_UnknownType_Array.value = var_injection_UnknownType;
    var_getFittingDatasFromAuxiliaryAsync.type = BasicType.Function;
    var_getFittingDatasFromAuxiliaryAsync.name = "getFittingDatasFromAuxiliaryAsync";
    var_getFittingDatasFromAuxiliaryAsync.varying = false;
    var_getFittingDatasFromAuxiliaryAsync.keepArgsHandle = true;
    var_getFittingDatasFromAuxiliaryAsync.args = [var_IBatchGetFittingDataByAuxiliaryOption];
    var_getFittingDatasFromAuxiliaryAsync.return = var_injection_UnknownType_Array_Array_Promise;
    var_IBatchGetFittingDataByAuxiliaryOption.type = BasicType.Object;
    var_IBatchGetFittingDataByAuxiliaryOption.properties = {
        "modelIds": var_stringType_Array,
    };
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_injection_UnknownType_Array_Array_Promise.type = BasicType.Object;
    var_injection_UnknownType_Array_Array_Promise.properties = {
        "then": var_injection_UnknownType_Array_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_UnknownType_Array_Array_Promise_then.type = BasicType.Function;
    var_injection_UnknownType_Array_Array_Promise_then.name = "";
    var_injection_UnknownType_Array_Array_Promise_then.varying = false;
    var_injection_UnknownType_Array_Array_Promise_then.keepArgsHandle = true;
    var_injection_UnknownType_Array_Array_Promise_then.args = [var_injection_UnknownType_Array_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_UnknownType_Array_Array_Promise_then.return = var_undefinedType;
    var_injection_UnknownType_Array_Array_Promise_then_onresolve.type = BasicType.Function;
    var_injection_UnknownType_Array_Array_Promise_then_onresolve.name = "";
    var_injection_UnknownType_Array_Array_Promise_then_onresolve.varying = false;
    var_injection_UnknownType_Array_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_UnknownType_Array_Array_Promise_then_onresolve.args = [var_injection_UnknownType_Array_Array];
    var_injection_UnknownType_Array_Array_Promise_then_onresolve.return = var_undefinedType;
    var_injection_UnknownType_Array_Array.type = BasicType.Array;
    var_injection_UnknownType_Array_Array.value = var_injection_UnknownType_Array;
    var_getIntersectedDataAsync.type = BasicType.Function;
    var_getIntersectedDataAsync.name = "getIntersectedDataAsync";
    var_getIntersectedDataAsync.varying = false;
    var_getIntersectedDataAsync.keepArgsHandle = true;
    var_getIntersectedDataAsync.args = [var_IGetIntersectedOption];
    var_getIntersectedDataAsync.return = var_IIntersectedData_Promise;
    var_IGetIntersectedOption.type = BasicType.Object;
    var_IGetIntersectedOption.properties = {
        "modelId": var_stringType,
        "tolerance": var_numberType,
        "timeout": var_numberType,
        "bodyDistTol": var_numberType,
        "faceDistTol": var_numberType,
        "thicknessFilter": var_booleanType,
        "computeModelTypes": var_numberType_Array,
        "products": var_IGetIntersectedOption_products_objectLiteral_Array,
        "direction": var_numberType,
        "intersectedInfoType": var_stringType_Array,
        "thicknessFilterBody": var_booleanType,
        "thicknessFilterFaceDistTol": var_numberType,
        "primitiveMatch": var_IGetIntersectedOption_primitiveMatch_objectLiteral,
        "productDirectionConfig": var_IGetIntersectedOption_productDirectionConfig_objectLiteral_Array,
    };
    var_IGetIntersectedOption_products_objectLiteral_Array.type = BasicType.Array;
    var_IGetIntersectedOption_products_objectLiteral_Array.value = var_IGetIntersectedOption_products_objectLiteral;
    var_IGetIntersectedOption_products_objectLiteral.type = BasicType.Object;
    var_IGetIntersectedOption_products_objectLiteral.properties = {
        "category": var_numberType,
    };
    var_IGetIntersectedOption_primitiveMatch_objectLiteral.type = BasicType.Object;
    var_IGetIntersectedOption_primitiveMatch_objectLiteral.properties = {
        "extendedCode": var_stringType_Array,
    };
    var_IGetIntersectedOption_productDirectionConfig_objectLiteral_Array.type = BasicType.Array;
    var_IGetIntersectedOption_productDirectionConfig_objectLiteral_Array.value = var_IGetIntersectedOption_productDirectionConfig_objectLiteral;
    var_IGetIntersectedOption_productDirectionConfig_objectLiteral.type = BasicType.Object;
    var_IGetIntersectedOption_productDirectionConfig_objectLiteral.properties = {
        "extendedCode": var_stringType,
        "referencePlane": var_stringType,
    };
    var_IIntersectedData_Promise.type = BasicType.Object;
    var_IIntersectedData_Promise.properties = {
        "then": var_IIntersectedData_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IIntersectedData_Promise_then.type = BasicType.Function;
    var_IIntersectedData_Promise_then.name = "";
    var_IIntersectedData_Promise_then.varying = false;
    var_IIntersectedData_Promise_then.keepArgsHandle = true;
    var_IIntersectedData_Promise_then.args = [var_IIntersectedData_Promise_then_onresolve, var_Promise_then_onreject];
    var_IIntersectedData_Promise_then.return = var_undefinedType;
    var_IIntersectedData_Promise_then_onresolve.type = BasicType.Function;
    var_IIntersectedData_Promise_then_onresolve.name = "";
    var_IIntersectedData_Promise_then_onresolve.varying = false;
    var_IIntersectedData_Promise_then_onresolve.keepArgsHandle = false;
    var_IIntersectedData_Promise_then_onresolve.args = [var_IIntersectedData];
    var_IIntersectedData_Promise_then_onresolve.return = var_undefinedType;
    var_IIntersectedData.type = BasicType.Object;
    var_IIntersectedData.properties = {
        "intersectedGroups": var_IIntersectedGroup_Array,
    };
    var_IIntersectedGroup_Array.type = BasicType.Array;
    var_IIntersectedGroup_Array.value = var_IIntersectedGroup;
    var_IIntersectedGroup.type = BasicType.Object;
    var_IIntersectedGroup.properties = {
        "id": var_stringType,
        "intersectType": var_numberType,
        "intersecteds": var_injection_UnknownType_Array,
    };
    var_EDesignExportCode.type = BasicType.Object;
    var_EDesignExportCode.properties = {
        "SUCCESS": var_numberType,
        "FAIL": var_numberType,
    };
    var_EPointType.type = BasicType.Object;
    var_EPointType.properties = {
        "NONE": var_numberType,
        "CIRCLE": var_numberType,
        "LINE": var_numberType,
        "CUT_CIRCLE": var_numberType,
        "ELLIPSE": var_numberType,
        "CIRCLE_CENTER": var_numberType,
    };
    var_EClockWise.type = BasicType.Object;
    var_EClockWise.properties = {
        "TRUE": var_stringType,
        "FALSE": var_stringType,
    };
    var_ELineType.type = BasicType.Object;
    var_ELineType.properties = {
        "SEGMENT": var_numberType,
        "CIRCLE_ARC": var_numberType,
        "ELLIPSE_ARC": var_numberType,
        "CIRCLE_CENTER": var_numberType,
    };
    var_EIntersectModelType.type = BasicType.Object;
    var_EIntersectModelType.properties = {
        "PLANK": var_numberType,
        "HARDWARE": var_numberType,
        "PRODUCT": var_numberType,
    };
    var_EProductDirection.type = BasicType.Object;
    var_EProductDirection.properties = {
        "XY": var_numberType,
        "YZ": var_numberType,
        "XZ": var_numberType,
    };
    var_EIntersectedInfoType.type = BasicType.Object;
    var_EIntersectedInfoType.properties = {
        "SHELL": var_stringType,
        "THROUGH_SHELL": var_stringType,
    };
    var_CustomModel.type = BasicType.Object;
    var_CustomModel.properties = {
        "newCustomModelByCategoryAsync": var_injection_NewCustomModelByCategoryAsync,
        "newCustomModelByProductIdAsync": var_injection_NewCustomModelByProductIdAsync,
        "getCustomModelByModelIdAsync": var_injection_GetCustomModelByModelIdAsync,
        "findTopModelsAsync": var_injection_FindTopModelsAsync,
        "insertAsync": var_injection_UpdateCustomModelAsync,
        "updateAsync": var_injection_UpdateCustomModelAsync,
        "deleteTopModelsAsync": var_injection_DeleteTopModelsAsync,
        "getPreviewImgAsync": var_getPreviewImgAsync,
        "getTopModelsLiteInfoAsync": var_getTopModelsLiteInfoAsync,
        "highlightModels": var_highlightModels,
        "findTopModelsSimpleAsync": var_findTopModelsSimpleAsync,
        "lockModel": var_lockModel,
        "unlockModel": var_unlockModel,
        "computeInnerSpacesAsync": var_computeInnerSpacesAsync,
        "ParamType": var_EParamType,
        "ParamModelType": var_EParamModelType,
        "BzPropertyType": var_EBzPropertyType,
    };
    var_getPreviewImgAsync.type = BasicType.Function;
    var_getPreviewImgAsync.name = "getPreviewImgAsync";
    var_getPreviewImgAsync.varying = false;
    var_getPreviewImgAsync.keepArgsHandle = true;
    var_getPreviewImgAsync.args = [var_IGetPreviewImgOption];
    var_getPreviewImgAsync.return = var_IPreviewImgInfo_Promise;
    var_IGetPreviewImgOption.type = BasicType.Object;
    var_IGetPreviewImgOption.properties = {
        "modelId": var_stringType,
        "ignoreCategory": var_numberType_Array,
    };
    var_IPreviewImgInfo_Promise.type = BasicType.Object;
    var_IPreviewImgInfo_Promise.properties = {
        "then": var_IPreviewImgInfo_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IPreviewImgInfo_Promise_then.type = BasicType.Function;
    var_IPreviewImgInfo_Promise_then.name = "";
    var_IPreviewImgInfo_Promise_then.varying = false;
    var_IPreviewImgInfo_Promise_then.keepArgsHandle = true;
    var_IPreviewImgInfo_Promise_then.args = [var_IPreviewImgInfo_Promise_then_onresolve, var_Promise_then_onreject];
    var_IPreviewImgInfo_Promise_then.return = var_undefinedType;
    var_IPreviewImgInfo_Promise_then_onresolve.type = BasicType.Function;
    var_IPreviewImgInfo_Promise_then_onresolve.name = "";
    var_IPreviewImgInfo_Promise_then_onresolve.varying = false;
    var_IPreviewImgInfo_Promise_then_onresolve.keepArgsHandle = false;
    var_IPreviewImgInfo_Promise_then_onresolve.args = [var_IPreviewImgInfo];
    var_IPreviewImgInfo_Promise_then_onresolve.return = var_undefinedType;
    var_IPreviewImgInfo.type = BasicType.Object;
    var_IPreviewImgInfo.properties = {
        "modelId": var_stringType,
        "imgData": var_stringType,
    };
    var_getTopModelsLiteInfoAsync.type = BasicType.Function;
    var_getTopModelsLiteInfoAsync.name = "getTopModelsLiteInfoAsync";
    var_getTopModelsLiteInfoAsync.varying = false;
    var_getTopModelsLiteInfoAsync.keepArgsHandle = true;
    var_getTopModelsLiteInfoAsync.args = [var_IGetTopModelsLiteInfoOption];
    var_getTopModelsLiteInfoAsync.return = var_getTopModelsLiteInfoAsync_objectLiteral_Promise;
    var_IGetTopModelsLiteInfoOption.type = BasicType.Object;
    var_IGetTopModelsLiteInfoOption.properties = {
        "page": var_numberType,
        "size": var_numberType,
        "ignoreCategory": var_numberType_Array,
    };
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise.properties = {
        "then": var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then.name = "";
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then.varying = false;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then.args = [var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve.args = [var_getTopModelsLiteInfoAsync_objectLiteral];
    var_getTopModelsLiteInfoAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getTopModelsLiteInfoAsync_objectLiteral.type = BasicType.Object;
    var_getTopModelsLiteInfoAsync_objectLiteral.properties = {
        "count": var_numberType,
        "page": var_numberType,
        "size": var_numberType,
        "data": var_ICustomModelLiteInfo_Array,
    };
    var_ICustomModelLiteInfo_Array.type = BasicType.Array;
    var_ICustomModelLiteInfo_Array.value = var_ICustomModelLiteInfo;
    var_ICustomModelLiteInfo.type = BasicType.Object;
    var_ICustomModelLiteInfo.properties = {
        "id": var_stringType,
        "toolType": var_stringType,
        "name": var_stringType,
        "roomInfo": var_IRoomInfo,
        "isSplit": var_booleanType,
        "isLocked": var_booleanType,
        "isHidden": var_booleanType,
        "isAudited": var_booleanType,
    };
    var_IRoomInfo.type = BasicType.Object;
    var_IRoomInfo.properties = {
        "roomId": var_stringType,
        "roomName": var_stringType,
    };
    var_highlightModels.type = BasicType.Function;
    var_highlightModels.name = "highlightModels";
    var_highlightModels.varying = false;
    var_highlightModels.keepArgsHandle = true;
    var_highlightModels.args = [var_highlightModels_option_objectLiteral];
    var_highlightModels.return = var_undefinedType;
    var_highlightModels_option_objectLiteral.type = BasicType.Object;
    var_highlightModels_option_objectLiteral.properties = {
        "modelIds": var_stringType_Array,
    };
    var_findTopModelsSimpleAsync.type = BasicType.Function;
    var_findTopModelsSimpleAsync.name = "findTopModelsSimpleAsync";
    var_findTopModelsSimpleAsync.varying = false;
    var_findTopModelsSimpleAsync.keepArgsHandle = true;
    var_findTopModelsSimpleAsync.args = [var_FindTopModelsSimpleOption];
    var_findTopModelsSimpleAsync.return = var_CustomModelSimpleData_Array_Promise;
    var_FindTopModelsSimpleOption.type = BasicType.Object;
    var_FindTopModelsSimpleOption.properties = {
        "ids": var_stringType_Array,
        "categories": var_numberType_Array,
        "toolType": var_stringType_Array,
        "locked": var_booleanType,
        "hidden": var_booleanType,
        "submitted": var_booleanType,
        "readonly": var_booleanType,
        "exclusive": var_booleanType,
        "child": var_booleanType,
        "accessory": var_booleanType,
    };
    var_CustomModelSimpleData_Array_Promise.type = BasicType.Object;
    var_CustomModelSimpleData_Array_Promise.properties = {
        "then": var_CustomModelSimpleData_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_CustomModelSimpleData_Array_Promise_then.type = BasicType.Function;
    var_CustomModelSimpleData_Array_Promise_then.name = "";
    var_CustomModelSimpleData_Array_Promise_then.varying = false;
    var_CustomModelSimpleData_Array_Promise_then.keepArgsHandle = true;
    var_CustomModelSimpleData_Array_Promise_then.args = [var_CustomModelSimpleData_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_CustomModelSimpleData_Array_Promise_then.return = var_undefinedType;
    var_CustomModelSimpleData_Array_Promise_then_onresolve.type = BasicType.Function;
    var_CustomModelSimpleData_Array_Promise_then_onresolve.name = "";
    var_CustomModelSimpleData_Array_Promise_then_onresolve.varying = false;
    var_CustomModelSimpleData_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_CustomModelSimpleData_Array_Promise_then_onresolve.args = [var_CustomModelSimpleData_Array];
    var_CustomModelSimpleData_Array_Promise_then_onresolve.return = var_undefinedType;
    var_CustomModelSimpleData_Array.type = BasicType.Array;
    var_CustomModelSimpleData_Array.value = var_CustomModelSimpleData;
    var_CustomModelSimpleData.type = BasicType.Object;
    var_CustomModelSimpleData.properties = {
        "id": var_stringType,
        "name": var_stringType,
        "type": var_numberType,
        "category": var_numberType,
        "toolType": var_stringType,
        "children": var_CustomModelSimpleData_Array,
        "accessories": var_CustomModelSimpleData_Array,
    };
    var_lockModel.type = BasicType.Function;
    var_lockModel.name = "lockModel";
    var_lockModel.varying = false;
    var_lockModel.keepArgsHandle = true;
    var_lockModel.args = [var_IModelLockOption];
    var_lockModel.return = var_undefinedType;
    var_IModelLockOption.type = BasicType.Object;
    var_IModelLockOption.properties = {
        "modelId": var_stringType,
        "businessId": var_stringType,
    };
    var_unlockModel.type = BasicType.Function;
    var_unlockModel.name = "unlockModel";
    var_unlockModel.varying = false;
    var_unlockModel.keepArgsHandle = true;
    var_unlockModel.args = [var_IModelLockOption];
    var_unlockModel.return = var_undefinedType;
    var_computeInnerSpacesAsync.type = BasicType.Function;
    var_computeInnerSpacesAsync.name = "computeInnerSpacesAsync";
    var_computeInnerSpacesAsync.varying = false;
    var_computeInnerSpacesAsync.keepArgsHandle = true;
    var_computeInnerSpacesAsync.args = [var_stringType];
    var_computeInnerSpacesAsync.return = var_IInnerSpaceWithBorders_Array_Promise;
    var_IInnerSpaceWithBorders_Array_Promise.type = BasicType.Object;
    var_IInnerSpaceWithBorders_Array_Promise.properties = {
        "then": var_IInnerSpaceWithBorders_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IInnerSpaceWithBorders_Array_Promise_then.type = BasicType.Function;
    var_IInnerSpaceWithBorders_Array_Promise_then.name = "";
    var_IInnerSpaceWithBorders_Array_Promise_then.varying = false;
    var_IInnerSpaceWithBorders_Array_Promise_then.keepArgsHandle = true;
    var_IInnerSpaceWithBorders_Array_Promise_then.args = [var_IInnerSpaceWithBorders_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IInnerSpaceWithBorders_Array_Promise_then.return = var_undefinedType;
    var_IInnerSpaceWithBorders_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IInnerSpaceWithBorders_Array_Promise_then_onresolve.name = "";
    var_IInnerSpaceWithBorders_Array_Promise_then_onresolve.varying = false;
    var_IInnerSpaceWithBorders_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IInnerSpaceWithBorders_Array_Promise_then_onresolve.args = [var_IInnerSpaceWithBorders_Array];
    var_IInnerSpaceWithBorders_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IInnerSpaceWithBorders_Array.type = BasicType.Array;
    var_IInnerSpaceWithBorders_Array.value = var_IInnerSpaceWithBorders;
    var_IInnerSpaceWithBorders.type = BasicType.Object;
    var_IInnerSpaceWithBorders.properties = {
        "rotate": var_Number3,
        "position": var_Number3,
        "size": var_Number3,
        "borderMap": var_unknownType,
    };
    var_EParamType.type = BasicType.Object;
    var_EParamType.properties = {
        "FLOAT": var_stringType,
        "FLOAT2": var_stringType,
        "FLOAT3": var_stringType,
        "FLOAT_BUFFER": var_stringType,
        "FLOAT2_BUFFER": var_stringType,
        "FLOAT3_BUFFER": var_stringType,
        "REFERENCE": var_stringType,
        "INT": var_stringType,
        "INT_BUFFER": var_stringType,
        "STRING": var_stringType,
        "SHAPE": var_stringType,
        "MATERIAL": var_stringType,
        "BOOLEAN": var_stringType,
        "BOOLEAN_BUFFER": var_stringType,
        "FIT": var_stringType,
        "PATH": var_stringType,
        "STYLE": var_stringType,
        "FORMULA_STRING": var_stringType,
        "BOOLEAN_LIST": var_stringType,
    };
    var_EParamModelType.type = BasicType.Object;
    var_EParamModelType.properties = {
        "COMMON": var_numberType,
        "ASSEMBLY": var_numberType,
        "TEMPLATE_ASSEMBLY": var_numberType,
    };
    var_EBzPropertyType.type = BasicType.Object;
    var_EBzPropertyType.properties = {
        "number": var_stringType,
        "string": var_stringType,
        "boolean": var_stringType,
    };
    var_Product.type = BasicType.Object;
    var_Product.properties = {
        "startDragProductAsync": var_startDragProductAsync,
        "isEnterpriseLibraryModel": var_isEnterpriseLibraryModel,
        "importProductAsync": var_importProductAsync,
    };
    var_startDragProductAsync.type = BasicType.Function;
    var_startDragProductAsync.name = "startDragProductAsync";
    var_startDragProductAsync.varying = false;
    var_startDragProductAsync.keepArgsHandle = true;
    var_startDragProductAsync.args = [var_stringType, var_startDragProductAsync_option_objectLiteral];
    var_startDragProductAsync.return = var_injection_DragCustomProductPromiseResult_Promise;
    var_startDragProductAsync_option_objectLiteral.type = BasicType.Object;
    var_startDragProductAsync_option_objectLiteral.properties = {
        "isAccessory": var_booleanType,
    };
    var_injection_DragCustomProductPromiseResult_Promise.type = BasicType.Object;
    var_injection_DragCustomProductPromiseResult_Promise.properties = {
        "then": var_injection_DragCustomProductPromiseResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_DragCustomProductPromiseResult_Promise_then.type = BasicType.Function;
    var_injection_DragCustomProductPromiseResult_Promise_then.name = "";
    var_injection_DragCustomProductPromiseResult_Promise_then.varying = false;
    var_injection_DragCustomProductPromiseResult_Promise_then.keepArgsHandle = true;
    var_injection_DragCustomProductPromiseResult_Promise_then.args = [var_injection_DragCustomProductPromiseResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_DragCustomProductPromiseResult_Promise_then.return = var_undefinedType;
    var_injection_DragCustomProductPromiseResult_Promise_then_onresolve.type = BasicType.Function;
    var_injection_DragCustomProductPromiseResult_Promise_then_onresolve.name = "";
    var_injection_DragCustomProductPromiseResult_Promise_then_onresolve.varying = false;
    var_injection_DragCustomProductPromiseResult_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_DragCustomProductPromiseResult_Promise_then_onresolve.args = [var_injection_DragCustomProductPromiseResult];
    var_injection_DragCustomProductPromiseResult_Promise_then_onresolve.return = var_undefinedType;
    var_isEnterpriseLibraryModel.type = BasicType.Function;
    var_isEnterpriseLibraryModel.name = "isEnterpriseLibraryModel";
    var_isEnterpriseLibraryModel.varying = false;
    var_isEnterpriseLibraryModel.keepArgsHandle = true;
    var_isEnterpriseLibraryModel.args = [var_stringType];
    var_isEnterpriseLibraryModel.return = var_booleanType;
    var_importProductAsync.type = BasicType.Function;
    var_importProductAsync.name = "importProductAsync";
    var_importProductAsync.varying = false;
    var_importProductAsync.keepArgsHandle = true;
    var_importProductAsync.args = [var_IImportModel];
    var_importProductAsync.return = var_IImportModelResult_Promise;
    var_IImportModel.type = BasicType.Object;
    var_IImportModel.properties = {
        "id": var_stringType,
        "type": var_stringType,
        "name": var_stringType,
        "category": var_stringType,
        "previewImgUrl": var_stringType,
        "previewImgData": var_stringType,
        "parameters": var_IParameter_Array,
        "subModels": var_unknownType_Array,
    };
    var_IParameter_Array.type = BasicType.Array;
    var_IParameter_Array.value = var_IParameter;
    var_IParameter.type = BasicType.Object;
    var_IParameter.properties = {
        "category": var_stringType,
        "name": var_stringType,
        "displayName": var_stringType,
        "value": var_stringType,
        "valueType": var_stringType,
        "setType": var_stringType,
        "min": var_stringType,
        "max": var_stringType,
        "options": var_IParameterOption_Array,
    };
    var_IParameterOption_Array.type = BasicType.Array;
    var_IParameterOption_Array.value = var_IParameterOption;
    var_IParameterOption.type = BasicType.Object;
    var_IParameterOption.properties = {
        "name": var_stringType,
        "value": var_stringType,
        "ignore": var_stringType,
        "priority": var_stringType,
    };
    var_IImportModelResult_Promise.type = BasicType.Object;
    var_IImportModelResult_Promise.properties = {
        "then": var_IImportModelResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IImportModelResult_Promise_then.type = BasicType.Function;
    var_IImportModelResult_Promise_then.name = "";
    var_IImportModelResult_Promise_then.varying = false;
    var_IImportModelResult_Promise_then.keepArgsHandle = true;
    var_IImportModelResult_Promise_then.args = [var_IImportModelResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IImportModelResult_Promise_then.return = var_undefinedType;
    var_IImportModelResult_Promise_then_onresolve.type = BasicType.Function;
    var_IImportModelResult_Promise_then_onresolve.name = "";
    var_IImportModelResult_Promise_then_onresolve.varying = false;
    var_IImportModelResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IImportModelResult_Promise_then_onresolve.args = [var_IImportModelResult];
    var_IImportModelResult_Promise_then_onresolve.return = var_undefinedType;
    var_IImportModelResult.type = BasicType.Object;
    var_IImportModelResult.properties = {
        "productId": var_stringType,
    };
    var_Drawing.type = BasicType.Object;
    var_Drawing.properties = {
        "getConstructionDrawingByElementsAsync": var_getConstructionDrawingByElementsAsync,
        "enterCustomDrawingAsync": var_enterCustomDrawingAsync,
        "DivisionType": var_EDivisionType,
        "FileType": var_EDrawingFileType,
    };
    var_getConstructionDrawingByElementsAsync.type = BasicType.Function;
    var_getConstructionDrawingByElementsAsync.name = "getConstructionDrawingByElementsAsync";
    var_getConstructionDrawingByElementsAsync.varying = false;
    var_getConstructionDrawingByElementsAsync.keepArgsHandle = true;
    var_getConstructionDrawingByElementsAsync.args = [var_unknownType];
    var_getConstructionDrawingByElementsAsync.return = var_IDrawingExportResponse_Array_Promise;
    var_IDrawingExportResponse_Array_Promise.type = BasicType.Object;
    var_IDrawingExportResponse_Array_Promise.properties = {
        "then": var_IDrawingExportResponse_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IDrawingExportResponse_Array_Promise_then.type = BasicType.Function;
    var_IDrawingExportResponse_Array_Promise_then.name = "";
    var_IDrawingExportResponse_Array_Promise_then.varying = false;
    var_IDrawingExportResponse_Array_Promise_then.keepArgsHandle = true;
    var_IDrawingExportResponse_Array_Promise_then.args = [var_IDrawingExportResponse_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IDrawingExportResponse_Array_Promise_then.return = var_undefinedType;
    var_IDrawingExportResponse_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IDrawingExportResponse_Array_Promise_then_onresolve.name = "";
    var_IDrawingExportResponse_Array_Promise_then_onresolve.varying = false;
    var_IDrawingExportResponse_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IDrawingExportResponse_Array_Promise_then_onresolve.args = [var_IDrawingExportResponse_Array];
    var_IDrawingExportResponse_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IDrawingExportResponse_Array.type = BasicType.Array;
    var_IDrawingExportResponse_Array.value = var_IDrawingExportResponse;
    var_IDrawingExportResponse.type = BasicType.Object;
    var_IDrawingExportResponse.properties = {
        "status": var_booleanType,
        "url": var_stringType,
        "fileType": var_numberType,
    };
    var_enterCustomDrawingAsync.type = BasicType.Function;
    var_enterCustomDrawingAsync.name = "enterCustomDrawingAsync";
    var_enterCustomDrawingAsync.varying = false;
    var_enterCustomDrawingAsync.keepArgsHandle = true;
    var_enterCustomDrawingAsync.args = [var_stringType];
    var_enterCustomDrawingAsync.return = var_undefinedType_Promise;
    var_EDivisionType.type = BasicType.Object;
    var_EDivisionType.properties = {
        "Space": var_stringType,
        "Relation": var_stringType,
    };
    var_EDrawingFileType.type = BasicType.Object;
    var_EDrawingFileType.properties = {
        "CAD": var_numberType,
        "JPG": var_numberType,
        "PDF": var_numberType,
    };
    var_FittingDesign.type = BasicType.Object;
    var_FittingDesign.properties = {
        "getIntersectedDataAsync": var_getIntersectedDataAsync_1,
        "getDesignDataAsync": var_getDesignDataAsync,
        "saveDesignDataAsync": var_saveDesignDataAsync,
        "deleteDesignDataAsync": var_deleteDesignDataAsync,
        "findDesignDataAsync": var_findDesignDataAsync,
        "saveDesignDatasAsync": var_saveDesignDatasAsync,
        "deleteDesignDatasAsync": var_deleteDesignDatasAsync,
        "IntersectModel": var_EIntersectModelType,
        "ProductDirection": var_EProductDirection,
        "IntersectedInfoType": var_EIntersectedInfoType,
    };
    var_getIntersectedDataAsync_1.type = BasicType.Function;
    var_getIntersectedDataAsync_1.name = "getIntersectedDataAsync";
    var_getIntersectedDataAsync_1.varying = false;
    var_getIntersectedDataAsync_1.keepArgsHandle = true;
    var_getIntersectedDataAsync_1.args = [var_IGetIntersectedOption];
    var_getIntersectedDataAsync_1.return = var_IIntersectedData_Promise;
    var_getDesignDataAsync.type = BasicType.Function;
    var_getDesignDataAsync.name = "getDesignDataAsync";
    var_getDesignDataAsync.varying = false;
    var_getDesignDataAsync.keepArgsHandle = true;
    var_getDesignDataAsync.args = [var_IFittingDesignOption];
    var_getDesignDataAsync.return = var_IFittingDesignData_Promise;
    var_IFittingDesignOption.type = BasicType.Object;
    var_IFittingDesignOption.properties = {
        "modelId": var_stringType,
    };
    var_IFittingDesignData_Promise.type = BasicType.Object;
    var_IFittingDesignData_Promise.properties = {
        "then": var_IFittingDesignData_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IFittingDesignData_Promise_then.type = BasicType.Function;
    var_IFittingDesignData_Promise_then.name = "";
    var_IFittingDesignData_Promise_then.varying = false;
    var_IFittingDesignData_Promise_then.keepArgsHandle = true;
    var_IFittingDesignData_Promise_then.args = [var_IFittingDesignData_Promise_then_onresolve, var_Promise_then_onreject];
    var_IFittingDesignData_Promise_then.return = var_undefinedType;
    var_IFittingDesignData_Promise_then_onresolve.type = BasicType.Function;
    var_IFittingDesignData_Promise_then_onresolve.name = "";
    var_IFittingDesignData_Promise_then_onresolve.varying = false;
    var_IFittingDesignData_Promise_then_onresolve.keepArgsHandle = false;
    var_IFittingDesignData_Promise_then_onresolve.args = [var_IFittingDesignData];
    var_IFittingDesignData_Promise_then_onresolve.return = var_undefinedType;
    var_IFittingDesignData.type = BasicType.Object;
    var_IFittingDesignData.properties = {
        "id": var_stringType,
        "holes": var_unknownType,
        "grooves": var_unknownType,
        "hardwares": var_unknownType,
        "hardwareGrooves": var_unknownType,
    };
    var_saveDesignDataAsync.type = BasicType.Function;
    var_saveDesignDataAsync.name = "saveDesignDataAsync";
    var_saveDesignDataAsync.varying = false;
    var_saveDesignDataAsync.keepArgsHandle = true;
    var_saveDesignDataAsync.args = [var_ISaveFittingDesignOption];
    var_saveDesignDataAsync.return = var_undefinedType_Promise;
    var_ISaveFittingDesignOption.type = BasicType.Object;
    var_ISaveFittingDesignOption.properties = {
        "data": var_IFittingDesignData,
    };
    var_deleteDesignDataAsync.type = BasicType.Function;
    var_deleteDesignDataAsync.name = "deleteDesignDataAsync";
    var_deleteDesignDataAsync.varying = false;
    var_deleteDesignDataAsync.keepArgsHandle = true;
    var_deleteDesignDataAsync.args = [var_IFittingDesignOption];
    var_deleteDesignDataAsync.return = var_undefinedType_Promise;
    var_findDesignDataAsync.type = BasicType.Function;
    var_findDesignDataAsync.name = "findDesignDataAsync";
    var_findDesignDataAsync.varying = false;
    var_findDesignDataAsync.keepArgsHandle = true;
    var_findDesignDataAsync.args = [var_stringType_Array];
    var_findDesignDataAsync.return = var_IFittingDesignData_Array_Promise;
    var_IFittingDesignData_Array_Promise.type = BasicType.Object;
    var_IFittingDesignData_Array_Promise.properties = {
        "then": var_IFittingDesignData_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IFittingDesignData_Array_Promise_then.type = BasicType.Function;
    var_IFittingDesignData_Array_Promise_then.name = "";
    var_IFittingDesignData_Array_Promise_then.varying = false;
    var_IFittingDesignData_Array_Promise_then.keepArgsHandle = true;
    var_IFittingDesignData_Array_Promise_then.args = [var_IFittingDesignData_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IFittingDesignData_Array_Promise_then.return = var_undefinedType;
    var_IFittingDesignData_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IFittingDesignData_Array_Promise_then_onresolve.name = "";
    var_IFittingDesignData_Array_Promise_then_onresolve.varying = false;
    var_IFittingDesignData_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IFittingDesignData_Array_Promise_then_onresolve.args = [var_IFittingDesignData_Array];
    var_IFittingDesignData_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IFittingDesignData_Array.type = BasicType.Array;
    var_IFittingDesignData_Array.value = var_IFittingDesignData;
    var_saveDesignDatasAsync.type = BasicType.Function;
    var_saveDesignDatasAsync.name = "saveDesignDatasAsync";
    var_saveDesignDatasAsync.varying = false;
    var_saveDesignDatasAsync.keepArgsHandle = true;
    var_saveDesignDatasAsync.args = [var_ISaveFittingDesignOption_Array];
    var_saveDesignDatasAsync.return = var_undefinedType_Promise;
    var_ISaveFittingDesignOption_Array.type = BasicType.Array;
    var_ISaveFittingDesignOption_Array.value = var_ISaveFittingDesignOption;
    var_deleteDesignDatasAsync.type = BasicType.Function;
    var_deleteDesignDatasAsync.name = "deleteDesignDatasAsync";
    var_deleteDesignDatasAsync.varying = false;
    var_deleteDesignDatasAsync.keepArgsHandle = true;
    var_deleteDesignDatasAsync.args = [var_stringType_Array];
    var_deleteDesignDatasAsync.return = var_undefinedType_Promise;
    var_FittingDesignV2.type = BasicType.Object;
    var_FittingDesignV2.properties = {
        "putDesignDataAsync": var_putDesignDataAsync,
        "findDesignDataAsync": var_findDesignDataAsync_1,
        "deleteDesignDataAsync": var_deleteDesignDataAsync_1,
    };
    var_putDesignDataAsync.type = BasicType.Function;
    var_putDesignDataAsync.name = "putDesignDataAsync";
    var_putDesignDataAsync.varying = false;
    var_putDesignDataAsync.keepArgsHandle = true;
    var_putDesignDataAsync.args = [var_injection_UnknownType_Array];
    var_putDesignDataAsync.return = var_injection_UnknownType_Array_Promise;
    var_findDesignDataAsync_1.type = BasicType.Function;
    var_findDesignDataAsync_1.name = "findDesignDataAsync";
    var_findDesignDataAsync_1.varying = false;
    var_findDesignDataAsync_1.keepArgsHandle = true;
    var_findDesignDataAsync_1.args = [var_stringType_Array];
    var_findDesignDataAsync_1.return = var_injection_UnknownType_Array_Promise;
    var_deleteDesignDataAsync_1.type = BasicType.Function;
    var_deleteDesignDataAsync_1.name = "deleteDesignDataAsync";
    var_deleteDesignDataAsync_1.varying = false;
    var_deleteDesignDataAsync_1.keepArgsHandle = true;
    var_deleteDesignDataAsync_1.args = [var_stringType_Array];
    var_deleteDesignDataAsync_1.return = var_undefinedType_Promise;
    var_Detection.type = BasicType.Object;
    var_Detection.properties = {
        "checkIntersectionAsync": var_checkIntersectionAsync,
        "detectAsync": var_detectAsync,
        "detectByModelIdAsync": var_detectByModelIdAsync,
        "getDetectConfig": var_getDetectConfig,
        "DetectType": var_EDetectType,
    };
    var_checkIntersectionAsync.type = BasicType.Function;
    var_checkIntersectionAsync.name = "checkIntersectionAsync";
    var_checkIntersectionAsync.varying = false;
    var_checkIntersectionAsync.keepArgsHandle = true;
    var_checkIntersectionAsync.args = [var_IIntersectCheckOption];
    var_checkIntersectionAsync.return = var_IIntersectCheckResult_Array_Promise;
    var_IIntersectCheckOption.type = BasicType.Object;
    var_IIntersectCheckOption.properties = {
        "modelIds": var_unknownType,
        "toolType": var_unknownType,
    };
    var_IIntersectCheckResult_Array_Promise.type = BasicType.Object;
    var_IIntersectCheckResult_Array_Promise.properties = {
        "then": var_IIntersectCheckResult_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IIntersectCheckResult_Array_Promise_then.type = BasicType.Function;
    var_IIntersectCheckResult_Array_Promise_then.name = "";
    var_IIntersectCheckResult_Array_Promise_then.varying = false;
    var_IIntersectCheckResult_Array_Promise_then.keepArgsHandle = true;
    var_IIntersectCheckResult_Array_Promise_then.args = [var_IIntersectCheckResult_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IIntersectCheckResult_Array_Promise_then.return = var_undefinedType;
    var_IIntersectCheckResult_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IIntersectCheckResult_Array_Promise_then_onresolve.name = "";
    var_IIntersectCheckResult_Array_Promise_then_onresolve.varying = false;
    var_IIntersectCheckResult_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IIntersectCheckResult_Array_Promise_then_onresolve.args = [var_IIntersectCheckResult_Array];
    var_IIntersectCheckResult_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IIntersectCheckResult_Array.type = BasicType.Array;
    var_IIntersectCheckResult_Array.value = var_IIntersectCheckResult;
    var_IIntersectCheckResult.type = BasicType.Object;
    var_IIntersectCheckResult.properties = {
        "id": var_stringType,
        "name": var_stringType,
        "level": var_numberType,
        "toolType": var_stringType,
        "children": var_IIntersectCheckResult_Array,
    };
    var_detectAsync.type = BasicType.Function;
    var_detectAsync.name = "detectAsync";
    var_detectAsync.varying = false;
    var_detectAsync.keepArgsHandle = true;
    var_detectAsync.args = [var_unknownType, var_IDetectOption];
    var_detectAsync.return = var_unknownType_Promise;
    var_IDetectOption.type = BasicType.Object;
    var_IDetectOption.properties = {
        "toolType": var_stringType,
    };
    var_detectByModelIdAsync.type = BasicType.Function;
    var_detectByModelIdAsync.name = "detectByModelIdAsync";
    var_detectByModelIdAsync.varying = false;
    var_detectByModelIdAsync.keepArgsHandle = true;
    var_detectByModelIdAsync.args = [var_unknownType, var_stringType_Array];
    var_detectByModelIdAsync.return = var_unknownType_Promise;
    var_getDetectConfig.type = BasicType.Function;
    var_getDetectConfig.name = "getDetectConfig";
    var_getDetectConfig.varying = false;
    var_getDetectConfig.keepArgsHandle = false;
    var_getDetectConfig.args = [];
    var_getDetectConfig.return = var_unknownType;
    var_EDetectType.type = BasicType.Object;
    var_EDetectType.properties = {
        "Intersect": var_stringType,
        "Validity": var_stringType,
        "Rule": var_stringType,
    };
    var_Order.type = BasicType.Object;
    var_Order.properties = {
        "getRelatedOrderAsync": var_getRelatedOrderAsync,
        "getCustomerOrderAsync": var_getCustomerOrderAsync,
        "getAuditOrderAsync": var_getAuditOrderAsync,
        "openOrderDetailPanel": var_openOrderDetailPanel,
    };
    var_getRelatedOrderAsync.type = BasicType.Function;
    var_getRelatedOrderAsync.name = "getRelatedOrderAsync";
    var_getRelatedOrderAsync.varying = false;
    var_getRelatedOrderAsync.keepArgsHandle = true;
    var_getRelatedOrderAsync.args = [var_stringType];
    var_getRelatedOrderAsync.return = var_IRelatedOrderResult_Promise;
    var_IRelatedOrderResult_Promise.type = BasicType.Object;
    var_IRelatedOrderResult_Promise.properties = {
        "then": var_IRelatedOrderResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IRelatedOrderResult_Promise_then.type = BasicType.Function;
    var_IRelatedOrderResult_Promise_then.name = "";
    var_IRelatedOrderResult_Promise_then.varying = false;
    var_IRelatedOrderResult_Promise_then.keepArgsHandle = true;
    var_IRelatedOrderResult_Promise_then.args = [var_IRelatedOrderResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IRelatedOrderResult_Promise_then.return = var_undefinedType;
    var_IRelatedOrderResult_Promise_then_onresolve.type = BasicType.Function;
    var_IRelatedOrderResult_Promise_then_onresolve.name = "";
    var_IRelatedOrderResult_Promise_then_onresolve.varying = false;
    var_IRelatedOrderResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IRelatedOrderResult_Promise_then_onresolve.args = [var_IRelatedOrderResult];
    var_IRelatedOrderResult_Promise_then_onresolve.return = var_undefinedType;
    var_IRelatedOrderResult.type = BasicType.Object;
    var_IRelatedOrderResult.properties = {
        "customerOrderId": var_stringType,
        "auditOrderId": var_stringType,
    };
    var_getCustomerOrderAsync.type = BasicType.Function;
    var_getCustomerOrderAsync.name = "getCustomerOrderAsync";
    var_getCustomerOrderAsync.varying = false;
    var_getCustomerOrderAsync.keepArgsHandle = true;
    var_getCustomerOrderAsync.args = [var_unknownType];
    var_getCustomerOrderAsync.return = var_ICustomerOrderDetail_Promise;
    var_ICustomerOrderDetail_Promise.type = BasicType.Object;
    var_ICustomerOrderDetail_Promise.properties = {
        "then": var_ICustomerOrderDetail_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ICustomerOrderDetail_Promise_then.type = BasicType.Function;
    var_ICustomerOrderDetail_Promise_then.name = "";
    var_ICustomerOrderDetail_Promise_then.varying = false;
    var_ICustomerOrderDetail_Promise_then.keepArgsHandle = true;
    var_ICustomerOrderDetail_Promise_then.args = [var_ICustomerOrderDetail_Promise_then_onresolve, var_Promise_then_onreject];
    var_ICustomerOrderDetail_Promise_then.return = var_undefinedType;
    var_ICustomerOrderDetail_Promise_then_onresolve.type = BasicType.Function;
    var_ICustomerOrderDetail_Promise_then_onresolve.name = "";
    var_ICustomerOrderDetail_Promise_then_onresolve.varying = false;
    var_ICustomerOrderDetail_Promise_then_onresolve.keepArgsHandle = false;
    var_ICustomerOrderDetail_Promise_then_onresolve.args = [var_ICustomerOrderDetail];
    var_ICustomerOrderDetail_Promise_then_onresolve.return = var_undefinedType;
    var_ICustomerOrderDetail.type = BasicType.Object;
    var_ICustomerOrderDetail.properties = {
        "templateKey": var_stringType,
        "orderId": var_stringType,
        "orderReadableId": var_stringType,
        "orderName": var_stringType,
        "customerName": var_stringType,
        "customerPhoneNumber": var_stringType,
        "customerArea": var_stringType,
        "customerAddress": var_stringType,
        "storeName": var_stringType,
        "buildingArea": var_stringType,
        "buildingAddress": var_stringType,
    };
    var_getAuditOrderAsync.type = BasicType.Function;
    var_getAuditOrderAsync.name = "getAuditOrderAsync";
    var_getAuditOrderAsync.varying = false;
    var_getAuditOrderAsync.keepArgsHandle = true;
    var_getAuditOrderAsync.args = [var_stringType];
    var_getAuditOrderAsync.return = var_IAuditOrderResult_Promise;
    var_IAuditOrderResult_Promise.type = BasicType.Object;
    var_IAuditOrderResult_Promise.properties = {
        "then": var_IAuditOrderResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IAuditOrderResult_Promise_then.type = BasicType.Function;
    var_IAuditOrderResult_Promise_then.name = "";
    var_IAuditOrderResult_Promise_then.varying = false;
    var_IAuditOrderResult_Promise_then.keepArgsHandle = true;
    var_IAuditOrderResult_Promise_then.args = [var_IAuditOrderResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IAuditOrderResult_Promise_then.return = var_undefinedType;
    var_IAuditOrderResult_Promise_then_onresolve.type = BasicType.Function;
    var_IAuditOrderResult_Promise_then_onresolve.name = "";
    var_IAuditOrderResult_Promise_then_onresolve.varying = false;
    var_IAuditOrderResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IAuditOrderResult_Promise_then_onresolve.args = [var_IAuditOrderResult];
    var_IAuditOrderResult_Promise_then_onresolve.return = var_undefinedType;
    var_IAuditOrderResult.type = BasicType.Object;
    var_IAuditOrderResult.properties = {
        "orderReadableId": var_stringType,
        "auditName": var_stringType,
        "auditorName": var_stringType,
        "foreignOrderCode": var_stringType,
        "remark": var_stringType,
    };
    var_openOrderDetailPanel.type = BasicType.Function;
    var_openOrderDetailPanel.name = "openOrderDetailPanel";
    var_openOrderDetailPanel.varying = false;
    var_openOrderDetailPanel.keepArgsHandle = true;
    var_openOrderDetailPanel.args = [var_stringType];
    var_openOrderDetailPanel.return = var_undefinedType;
    var_Group.type = BasicType.Object;
    var_Group.properties = {
        "getGroupDataAsync": var_getGroupDataAsync,
        "updateGroupDataAsync": var_updateGroupDataAsync,
    };
    var_getGroupDataAsync.type = BasicType.Function;
    var_getGroupDataAsync.name = "getGroupDataAsync";
    var_getGroupDataAsync.varying = false;
    var_getGroupDataAsync.keepArgsHandle = false;
    var_getGroupDataAsync.args = [];
    var_getGroupDataAsync.return = var_IGroupData_Promise;
    var_IGroupData_Promise.type = BasicType.Object;
    var_IGroupData_Promise.properties = {
        "then": var_IGroupData_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IGroupData_Promise_then.type = BasicType.Function;
    var_IGroupData_Promise_then.name = "";
    var_IGroupData_Promise_then.varying = false;
    var_IGroupData_Promise_then.keepArgsHandle = true;
    var_IGroupData_Promise_then.args = [var_IGroupData_Promise_then_onresolve, var_Promise_then_onreject];
    var_IGroupData_Promise_then.return = var_undefinedType;
    var_IGroupData_Promise_then_onresolve.type = BasicType.Function;
    var_IGroupData_Promise_then_onresolve.name = "";
    var_IGroupData_Promise_then_onresolve.varying = false;
    var_IGroupData_Promise_then_onresolve.keepArgsHandle = false;
    var_IGroupData_Promise_then_onresolve.args = [var_IGroupData];
    var_IGroupData_Promise_then_onresolve.return = var_undefinedType;
    var_IGroupData.type = BasicType.Object;
    var_IGroupData.properties = {
        "rooms": var_IGroupData_rooms_objectLiteral_Array,
    };
    var_IGroupData_rooms_objectLiteral_Array.type = BasicType.Array;
    var_IGroupData_rooms_objectLiteral_Array.value = var_IGroupData_rooms_objectLiteral;
    var_IGroupData_rooms_objectLiteral.type = BasicType.Object;
    var_IGroupData_rooms_objectLiteral.properties = {
        "roomId": var_stringType,
        "order": var_numberType,
        "groups": var_IGroupData_rooms_groups_objectLiteral_Array,
    };
    var_IGroupData_rooms_groups_objectLiteral_Array.type = BasicType.Array;
    var_IGroupData_rooms_groups_objectLiteral_Array.value = var_IGroupData_rooms_groups_objectLiteral;
    var_IGroupData_rooms_groups_objectLiteral.type = BasicType.Object;
    var_IGroupData_rooms_groups_objectLiteral.properties = {
        "name": var_stringType,
        "order": var_numberType,
        "id": var_stringType,
        "wallFaces": var_IGroupData_rooms_groups_wallFaces_objectLiteral_Array,
        "models": var_IGroupData_rooms_groups_models_objectLiteral_Array,
    };
    var_IGroupData_rooms_groups_wallFaces_objectLiteral_Array.type = BasicType.Array;
    var_IGroupData_rooms_groups_wallFaces_objectLiteral_Array.value = var_IGroupData_rooms_groups_wallFaces_objectLiteral;
    var_IGroupData_rooms_groups_wallFaces_objectLiteral.type = BasicType.Object;
    var_IGroupData_rooms_groups_wallFaces_objectLiteral.properties = {
        "wallFaceId": var_stringType,
    };
    var_IGroupData_rooms_groups_models_objectLiteral_Array.type = BasicType.Array;
    var_IGroupData_rooms_groups_models_objectLiteral_Array.value = var_IGroupData_rooms_groups_models_objectLiteral;
    var_IGroupData_rooms_groups_models_objectLiteral.type = BasicType.Object;
    var_IGroupData_rooms_groups_models_objectLiteral.properties = {
        "id": var_stringType,
    };
    var_updateGroupDataAsync.type = BasicType.Function;
    var_updateGroupDataAsync.name = "updateGroupDataAsync";
    var_updateGroupDataAsync.varying = false;
    var_updateGroupDataAsync.keepArgsHandle = true;
    var_updateGroupDataAsync.args = [var_IGroupData];
    var_updateGroupDataAsync.return = var_undefinedType_Promise;
    var_InstallCode.type = BasicType.Object;
    var_InstallCode.properties = {
        "attachInstallDataAsync": var_attachInstallDataAsync,
        "getInstallCodeAsync": var_injection_AsyncFunctionType,
    };
    var_attachInstallDataAsync.type = BasicType.Function;
    var_attachInstallDataAsync.name = "attachInstallDataAsync";
    var_attachInstallDataAsync.varying = false;
    var_attachInstallDataAsync.keepArgsHandle = true;
    var_attachInstallDataAsync.args = [var_IInstallData];
    var_attachInstallDataAsync.return = var_undefinedType_Promise;
    var_IInstallData.type = BasicType.Object;
    var_IInstallData.properties = {
        "rooms": var_IInstallData_rooms_objectLiteral_Array,
        "models": var_IInstallData_models_objectLiteral_Array,
    };
    var_IInstallData_rooms_objectLiteral_Array.type = BasicType.Array;
    var_IInstallData_rooms_objectLiteral_Array.value = var_IInstallData_rooms_objectLiteral;
    var_IInstallData_rooms_objectLiteral.type = BasicType.Object;
    var_IInstallData_rooms_objectLiteral.properties = {
        "roomId": var_stringType,
        "roomCode": var_stringType,
    };
    var_IInstallData_models_objectLiteral_Array.type = BasicType.Array;
    var_IInstallData_models_objectLiteral_Array.value = var_IInstallData_models_objectLiteral;
    var_IInstallData_models_objectLiteral.type = BasicType.Object;
    var_IInstallData_models_objectLiteral.properties = {
        "id": var_stringType,
        "installCode": var_stringType,
    };
    var_LeftPanel.type = BasicType.Object;
    var_LeftPanel.properties = {
    };
    var_BoolModeling.type = BasicType.Object;
    var_BoolModeling.properties = {
        "triggerBoolEffectAsync": var_triggerBoolEffectAsync,
    };
    var_triggerBoolEffectAsync.type = BasicType.Function;
    var_triggerBoolEffectAsync.name = "triggerBoolEffectAsync";
    var_triggerBoolEffectAsync.varying = false;
    var_triggerBoolEffectAsync.keepArgsHandle = true;
    var_triggerBoolEffectAsync.args = [var_stringType];
    var_triggerBoolEffectAsync.return = var_ITriggerBoolEffectResult_Promise;
    var_ITriggerBoolEffectResult_Promise.type = BasicType.Object;
    var_ITriggerBoolEffectResult_Promise.properties = {
        "then": var_ITriggerBoolEffectResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ITriggerBoolEffectResult_Promise_then.type = BasicType.Function;
    var_ITriggerBoolEffectResult_Promise_then.name = "";
    var_ITriggerBoolEffectResult_Promise_then.varying = false;
    var_ITriggerBoolEffectResult_Promise_then.keepArgsHandle = true;
    var_ITriggerBoolEffectResult_Promise_then.args = [var_ITriggerBoolEffectResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_ITriggerBoolEffectResult_Promise_then.return = var_undefinedType;
    var_ITriggerBoolEffectResult_Promise_then_onresolve.type = BasicType.Function;
    var_ITriggerBoolEffectResult_Promise_then_onresolve.name = "";
    var_ITriggerBoolEffectResult_Promise_then_onresolve.varying = false;
    var_ITriggerBoolEffectResult_Promise_then_onresolve.keepArgsHandle = false;
    var_ITriggerBoolEffectResult_Promise_then_onresolve.args = [var_ITriggerBoolEffectResult];
    var_ITriggerBoolEffectResult_Promise_then_onresolve.return = var_undefinedType;
    var_ITriggerBoolEffectResult.type = BasicType.Object;
    var_ITriggerBoolEffectResult.properties = {
        "success": var_booleanType,
        "code": var_numberType,
    };
    var_UI.type = BasicType.Object;
    var_UI.properties = {
        "openStyleExtensionAsync": var_openStyleExtensionAsync,
    };
    var_openStyleExtensionAsync.type = BasicType.Function;
    var_openStyleExtensionAsync.name = "openStyleExtensionAsync";
    var_openStyleExtensionAsync.varying = false;
    var_openStyleExtensionAsync.keepArgsHandle = true;
    var_openStyleExtensionAsync.args = [var_ILeftPanelStyleExtensionOptions];
    var_openStyleExtensionAsync.return = var_undefinedType_Promise;
    var_ILeftPanelStyleExtensionOptions.type = BasicType.Object;
    var_ILeftPanelStyleExtensionOptions.properties = {
        "title": var_stringType,
        "prodCatId": var_numberType,
        "paramPackageId": var_stringType,
        "toolType": var_stringType,
        "mode": var_numberType,
        "pos": var_numberType,
        "defaultLeft": var_numberType,
    };
    var_Product_1.type = BasicType.Object;
    var_Product_1.properties = {
        "getProductAsync": var_getProductAsync,
        "getProductsAsync": var_injection_AsyncFunctionType,
        "findCustomFoldersAsync": var_findCustomFoldersAsync,
        "findCustomProductsAsync": var_findCustomProductsAsync,
        "getProductDefaultBuildAsync": var_getProductDefaultBuildAsync,
    };
    var_getProductAsync.type = BasicType.Function;
    var_getProductAsync.name = "getProductAsync";
    var_getProductAsync.varying = false;
    var_getProductAsync.keepArgsHandle = true;
    var_getProductAsync.args = [var_stringType];
    var_getProductAsync.return = var_IProduct_Promise;
    var_IProduct_Promise.type = BasicType.Object;
    var_IProduct_Promise.properties = {
        "then": var_IProduct_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IProduct_Promise_then.type = BasicType.Function;
    var_IProduct_Promise_then.name = "";
    var_IProduct_Promise_then.varying = false;
    var_IProduct_Promise_then.keepArgsHandle = true;
    var_IProduct_Promise_then.args = [var_IProduct_Promise_then_onresolve, var_Promise_then_onreject];
    var_IProduct_Promise_then.return = var_undefinedType;
    var_IProduct_Promise_then_onresolve.type = BasicType.Function;
    var_IProduct_Promise_then_onresolve.name = "";
    var_IProduct_Promise_then_onresolve.varying = false;
    var_IProduct_Promise_then_onresolve.keepArgsHandle = false;
    var_IProduct_Promise_then_onresolve.args = [var_IProduct];
    var_IProduct_Promise_then_onresolve.return = var_undefinedType;
    var_IProduct.type = BasicType.Object;
    var_IProduct.properties = {
        "productId": var_stringType,
        "category": var_numberType,
        "name": var_stringType,
        "coverImgUrl": var_stringType,
        "sketchImgUrl": var_stringType,
        "customFields": var_IProductCustomField_Array,
        "ownerCompanyId": var_stringType,
    };
    var_IProductCustomField_Array.type = BasicType.Array;
    var_IProductCustomField_Array.value = var_IProductCustomField;
    var_IProductCustomField.type = BasicType.Object;
    var_IProductCustomField.properties = {
        "id": var_stringType,
        "name": var_stringType,
        "code": var_stringType,
        "value": var_stringType,
    };
    var_findCustomFoldersAsync.type = BasicType.Function;
    var_findCustomFoldersAsync.name = "findCustomFoldersAsync";
    var_findCustomFoldersAsync.varying = false;
    var_findCustomFoldersAsync.keepArgsHandle = true;
    var_findCustomFoldersAsync.args = [var_IFindCustomFoldersOptions];
    var_findCustomFoldersAsync.return = var_IFolder_Array_Promise;
    var_IFindCustomFoldersOptions.type = BasicType.Object;
    var_IFindCustomFoldersOptions.properties = {
        "lib": var_stringType,
        "tool": var_stringType,
        "packageId": var_stringType,
        "categoryIds": var_numberType_Array,
    };
    var_IFolder_Array_Promise.type = BasicType.Object;
    var_IFolder_Array_Promise.properties = {
        "then": var_IFolder_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IFolder_Array_Promise_then.type = BasicType.Function;
    var_IFolder_Array_Promise_then.name = "";
    var_IFolder_Array_Promise_then.varying = false;
    var_IFolder_Array_Promise_then.keepArgsHandle = true;
    var_IFolder_Array_Promise_then.args = [var_IFolder_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IFolder_Array_Promise_then.return = var_undefinedType;
    var_IFolder_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IFolder_Array_Promise_then_onresolve.name = "";
    var_IFolder_Array_Promise_then_onresolve.varying = false;
    var_IFolder_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IFolder_Array_Promise_then_onresolve.args = [var_IFolder_Array];
    var_IFolder_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IFolder_Array.type = BasicType.Array;
    var_IFolder_Array.value = var_IFolder;
    var_IFolder.type = BasicType.Object;
    var_IFolder.properties = {
        "id": var_stringType,
        "name": var_stringType,
        "imageUrl": var_stringType,
        "children": var_IFolder_Array,
    };
    var_findCustomProductsAsync.type = BasicType.Function;
    var_findCustomProductsAsync.name = "findCustomProductsAsync";
    var_findCustomProductsAsync.varying = false;
    var_findCustomProductsAsync.keepArgsHandle = true;
    var_findCustomProductsAsync.args = [var_IFindCustomProductsOptions];
    var_findCustomProductsAsync.return = var_findCustomProductsAsync_objectLiteral_Promise;
    var_IFindCustomProductsOptions.type = BasicType.Object;
    var_IFindCustomProductsOptions.properties = {
        "lib": var_stringType,
        "tool": var_stringType,
        "packageId": var_stringType,
        "categoryIds": var_numberType_Array,
        "start": var_numberType,
        "num": var_numberType,
        "folderId": var_stringType,
        "deep": var_booleanType,
    };
    var_findCustomProductsAsync_objectLiteral_Promise.type = BasicType.Object;
    var_findCustomProductsAsync_objectLiteral_Promise.properties = {
        "then": var_findCustomProductsAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_findCustomProductsAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_findCustomProductsAsync_objectLiteral_Promise_then.name = "";
    var_findCustomProductsAsync_objectLiteral_Promise_then.varying = false;
    var_findCustomProductsAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_findCustomProductsAsync_objectLiteral_Promise_then.args = [var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_findCustomProductsAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve.args = [var_findCustomProductsAsync_objectLiteral];
    var_findCustomProductsAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_findCustomProductsAsync_objectLiteral.type = BasicType.Object;
    var_findCustomProductsAsync_objectLiteral.properties = {
        "list": var_IProduct_Array,
        "totalCount": var_numberType,
    };
    var_IProduct_Array.type = BasicType.Array;
    var_IProduct_Array.value = var_IProduct;
    var_getProductDefaultBuildAsync.type = BasicType.Function;
    var_getProductDefaultBuildAsync.name = "getProductDefaultBuildAsync";
    var_getProductDefaultBuildAsync.varying = false;
    var_getProductDefaultBuildAsync.keepArgsHandle = true;
    var_getProductDefaultBuildAsync.args = [var_IGetProductDefaultBuildOption];
    var_getProductDefaultBuildAsync.return = var_stringType_Promise;
    var_IGetProductDefaultBuildOption.type = BasicType.Object;
    var_IGetProductDefaultBuildOption.properties = {
        "productId": var_stringType,
        "version": var_numberType,
    };
    var_stringType_Promise.type = BasicType.Object;
    var_stringType_Promise.properties = {
        "then": var_stringType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_stringType_Promise_then.type = BasicType.Function;
    var_stringType_Promise_then.name = "";
    var_stringType_Promise_then.varying = false;
    var_stringType_Promise_then.keepArgsHandle = true;
    var_stringType_Promise_then.args = [var_stringType_Promise_then_onresolve, var_Promise_then_onreject];
    var_stringType_Promise_then.return = var_undefinedType;
    var_stringType_Promise_then_onresolve.type = BasicType.Function;
    var_stringType_Promise_then_onresolve.name = "";
    var_stringType_Promise_then_onresolve.varying = false;
    var_stringType_Promise_then_onresolve.keepArgsHandle = false;
    var_stringType_Promise_then_onresolve.args = [var_stringType];
    var_stringType_Promise_then_onresolve.return = var_undefinedType;
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "getCustomGroup": var_getCustomGroup,
        "createCabinetAsync": var_createCabinetAsync,
        "updateCabinetAsync": var_updateCabinetAsync,
        "deleteCabinetAsync": var_deleteCabinetAsync,
        "getCabinetAsync": var_getCabinetAsync,
        "findCabinetListAsync": var_findCabinetListAsync,
        "findCabinetRefListAsync": var_injection_AsyncFunctionType,
        "createCabinetCopyAsync": var_createCabinetCopyAsync,
        "updateCabinetCopyAsync": var_updateCabinetCopyAsync,
        "deleteCabinetCopyAsync": var_deleteCabinetCopyAsync,
        "getCabinetCopyAsync": var_getCabinetCopyAsync,
        "findCabinetCopyListAsync": var_findCabinetCopyListAsync,
        "findCabinetCopyRefListAsync": var_injection_AsyncFunctionType,
        "createWardrobeAsync": var_createWardrobeAsync,
        "updateWardrobeAsync": var_updateWardrobeAsync,
        "deleteWardrobeAsync": var_deleteWardrobeAsync,
        "getWardrobeAsync": var_getWardrobeAsync,
        "findWardrobeListAsync": var_findWardrobeListAsync,
        "createWardrobeCopyAsync": var_createWardrobeCopyAsync,
        "updateWardrobeCopyAsync": var_updateWardrobeCopyAsync,
        "deleteWardrobeCopyAsync": var_deleteWardrobeCopyAsync,
        "getWardrobeCopyAsync": var_getWardrobeCopyAsync,
        "findWardrobeCopyListAsync": var_findWardrobeCopyListAsync,
        "findWardrobeRefListAsync": var_injection_AsyncFunctionType,
        "findWardrobeCopyRefListAsync": var_injection_AsyncFunctionType,
    };
    var_getCustomGroup.type = BasicType.Function;
    var_getCustomGroup.name = "getCustomGroup";
    var_getCustomGroup.varying = false;
    var_getCustomGroup.keepArgsHandle = true;
    var_getCustomGroup.args = [var_ElementId];
    var_getCustomGroup.return = var_CustomGroup;
    var_CustomGroup.type = BasicType.Object;
    var_CustomGroup.properties = {
        "elementId": var_ElementId,
        "subElements": var_ElementId_Array,
    };
    var_createCabinetAsync.type = BasicType.Function;
    var_createCabinetAsync.name = "createCabinetAsync";
    var_createCabinetAsync.varying = false;
    var_createCabinetAsync.keepArgsHandle = true;
    var_createCabinetAsync.args = [var_ICreateCustomModelSimpleOption];
    var_createCabinetAsync.return = var_ElementId_Promise;
    var_ICreateCustomModelSimpleOption.type = BasicType.Object;
    var_ICreateCustomModelSimpleOption.properties = {
        "productId": var_stringType,
    };
    var_ICustomModelSimpleChangeable.type = BasicType.Object;
    var_ICustomModelSimpleChangeable.properties = {
        "name": var_stringType,
        "position": var_Number3,
        "rotation": var_Number3,
        "size": var_Number3,
    };
    var_ElementId_Promise.type = BasicType.Object;
    var_ElementId_Promise.properties = {
        "then": var_ElementId_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ElementId_Promise_then.type = BasicType.Function;
    var_ElementId_Promise_then.name = "";
    var_ElementId_Promise_then.varying = false;
    var_ElementId_Promise_then.keepArgsHandle = true;
    var_ElementId_Promise_then.args = [var_ElementId_Promise_then_onresolve, var_Promise_then_onreject];
    var_ElementId_Promise_then.return = var_undefinedType;
    var_ElementId_Promise_then_onresolve.type = BasicType.Function;
    var_ElementId_Promise_then_onresolve.name = "";
    var_ElementId_Promise_then_onresolve.varying = false;
    var_ElementId_Promise_then_onresolve.keepArgsHandle = false;
    var_ElementId_Promise_then_onresolve.args = [var_ElementId];
    var_ElementId_Promise_then_onresolve.return = var_undefinedType;
    var_updateCabinetAsync.type = BasicType.Function;
    var_updateCabinetAsync.name = "updateCabinetAsync";
    var_updateCabinetAsync.varying = false;
    var_updateCabinetAsync.keepArgsHandle = true;
    var_updateCabinetAsync.args = [var_IUpdateCustomModelSimpleOption];
    var_updateCabinetAsync.return = var_ElementId_Promise;
    var_IUpdateCustomModelSimpleOption.type = BasicType.Object;
    var_IUpdateCustomModelSimpleOption.properties = {
        "elementId": var_ElementId_1,
    };
    var_deleteCabinetAsync.type = BasicType.Function;
    var_deleteCabinetAsync.name = "deleteCabinetAsync";
    var_deleteCabinetAsync.varying = false;
    var_deleteCabinetAsync.keepArgsHandle = true;
    var_deleteCabinetAsync.args = [var_ElementId];
    var_deleteCabinetAsync.return = var_booleanType_Promise;
    var_booleanType_Promise.type = BasicType.Object;
    var_booleanType_Promise.properties = {
        "then": var_booleanType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_booleanType_Promise_then.type = BasicType.Function;
    var_booleanType_Promise_then.name = "";
    var_booleanType_Promise_then.varying = false;
    var_booleanType_Promise_then.keepArgsHandle = true;
    var_booleanType_Promise_then.args = [var_booleanType_Promise_then_onresolve, var_Promise_then_onreject];
    var_booleanType_Promise_then.return = var_undefinedType;
    var_booleanType_Promise_then_onresolve.type = BasicType.Function;
    var_booleanType_Promise_then_onresolve.name = "";
    var_booleanType_Promise_then_onresolve.varying = false;
    var_booleanType_Promise_then_onresolve.keepArgsHandle = false;
    var_booleanType_Promise_then_onresolve.args = [var_booleanType];
    var_booleanType_Promise_then_onresolve.return = var_undefinedType;
    var_getCabinetAsync.type = BasicType.Function;
    var_getCabinetAsync.name = "getCabinetAsync";
    var_getCabinetAsync.varying = false;
    var_getCabinetAsync.keepArgsHandle = true;
    var_getCabinetAsync.args = [var_ElementId];
    var_getCabinetAsync.return = var_injection_UnknownType_Promise;
    var_injection_UnknownType_Promise.type = BasicType.Object;
    var_injection_UnknownType_Promise.properties = {
        "then": var_injection_UnknownType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_UnknownType_Promise_then.type = BasicType.Function;
    var_injection_UnknownType_Promise_then.name = "";
    var_injection_UnknownType_Promise_then.varying = false;
    var_injection_UnknownType_Promise_then.keepArgsHandle = true;
    var_injection_UnknownType_Promise_then.args = [var_injection_UnknownType_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_UnknownType_Promise_then.return = var_undefinedType;
    var_injection_UnknownType_Promise_then_onresolve.type = BasicType.Function;
    var_injection_UnknownType_Promise_then_onresolve.name = "";
    var_injection_UnknownType_Promise_then_onresolve.varying = false;
    var_injection_UnknownType_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_UnknownType_Promise_then_onresolve.args = [var_injection_UnknownType];
    var_injection_UnknownType_Promise_then_onresolve.return = var_undefinedType;
    var_findCabinetListAsync.type = BasicType.Function;
    var_findCabinetListAsync.name = "findCabinetListAsync";
    var_findCabinetListAsync.varying = false;
    var_findCabinetListAsync.keepArgsHandle = false;
    var_findCabinetListAsync.args = [];
    var_findCabinetListAsync.return = var_injection_UnknownType_Array_Promise;
    var_createCabinetCopyAsync.type = BasicType.Function;
    var_createCabinetCopyAsync.name = "createCabinetCopyAsync";
    var_createCabinetCopyAsync.varying = false;
    var_createCabinetCopyAsync.keepArgsHandle = true;
    var_createCabinetCopyAsync.args = [var_ICreateCustomModelSimpleOption];
    var_createCabinetCopyAsync.return = var_ElementId_Promise;
    var_updateCabinetCopyAsync.type = BasicType.Function;
    var_updateCabinetCopyAsync.name = "updateCabinetCopyAsync";
    var_updateCabinetCopyAsync.varying = false;
    var_updateCabinetCopyAsync.keepArgsHandle = true;
    var_updateCabinetCopyAsync.args = [var_IUpdateCustomModelSimpleOption];
    var_updateCabinetCopyAsync.return = var_ElementId_Promise;
    var_deleteCabinetCopyAsync.type = BasicType.Function;
    var_deleteCabinetCopyAsync.name = "deleteCabinetCopyAsync";
    var_deleteCabinetCopyAsync.varying = false;
    var_deleteCabinetCopyAsync.keepArgsHandle = true;
    var_deleteCabinetCopyAsync.args = [var_ElementId];
    var_deleteCabinetCopyAsync.return = var_booleanType_Promise;
    var_getCabinetCopyAsync.type = BasicType.Function;
    var_getCabinetCopyAsync.name = "getCabinetCopyAsync";
    var_getCabinetCopyAsync.varying = false;
    var_getCabinetCopyAsync.keepArgsHandle = true;
    var_getCabinetCopyAsync.args = [var_ElementId];
    var_getCabinetCopyAsync.return = var_injection_UnknownType_Promise;
    var_findCabinetCopyListAsync.type = BasicType.Function;
    var_findCabinetCopyListAsync.name = "findCabinetCopyListAsync";
    var_findCabinetCopyListAsync.varying = false;
    var_findCabinetCopyListAsync.keepArgsHandle = false;
    var_findCabinetCopyListAsync.args = [];
    var_findCabinetCopyListAsync.return = var_injection_UnknownType_Array_Promise;
    var_createWardrobeAsync.type = BasicType.Function;
    var_createWardrobeAsync.name = "createWardrobeAsync";
    var_createWardrobeAsync.varying = false;
    var_createWardrobeAsync.keepArgsHandle = true;
    var_createWardrobeAsync.args = [var_ICreateCustomModelSimpleOption];
    var_createWardrobeAsync.return = var_ElementId_Promise;
    var_updateWardrobeAsync.type = BasicType.Function;
    var_updateWardrobeAsync.name = "updateWardrobeAsync";
    var_updateWardrobeAsync.varying = false;
    var_updateWardrobeAsync.keepArgsHandle = true;
    var_updateWardrobeAsync.args = [var_IUpdateCustomModelSimpleOption];
    var_updateWardrobeAsync.return = var_ElementId_Promise;
    var_deleteWardrobeAsync.type = BasicType.Function;
    var_deleteWardrobeAsync.name = "deleteWardrobeAsync";
    var_deleteWardrobeAsync.varying = false;
    var_deleteWardrobeAsync.keepArgsHandle = true;
    var_deleteWardrobeAsync.args = [var_ElementId];
    var_deleteWardrobeAsync.return = var_booleanType_Promise;
    var_getWardrobeAsync.type = BasicType.Function;
    var_getWardrobeAsync.name = "getWardrobeAsync";
    var_getWardrobeAsync.varying = false;
    var_getWardrobeAsync.keepArgsHandle = true;
    var_getWardrobeAsync.args = [var_ElementId];
    var_getWardrobeAsync.return = var_injection_UnknownType_Promise;
    var_findWardrobeListAsync.type = BasicType.Function;
    var_findWardrobeListAsync.name = "findWardrobeListAsync";
    var_findWardrobeListAsync.varying = false;
    var_findWardrobeListAsync.keepArgsHandle = false;
    var_findWardrobeListAsync.args = [];
    var_findWardrobeListAsync.return = var_injection_UnknownType_Array_Promise;
    var_createWardrobeCopyAsync.type = BasicType.Function;
    var_createWardrobeCopyAsync.name = "createWardrobeCopyAsync";
    var_createWardrobeCopyAsync.varying = false;
    var_createWardrobeCopyAsync.keepArgsHandle = true;
    var_createWardrobeCopyAsync.args = [var_ICreateCustomModelSimpleOption];
    var_createWardrobeCopyAsync.return = var_ElementId_Promise;
    var_updateWardrobeCopyAsync.type = BasicType.Function;
    var_updateWardrobeCopyAsync.name = "updateWardrobeCopyAsync";
    var_updateWardrobeCopyAsync.varying = false;
    var_updateWardrobeCopyAsync.keepArgsHandle = true;
    var_updateWardrobeCopyAsync.args = [var_IUpdateCustomModelSimpleOption];
    var_updateWardrobeCopyAsync.return = var_ElementId_Promise;
    var_deleteWardrobeCopyAsync.type = BasicType.Function;
    var_deleteWardrobeCopyAsync.name = "deleteWardrobeCopyAsync";
    var_deleteWardrobeCopyAsync.varying = false;
    var_deleteWardrobeCopyAsync.keepArgsHandle = true;
    var_deleteWardrobeCopyAsync.args = [var_ElementId];
    var_deleteWardrobeCopyAsync.return = var_booleanType_Promise;
    var_getWardrobeCopyAsync.type = BasicType.Function;
    var_getWardrobeCopyAsync.name = "getWardrobeCopyAsync";
    var_getWardrobeCopyAsync.varying = false;
    var_getWardrobeCopyAsync.keepArgsHandle = true;
    var_getWardrobeCopyAsync.args = [var_ElementId];
    var_getWardrobeCopyAsync.return = var_injection_UnknownType_Promise;
    var_findWardrobeCopyListAsync.type = BasicType.Function;
    var_findWardrobeCopyListAsync.name = "findWardrobeCopyListAsync";
    var_findWardrobeCopyListAsync.varying = false;
    var_findWardrobeCopyListAsync.keepArgsHandle = false;
    var_findWardrobeCopyListAsync.args = [];
    var_findWardrobeCopyListAsync.return = var_injection_UnknownType_Array_Promise;
    var_Integration.type = BasicType.Object;
    var_Integration.properties = {
        "FOP": var_FOP,
        "Upload": var_Upload,
        "EngravingMachineCutting": var_EngravingMachineCutting,
        "Bom": var_Bom,
        "Report": var_Report,
    };
    var_FOP.type = BasicType.Object;
    var_FOP.properties = {
        "createOrderAsync": var_createOrderAsync,
        "getOrderAsync": var_getOrderAsync,
        "addAttachmentAsync": var_injection_AsyncFunctionType,
        "deleteAttachmentAsync": var_injection_AsyncFunctionType,
        "updateRemarkAsync": var_injection_AsyncFunctionType,
        "addRemarkAsync": var_injection_AsyncFunctionType,
        "deleteRemarkAsync": var_deleteRemarkAsync,
        "getDesignOrderListAsync": var_injection_AsyncFunctionType,
        "getModelAssociatedOrderAsync": var_getModelAssociatedOrderAsync,
        "executeOrderOperationAsync": var_executeOrderOperationAsync,
        "checkOperatePermissionAsync": var_checkOperatePermissionAsync,
        "createCustomerAsync": var_createCustomerAsync,
        "findCustomerListAsync": var_findCustomerListAsync,
        "getStoreAsync": var_getStoreAsync,
        "getOrderFieldsAsync": var_injection_AsyncFunctionType,
        "findOrdersAsync": var_injection_AsyncFunctionType,
        "getAttachmentsAsync": var_injection_AsyncFunctionType,
        "getRemarksAsync": var_injection_AsyncFunctionType,
        "createAfterSaleOrderFromDesignAsync": var_createAfterSaleOrderFromDesignAsync,
        "findAuditedModelAsync": var_findAuditedModelAsync,
        "getStoresAsync": var_getStoresAsync,
        "getStatesAsync": var_getStatesAsync,
        "getOperationsAsync": var_getOperationsAsync,
        "updateOrderModelsAsync": var_updateOrderModelsAsync,
        "findOrderModelsAsync": var_findOrderModelsAsync,
        "getReportFileAsync": var_getReportFileAsync,
        "getAllPlankDrawingAsync": var_getAllPlankDrawingAsync,
        "getPlankDrawingAsync": var_getPlankDrawingAsync,
        "getPlankDrawingsAsync": var_getPlankDrawingsAsync,
        "updateOrderAsync": var_updateOrderAsync,
        "updateCustomerAsync": var_updateCustomerAsync,
        "findAfterSaleOrderListAsync": var_findAfterSaleOrderListAsync,
        "createAfterSaleOrderFromBomAsync": var_createAfterSaleOrderFromBomAsync,
        "deleteOrderAsync": var_deleteOrderAsync,
        "CustomerSource": var_ECustomerSource,
        "CustomerQueryType": var_ECustomerQueryType,
        "OrderMode": var_OrderMode,
        "Config": var_Config,
        "InstallationSharing": var_InstallationSharing,
    };
    var_createOrderAsync.type = BasicType.Function;
    var_createOrderAsync.name = "createOrderAsync";
    var_createOrderAsync.varying = false;
    var_createOrderAsync.keepArgsHandle = true;
    var_createOrderAsync.args = [var_ICreateOrderOption];
    var_createOrderAsync.return = var_ICreateOrderResult_Promise;
    var_ICreateOrderOption.type = BasicType.Object;
    var_ICreateOrderOption.properties = {
        "orderName": var_stringType,
        "externalCode": var_stringType,
        "orderType": var_stringType_Array,
        "customerId": var_stringType,
        "customerName": var_stringType,
        "customerPhone": var_stringType,
        "customerAddr": var_stringType,
        "storeId": var_stringType,
        "storeName": var_stringType,
        "contactUser": var_stringType,
        "contactPhone": var_stringType,
        "shippingAddr": var_stringType,
        "remark": var_stringType,
        "modelIds": var_stringType_Array,
        "customFields": var_unknownType,
        "designAttachments": var_IDesignAttachments_Array,
        "emergencyLevel": var_numberType,
        "receiptType": var_numberType,
        "parentOrderId": var_stringType,
        "subModels": var_ISubModel_Array,
        "designRemark": var_stringType,
        "attachments": var_IDesignAttachments_Array,
        "repetitions": var_numberType,
    };
    var_IDesignAttachments_Array.type = BasicType.Array;
    var_IDesignAttachments_Array.value = var_IDesignAttachments;
    var_IDesignAttachments.type = BasicType.Object;
    var_IDesignAttachments.properties = {
        "uploadKey": var_stringType,
        "name": var_stringType,
        "url": var_stringType,
    };
    var_ISubModel_Array.type = BasicType.Array;
    var_ISubModel_Array.value = var_ISubModel;
    var_ISubModel.type = BasicType.Object;
    var_ISubModel.properties = {
        "modelId": var_stringType,
        "rootModelId": var_stringType,
        "combinationModelId": var_stringType,
    };
    var_ICreateOrderResult_Promise.type = BasicType.Object;
    var_ICreateOrderResult_Promise.properties = {
        "then": var_ICreateOrderResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ICreateOrderResult_Promise_then.type = BasicType.Function;
    var_ICreateOrderResult_Promise_then.name = "";
    var_ICreateOrderResult_Promise_then.varying = false;
    var_ICreateOrderResult_Promise_then.keepArgsHandle = true;
    var_ICreateOrderResult_Promise_then.args = [var_ICreateOrderResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_ICreateOrderResult_Promise_then.return = var_undefinedType;
    var_ICreateOrderResult_Promise_then_onresolve.type = BasicType.Function;
    var_ICreateOrderResult_Promise_then_onresolve.name = "";
    var_ICreateOrderResult_Promise_then_onresolve.varying = false;
    var_ICreateOrderResult_Promise_then_onresolve.keepArgsHandle = false;
    var_ICreateOrderResult_Promise_then_onresolve.args = [var_ICreateOrderResult];
    var_ICreateOrderResult_Promise_then_onresolve.return = var_undefinedType;
    var_ICreateOrderResult.type = BasicType.Object;
    var_ICreateOrderResult.properties = {
        "orderId": var_stringType,
        "orderNo": var_stringType,
    };
    var_getOrderAsync.type = BasicType.Function;
    var_getOrderAsync.name = "getOrderAsync";
    var_getOrderAsync.varying = false;
    var_getOrderAsync.keepArgsHandle = true;
    var_getOrderAsync.args = [var_stringType];
    var_getOrderAsync.return = var_IOrderData_Promise;
    var_IOrderData_Promise.type = BasicType.Object;
    var_IOrderData_Promise.properties = {
        "then": var_IOrderData_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IOrderData_Promise_then.type = BasicType.Function;
    var_IOrderData_Promise_then.name = "";
    var_IOrderData_Promise_then.varying = false;
    var_IOrderData_Promise_then.keepArgsHandle = true;
    var_IOrderData_Promise_then.args = [var_IOrderData_Promise_then_onresolve, var_Promise_then_onreject];
    var_IOrderData_Promise_then.return = var_undefinedType;
    var_IOrderData_Promise_then_onresolve.type = BasicType.Function;
    var_IOrderData_Promise_then_onresolve.name = "";
    var_IOrderData_Promise_then_onresolve.varying = false;
    var_IOrderData_Promise_then_onresolve.keepArgsHandle = false;
    var_IOrderData_Promise_then_onresolve.args = [var_IOrderData];
    var_IOrderData_Promise_then_onresolve.return = var_undefinedType;
    var_IOrderData.type = BasicType.Object;
    var_IOrderData.properties = {
        "externalCode": var_stringType,
        "orderType": var_stringType_Array,
        "processDirect": var_numberType,
        "placeTime": var_numberType,
        "customerId": var_stringType,
        "customerSource": var_numberType,
        "storeId": var_stringType,
        "storeName": var_stringType,
        "contactUser": var_stringType,
        "contactPhone": var_stringType,
        "shippingAddr": var_stringType,
        "remark": var_stringType,
        "orderRemarks": var_IOrderRemarks_Array,
        "attachments": var_IOrderAttachments_Array,
        "designId": var_stringType,
        "designName": var_stringType,
        "designerName": var_stringType,
        "designerId": var_stringType,
        "customFields": var_unknownType,
        "designRemark": var_stringType,
        "designRemarks": var_IOrderRemarks_Array,
        "designAttachments": var_IOrderAttachments_Array,
        "auditOrderNo": var_stringType,
        "auditorName": var_stringType,
        "auditRemarks": var_IAuditOrderRemarks_Array,
        "auditAttachments": var_IOrderAttachments_Array,
        "payAmt": var_stringType,
        "actualPayAmt": var_stringType,
        "payRecordCreator": var_stringType,
        "payCertificateSendBack": var_booleanType,
        "payCertificates": var_IOrderAttachment_Array,
        "events": var_IOrderEvent_Array,
        "emergencyLevel": var_numberType,
        "emergencyLevelName": var_stringType,
        "receiptType": var_numberType,
        "receiptTypeName": var_stringType,
        "parentOrderId": var_stringType,
        "parentOrderName": var_stringType,
        "creatorName": var_stringType,
        "actions": var_IOrderAction_Array,
        "deleted": var_booleanType,
        "processSetPlanCompleteTime": var_numberType,
        "repetitions": var_numberType,
        "levelId": var_stringType,
        "levelIndex": var_numberType,
    };
    var_IOrderRemarks_Array.type = BasicType.Array;
    var_IOrderRemarks_Array.value = var_IOrderRemarks;
    var_IOrderRemarks.type = BasicType.Object;
    var_IOrderRemarks.properties = {
        "friendlyTime": var_stringType,
    };
    var_IGetOrderRemarksResult.type = BasicType.Object;
    var_IGetOrderRemarksResult.properties = {
        "userName": var_stringType,
        "userAvatar": var_stringType,
        "orderState": var_numberType,
        "orderStateName": var_stringType,
        "remarkId": var_stringType,
        "content": var_stringType,
        "createTime": var_numberType,
        "type": var_numberType,
        "auths": var_stringType_Array,
        "images": var_IOrderAttachment_Array,
    };
    var_IOrderAttachment_Array.type = BasicType.Array;
    var_IOrderAttachment_Array.value = var_IOrderAttachment;
    var_IOrderAttachment.type = BasicType.Object;
    var_IOrderAttachment.properties = {
        "uploadKey": var_stringType,
        "name": var_stringType,
        "url": var_stringType,
    };
    var_IOrderAttachments_Array.type = BasicType.Array;
    var_IOrderAttachments_Array.value = var_IOrderAttachments;
    var_IOrderAttachments.type = BasicType.Object;
    var_IOrderAttachments.properties = {
        "attachmentType": var_numberType,
        "auths": var_stringType_Array,
    };
    var_IAuditOrderRemarks_Array.type = BasicType.Array;
    var_IAuditOrderRemarks_Array.value = var_IAuditOrderRemarks;
    var_IAuditOrderRemarks.type = BasicType.Object;
    var_IAuditOrderRemarks.properties = {
        "images": var_IOrderAttachment_Array,
    };
    var_IOrderEvent_Array.type = BasicType.Array;
    var_IOrderEvent_Array.value = var_IOrderEvent;
    var_IOrderEvent.type = BasicType.Object;
    var_IOrderEvent.properties = {
        "name": var_stringType,
        "key": var_stringType,
        "nextState": var_numberType,
        "nextStateName": var_stringType,
        "eventType": var_numberType,
    };
    var_IOrderAction_Array.type = BasicType.Array;
    var_IOrderAction_Array.value = var_IOrderAction;
    var_IOrderAction.type = BasicType.Object;
    var_IOrderAction.properties = {
        "name": var_stringType,
        "key": var_stringType,
        "extra": var_IOrderJumpActionExtra,
    };
    var_IOrderJumpActionExtra.type = BasicType.Object;
    var_IOrderJumpActionExtra.properties = {
        "targetMiniappId": var_stringType,
        "targetMiniappName": var_stringType,
        "info": var_IOrderJumpActionExtra_info_objectLiteral,
    };
    var_IOrderJumpActionExtra_info_objectLiteral.type = BasicType.Object;
    var_IOrderJumpActionExtra_info_objectLiteral.properties = {
        "needWriteOrderId": var_booleanType,
        "needWriteMergeId": var_booleanType,
        "customContent": var_stringType,
    };
    var_IOrderBaseInfo.type = BasicType.Object;
    var_IOrderBaseInfo.properties = {
        "orderId": var_stringType,
        "orderNo": var_stringType,
        "orderName": var_stringType,
        "orderState": var_numberType,
        "orderStateName": var_stringType,
    };
    var_ICustomerBaseInfo.type = BasicType.Object;
    var_ICustomerBaseInfo.properties = {
        "customerName": var_stringType,
        "customerPhone": var_stringType,
        "customerAddr": var_stringType,
    };
    var_deleteRemarkAsync.type = BasicType.Function;
    var_deleteRemarkAsync.name = "deleteRemarkAsync";
    var_deleteRemarkAsync.varying = false;
    var_deleteRemarkAsync.keepArgsHandle = true;
    var_deleteRemarkAsync.args = [var_IDeleteOrderRemarkOption];
    var_deleteRemarkAsync.return = var_undefinedType_Promise;
    var_IDeleteOrderRemarkOption.type = BasicType.Object;
    var_IDeleteOrderRemarkOption.properties = {
        "remarkId": var_stringType,
        "orderId": var_stringType,
    };
    var_getModelAssociatedOrderAsync.type = BasicType.Function;
    var_getModelAssociatedOrderAsync.name = "getModelAssociatedOrderAsync";
    var_getModelAssociatedOrderAsync.varying = false;
    var_getModelAssociatedOrderAsync.keepArgsHandle = true;
    var_getModelAssociatedOrderAsync.args = [var_IModelIsAssociatedOrderOption];
    var_getModelAssociatedOrderAsync.return = var_IModelIsAssociatedOrderResult_Array_Promise;
    var_IModelIsAssociatedOrderOption.type = BasicType.Object;
    var_IModelIsAssociatedOrderOption.properties = {
        "modelIds": var_unknownType,
        "excludeOrderId": var_stringType,
    };
    var_IModelIsAssociatedOrderResult_Array_Promise.type = BasicType.Object;
    var_IModelIsAssociatedOrderResult_Array_Promise.properties = {
        "then": var_IModelIsAssociatedOrderResult_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IModelIsAssociatedOrderResult_Array_Promise_then.type = BasicType.Function;
    var_IModelIsAssociatedOrderResult_Array_Promise_then.name = "";
    var_IModelIsAssociatedOrderResult_Array_Promise_then.varying = false;
    var_IModelIsAssociatedOrderResult_Array_Promise_then.keepArgsHandle = true;
    var_IModelIsAssociatedOrderResult_Array_Promise_then.args = [var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IModelIsAssociatedOrderResult_Array_Promise_then.return = var_undefinedType;
    var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve.name = "";
    var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve.varying = false;
    var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve.args = [var_IModelIsAssociatedOrderResult_Array];
    var_IModelIsAssociatedOrderResult_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IModelIsAssociatedOrderResult_Array.type = BasicType.Array;
    var_IModelIsAssociatedOrderResult_Array.value = var_IModelIsAssociatedOrderResult;
    var_IModelIsAssociatedOrderResult.type = BasicType.Object;
    var_IModelIsAssociatedOrderResult.properties = {
        "modelId": var_stringType,
        "orderId": var_stringType,
        "orderNo": var_stringType,
    };
    var_executeOrderOperationAsync.type = BasicType.Function;
    var_executeOrderOperationAsync.name = "executeOrderOperationAsync";
    var_executeOrderOperationAsync.varying = false;
    var_executeOrderOperationAsync.keepArgsHandle = true;
    var_executeOrderOperationAsync.args = [var_IExecuteOrderOperationOption];
    var_executeOrderOperationAsync.return = var_booleanType_Promise;
    var_IExecuteOrderOperationOption.type = BasicType.Object;
    var_IExecuteOrderOperationOption.properties = {
        "key": var_stringType,
        "orderId": var_stringType,
        "extraParam": var_unknownType,
    };
    var_checkOperatePermissionAsync.type = BasicType.Function;
    var_checkOperatePermissionAsync.name = "checkOperatePermissionAsync";
    var_checkOperatePermissionAsync.varying = false;
    var_checkOperatePermissionAsync.keepArgsHandle = true;
    var_checkOperatePermissionAsync.args = [var_ICheckOperatePermissionOption];
    var_checkOperatePermissionAsync.return = var_booleanType_Promise;
    var_ICheckOperatePermissionOption.type = BasicType.Object;
    var_ICheckOperatePermissionOption.properties = {
        "eventKey": var_stringType,
        "operationKey": var_stringType,
        "orderId": var_stringType,
    };
    var_createCustomerAsync.type = BasicType.Function;
    var_createCustomerAsync.name = "createCustomerAsync";
    var_createCustomerAsync.varying = false;
    var_createCustomerAsync.keepArgsHandle = true;
    var_createCustomerAsync.args = [var_ICreateCustomerOption];
    var_createCustomerAsync.return = var_ICreateCustomerResult_Promise;
    var_ICreateCustomerOption.type = BasicType.Object;
    var_ICreateCustomerOption.properties = {
        "customerName": var_stringType,
        "customerPhone": var_stringType,
        "customerAddr": var_stringType,
        "storeId": var_stringType,
    };
    var_ICreateCustomerResult_Promise.type = BasicType.Object;
    var_ICreateCustomerResult_Promise.properties = {
        "then": var_ICreateCustomerResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ICreateCustomerResult_Promise_then.type = BasicType.Function;
    var_ICreateCustomerResult_Promise_then.name = "";
    var_ICreateCustomerResult_Promise_then.varying = false;
    var_ICreateCustomerResult_Promise_then.keepArgsHandle = true;
    var_ICreateCustomerResult_Promise_then.args = [var_ICreateCustomerResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_ICreateCustomerResult_Promise_then.return = var_undefinedType;
    var_ICreateCustomerResult_Promise_then_onresolve.type = BasicType.Function;
    var_ICreateCustomerResult_Promise_then_onresolve.name = "";
    var_ICreateCustomerResult_Promise_then_onresolve.varying = false;
    var_ICreateCustomerResult_Promise_then_onresolve.keepArgsHandle = false;
    var_ICreateCustomerResult_Promise_then_onresolve.args = [var_ICreateCustomerResult];
    var_ICreateCustomerResult_Promise_then_onresolve.return = var_undefinedType;
    var_ICreateCustomerResult.type = BasicType.Object;
    var_ICreateCustomerResult.properties = {
        "customerId": var_stringType,
    };
    var_findCustomerListAsync.type = BasicType.Function;
    var_findCustomerListAsync.name = "findCustomerListAsync";
    var_findCustomerListAsync.varying = false;
    var_findCustomerListAsync.keepArgsHandle = true;
    var_findCustomerListAsync.args = [var_IGetCustomerListOption];
    var_findCustomerListAsync.return = var_unknownType_Promise;
    var_IGetCustomerListOption.type = BasicType.Object;
    var_IGetCustomerListOption.properties = {
        "keyWord": var_stringType,
        "queryType": var_numberType,
        "queryRange": var_numberType,
    };
    var_IPaginationQuery.type = BasicType.Object;
    var_IPaginationQuery.properties = {
        "start": var_numberType,
        "pageSize": var_numberType,
    };
    var_getStoreAsync.type = BasicType.Function;
    var_getStoreAsync.name = "getStoreAsync";
    var_getStoreAsync.varying = false;
    var_getStoreAsync.keepArgsHandle = false;
    var_getStoreAsync.args = [];
    var_getStoreAsync.return = var_IGetStoreInfoResult_Promise;
    var_IGetStoreInfoResult_Promise.type = BasicType.Object;
    var_IGetStoreInfoResult_Promise.properties = {
        "then": var_IGetStoreInfoResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IGetStoreInfoResult_Promise_then.type = BasicType.Function;
    var_IGetStoreInfoResult_Promise_then.name = "";
    var_IGetStoreInfoResult_Promise_then.varying = false;
    var_IGetStoreInfoResult_Promise_then.keepArgsHandle = true;
    var_IGetStoreInfoResult_Promise_then.args = [var_IGetStoreInfoResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IGetStoreInfoResult_Promise_then.return = var_undefinedType;
    var_IGetStoreInfoResult_Promise_then_onresolve.type = BasicType.Function;
    var_IGetStoreInfoResult_Promise_then_onresolve.name = "";
    var_IGetStoreInfoResult_Promise_then_onresolve.varying = false;
    var_IGetStoreInfoResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IGetStoreInfoResult_Promise_then_onresolve.args = [var_IGetStoreInfoResult];
    var_IGetStoreInfoResult_Promise_then_onresolve.return = var_undefinedType;
    var_IGetStoreInfoResult.type = BasicType.Object;
    var_IGetStoreInfoResult.properties = {
        "storeId": var_stringType,
        "storeName": var_stringType,
        "contactUser": var_stringType,
        "contactPhone": var_stringType,
        "shippingAddr": var_stringType,
    };
    var_createAfterSaleOrderFromDesignAsync.type = BasicType.Function;
    var_createAfterSaleOrderFromDesignAsync.name = "createAfterSaleOrderFromDesignAsync";
    var_createAfterSaleOrderFromDesignAsync.varying = false;
    var_createAfterSaleOrderFromDesignAsync.keepArgsHandle = true;
    var_createAfterSaleOrderFromDesignAsync.args = [var_ICreateOrderOption];
    var_createAfterSaleOrderFromDesignAsync.return = var_ICreateOrderResult_Promise;
    var_findAuditedModelAsync.type = BasicType.Function;
    var_findAuditedModelAsync.name = "findAuditedModelAsync";
    var_findAuditedModelAsync.varying = false;
    var_findAuditedModelAsync.keepArgsHandle = true;
    var_findAuditedModelAsync.args = [var_findAuditedModelAsync_option_objectLiteral];
    var_findAuditedModelAsync.return = var_IFindAuditedModelResult_Promise;
    var_findAuditedModelAsync_option_objectLiteral.type = BasicType.Object;
    var_findAuditedModelAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
    };
    var_IFindAuditedModelResult_Promise.type = BasicType.Object;
    var_IFindAuditedModelResult_Promise.properties = {
        "then": var_IFindAuditedModelResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IFindAuditedModelResult_Promise_then.type = BasicType.Function;
    var_IFindAuditedModelResult_Promise_then.name = "";
    var_IFindAuditedModelResult_Promise_then.varying = false;
    var_IFindAuditedModelResult_Promise_then.keepArgsHandle = true;
    var_IFindAuditedModelResult_Promise_then.args = [var_IFindAuditedModelResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IFindAuditedModelResult_Promise_then.return = var_undefinedType;
    var_IFindAuditedModelResult_Promise_then_onresolve.type = BasicType.Function;
    var_IFindAuditedModelResult_Promise_then_onresolve.name = "";
    var_IFindAuditedModelResult_Promise_then_onresolve.varying = false;
    var_IFindAuditedModelResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IFindAuditedModelResult_Promise_then_onresolve.args = [var_IFindAuditedModelResult];
    var_IFindAuditedModelResult_Promise_then_onresolve.return = var_undefinedType;
    var_IFindAuditedModelResult.type = BasicType.Object;
    var_IFindAuditedModelResult.properties = {
        "modelIds": var_stringType_Array,
        "subModels": var_ISubModel_Array,
    };
    var_getStoresAsync.type = BasicType.Function;
    var_getStoresAsync.name = "getStoresAsync";
    var_getStoresAsync.varying = false;
    var_getStoresAsync.keepArgsHandle = false;
    var_getStoresAsync.args = [];
    var_getStoresAsync.return = var_IStoreInfo_Array_Promise;
    var_IStoreInfo_Array_Promise.type = BasicType.Object;
    var_IStoreInfo_Array_Promise.properties = {
        "then": var_IStoreInfo_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IStoreInfo_Array_Promise_then.type = BasicType.Function;
    var_IStoreInfo_Array_Promise_then.name = "";
    var_IStoreInfo_Array_Promise_then.varying = false;
    var_IStoreInfo_Array_Promise_then.keepArgsHandle = true;
    var_IStoreInfo_Array_Promise_then.args = [var_IStoreInfo_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IStoreInfo_Array_Promise_then.return = var_undefinedType;
    var_IStoreInfo_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IStoreInfo_Array_Promise_then_onresolve.name = "";
    var_IStoreInfo_Array_Promise_then_onresolve.varying = false;
    var_IStoreInfo_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IStoreInfo_Array_Promise_then_onresolve.args = [var_IStoreInfo_Array];
    var_IStoreInfo_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IStoreInfo_Array.type = BasicType.Array;
    var_IStoreInfo_Array.value = var_IStoreInfo;
    var_IStoreInfo.type = BasicType.Object;
    var_IStoreInfo.properties = {
        "storeId": var_stringType,
        "storeName": var_stringType,
        "contactUser": var_stringType,
        "contactPhone": var_stringType,
        "shippingAddr": var_stringType,
    };
    var_getStatesAsync.type = BasicType.Function;
    var_getStatesAsync.name = "getStatesAsync";
    var_getStatesAsync.varying = false;
    var_getStatesAsync.keepArgsHandle = false;
    var_getStatesAsync.args = [];
    var_getStatesAsync.return = var_getStatesAsync_objectLiteral_Promise;
    var_getStatesAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getStatesAsync_objectLiteral_Promise.properties = {
        "then": var_getStatesAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getStatesAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getStatesAsync_objectLiteral_Promise_then.name = "";
    var_getStatesAsync_objectLiteral_Promise_then.varying = false;
    var_getStatesAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getStatesAsync_objectLiteral_Promise_then.args = [var_getStatesAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getStatesAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getStatesAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getStatesAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getStatesAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getStatesAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getStatesAsync_objectLiteral_Promise_then_onresolve.args = [var_getStatesAsync_objectLiteral];
    var_getStatesAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getStatesAsync_objectLiteral.type = BasicType.Object;
    var_getStatesAsync_objectLiteral.properties = {
        "sysStates": var_IOrderStateData_Array,
        "customStates": var_IOrderStateData_Array,
    };
    var_IOrderStateData_Array.type = BasicType.Array;
    var_IOrderStateData_Array.value = var_IOrderStateData;
    var_IOrderStateData.type = BasicType.Object;
    var_IOrderStateData.properties = {
        "name": var_stringType,
        "key": var_numberType,
    };
    var_getOperationsAsync.type = BasicType.Function;
    var_getOperationsAsync.name = "getOperationsAsync";
    var_getOperationsAsync.varying = false;
    var_getOperationsAsync.keepArgsHandle = false;
    var_getOperationsAsync.args = [];
    var_getOperationsAsync.return = var_getOperationsAsync_objectLiteral_Promise;
    var_getOperationsAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getOperationsAsync_objectLiteral_Promise.properties = {
        "then": var_getOperationsAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getOperationsAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getOperationsAsync_objectLiteral_Promise_then.name = "";
    var_getOperationsAsync_objectLiteral_Promise_then.varying = false;
    var_getOperationsAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getOperationsAsync_objectLiteral_Promise_then.args = [var_getOperationsAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getOperationsAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getOperationsAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getOperationsAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getOperationsAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getOperationsAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getOperationsAsync_objectLiteral_Promise_then_onresolve.args = [var_getOperationsAsync_objectLiteral];
    var_getOperationsAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getOperationsAsync_objectLiteral.type = BasicType.Object;
    var_getOperationsAsync_objectLiteral.properties = {
        "sysOperations": var_IOrderOperationData_Array,
        "customOperations": var_IOrderOperationData_Array,
    };
    var_IOrderOperationData_Array.type = BasicType.Array;
    var_IOrderOperationData_Array.value = var_IOrderOperationData;
    var_IOrderOperationData.type = BasicType.Object;
    var_IOrderOperationData.properties = {
        "key": var_stringType,
        "name": var_stringType,
        "type": var_numberType,
    };
    var_updateOrderModelsAsync.type = BasicType.Function;
    var_updateOrderModelsAsync.name = "updateOrderModelsAsync";
    var_updateOrderModelsAsync.varying = false;
    var_updateOrderModelsAsync.keepArgsHandle = true;
    var_updateOrderModelsAsync.args = [var_IUpdateOrderModel];
    var_updateOrderModelsAsync.return = var_undefinedType_Promise;
    var_IUpdateOrderModel.type = BasicType.Object;
    var_IUpdateOrderModel.properties = {
        "orderId": var_stringType,
    };
    var_IOrderModel.type = BasicType.Object;
    var_IOrderModel.properties = {
        "modelIds": var_stringType_Array,
        "subModels": var_ISubModel_Array,
    };
    var_findOrderModelsAsync.type = BasicType.Function;
    var_findOrderModelsAsync.name = "findOrderModelsAsync";
    var_findOrderModelsAsync.varying = false;
    var_findOrderModelsAsync.keepArgsHandle = true;
    var_findOrderModelsAsync.args = [var_findOrderModelsAsync_option_objectLiteral];
    var_findOrderModelsAsync.return = var_IOrderModel_Promise;
    var_findOrderModelsAsync_option_objectLiteral.type = BasicType.Object;
    var_findOrderModelsAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
    };
    var_IOrderModel_Promise.type = BasicType.Object;
    var_IOrderModel_Promise.properties = {
        "then": var_IOrderModel_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IOrderModel_Promise_then.type = BasicType.Function;
    var_IOrderModel_Promise_then.name = "";
    var_IOrderModel_Promise_then.varying = false;
    var_IOrderModel_Promise_then.keepArgsHandle = true;
    var_IOrderModel_Promise_then.args = [var_IOrderModel_Promise_then_onresolve, var_Promise_then_onreject];
    var_IOrderModel_Promise_then.return = var_undefinedType;
    var_IOrderModel_Promise_then_onresolve.type = BasicType.Function;
    var_IOrderModel_Promise_then_onresolve.name = "";
    var_IOrderModel_Promise_then_onresolve.varying = false;
    var_IOrderModel_Promise_then_onresolve.keepArgsHandle = false;
    var_IOrderModel_Promise_then_onresolve.args = [var_IOrderModel];
    var_IOrderModel_Promise_then_onresolve.return = var_undefinedType;
    var_getReportFileAsync.type = BasicType.Function;
    var_getReportFileAsync.name = "getReportFileAsync";
    var_getReportFileAsync.varying = false;
    var_getReportFileAsync.keepArgsHandle = true;
    var_getReportFileAsync.args = [var_unknownType];
    var_getReportFileAsync.return = var_getReportFileAsync_objectLiteral_Promise;
    var_getReportFileAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getReportFileAsync_objectLiteral_Promise.properties = {
        "then": var_getReportFileAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getReportFileAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getReportFileAsync_objectLiteral_Promise_then.name = "";
    var_getReportFileAsync_objectLiteral_Promise_then.varying = false;
    var_getReportFileAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getReportFileAsync_objectLiteral_Promise_then.args = [var_getReportFileAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getReportFileAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getReportFileAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getReportFileAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getReportFileAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getReportFileAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getReportFileAsync_objectLiteral_Promise_then_onresolve.args = [var_getReportFileAsync_objectLiteral];
    var_getReportFileAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getReportFileAsync_objectLiteral.type = BasicType.Object;
    var_getReportFileAsync_objectLiteral.properties = {
        "url": var_stringType,
    };
    var_getAllPlankDrawingAsync.type = BasicType.Function;
    var_getAllPlankDrawingAsync.name = "getAllPlankDrawingAsync";
    var_getAllPlankDrawingAsync.varying = false;
    var_getAllPlankDrawingAsync.keepArgsHandle = true;
    var_getAllPlankDrawingAsync.args = [var_stringType];
    var_getAllPlankDrawingAsync.return = var_getAllPlankDrawingAsync_objectLiteral_Promise;
    var_getAllPlankDrawingAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getAllPlankDrawingAsync_objectLiteral_Promise.properties = {
        "then": var_getAllPlankDrawingAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then.name = "";
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then.varying = false;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then.args = [var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve.args = [var_getAllPlankDrawingAsync_objectLiteral];
    var_getAllPlankDrawingAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getAllPlankDrawingAsync_objectLiteral.type = BasicType.Object;
    var_getAllPlankDrawingAsync_objectLiteral.properties = {
        "url": var_stringType,
    };
    var_getPlankDrawingAsync.type = BasicType.Function;
    var_getPlankDrawingAsync.name = "getPlankDrawingAsync";
    var_getPlankDrawingAsync.varying = false;
    var_getPlankDrawingAsync.keepArgsHandle = true;
    var_getPlankDrawingAsync.args = [var_getPlankDrawingAsync_option_objectLiteral];
    var_getPlankDrawingAsync.return = var_getPlankDrawingAsync_objectLiteral_Promise;
    var_getPlankDrawingAsync_option_objectLiteral.type = BasicType.Object;
    var_getPlankDrawingAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
        "code": var_stringType,
    };
    var_getPlankDrawingAsync_objectLiteral_Promise.type = BasicType.Object;
    var_getPlankDrawingAsync_objectLiteral_Promise.properties = {
        "then": var_getPlankDrawingAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getPlankDrawingAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_getPlankDrawingAsync_objectLiteral_Promise_then.name = "";
    var_getPlankDrawingAsync_objectLiteral_Promise_then.varying = false;
    var_getPlankDrawingAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_getPlankDrawingAsync_objectLiteral_Promise_then.args = [var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_getPlankDrawingAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve.args = [var_getPlankDrawingAsync_objectLiteral];
    var_getPlankDrawingAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_getPlankDrawingAsync_objectLiteral.type = BasicType.Object;
    var_getPlankDrawingAsync_objectLiteral.properties = {
        "url": var_stringType,
    };
    var_getPlankDrawingsAsync.type = BasicType.Function;
    var_getPlankDrawingsAsync.name = "getPlankDrawingsAsync";
    var_getPlankDrawingsAsync.varying = false;
    var_getPlankDrawingsAsync.keepArgsHandle = true;
    var_getPlankDrawingsAsync.args = [var_getPlankDrawingsAsync_option_objectLiteral];
    var_getPlankDrawingsAsync.return = var_getPlankDrawingsAsync_objectLiteral_Array_Promise;
    var_getPlankDrawingsAsync_option_objectLiteral.type = BasicType.Object;
    var_getPlankDrawingsAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
        "codes": var_stringType_Array,
    };
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise.type = BasicType.Object;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise.properties = {
        "then": var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then.type = BasicType.Function;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then.name = "";
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then.varying = false;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then.keepArgsHandle = true;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then.args = [var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then.return = var_undefinedType;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve.type = BasicType.Function;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve.name = "";
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve.varying = false;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve.args = [var_getPlankDrawingsAsync_objectLiteral_Array];
    var_getPlankDrawingsAsync_objectLiteral_Array_Promise_then_onresolve.return = var_undefinedType;
    var_getPlankDrawingsAsync_objectLiteral_Array.type = BasicType.Array;
    var_getPlankDrawingsAsync_objectLiteral_Array.value = var_getPlankDrawingsAsync_objectLiteral;
    var_getPlankDrawingsAsync_objectLiteral.type = BasicType.Object;
    var_getPlankDrawingsAsync_objectLiteral.properties = {
        "code": var_stringType,
        "url": var_stringType,
    };
    var_updateOrderAsync.type = BasicType.Function;
    var_updateOrderAsync.name = "updateOrderAsync";
    var_updateOrderAsync.varying = false;
    var_updateOrderAsync.keepArgsHandle = true;
    var_updateOrderAsync.args = [var_IUpdateOrderOption];
    var_updateOrderAsync.return = var_undefinedType_Promise;
    var_IUpdateOrderOption.type = BasicType.Object;
    var_IUpdateOrderOption.properties = {
        "orderId": var_stringType,
        "fields": var_unknownType,
    };
    var_updateCustomerAsync.type = BasicType.Function;
    var_updateCustomerAsync.name = "updateCustomerAsync";
    var_updateCustomerAsync.varying = false;
    var_updateCustomerAsync.keepArgsHandle = true;
    var_updateCustomerAsync.args = [var_IUpdateCustomerOption];
    var_updateCustomerAsync.return = var_undefinedType_Promise;
    var_IUpdateCustomerOption.type = BasicType.Object;
    var_IUpdateCustomerOption.properties = {
        "customerId": var_stringType,
        "customerName": var_stringType,
        "storeId": var_stringType,
    };
    var_findAfterSaleOrderListAsync.type = BasicType.Function;
    var_findAfterSaleOrderListAsync.name = "findAfterSaleOrderListAsync";
    var_findAfterSaleOrderListAsync.varying = false;
    var_findAfterSaleOrderListAsync.keepArgsHandle = true;
    var_findAfterSaleOrderListAsync.args = [var_findAfterSaleOrderListAsync_option_objectLiteral];
    var_findAfterSaleOrderListAsync.return = var_injection_UnknownType_Array_Promise;
    var_findAfterSaleOrderListAsync_option_objectLiteral.type = BasicType.Object;
    var_findAfterSaleOrderListAsync_option_objectLiteral.properties = {
        "parentOrderId": var_stringType,
    };
    var_createAfterSaleOrderFromBomAsync.type = BasicType.Function;
    var_createAfterSaleOrderFromBomAsync.name = "createAfterSaleOrderFromBomAsync";
    var_createAfterSaleOrderFromBomAsync.varying = false;
    var_createAfterSaleOrderFromBomAsync.keepArgsHandle = true;
    var_createAfterSaleOrderFromBomAsync.args = [var_ICreateAfterSaleOrderFromBomOption];
    var_createAfterSaleOrderFromBomAsync.return = var_ICreateOrderResult_Promise;
    var_ICreateAfterSaleOrderFromBomOption.type = BasicType.Object;
    var_ICreateAfterSaleOrderFromBomOption.properties = {
        "orderName": var_stringType,
        "receiptType": var_numberType,
        "parentOrderId": var_stringType,
        "bomMaterialIds": var_stringType_Array,
        "bomGroupRootIds": var_stringType_Array,
    };
    var_deleteOrderAsync.type = BasicType.Function;
    var_deleteOrderAsync.name = "deleteOrderAsync";
    var_deleteOrderAsync.varying = false;
    var_deleteOrderAsync.keepArgsHandle = true;
    var_deleteOrderAsync.args = [var_deleteOrderAsync_option_objectLiteral];
    var_deleteOrderAsync.return = var_undefinedType_Promise;
    var_deleteOrderAsync_option_objectLiteral.type = BasicType.Object;
    var_deleteOrderAsync_option_objectLiteral.properties = {
        "orderId": var_unknownType,
    };
    var_ECustomerSource.type = BasicType.Object;
    var_ECustomerSource.properties = {
        "OTHER": var_numberType,
        "NATURAL": var_numberType,
        "MODEL_ROOM": var_numberType,
        "NEW_MEDIA": var_numberType,
        "LIVE": var_numberType,
        "CROSS_COOP": var_numberType,
        "TELEMARKETING": var_numberType,
        "OLD_INTRODUCE": var_numberType,
        "REPURCHASE": var_numberType,
    };
    var_ECustomerQueryType.type = BasicType.Object;
    var_ECustomerQueryType.properties = {
        "CUSTOMER_NAME": var_numberType,
        "CUSTOMER_PHONE": var_numberType,
        "CUSTOMER_ADDR": var_numberType,
    };
    var_OrderMode.type = BasicType.Object;
    var_OrderMode.properties = {
        "getEditable": var_getEditable,
        "setEditable": var_setEditable,
        "getOrderId": var_getOrderId,
        "pauseAutoUpdateOrderModel": var_pauseAutoUpdateOrderModel,
        "resumeAutoUpdateOrderModel": var_resumeAutoUpdateOrderModel,
    };
    var_getEditable.type = BasicType.Function;
    var_getEditable.name = "getEditable";
    var_getEditable.varying = false;
    var_getEditable.keepArgsHandle = false;
    var_getEditable.args = [];
    var_getEditable.return = var_booleanType;
    var_setEditable.type = BasicType.Function;
    var_setEditable.name = "setEditable";
    var_setEditable.varying = false;
    var_setEditable.keepArgsHandle = true;
    var_setEditable.args = [var_booleanType];
    var_setEditable.return = var_undefinedType;
    var_getOrderId.type = BasicType.Function;
    var_getOrderId.name = "getOrderId";
    var_getOrderId.varying = false;
    var_getOrderId.keepArgsHandle = false;
    var_getOrderId.args = [];
    var_getOrderId.return = var_stringType;
    var_pauseAutoUpdateOrderModel.type = BasicType.Function;
    var_pauseAutoUpdateOrderModel.name = "pauseAutoUpdateOrderModel";
    var_pauseAutoUpdateOrderModel.varying = false;
    var_pauseAutoUpdateOrderModel.keepArgsHandle = false;
    var_pauseAutoUpdateOrderModel.args = [];
    var_pauseAutoUpdateOrderModel.return = var_undefinedType;
    var_resumeAutoUpdateOrderModel.type = BasicType.Function;
    var_resumeAutoUpdateOrderModel.name = "resumeAutoUpdateOrderModel";
    var_resumeAutoUpdateOrderModel.varying = false;
    var_resumeAutoUpdateOrderModel.keepArgsHandle = false;
    var_resumeAutoUpdateOrderModel.args = [];
    var_resumeAutoUpdateOrderModel.return = var_undefinedType;
    var_Config.type = BasicType.Object;
    var_Config.properties = {
        "getEnumAsync": var_getEnumAsync,
        "FieldEnum": var_EFieldEnum,
    };
    var_getEnumAsync.type = BasicType.Function;
    var_getEnumAsync.name = "getEnumAsync";
    var_getEnumAsync.varying = false;
    var_getEnumAsync.keepArgsHandle = true;
    var_getEnumAsync.args = [var_getEnumAsync_option_objectLiteral];
    var_getEnumAsync.return = var_IFieldEnumResult_Array_Promise;
    var_getEnumAsync_option_objectLiteral.type = BasicType.Object;
    var_getEnumAsync_option_objectLiteral.properties = {
        "field": var_numberType,
    };
    var_IFieldEnumResult_Array_Promise.type = BasicType.Object;
    var_IFieldEnumResult_Array_Promise.properties = {
        "then": var_IFieldEnumResult_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IFieldEnumResult_Array_Promise_then.type = BasicType.Function;
    var_IFieldEnumResult_Array_Promise_then.name = "";
    var_IFieldEnumResult_Array_Promise_then.varying = false;
    var_IFieldEnumResult_Array_Promise_then.keepArgsHandle = true;
    var_IFieldEnumResult_Array_Promise_then.args = [var_IFieldEnumResult_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IFieldEnumResult_Array_Promise_then.return = var_undefinedType;
    var_IFieldEnumResult_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IFieldEnumResult_Array_Promise_then_onresolve.name = "";
    var_IFieldEnumResult_Array_Promise_then_onresolve.varying = false;
    var_IFieldEnumResult_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IFieldEnumResult_Array_Promise_then_onresolve.args = [var_IFieldEnumResult_Array];
    var_IFieldEnumResult_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IFieldEnumResult_Array.type = BasicType.Array;
    var_IFieldEnumResult_Array.value = var_IFieldEnumResult;
    var_IFieldEnumResult.type = BasicType.Object;
    var_IFieldEnumResult.properties = {
        "name": var_stringType,
        "value": var_unknownType,
    };
    var_EFieldEnum.type = BasicType.Object;
    var_EFieldEnum.properties = {
        "emergencyLevel": var_numberType,
        "receiptType": var_numberType,
    };
    var_InstallationSharing.type = BasicType.Object;
    var_InstallationSharing.properties = {
        "createShareDataAsync": var_createShareDataAsync,
    };
    var_createShareDataAsync.type = BasicType.Function;
    var_createShareDataAsync.name = "createShareDataAsync";
    var_createShareDataAsync.varying = false;
    var_createShareDataAsync.keepArgsHandle = true;
    var_createShareDataAsync.args = [var_createShareDataAsync_option_objectLiteral];
    var_createShareDataAsync.return = var_createShareDataAsync_objectLiteral_Promise;
    var_createShareDataAsync_option_objectLiteral.type = BasicType.Object;
    var_createShareDataAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
    };
    var_createShareDataAsync_objectLiteral_Promise.type = BasicType.Object;
    var_createShareDataAsync_objectLiteral_Promise.properties = {
        "then": var_createShareDataAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_createShareDataAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_createShareDataAsync_objectLiteral_Promise_then.name = "";
    var_createShareDataAsync_objectLiteral_Promise_then.varying = false;
    var_createShareDataAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_createShareDataAsync_objectLiteral_Promise_then.args = [var_createShareDataAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_createShareDataAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_createShareDataAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_createShareDataAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_createShareDataAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_createShareDataAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_createShareDataAsync_objectLiteral_Promise_then_onresolve.args = [var_createShareDataAsync_objectLiteral];
    var_createShareDataAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_createShareDataAsync_objectLiteral.type = BasicType.Object;
    var_createShareDataAsync_objectLiteral.properties = {
        "expirationTime": var_numberType,
    };
    var_Upload.type = BasicType.Object;
    var_Upload.properties = {
    };
    var_EngravingMachineCutting.type = BasicType.Object;
    var_EngravingMachineCutting.properties = {
        "createTaskAsync": var_createTaskAsync,
        "findTaskAsync": var_findTaskAsync,
        "deleteTaskAsync": var_deleteTaskAsync,
    };
    var_createTaskAsync.type = BasicType.Function;
    var_createTaskAsync.name = "createTaskAsync";
    var_createTaskAsync.varying = false;
    var_createTaskAsync.keepArgsHandle = true;
    var_createTaskAsync.args = [var_IEngravingMachineCuttingTaskCreate];
    var_createTaskAsync.return = var_unknownType_Promise;
    var_IEngravingMachineCuttingTaskCreate.type = BasicType.Object;
    var_IEngravingMachineCuttingTaskCreate.properties = {
        "item_spacing": var_numberType,
        "items": var_IEngravingMachineCuttingTaskItem_Array,
        "origin": var_numberType,
        "plate_spacing": var_numberType,
        "plates": var_IEngravingMachineCuttingPlate_Array,
        "project_id": var_stringType,
        "task_id": var_stringType,
        "time": var_numberType,
    };
    var_IEngravingMachineCuttingTaskItem_Array.type = BasicType.Array;
    var_IEngravingMachineCuttingTaskItem_Array.value = var_IEngravingMachineCuttingTaskItem;
    var_IEngravingMachineCuttingTaskItem.type = BasicType.Object;
    var_IEngravingMachineCuttingTaskItem.properties = {
        "item_type": var_stringType,
        "objects": var_IEngravingMachineCuttingTaskItemObject_Array,
    };
    var_IEngravingMachineCuttingTaskItemObject_Array.type = BasicType.Array;
    var_IEngravingMachineCuttingTaskItemObject_Array.value = var_IEngravingMachineCuttingTaskItemObject;
    var_IEngravingMachineCuttingTaskItemObject.type = BasicType.Object;
    var_IEngravingMachineCuttingTaskItemObject.properties = {
        "count": var_numberType,
        "flip": var_numberType,
        "is_irshape": var_booleanType,
        "item_id": var_stringType,
        "item_name": var_stringType,
        "orientations": var_numberType_Array,
        "vertices": var_unknownType_Array_Array,
    };
    var_unknownType_Array_Array.type = BasicType.Array;
    var_unknownType_Array_Array.value = var_unknownType_Array;
    var_IEngravingMachineCuttingPlate_Array.type = BasicType.Array;
    var_IEngravingMachineCuttingPlate_Array.value = var_IEngravingMachineCuttingPlate;
    var_IEngravingMachineCuttingPlate.type = BasicType.Object;
    var_IEngravingMachineCuttingPlate.properties = {
        "is_residual": var_booleanType,
        "plate_cost": var_numberType,
        "plate_count": var_numberType,
        "plate_id": var_numberType,
        "plate_length": var_numberType,
        "plate_name": var_stringType,
        "plate_width": var_numberType,
    };
    var_findTaskAsync.type = BasicType.Function;
    var_findTaskAsync.name = "findTaskAsync";
    var_findTaskAsync.varying = false;
    var_findTaskAsync.keepArgsHandle = true;
    var_findTaskAsync.args = [var_unknownType];
    var_findTaskAsync.return = var_IEngravingMachineCuttingResponse_Promise;
    var_IEngravingMachineCuttingResponse_Promise.type = BasicType.Object;
    var_IEngravingMachineCuttingResponse_Promise.properties = {
        "then": var_IEngravingMachineCuttingResponse_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IEngravingMachineCuttingResponse_Promise_then.type = BasicType.Function;
    var_IEngravingMachineCuttingResponse_Promise_then.name = "";
    var_IEngravingMachineCuttingResponse_Promise_then.varying = false;
    var_IEngravingMachineCuttingResponse_Promise_then.keepArgsHandle = true;
    var_IEngravingMachineCuttingResponse_Promise_then.args = [var_IEngravingMachineCuttingResponse_Promise_then_onresolve, var_Promise_then_onreject];
    var_IEngravingMachineCuttingResponse_Promise_then.return = var_undefinedType;
    var_IEngravingMachineCuttingResponse_Promise_then_onresolve.type = BasicType.Function;
    var_IEngravingMachineCuttingResponse_Promise_then_onresolve.name = "";
    var_IEngravingMachineCuttingResponse_Promise_then_onresolve.varying = false;
    var_IEngravingMachineCuttingResponse_Promise_then_onresolve.keepArgsHandle = false;
    var_IEngravingMachineCuttingResponse_Promise_then_onresolve.args = [var_IEngravingMachineCuttingResponse];
    var_IEngravingMachineCuttingResponse_Promise_then_onresolve.return = var_undefinedType;
    var_IEngravingMachineCuttingResponse.type = BasicType.Object;
    var_IEngravingMachineCuttingResponse.properties = {
        "result": var_IEngravingMachineCuttingResultResponse,
        "status": var_stringType,
        "task_id": var_stringType,
    };
    var_IEngravingMachineCuttingResultResponse.type = BasicType.Object;
    var_IEngravingMachineCuttingResultResponse.properties = {
        "objects": var_IEngravingMachineCuttingResultResponseObjects_Array,
        "origin": var_numberType,
        "plate_type_num": var_numberType,
        "project_id": var_stringType,
        "task_id": var_stringType,
    };
    var_IEngravingMachineCuttingResultResponseObjects_Array.type = BasicType.Array;
    var_IEngravingMachineCuttingResultResponseObjects_Array.value = var_IEngravingMachineCuttingResultResponseObjects;
    var_IEngravingMachineCuttingResultResponseObjects.type = BasicType.Object;
    var_IEngravingMachineCuttingResultResponseObjects.properties = {
        "average_ratio": var_numberType,
        "nesting": var_IIEngravingMachineCuttingResultResponseNesting_Array,
        "plate_count": var_numberType,
        "plate_type": var_stringType,
        "total_item_count": var_numberType,
    };
    var_IIEngravingMachineCuttingResultResponseNesting_Array.type = BasicType.Array;
    var_IIEngravingMachineCuttingResultResponseNesting_Array.value = var_IIEngravingMachineCuttingResultResponseNesting;
    var_IIEngravingMachineCuttingResultResponseNesting.type = BasicType.Object;
    var_IIEngravingMachineCuttingResultResponseNesting.properties = {
        "item_count": var_numberType,
        "items": var_IIEngravingMachineCuttingResultResponseNestingItem_Array,
        "plate_id": var_numberType,
        "plate_length": var_numberType,
        "plate_name": var_stringType,
        "plate_ratio": var_numberType,
        "plate_width": var_numberType,
    };
    var_IIEngravingMachineCuttingResultResponseNestingItem_Array.type = BasicType.Array;
    var_IIEngravingMachineCuttingResultResponseNestingItem_Array.value = var_IIEngravingMachineCuttingResultResponseNestingItem;
    var_IIEngravingMachineCuttingResultResponseNestingItem.type = BasicType.Object;
    var_IIEngravingMachineCuttingResultResponseNestingItem.properties = {
        "flip_x": var_numberType,
        "flip_y": var_numberType,
        "item_angle": var_numberType,
        "item_name": var_stringType,
        "vertices": var_unknownType_Array_Array,
    };
    var_deleteTaskAsync.type = BasicType.Function;
    var_deleteTaskAsync.name = "deleteTaskAsync";
    var_deleteTaskAsync.varying = false;
    var_deleteTaskAsync.keepArgsHandle = true;
    var_deleteTaskAsync.args = [var_unknownType];
    var_deleteTaskAsync.return = var_undefinedType_Promise;
    var_Bom.type = BasicType.Object;
    var_Bom.properties = {
        "createMaterialsAsync": var_createMaterialsAsync,
        "deleteMaterialsAsync": var_deleteMaterialsAsync,
        "deleteMaterialsByOrderIdAsync": var_injection_AsyncFunctionType,
        "unpackMaterialsByBomIdAsync": var_injection_AsyncFunctionType,
        "unpackMaterialsByPackageIdAsync": var_injection_AsyncFunctionType,
        "unpackMaterialsByOrderIdAsync": var_injection_AsyncFunctionType,
        "findPlankListAsync": var_injection_AsyncFunctionType,
        "createPlankAsync": var_injection_AsyncFunctionType,
        "updatePlankAsync": var_injection_AsyncFunctionType,
        "deletePlankAsync": var_deletePlankAsync,
        "createPlanksAsync": var_injection_AsyncFunctionType,
        "updatePlanksAsync": var_injection_AsyncFunctionType,
        "deletePlanksAsync": var_deletePlanksAsync,
        "deletePlanksByProductIdsAsync": var_deletePlanksByProductIdsAsync,
        "deletePlanksByOrderIdsAsync": var_deletePlanksByOrderIdsAsync,
        "createMoldingsAsync": var_injection_AsyncFunctionType,
        "updateMoldingsAsync": var_injection_AsyncFunctionType,
        "deleteMoldingsAsync": var_injection_AsyncFunctionType,
        "findMoldingsAsync": var_injection_AsyncFunctionType,
        "createFinishedProductsAsync": var_injection_AsyncFunctionType,
        "updateFinishedProductsAsync": var_injection_AsyncFunctionType,
        "deleteFinishedProductsAsync": var_injection_AsyncFunctionType,
        "findFinishedProductsAsync": var_injection_AsyncFunctionType,
        "createGroupsAsync": var_injection_AsyncFunctionType,
        "updateGroupsAsync": var_injection_AsyncFunctionType,
        "deleteGroupsAsync": var_injection_AsyncFunctionType,
        "deleteGroupsByProductIdAsync": var_deleteGroupsByProductIdAsync,
        "generateGroupRelationsAsync": var_generateGroupRelationsAsync,
        "findGroupsByRootIdAsync": var_injection_AsyncFunctionType,
        "findGroupsByBomIdAsync": var_injection_AsyncFunctionType,
        "findGroupsByOrderIdAsync": var_injection_AsyncFunctionType,
        "findGroupsByProductIdAsync": var_injection_AsyncFunctionType,
        "createRawPlanksAsync": var_injection_AsyncFunctionType,
        "deleteRawPlanksAsync": var_injection_AsyncFunctionType,
        "updateRawPlanksAsync": var_injection_AsyncFunctionType,
        "findRawPlanksAsync": var_injection_AsyncFunctionType,
        "arrangePlanksAsync": var_injection_AsyncFunctionType,
        "updateRawPlankProcessingAttributesAsync": var_updateRawPlankProcessingAttributesAsync,
        "findLayoutAsync": var_injection_AsyncFunctionType,
        "deleteLayoutsAsync": var_injection_AsyncFunctionType,
        "deleteLayoutsByPlankIdAsync": var_injection_AsyncFunctionType,
        "createSurplusPlanksAsync": var_injection_AsyncFunctionType,
        "deleteSurplusPlanksAsync": var_injection_AsyncFunctionType,
        "updateSurplusPlanksAsync": var_injection_AsyncFunctionType,
        "findSurplusPlanksAsync": var_injection_AsyncFunctionType,
        "findCategoryAttrsAsync": var_findCategoryAttrsAsync,
        "clearMaterialPropertiesAsync": var_clearMaterialPropertiesAsync,
        "clearGroupPropertiesAsync": var_clearGroupPropertiesAsync,
        "refreshProductCodeInOrderAsync": var_refreshProductCodeInOrderAsync,
        "updateProductsAsync": var_updateProductsAsync,
        "refreshCodeAsync": var_refreshCodeAsync,
        "findMaterialsAsync": var_injection_AsyncFunctionType,
        "previewPlankDrawingAsync": var_injection_AsyncFunctionType,
        "findProductsAsync": var_findProductsAsync,
        "previewStructureDrawingAsync": var_injection_AsyncFunctionType,
        "updateStructureAsync": var_injection_AsyncFunctionType,
        "previewMaterialDrawingsAsync": var_previewMaterialDrawingsAsync,
        "BomPlankType": var_EBomPlankType,
        "BomCurveType": var_EBomCurveType,
        "BomPlankHoleType": var_EBomPlankHoleType,
        "Plank": var_Plank,
        "Detection": var_Detection_1,
    };
    var_createMaterialsAsync.type = BasicType.Function;
    var_createMaterialsAsync.name = "createMaterialsAsync";
    var_createMaterialsAsync.varying = false;
    var_createMaterialsAsync.keepArgsHandle = true;
    var_createMaterialsAsync.args = [var_injection_UnknownType];
    var_createMaterialsAsync.return = var_CreateMaterialsResult_Promise;
    var_CreateMaterialsResult_Promise.type = BasicType.Object;
    var_CreateMaterialsResult_Promise.properties = {
        "then": var_CreateMaterialsResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_CreateMaterialsResult_Promise_then.type = BasicType.Function;
    var_CreateMaterialsResult_Promise_then.name = "";
    var_CreateMaterialsResult_Promise_then.varying = false;
    var_CreateMaterialsResult_Promise_then.keepArgsHandle = true;
    var_CreateMaterialsResult_Promise_then.args = [var_CreateMaterialsResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_CreateMaterialsResult_Promise_then.return = var_undefinedType;
    var_CreateMaterialsResult_Promise_then_onresolve.type = BasicType.Function;
    var_CreateMaterialsResult_Promise_then_onresolve.name = "";
    var_CreateMaterialsResult_Promise_then_onresolve.varying = false;
    var_CreateMaterialsResult_Promise_then_onresolve.keepArgsHandle = false;
    var_CreateMaterialsResult_Promise_then_onresolve.args = [var_CreateMaterialsResult];
    var_CreateMaterialsResult_Promise_then_onresolve.return = var_undefinedType;
    var_CreateMaterialsResult.type = BasicType.Object;
    var_CreateMaterialsResult.properties = {
        "moldingIds": var_stringType_Array,
        "plankIds": var_stringType_Array,
        "finishedProductIds": var_stringType_Array,
        "rootIds": var_stringType_Array,
        "structureIds": var_stringType_Array,
    };
    var_deleteMaterialsAsync.type = BasicType.Function;
    var_deleteMaterialsAsync.name = "deleteMaterialsAsync";
    var_deleteMaterialsAsync.varying = false;
    var_deleteMaterialsAsync.keepArgsHandle = true;
    var_deleteMaterialsAsync.args = [var_DeleteMaterialsOption];
    var_deleteMaterialsAsync.return = var_DeleteMaterialsResult_Promise;
    var_DeleteMaterialsOption.type = BasicType.Object;
    var_DeleteMaterialsOption.properties = {
        "orderId": var_stringType,
        "productIds": var_stringType_Array,
    };
    var_DeleteMaterialsResult_Promise.type = BasicType.Object;
    var_DeleteMaterialsResult_Promise.properties = {
        "then": var_DeleteMaterialsResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_DeleteMaterialsResult_Promise_then.type = BasicType.Function;
    var_DeleteMaterialsResult_Promise_then.name = "";
    var_DeleteMaterialsResult_Promise_then.varying = false;
    var_DeleteMaterialsResult_Promise_then.keepArgsHandle = true;
    var_DeleteMaterialsResult_Promise_then.args = [var_DeleteMaterialsResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_DeleteMaterialsResult_Promise_then.return = var_undefinedType;
    var_DeleteMaterialsResult_Promise_then_onresolve.type = BasicType.Function;
    var_DeleteMaterialsResult_Promise_then_onresolve.name = "";
    var_DeleteMaterialsResult_Promise_then_onresolve.varying = false;
    var_DeleteMaterialsResult_Promise_then_onresolve.keepArgsHandle = false;
    var_DeleteMaterialsResult_Promise_then_onresolve.args = [var_DeleteMaterialsResult];
    var_DeleteMaterialsResult_Promise_then_onresolve.return = var_undefinedType;
    var_DeleteMaterialsResult.type = BasicType.Object;
    var_DeleteMaterialsResult.properties = {
        "productMaterials": var_DeleteMaterialsResult_productMaterials_objectLiteral_Array,
    };
    var_DeleteMaterialsResult_productMaterials_objectLiteral_Array.type = BasicType.Array;
    var_DeleteMaterialsResult_productMaterials_objectLiteral_Array.value = var_DeleteMaterialsResult_productMaterials_objectLiteral;
    var_DeleteMaterialsResult_productMaterials_objectLiteral.type = BasicType.Object;
    var_DeleteMaterialsResult_productMaterials_objectLiteral.properties = {
        "bomIds": var_stringType_Array,
        "productId": var_stringType,
        "groupIds": var_stringType_Array,
    };
    var_deletePlankAsync.type = BasicType.Function;
    var_deletePlankAsync.name = "deletePlankAsync";
    var_deletePlankAsync.varying = false;
    var_deletePlankAsync.keepArgsHandle = true;
    var_deletePlankAsync.args = [var_stringType];
    var_deletePlankAsync.return = var_undefinedType_Promise;
    var_deletePlanksAsync.type = BasicType.Function;
    var_deletePlanksAsync.name = "deletePlanksAsync";
    var_deletePlanksAsync.varying = false;
    var_deletePlanksAsync.keepArgsHandle = true;
    var_deletePlanksAsync.args = [var_deletePlanksAsync_option_objectLiteral];
    var_deletePlanksAsync.return = var_deletePlanksAsync_objectLiteral_Promise;
    var_deletePlanksAsync_option_objectLiteral.type = BasicType.Object;
    var_deletePlanksAsync_option_objectLiteral.properties = {
        "plankIds": var_stringType_Array,
    };
    var_deletePlanksAsync_objectLiteral_Promise.type = BasicType.Object;
    var_deletePlanksAsync_objectLiteral_Promise.properties = {
        "then": var_deletePlanksAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_deletePlanksAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_deletePlanksAsync_objectLiteral_Promise_then.name = "";
    var_deletePlanksAsync_objectLiteral_Promise_then.varying = false;
    var_deletePlanksAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_deletePlanksAsync_objectLiteral_Promise_then.args = [var_deletePlanksAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_deletePlanksAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_deletePlanksAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_deletePlanksAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_deletePlanksAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_deletePlanksAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_deletePlanksAsync_objectLiteral_Promise_then_onresolve.args = [var_deletePlanksAsync_objectLiteral];
    var_deletePlanksAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_deletePlanksAsync_objectLiteral.type = BasicType.Object;
    var_deletePlanksAsync_objectLiteral.properties = {
        "plankIds": var_stringType_Array,
    };
    var_deletePlanksByProductIdsAsync.type = BasicType.Function;
    var_deletePlanksByProductIdsAsync.name = "deletePlanksByProductIdsAsync";
    var_deletePlanksByProductIdsAsync.varying = false;
    var_deletePlanksByProductIdsAsync.keepArgsHandle = true;
    var_deletePlanksByProductIdsAsync.args = [var_deletePlanksByProductIdsAsync_option_objectLiteral];
    var_deletePlanksByProductIdsAsync.return = var_deletePlanksByProductIdsAsync_objectLiteral_Promise;
    var_deletePlanksByProductIdsAsync_option_objectLiteral.type = BasicType.Object;
    var_deletePlanksByProductIdsAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
        "productIds": var_stringType_Array,
    };
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise.type = BasicType.Object;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise.properties = {
        "then": var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then.name = "";
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then.varying = false;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then.args = [var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve.args = [var_deletePlanksByProductIdsAsync_objectLiteral];
    var_deletePlanksByProductIdsAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_deletePlanksByProductIdsAsync_objectLiteral.type = BasicType.Object;
    var_deletePlanksByProductIdsAsync_objectLiteral.properties = {
        "plankIds": var_stringType_Array,
    };
    var_deletePlanksByOrderIdsAsync.type = BasicType.Function;
    var_deletePlanksByOrderIdsAsync.name = "deletePlanksByOrderIdsAsync";
    var_deletePlanksByOrderIdsAsync.varying = false;
    var_deletePlanksByOrderIdsAsync.keepArgsHandle = true;
    var_deletePlanksByOrderIdsAsync.args = [var_deletePlanksByOrderIdsAsync_option_objectLiteral];
    var_deletePlanksByOrderIdsAsync.return = var_deletePlanksByOrderIdsAsync_objectLiteral_Promise;
    var_deletePlanksByOrderIdsAsync_option_objectLiteral.type = BasicType.Object;
    var_deletePlanksByOrderIdsAsync_option_objectLiteral.properties = {
        "orderIds": var_stringType_Array,
    };
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise.type = BasicType.Object;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise.properties = {
        "then": var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then.type = BasicType.Function;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then.name = "";
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then.varying = false;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then.keepArgsHandle = true;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then.args = [var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then.return = var_undefinedType;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve.name = "";
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve.varying = false;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve.args = [var_deletePlanksByOrderIdsAsync_objectLiteral];
    var_deletePlanksByOrderIdsAsync_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var_deletePlanksByOrderIdsAsync_objectLiteral.type = BasicType.Object;
    var_deletePlanksByOrderIdsAsync_objectLiteral.properties = {
        "plankIds": var_stringType_Array,
    };
    var_deleteGroupsByProductIdAsync.type = BasicType.Function;
    var_deleteGroupsByProductIdAsync.name = "deleteGroupsByProductIdAsync";
    var_deleteGroupsByProductIdAsync.varying = false;
    var_deleteGroupsByProductIdAsync.keepArgsHandle = true;
    var_deleteGroupsByProductIdAsync.args = [var_BomGroupDeleteByProductIdsOption];
    var_deleteGroupsByProductIdAsync.return = var_BomGroupDeleteByProductIdsResult_Promise;
    var_BomGroupDeleteByProductIdsOption.type = BasicType.Object;
    var_BomGroupDeleteByProductIdsOption.properties = {
        "orderId": var_stringType,
        "productIds": var_stringType_Array,
    };
    var_BomGroupDeleteByProductIdsResult_Promise.type = BasicType.Object;
    var_BomGroupDeleteByProductIdsResult_Promise.properties = {
        "then": var_BomGroupDeleteByProductIdsResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_BomGroupDeleteByProductIdsResult_Promise_then.type = BasicType.Function;
    var_BomGroupDeleteByProductIdsResult_Promise_then.name = "";
    var_BomGroupDeleteByProductIdsResult_Promise_then.varying = false;
    var_BomGroupDeleteByProductIdsResult_Promise_then.keepArgsHandle = true;
    var_BomGroupDeleteByProductIdsResult_Promise_then.args = [var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_BomGroupDeleteByProductIdsResult_Promise_then.return = var_undefinedType;
    var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve.type = BasicType.Function;
    var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve.name = "";
    var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve.varying = false;
    var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve.keepArgsHandle = false;
    var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve.args = [var_BomGroupDeleteByProductIdsResult];
    var_BomGroupDeleteByProductIdsResult_Promise_then_onresolve.return = var_undefinedType;
    var_BomGroupDeleteByProductIdsResult.type = BasicType.Object;
    var_BomGroupDeleteByProductIdsResult.properties = {
        "groups": var_BomGroupDeleteByProductIdsResult_groups_objectLiteral_Array,
    };
    var_BomGroupDeleteByProductIdsResult_groups_objectLiteral_Array.type = BasicType.Array;
    var_BomGroupDeleteByProductIdsResult_groups_objectLiteral_Array.value = var_BomGroupDeleteByProductIdsResult_groups_objectLiteral;
    var_BomGroupDeleteByProductIdsResult_groups_objectLiteral.type = BasicType.Object;
    var_BomGroupDeleteByProductIdsResult_groups_objectLiteral.properties = {
        "productId": var_stringType,
        "groupIds": var_stringType_Array,
    };
    var_generateGroupRelationsAsync.type = BasicType.Function;
    var_generateGroupRelationsAsync.name = "generateGroupRelationsAsync";
    var_generateGroupRelationsAsync.varying = false;
    var_generateGroupRelationsAsync.keepArgsHandle = true;
    var_generateGroupRelationsAsync.args = [var_generateGroupRelationsAsync_option_objectLiteral];
    var_generateGroupRelationsAsync.return = var_undefinedType_Promise;
    var_generateGroupRelationsAsync_option_objectLiteral.type = BasicType.Object;
    var_generateGroupRelationsAsync_option_objectLiteral.properties = {
        "regenerate": var_booleanType,
        "orderId": var_stringType,
    };
    var_updateRawPlankProcessingAttributesAsync.type = BasicType.Function;
    var_updateRawPlankProcessingAttributesAsync.name = "updateRawPlankProcessingAttributesAsync";
    var_updateRawPlankProcessingAttributesAsync.varying = false;
    var_updateRawPlankProcessingAttributesAsync.keepArgsHandle = true;
    var_updateRawPlankProcessingAttributesAsync.args = [var_updateRawPlankProcessingAttributesAsync_option_objectLiteral];
    var_updateRawPlankProcessingAttributesAsync.return = var_undefinedType_Promise;
    var_updateRawPlankProcessingAttributesAsync_option_objectLiteral.type = BasicType.Object;
    var_updateRawPlankProcessingAttributesAsync_option_objectLiteral.properties = {
        "attributes": var_IBomPlankLayoutProcessAttribute_Array,
    };
    var_IBomPlankLayoutProcessAttribute_Array.type = BasicType.Array;
    var_IBomPlankLayoutProcessAttribute_Array.value = var_IBomPlankLayoutProcessAttribute;
    var_IBomPlankLayoutProcessAttribute.type = BasicType.Object;
    var_IBomPlankLayoutProcessAttribute.properties = {
        "plankSpacing": var_numberType,
    };
    var_IBomRawPlankMeta.type = BasicType.Object;
    var_IBomRawPlankMeta.properties = {
        "rawPlankIndex": var_numberType,
        "rawPlankType": var_stringType,
        "rawPlankId": var_stringType,
    };
    var_findCategoryAttrsAsync.type = BasicType.Function;
    var_findCategoryAttrsAsync.name = "findCategoryAttrsAsync";
    var_findCategoryAttrsAsync.varying = false;
    var_findCategoryAttrsAsync.keepArgsHandle = true;
    var_findCategoryAttrsAsync.args = [var_FindCategoryAttrOption];
    var_findCategoryAttrsAsync.return = var_FindCategoryAttrResult_Promise;
    var_FindCategoryAttrOption.type = BasicType.Object;
    var_FindCategoryAttrOption.properties = {
        "categoryCodes": var_stringType_Array,
    };
    var_FindCategoryAttrResult_Promise.type = BasicType.Object;
    var_FindCategoryAttrResult_Promise.properties = {
        "then": var_FindCategoryAttrResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_FindCategoryAttrResult_Promise_then.type = BasicType.Function;
    var_FindCategoryAttrResult_Promise_then.name = "";
    var_FindCategoryAttrResult_Promise_then.varying = false;
    var_FindCategoryAttrResult_Promise_then.keepArgsHandle = true;
    var_FindCategoryAttrResult_Promise_then.args = [var_FindCategoryAttrResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_FindCategoryAttrResult_Promise_then.return = var_undefinedType;
    var_FindCategoryAttrResult_Promise_then_onresolve.type = BasicType.Function;
    var_FindCategoryAttrResult_Promise_then_onresolve.name = "";
    var_FindCategoryAttrResult_Promise_then_onresolve.varying = false;
    var_FindCategoryAttrResult_Promise_then_onresolve.keepArgsHandle = false;
    var_FindCategoryAttrResult_Promise_then_onresolve.args = [var_FindCategoryAttrResult];
    var_FindCategoryAttrResult_Promise_then_onresolve.return = var_undefinedType;
    var_FindCategoryAttrResult.type = BasicType.Object;
    var_FindCategoryAttrResult.properties = {
        "catAttrs": var_FindCategoryAttrResult_catAttrs_objectLiteral_Array,
    };
    var_FindCategoryAttrResult_catAttrs_objectLiteral_Array.type = BasicType.Array;
    var_FindCategoryAttrResult_catAttrs_objectLiteral_Array.value = var_FindCategoryAttrResult_catAttrs_objectLiteral;
    var_FindCategoryAttrResult_catAttrs_objectLiteral.type = BasicType.Object;
    var_FindCategoryAttrResult_catAttrs_objectLiteral.properties = {
        "attributes": var_ICategoryAttribute_Array,
        "category": var_ICategory,
    };
    var_ICategoryAttribute_Array.type = BasicType.Array;
    var_ICategoryAttribute_Array.value = var_ICategoryAttribute;
    var_ICategoryAttribute.type = BasicType.Object;
    var_ICategoryAttribute.properties = {
        "name": var_stringType,
        "description": var_stringType,
        "type": var_injection_UnknownType,
        "rules": var_ICategoryAttributeRule_Array,
    };
    var_ICategoryAttributeRule_Array.type = BasicType.Array;
    var_ICategoryAttributeRule_Array.value = var_ICategoryAttributeRule;
    var_ICategoryAttributeRule.type = BasicType.Object;
    var_ICategoryAttributeRule.properties = {
        "notNull": var_booleanType,
        "enumValues": var_stringType_Array,
    };
    var_ICategory.type = BasicType.Object;
    var_ICategory.properties = {
        "code": var_stringType,
        "name": var_stringType,
        "description": var_stringType,
    };
    var_clearMaterialPropertiesAsync.type = BasicType.Function;
    var_clearMaterialPropertiesAsync.name = "clearMaterialPropertiesAsync";
    var_clearMaterialPropertiesAsync.varying = false;
    var_clearMaterialPropertiesAsync.keepArgsHandle = true;
    var_clearMaterialPropertiesAsync.args = [var_unknownType];
    var_clearMaterialPropertiesAsync.return = var_undefinedType_Promise;
    var_clearGroupPropertiesAsync.type = BasicType.Function;
    var_clearGroupPropertiesAsync.name = "clearGroupPropertiesAsync";
    var_clearGroupPropertiesAsync.varying = false;
    var_clearGroupPropertiesAsync.keepArgsHandle = true;
    var_clearGroupPropertiesAsync.args = [var_unknownType];
    var_clearGroupPropertiesAsync.return = var_undefinedType_Promise;
    var_refreshProductCodeInOrderAsync.type = BasicType.Function;
    var_refreshProductCodeInOrderAsync.name = "refreshProductCodeInOrderAsync";
    var_refreshProductCodeInOrderAsync.varying = false;
    var_refreshProductCodeInOrderAsync.keepArgsHandle = true;
    var_refreshProductCodeInOrderAsync.args = [var_refreshProductCodeInOrderAsync_option_objectLiteral];
    var_refreshProductCodeInOrderAsync.return = var_ProductCodeInOrder_Promise;
    var_refreshProductCodeInOrderAsync_option_objectLiteral.type = BasicType.Object;
    var_refreshProductCodeInOrderAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
    };
    var_ProductCodeInOrder_Promise.type = BasicType.Object;
    var_ProductCodeInOrder_Promise.properties = {
        "then": var_ProductCodeInOrder_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ProductCodeInOrder_Promise_then.type = BasicType.Function;
    var_ProductCodeInOrder_Promise_then.name = "";
    var_ProductCodeInOrder_Promise_then.varying = false;
    var_ProductCodeInOrder_Promise_then.keepArgsHandle = true;
    var_ProductCodeInOrder_Promise_then.args = [var_ProductCodeInOrder_Promise_then_onresolve, var_Promise_then_onreject];
    var_ProductCodeInOrder_Promise_then.return = var_undefinedType;
    var_ProductCodeInOrder_Promise_then_onresolve.type = BasicType.Function;
    var_ProductCodeInOrder_Promise_then_onresolve.name = "";
    var_ProductCodeInOrder_Promise_then_onresolve.varying = false;
    var_ProductCodeInOrder_Promise_then_onresolve.keepArgsHandle = false;
    var_ProductCodeInOrder_Promise_then_onresolve.args = [var_ProductCodeInOrder];
    var_ProductCodeInOrder_Promise_then_onresolve.return = var_undefinedType;
    var_ProductCodeInOrder.type = BasicType.Object;
    var_ProductCodeInOrder.properties = {
        "products": var_IProductCodeInOrderInfo_Array,
    };
    var_IProductCodeInOrderInfo_Array.type = BasicType.Array;
    var_IProductCodeInOrderInfo_Array.value = var_IProductCodeInOrderInfo;
    var_IProductCodeInOrderInfo.type = BasicType.Object;
    var_IProductCodeInOrderInfo.properties = {
        "productId": var_stringType,
        "codeInOrder": var_stringType,
    };
    var_updateProductsAsync.type = BasicType.Function;
    var_updateProductsAsync.name = "updateProductsAsync";
    var_updateProductsAsync.varying = false;
    var_updateProductsAsync.keepArgsHandle = true;
    var_updateProductsAsync.args = [var_UpdateProductsOption];
    var_updateProductsAsync.return = var_undefinedType_Promise;
    var_UpdateProductsOption.type = BasicType.Object;
    var_UpdateProductsOption.properties = {
        "orderId": var_stringType,
    };
    var_refreshCodeAsync.type = BasicType.Function;
    var_refreshCodeAsync.name = "refreshCodeAsync";
    var_refreshCodeAsync.varying = false;
    var_refreshCodeAsync.keepArgsHandle = true;
    var_refreshCodeAsync.args = [var_refreshCodeAsync_option_objectLiteral];
    var_refreshCodeAsync.return = var_undefinedType_Promise;
    var_refreshCodeAsync_option_objectLiteral.type = BasicType.Object;
    var_refreshCodeAsync_option_objectLiteral.properties = {
        "orderId": var_stringType,
    };
    var_findProductsAsync.type = BasicType.Function;
    var_findProductsAsync.name = "findProductsAsync";
    var_findProductsAsync.varying = false;
    var_findProductsAsync.keepArgsHandle = true;
    var_findProductsAsync.args = [var_stringType];
    var_findProductsAsync.return = var_findProductsAsync_objectLiteral_Array_Promise;
    var_findProductsAsync_objectLiteral_Array_Promise.type = BasicType.Object;
    var_findProductsAsync_objectLiteral_Array_Promise.properties = {
        "then": var_findProductsAsync_objectLiteral_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_findProductsAsync_objectLiteral_Array_Promise_then.type = BasicType.Function;
    var_findProductsAsync_objectLiteral_Array_Promise_then.name = "";
    var_findProductsAsync_objectLiteral_Array_Promise_then.varying = false;
    var_findProductsAsync_objectLiteral_Array_Promise_then.keepArgsHandle = true;
    var_findProductsAsync_objectLiteral_Array_Promise_then.args = [var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_findProductsAsync_objectLiteral_Array_Promise_then.return = var_undefinedType;
    var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve.type = BasicType.Function;
    var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve.name = "";
    var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve.varying = false;
    var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve.args = [var_findProductsAsync_objectLiteral_Array];
    var_findProductsAsync_objectLiteral_Array_Promise_then_onresolve.return = var_undefinedType;
    var_findProductsAsync_objectLiteral_Array.type = BasicType.Array;
    var_findProductsAsync_objectLiteral_Array.value = var_findProductsAsync_objectLiteral;
    var_findProductsAsync_objectLiteral.type = BasicType.Object;
    var_findProductsAsync_objectLiteral.properties = {
        "productId": var_stringType,
    };
    var_previewMaterialDrawingsAsync.type = BasicType.Function;
    var_previewMaterialDrawingsAsync.name = "previewMaterialDrawingsAsync";
    var_previewMaterialDrawingsAsync.varying = false;
    var_previewMaterialDrawingsAsync.keepArgsHandle = true;
    var_previewMaterialDrawingsAsync.args = [var_IPreviewMaterialDrawingsOption];
    var_previewMaterialDrawingsAsync.return = var_IPreviewMaterialDrawingsResult_Promise;
    var_IPreviewMaterialDrawingsOption.type = BasicType.Object;
    var_IPreviewMaterialDrawingsOption.properties = {
        "planks": var_injection_UnknownType_Array,
        "structures": var_injection_UnknownType_Array,
    };
    var_IPreviewMaterialDrawingsResult_Promise.type = BasicType.Object;
    var_IPreviewMaterialDrawingsResult_Promise.properties = {
        "then": var_IPreviewMaterialDrawingsResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IPreviewMaterialDrawingsResult_Promise_then.type = BasicType.Function;
    var_IPreviewMaterialDrawingsResult_Promise_then.name = "";
    var_IPreviewMaterialDrawingsResult_Promise_then.varying = false;
    var_IPreviewMaterialDrawingsResult_Promise_then.keepArgsHandle = true;
    var_IPreviewMaterialDrawingsResult_Promise_then.args = [var_IPreviewMaterialDrawingsResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IPreviewMaterialDrawingsResult_Promise_then.return = var_undefinedType;
    var_IPreviewMaterialDrawingsResult_Promise_then_onresolve.type = BasicType.Function;
    var_IPreviewMaterialDrawingsResult_Promise_then_onresolve.name = "";
    var_IPreviewMaterialDrawingsResult_Promise_then_onresolve.varying = false;
    var_IPreviewMaterialDrawingsResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IPreviewMaterialDrawingsResult_Promise_then_onresolve.args = [var_IPreviewMaterialDrawingsResult];
    var_IPreviewMaterialDrawingsResult_Promise_then_onresolve.return = var_undefinedType;
    var_IPreviewMaterialDrawingsResult.type = BasicType.Object;
    var_IPreviewMaterialDrawingsResult.properties = {
        "planks": var_IPreviewMaterialDrawing_Array,
        "structures": var_IPreviewMaterialDrawing_Array,
    };
    var_IPreviewMaterialDrawing_Array.type = BasicType.Array;
    var_IPreviewMaterialDrawing_Array.value = var_IPreviewMaterialDrawing;
    var_IPreviewMaterialDrawing.type = BasicType.Object;
    var_IPreviewMaterialDrawing.properties = {
        "url": var_stringType,
    };
    var_EBomPlankType.type = BasicType.Object;
    var_EBomPlankType.properties = {
        "BODY": var_stringType,
        "BACK": var_stringType,
        "DOOR": var_stringType,
        "SLIDINGDOOR": var_stringType,
    };
    var_EBomCurveType.type = BasicType.Object;
    var_EBomCurveType.properties = {
        "LineSeg": var_stringType,
        "Arc": var_stringType,
    };
    var_EBomPlankHoleType.type = BasicType.Object;
    var_EBomPlankHoleType.properties = {
        "PLANK": var_stringType,
        "FITTING": var_stringType,
        "AUXILIARY": var_stringType,
    };
    var_Plank.type = BasicType.Object;
    var_Plank.properties = {
        "createHolesAsync": var_injection_AsyncFunctionType,
        "updateHoleAsync": var_injection_AsyncFunctionType,
        "deleteHoleAsync": var_injection_AsyncFunctionType,
        "createGroovesAsync": var_injection_AsyncFunctionType,
        "updateGrooveAsync": var_injection_AsyncFunctionType,
        "deleteGrooveAsync": var_injection_AsyncFunctionType,
    };
    var_Detection_1.type = BasicType.Object;
    var_Detection_1.properties = {
        "detectAsync": var_injection_AsyncFunctionType,
        "findRuleListAsync": var_injection_AsyncFunctionType,
    };
    var_Report.type = BasicType.Object;
    var_Report.properties = {
        "generateReportPageAsync": var_generateReportPageAsync,
    };
    var_generateReportPageAsync.type = BasicType.Function;
    var_generateReportPageAsync.name = "generateReportPageAsync";
    var_generateReportPageAsync.varying = false;
    var_generateReportPageAsync.keepArgsHandle = true;
    var_generateReportPageAsync.args = [var_GenerateReportPageOption];
    var_generateReportPageAsync.return = var_stringType_Promise;
    var_GenerateReportPageOption.type = BasicType.Object;
    var_GenerateReportPageOption.properties = {
        "data": var_unknownType,
        "templateId": var_stringType,
    };
    var_User.type = BasicType.Object;
    var_User.properties = {
        "getAppUidAsync": var_getAppUidAsync,
        "getUserDetailsAsync": var_getUserDetailsAsync,
    };
    var_getAppUidAsync.type = BasicType.Function;
    var_getAppUidAsync.name = "getAppUidAsync";
    var_getAppUidAsync.varying = false;
    var_getAppUidAsync.keepArgsHandle = false;
    var_getAppUidAsync.args = [];
    var_getAppUidAsync.return = var_IGetAppUidResult_Promise;
    var_IGetAppUidResult_Promise.type = BasicType.Object;
    var_IGetAppUidResult_Promise.properties = {
        "then": var_IGetAppUidResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IGetAppUidResult_Promise_then.type = BasicType.Function;
    var_IGetAppUidResult_Promise_then.name = "";
    var_IGetAppUidResult_Promise_then.varying = false;
    var_IGetAppUidResult_Promise_then.keepArgsHandle = true;
    var_IGetAppUidResult_Promise_then.args = [var_IGetAppUidResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IGetAppUidResult_Promise_then.return = var_undefinedType;
    var_IGetAppUidResult_Promise_then_onresolve.type = BasicType.Function;
    var_IGetAppUidResult_Promise_then_onresolve.name = "";
    var_IGetAppUidResult_Promise_then_onresolve.varying = false;
    var_IGetAppUidResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IGetAppUidResult_Promise_then_onresolve.args = [var_IGetAppUidResult];
    var_IGetAppUidResult_Promise_then_onresolve.return = var_undefinedType;
    var_IGetAppUidResult.type = BasicType.Object;
    var_IGetAppUidResult.properties = {
        "appUid": var_unknownType,
    };
    var_getUserDetailsAsync.type = BasicType.Function;
    var_getUserDetailsAsync.name = "getUserDetailsAsync";
    var_getUserDetailsAsync.varying = false;
    var_getUserDetailsAsync.keepArgsHandle = false;
    var_getUserDetailsAsync.args = [];
    var_getUserDetailsAsync.return = var_IUserDetails_Promise;
    var_IUserDetails_Promise.type = BasicType.Object;
    var_IUserDetails_Promise.properties = {
        "then": var_IUserDetails_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IUserDetails_Promise_then.type = BasicType.Function;
    var_IUserDetails_Promise_then.name = "";
    var_IUserDetails_Promise_then.varying = false;
    var_IUserDetails_Promise_then.keepArgsHandle = true;
    var_IUserDetails_Promise_then.args = [var_IUserDetails_Promise_then_onresolve, var_Promise_then_onreject];
    var_IUserDetails_Promise_then.return = var_undefinedType;
    var_IUserDetails_Promise_then_onresolve.type = BasicType.Function;
    var_IUserDetails_Promise_then_onresolve.name = "";
    var_IUserDetails_Promise_then_onresolve.varying = false;
    var_IUserDetails_Promise_then_onresolve.keepArgsHandle = false;
    var_IUserDetails_Promise_then_onresolve.args = [var_IUserDetails];
    var_IUserDetails_Promise_then_onresolve.return = var_undefinedType;
    var_IUserDetails.type = BasicType.Object;
    var_IUserDetails.properties = {
        "userId": var_stringType,
        "company": var_IUserDetails_company_objectLiteral,
        "userName": var_stringType,
        "businessName": var_stringType,
    };
    var_IUserDetails_company_objectLiteral.type = BasicType.Object;
    var_IUserDetails_company_objectLiteral.properties = {
        "id": var_stringType,
        "name": var_stringType,
    };
    var_Storage.type = BasicType.Object;
    var_Storage.properties = {
        "Common": var_Common_1,
        "Design": var_Design_2,
        "DesignV2": var_DesignV2,
        "Enterprise": var_Enterprise,
    };
    var_Common_1.type = BasicType.Object;
    var_Common_1.properties = {
        "putItemAsync": var_putItemAsync,
        "getItemAsync": var_getItemAsync,
        "getItemListAsync": var_getItemListAsync,
        "deleteItemAsync": var_deleteItemAsync,
        "getKeysAsync": var_getKeysAsync,
    };
    var_putItemAsync.type = BasicType.Function;
    var_putItemAsync.name = "putItemAsync";
    var_putItemAsync.varying = false;
    var_putItemAsync.keepArgsHandle = true;
    var_putItemAsync.args = [var_IStorageItem];
    var_putItemAsync.return = var_undefinedType_Promise;
    var_IStorageItem.type = BasicType.Object;
    var_IStorageItem.properties = {
        "key": var_stringType,
        "value": var_unknownType,
    };
    var_getItemAsync.type = BasicType.Function;
    var_getItemAsync.name = "getItemAsync";
    var_getItemAsync.varying = false;
    var_getItemAsync.keepArgsHandle = true;
    var_getItemAsync.args = [var_stringType];
    var_getItemAsync.return = var_unknownType_Promise;
    var_getItemListAsync.type = BasicType.Function;
    var_getItemListAsync.name = "getItemListAsync";
    var_getItemListAsync.varying = false;
    var_getItemListAsync.keepArgsHandle = true;
    var_getItemListAsync.args = [var_stringType_Array];
    var_getItemListAsync.return = var_IStorageItem_Array_Promise;
    var_IStorageItem_Array_Promise.type = BasicType.Object;
    var_IStorageItem_Array_Promise.properties = {
        "then": var_IStorageItem_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IStorageItem_Array_Promise_then.type = BasicType.Function;
    var_IStorageItem_Array_Promise_then.name = "";
    var_IStorageItem_Array_Promise_then.varying = false;
    var_IStorageItem_Array_Promise_then.keepArgsHandle = true;
    var_IStorageItem_Array_Promise_then.args = [var_IStorageItem_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_IStorageItem_Array_Promise_then.return = var_undefinedType;
    var_IStorageItem_Array_Promise_then_onresolve.type = BasicType.Function;
    var_IStorageItem_Array_Promise_then_onresolve.name = "";
    var_IStorageItem_Array_Promise_then_onresolve.varying = false;
    var_IStorageItem_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_IStorageItem_Array_Promise_then_onresolve.args = [var_IStorageItem_Array];
    var_IStorageItem_Array_Promise_then_onresolve.return = var_undefinedType;
    var_IStorageItem_Array.type = BasicType.Array;
    var_IStorageItem_Array.value = var_IStorageItem;
    var_deleteItemAsync.type = BasicType.Function;
    var_deleteItemAsync.name = "deleteItemAsync";
    var_deleteItemAsync.varying = false;
    var_deleteItemAsync.keepArgsHandle = true;
    var_deleteItemAsync.args = [var_stringType];
    var_deleteItemAsync.return = var_undefinedType_Promise;
    var_getKeysAsync.type = BasicType.Function;
    var_getKeysAsync.name = "getKeysAsync";
    var_getKeysAsync.varying = false;
    var_getKeysAsync.keepArgsHandle = true;
    var_getKeysAsync.args = [var_IStorageGetKeysOption];
    var_getKeysAsync.return = var_IStorageGetKeysResult_Promise;
    var_IStorageGetKeysOption.type = BasicType.Object;
    var_IStorageGetKeysOption.properties = {
        "pageSize": var_numberType,
        "pageNum": var_numberType,
        "prefix": var_stringType,
    };
    var_IStorageGetKeysResult_Promise.type = BasicType.Object;
    var_IStorageGetKeysResult_Promise.properties = {
        "then": var_IStorageGetKeysResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_IStorageGetKeysResult_Promise_then.type = BasicType.Function;
    var_IStorageGetKeysResult_Promise_then.name = "";
    var_IStorageGetKeysResult_Promise_then.varying = false;
    var_IStorageGetKeysResult_Promise_then.keepArgsHandle = true;
    var_IStorageGetKeysResult_Promise_then.args = [var_IStorageGetKeysResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_IStorageGetKeysResult_Promise_then.return = var_undefinedType;
    var_IStorageGetKeysResult_Promise_then_onresolve.type = BasicType.Function;
    var_IStorageGetKeysResult_Promise_then_onresolve.name = "";
    var_IStorageGetKeysResult_Promise_then_onresolve.varying = false;
    var_IStorageGetKeysResult_Promise_then_onresolve.keepArgsHandle = false;
    var_IStorageGetKeysResult_Promise_then_onresolve.args = [var_IStorageGetKeysResult];
    var_IStorageGetKeysResult_Promise_then_onresolve.return = var_undefinedType;
    var_IStorageGetKeysResult.type = BasicType.Object;
    var_IStorageGetKeysResult.properties = {
        "curPage": var_numberType,
        "totalPage": var_numberType,
    };
    var_Design_2.type = BasicType.Object;
    var_Design_2.properties = {
        "putItemAsync": var_putItemAsync_1,
        "getItemAsync": var_getItemAsync_1,
        "getItemListAsync": var_getItemListAsync_1,
        "deleteItemAsync": var_deleteItemAsync_1,
        "getKeysAsync": var_getKeysAsync_1,
    };
    var_putItemAsync_1.type = BasicType.Function;
    var_putItemAsync_1.name = "putItemAsync";
    var_putItemAsync_1.varying = false;
    var_putItemAsync_1.keepArgsHandle = true;
    var_putItemAsync_1.args = [var_IStorageItem];
    var_putItemAsync_1.return = var_undefinedType_Promise;
    var_getItemAsync_1.type = BasicType.Function;
    var_getItemAsync_1.name = "getItemAsync";
    var_getItemAsync_1.varying = false;
    var_getItemAsync_1.keepArgsHandle = true;
    var_getItemAsync_1.args = [var_stringType];
    var_getItemAsync_1.return = var_unknownType_Promise;
    var_getItemListAsync_1.type = BasicType.Function;
    var_getItemListAsync_1.name = "getItemListAsync";
    var_getItemListAsync_1.varying = false;
    var_getItemListAsync_1.keepArgsHandle = true;
    var_getItemListAsync_1.args = [var_stringType_Array];
    var_getItemListAsync_1.return = var_IStorageItem_Array_Promise;
    var_deleteItemAsync_1.type = BasicType.Function;
    var_deleteItemAsync_1.name = "deleteItemAsync";
    var_deleteItemAsync_1.varying = false;
    var_deleteItemAsync_1.keepArgsHandle = true;
    var_deleteItemAsync_1.args = [var_stringType];
    var_deleteItemAsync_1.return = var_undefinedType_Promise;
    var_getKeysAsync_1.type = BasicType.Function;
    var_getKeysAsync_1.name = "getKeysAsync";
    var_getKeysAsync_1.varying = false;
    var_getKeysAsync_1.keepArgsHandle = true;
    var_getKeysAsync_1.args = [var_IStorageGetKeysOption];
    var_getKeysAsync_1.return = var_IStorageGetKeysResult_Promise;
    var_DesignV2.type = BasicType.Object;
    var_DesignV2.properties = {
        "putItemAsync": var_putItemAsync_2,
        "getItemAsync": var_getItemAsync_2,
        "deleteItemAsync": var_deleteItemAsync_2,
    };
    var_putItemAsync_2.type = BasicType.Function;
    var_putItemAsync_2.name = "putItemAsync";
    var_putItemAsync_2.varying = false;
    var_putItemAsync_2.keepArgsHandle = true;
    var_putItemAsync_2.args = [var_IStorageItem];
    var_putItemAsync_2.return = var_undefinedType_Promise;
    var_getItemAsync_2.type = BasicType.Function;
    var_getItemAsync_2.name = "getItemAsync";
    var_getItemAsync_2.varying = false;
    var_getItemAsync_2.keepArgsHandle = true;
    var_getItemAsync_2.args = [var_stringType];
    var_getItemAsync_2.return = var_unknownType_Promise;
    var_deleteItemAsync_2.type = BasicType.Function;
    var_deleteItemAsync_2.name = "deleteItemAsync";
    var_deleteItemAsync_2.varying = false;
    var_deleteItemAsync_2.keepArgsHandle = true;
    var_deleteItemAsync_2.args = [var_stringType];
    var_deleteItemAsync_2.return = var_undefinedType_Promise;
    var_Enterprise.type = BasicType.Object;
    var_Enterprise.properties = {
        "putItemAsync": var_putItemAsync_3,
        "getItemAsync": var_getItemAsync_3,
        "getItemListAsync": var_getItemListAsync_2,
        "deleteItemAsync": var_deleteItemAsync_3,
        "getKeysAsync": var_getKeysAsync_2,
    };
    var_putItemAsync_3.type = BasicType.Function;
    var_putItemAsync_3.name = "putItemAsync";
    var_putItemAsync_3.varying = false;
    var_putItemAsync_3.keepArgsHandle = true;
    var_putItemAsync_3.args = [var_IStorageItem];
    var_putItemAsync_3.return = var_undefinedType_Promise;
    var_getItemAsync_3.type = BasicType.Function;
    var_getItemAsync_3.name = "getItemAsync";
    var_getItemAsync_3.varying = false;
    var_getItemAsync_3.keepArgsHandle = true;
    var_getItemAsync_3.args = [var_stringType];
    var_getItemAsync_3.return = var_unknownType_Promise;
    var_getItemListAsync_2.type = BasicType.Function;
    var_getItemListAsync_2.name = "getItemListAsync";
    var_getItemListAsync_2.varying = false;
    var_getItemListAsync_2.keepArgsHandle = true;
    var_getItemListAsync_2.args = [var_stringType_Array];
    var_getItemListAsync_2.return = var_IStorageItem_Array_Promise;
    var_deleteItemAsync_3.type = BasicType.Function;
    var_deleteItemAsync_3.name = "deleteItemAsync";
    var_deleteItemAsync_3.varying = false;
    var_deleteItemAsync_3.keepArgsHandle = true;
    var_deleteItemAsync_3.args = [var_stringType];
    var_deleteItemAsync_3.return = var_undefinedType_Promise;
    var_getKeysAsync_2.type = BasicType.Function;
    var_getKeysAsync_2.name = "getKeysAsync";
    var_getKeysAsync_2.varying = false;
    var_getKeysAsync_2.keepArgsHandle = true;
    var_getKeysAsync_2.args = [var_IStorageGetKeysOption];
    var_getKeysAsync_2.return = var_IStorageGetKeysResult_Promise;
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "getSelectedElements": var_getSelectedElements_1,
        "setSelectedElements": var_setSelectedElements,
        "getSelectedRooms": var_getSelectedRooms,
    };
    var_getSelectedElements_1.type = BasicType.Function;
    var_getSelectedElements_1.name = "getSelectedElements";
    var_getSelectedElements_1.varying = false;
    var_getSelectedElements_1.keepArgsHandle = false;
    var_getSelectedElements_1.args = [];
    var_getSelectedElements_1.return = var_ElementId_Array;
    var_setSelectedElements.type = BasicType.Function;
    var_setSelectedElements.name = "setSelectedElements";
    var_setSelectedElements.varying = false;
    var_setSelectedElements.keepArgsHandle = true;
    var_setSelectedElements.args = [var_ElementId_Array];
    var_setSelectedElements.return = var_ElementId_Array;
    var_getSelectedRooms.type = BasicType.Function;
    var_getSelectedRooms.name = "getSelectedRooms";
    var_getSelectedRooms.varying = false;
    var_getSelectedRooms.keepArgsHandle = false;
    var_getSelectedRooms.args = [];
    var_getSelectedRooms.return = var_getSelectedRooms_objectLiteral_Array;
    var_getSelectedRooms_objectLiteral_Array.type = BasicType.Array;
    var_getSelectedRooms_objectLiteral_Array.value = var_getSelectedRooms_objectLiteral;
    var_getSelectedRooms_objectLiteral.type = BasicType.Object;
    var_getSelectedRooms_objectLiteral.properties = {
        "roomId": var_stringType,
        "roomName": var_stringType,
    };
    var_Platform.type = BasicType.Object;
    var_Platform.properties = {
        "enterCustomModeAsync": var_enterCustomModeAsync,
    };
    var_enterCustomModeAsync.type = BasicType.Function;
    var_enterCustomModeAsync.name = "enterCustomModeAsync";
    var_enterCustomModeAsync.varying = false;
    var_enterCustomModeAsync.keepArgsHandle = true;
    var_enterCustomModeAsync.args = [var_stringType];
    var_enterCustomModeAsync.return = var_undefinedType_Promise;
    var_Number3.properties = Object.assign({}, var_Number2.properties, var_Number3.properties);
    var_IGetFittingDataByAuxiliaryOption.properties = Object.assign({}, var_IGetFittingDataByAuxiliaryBaseOption.properties, var_IGetFittingDataByAuxiliaryOption.properties);
    var_IBatchGetFittingDataByAuxiliaryOption.properties = Object.assign({}, var_IGetFittingDataByAuxiliaryBaseOption.properties, var_IBatchGetFittingDataByAuxiliaryOption.properties);
    var_ISaveFittingDesignOption.properties = Object.assign({}, var_IFittingDesignOption.properties, var_ISaveFittingDesignOption.properties);
    var_ICreateCustomModelSimpleOption.properties = Object.assign({}, var_ICustomModelSimpleChangeable.properties, var_ICreateCustomModelSimpleOption.properties);
    var_IUpdateCustomModelSimpleOption.properties = Object.assign({}, var_ICustomModelSimpleChangeable.properties, var_IUpdateCustomModelSimpleOption.properties);
    var_IOrderData.properties = Object.assign({}, var_IOrderBaseInfo.properties, var_ICustomerBaseInfo.properties, var_IOrderData.properties);
    var_IOrderRemarks.properties = Object.assign({}, var_IGetOrderRemarksResult.properties, var_IOrderRemarks.properties);
    var_IOrderAttachments.properties = Object.assign({}, var_IOrderAttachment.properties, var_IOrderAttachments.properties);
    var_IAuditOrderRemarks.properties = Object.assign({}, var_IOrderRemarks.properties, var_IAuditOrderRemarks.properties);
    var_IGetCustomerListOption.properties = Object.assign({}, var_IPaginationQuery.properties, var_IGetCustomerListOption.properties);
    var_IUpdateOrderModel.properties = Object.assign({}, var_IOrderModel.properties, var_IUpdateOrderModel.properties);
    var_IUpdateCustomerOption.properties = Object.assign({}, var_ICustomerBaseInfo.properties, var_IUpdateCustomerOption.properties);
    var_IBomPlankLayoutProcessAttribute.properties = Object.assign({}, var_IBomRawPlankMeta.properties, var_IBomPlankLayoutProcessAttribute.properties);
    var_UpdateProductsOption.properties = Object.assign({}, var_ProductCodeInOrder.properties, var_UpdateProductsOption.properties);
    
for (var k in var_IStorageGetKeysResult) {
    if (k !== 'type') delete var_IStorageGetKeysResult[k];
}
                        
    var_IStorageGetKeysResult.type = BasicType.Unknown;
    
    return var_sourceFile;
};
