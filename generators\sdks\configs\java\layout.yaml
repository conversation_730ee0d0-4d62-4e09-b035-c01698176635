'!include': '../../shared/shared-data-mappings.yaml'
generatorName: "java"
inputSpec: "specifications/services/layout/openapi.yaml"
outputDir: "build/sdks/layout/java"
templateDir: "generators/sdks/templates/java"
additionalProperties:
  groupId: "com.manycoreapis"
  artifactId: "layout-rest-client"
  artifactVersion: "0.0.1"
  modelPackage: "com.manycore.layout.client.model"
  apiPackage: "com.manycore.layout.client.api"
  invokerPackage: "com.manycore.layout.client"
  java8: true
  dateLibrary: "java8"
  library: "resttemplate"
  sourceFolder: "src/main/java"
  hideGenerationTimestamp: true
  serializationLibrary: "jackson"
  useRuntimeException: false

    

