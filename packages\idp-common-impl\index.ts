import { once } from 'lodash';
import { FunctionTypeBuilder } from '@qunhe/kls-runtime';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';

type PublicInjections = typeof createVMBindingType extends (injections: infer Injections) => any ? Injections : never;
type InternalInjections = typeof createVMBindingTypeInternal extends (injections: infer Injections) => any ? Injections : never;

function getTypeInjections(internal: true): InternalInjections;
function getTypeInjections(internal: false): PublicInjections;
function getTypeInjections(internal: boolean): InternalInjections | PublicInjections {
    const eventListenerFuncType = new FunctionTypeBuilder()
        .addStringArgument()
        .addFunctionArgument(builder => {
            builder.addUnknownArgument()
        })
        .setKeepArgsHandle(true)
        .build();

    if (!internal) {
        return {
            types: {
                IDP_Events_on: eventListenerFuncType,
                IDP_Events_once: eventListenerFuncType,
                IDP_Events_off: eventListenerFuncType,
            },
        };
    }

    const getURLSearchParamsType = new FunctionTypeBuilder()
        .withUnknownReturn()
        .build();

    return {
        types: {
            IDP_Events_on: eventListenerFuncType,
            IDP_Events_once: eventListenerFuncType,
            IDP_Events_off: eventListenerFuncType,
            IDP_Platform_getURLSearchParams: getURLSearchParamsType,
        },
    };
}

export const getVMBindingType = once(() => createVMBindingType(getTypeInjections(false)));
export const getVMBindingTypeInternal = once(() => createVMBindingTypeInternal(getTypeInjections(true)));

/**
 * 应用模式标识枚举值
 */
export enum AppMode {
    /**
     * 云图模式
     */
    DefaultMode = 'DefaultMode',
    /**
     * 户型模式
     */
    SuperFloorplanMode = 'SuperFloorplanMode',
    /**
     * 定制模式
     */
    CustomMode = 'CustomMode',
    /**
     * 其余模式统称
     */
    UnknownMode = 'UnknownMode',
}

/**
 * 设计对象类型枚举值
 * @description 枚举名与枚举值需要保持相同，以便遍历枚举生成正确的类型
 */
export enum ElementType {
    /** 软装家具 */
    Furniture = 'Furniture',
    /** 软装组合家具 */
    FurnitureGroup = 'FurnitureGroup',
    /** 全屋定制家具 */
    Wardrobe = 'Wardrobe',
    /** 全屋定制家具副本 */
    WardrobeCopy = 'WardrobeCopy',
    /** 厨卫 */
    Cabinet = 'Cabinet',
    /** 厨卫副本 */
    CabinetCopy = 'CabinetCopy',
    /** 门窗 */
    DoorWindow = 'DoorWindow',
    /** 门窗副本 */
    DoorWindowCopy = 'DoorWindowCopy',
    /** 踢脚线 */
    Skirting = 'Skirting',
    /** 硬装铺贴 */
    Paving = 'Paving',
    /** 硬装线条 */
    DecoMolding = 'DecoMolding',
    /** 硬装造型(墙面/地面/顶面) */
    PlanarModelingDesign = 'PlanarModelingDesign',
    /** 硬装灯带 */
    StripLight = 'StripLight',
    /** 硬装灯槽 */
    StripLightPlaster = 'StripLightPlaster',
    /** 硬装自由造型 */
    DecoFreeStyleModel = 'DecoFreeStyleModel',
    /** 参数化吊顶 */
    ParamCeiling = 'ParamCeiling',
    /** 定制组合 */
    CustomGroup = 'CustomGroup',
    /** 模型门窗 */
    ModelDoorWindow = 'ModelDoorWindow',
    /** 模型踢脚线 */
    ModelMolding = 'ModelMolding',
    /** 混组 */
    MixGroup = 'MixGroup',

    /** 户型-墙 */
    Wall = 'Wall',
    /** 户型-洞 */
    Opening = 'Opening',
    /** 户型-房间 */
    Room = 'Room',
    /** 户型-方柱 */
    SquareColumn = 'SquareColumn',
    /** 户型-楼板 */
    Floor = 'Floor',
    /** 户型-梁 */
    Beam = 'Beam',
    /** 户型-门洞 */
    DoorOpening = 'DoorOpening',
    /** 户型-窗洞 */
    WindowOpening = 'WindowOpening',
    /** 户型-房间分割线 */
    RoomSeparator = 'RoomSeparator',
    /** 户型-楼板洞 */
    FloorOpening = 'FloorOpening',
    /** 家具图例 */
    FurnitureLegend = 'FurnitureLegend',
    /** 参数化图例 */
    ParamLegend = 'ParamLegend',
    /** 组合图例 */
    LegendGroup = 'LegendGroup',
    /** 区域 */
    Zone = 'Zone',
    /** 厨卫引用模型 */
    CabinetRef = 'CabinetRef',
    /** 厨卫副本引用模型 */
    CabinetCopyRef = 'CabinetCopyRef',
    /** 全屋引用模型 */
    WardrobeRef = 'WardrobeRef',
    /** 全屋副本引用模型 */
    WardrobeCopyRef = 'WardrobeCopyRef',
    /** 厨卫装配模型 */
    CabinetSnapper = 'CabinetSnapper',
    /** 厨卫副本装配模型 */
    CabinetCopySnapper = 'CabinetCopySnapper',
    /** 全屋装配模型 */
    WardrobeSnapper = 'WardrobeSnapper',
    /** 全屋副本装配模型 */
    WardrobeCopySnapper = 'WardrobeCopySnapper',
    /** 厨卫装配体 */
    CabinetSnapperGroup = 'CabinetSnapperGroup',
    /** 厨卫副本装配体 */
    CabinetCopySnapperGroup = 'CabinetCopySnapperGroup',
    /** 全屋装配体 */
    WardrobeSnapperGroup = 'WardrobeSnapperGroup',
    /** 全屋副本装配体 */
    WardrobeCopySnapperGroup = 'WardrobeCopySnapperGroup',
    /** 通用设计对象 */
    GenericModel = 'GenericModel',
    /** 冻结模型 */
    FrozenModel = 'FrozenModel',
}

/**
 * 设计对象 ID
 */
export interface ElementId {
    /**
     * 实例 ID，即 `KElement.id`
     */
    id: string;
    /** 类型 */
    type: ElementType;
}
