import { once } from 'lodash';
import { AsyncFunctionType, generateEventType } from './custom-types';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { createVMBindingType } from './dist/vmTypePublic';
import {
    InnerSpaceDataTypes,
    KioLog,
    ParamModelLiteBaseType,
    UnknownType,
    deleteTopModelsAsync,
    dragCustomProductPromiseResultType,
    findTopModelsAsync,
    getCustomModelByModelIdAsync,
    newCustomModelByCategoryAsync,
    newCustomModelByProductIdAsync,
    updateCustomModelAsync
} from './src';

const types = {
    UnknownType,
    KioLog,
    ISelectionEvtType: generateEventType(InnerSpaceDataTypes),
    AsyncFunctionType,
    CustomModel: ParamModelLiteBaseType,
    NewCustomModelByCategoryAsync: newCustomModelByCategoryAsync,
    NewCustomModelByProductIdAsync: newCustomModelByProductIdAsync,
    GetCustomModelByModelIdAsync: getCustomModelByModelIdAsync,
    FindTopModelsAsync: findTopModelsAsync,
    UpdateCustomModelAsync: updateCustomModelAsync,
    DeleteTopModelsAsync: deleteTopModelsAsync,
    DragCustomProductPromiseResult: dragCustomProductPromiseResultType
}

export const getVMBindingType = once(() => {
    return createVMBindingType({
        types,
    });
});
export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal({
        types,
    });
});
