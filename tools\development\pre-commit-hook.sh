#!/bin/bash

# API 评审 Pre-commit Hook
# 在提交前自动校验 OpenAPI 规范文件

set -e

echo "🚀 API 评审 Pre-commit Hook 开始执行..."

# 检查是否有 OpenAPI 规范文件的变更
OPENAPI_FILES=$(git diff --cached --name-only | grep -E "openapi/.*\.yaml$" || true)

if [ -z "$OPENAPI_FILES" ]; then
    echo "✅ 没有 OpenAPI 规范文件变更，跳过校验"
    exit 0
fi

echo "📄 检测到 OpenAPI 规范文件变更："
echo "$OPENAPI_FILES"

# 检查必要的工具是否安装
check_tool() {
    local tool=$1
    local install_cmd=$2
    local required=$3  # true/false
    
    if ! command -v "$tool" &> /dev/null; then
        if [ "$required" = "true" ]; then
            echo "❌ $tool 未安装（必需），请运行: $install_cmd"
            return 1
        else
            echo "⚠️  $tool 未安装（可选），建议运行: $install_cmd"
            return 2
        fi
    fi
    echo "✅ $tool 已安装"
    return 0
}

echo "🔧 检查必要工具..."

# Spectral 是必需的
if ! check_tool "spectral" "npm install -g @stoplight/spectral-cli" "true"; then
    echo ""
    echo "❌ Spectral CLI 是必需的工具，无法继续"
    echo "💡 请先安装："
    echo "   npm install -g @stoplight/spectral-cli"
    echo "   或运行: bash scripts/setup-local-env-final.sh"
    exit 1
fi

# 文档生成工具是可选的
DOC_TOOL=""
if check_tool "redoc-cli" "npm install -g redoc-cli" "false"; then
    DOC_TOOL="redoc"
elif check_tool "elements" "npm install -g @skriptfabrik/elements-cli --legacy-peer-deps" "false"; then
    DOC_TOOL="elements"
else
    echo "💡 未安装文档生成工具，将跳过文档生成测试"
    echo "   建议安装: npm install -g redoc-cli"
fi

# 校验每个变更的 OpenAPI 文件
VALIDATION_FAILED=false

for file in $OPENAPI_FILES; do
    echo "🔍 校验文件: $file"
    
    # 使用 Spectral 校验（必需）
    if spectral lint "$file"; then
        echo "✅ $file Spectral 校验通过"
    else
        echo "❌ $file Spectral 校验失败"
        VALIDATION_FAILED=true
    fi
    
    # 尝试生成文档（可选，验证文件格式）
    if [ -n "$DOC_TOOL" ]; then
        temp_output="/tmp/api-doc-$(basename "$file" .yaml).html"
        
        if [ "$DOC_TOOL" = "redoc" ]; then
            if redoc-cli build "$file" --output "$temp_output" > /dev/null 2>&1; then
                echo "✅ $file 文档生成测试通过 (Redoc)"
                rm -f "$temp_output"
            else
                echo "⚠️  $file 文档生成测试失败 (Redoc) - 但不阻塞提交"
            fi
        elif [ "$DOC_TOOL" = "elements" ]; then
            if elements export "$file" > "$temp_output" 2>/dev/null; then
                echo "✅ $file 文档生成测试通过 (Elements)"
                rm -f "$temp_output"
            else
                echo "⚠️  $file 文档生成测试失败 (Elements) - 但不阻塞提交"
            fi
        fi
    else
        echo "⚠️  跳过 $file 文档生成测试（未安装工具）"
    fi
done

if [ "$VALIDATION_FAILED" = true ]; then
    echo ""
    echo "❌ OpenAPI 规范校验失败！"
    echo "请修复上述错误后重新提交。"
    echo ""
    echo "💡 提示："
    echo "  - 查看 Spectral 错误信息并修复"
    echo "  - 确保 OpenAPI 文件格式正确"
    echo "  - 参考文档：docs/quick-start-guide.md"
    exit 1
fi

echo ""
echo "🎉 所有 OpenAPI 规范文件校验通过！"
echo "📋 提交前检查清单："
echo "  ✅ OpenAPI 规范语法正确"
if [ -n "$DOC_TOOL" ]; then
    echo "  ✅ 文档生成测试通过"
else
    echo "  ⚠️  文档生成测试跳过（建议安装 redoc-cli）"
fi
echo ""
echo "📝 记住在 GitLab MR 中："
echo "  1. 使用 API 评审 MR 模板"
echo "  2. 添加适当的标签"
echo "  3. @mention 相关评审人员"
echo ""

exit 0 