---
id: 户型图例管理api
title: "户型图例管理API"
description: "户型图例管理服务REST API"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import ApiLogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: 1.0.0"}
>
</span>

<Export
  url={"/specifications/services/layout/openapi.yaml"}
  proxy={undefined}
>
  
</Export>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"户型图例管理API"}
>
</Heading>



户型图例管理服务REST API

提供图例数据的完整生命周期管理接口，图例是户型设计中用于标识空间元素的核心组件。支持单个和批量操作，包括图例的创建、查询、更新、删除以及复杂的图例组合功能。

**主要功能特性：**
- 单个图例CRUD操作：创建、获取、更新、删除单个图例
- 批量操作支持：高效的批量创建、获取、更新、删除操作
- 分页查询：支持大量图例数据的分页展示和管理
- 图例组合：支持创建图例组合，实现复杂的空间布局
- 异步处理：批量操作采用异步处理机制，确保大数据量操作的性能
- 权限控制：严格的读写权限控制，保障数据安全

**业务场景应用：**
- 户型设计工具中的图例管理
- 空间布局设计和优化
- 图例模板和组合的快速应用
- 户型数据的批量导入导出
- 三维空间定位和图层管理

**技术规范：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：基于设计方案的权限验证
- 异步操作：支持长时间批量操作的异步处理

<div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    群核设计服务开发团队: [<EMAIL>](mailto:<EMAIL>)
  </span><span>
    URL: [https://wiki.manycore.com/design-services](https://wiki.manycore.com/design-services)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://manycore.com/license"}
  >
    群核科技专有许可证
  </a>
</div>
      