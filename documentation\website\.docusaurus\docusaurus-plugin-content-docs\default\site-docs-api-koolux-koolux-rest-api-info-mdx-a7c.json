{"id": "api/koolux/koolux-rest-api", "title": "KooLux REST API", "description": "KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。", "source": "@site/docs/api/koolux/koolux-rest-api.info.mdx", "sourceDirName": "api/koolux", "slug": "/api/koolux/koolux-rest-api", "permalink": "/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "sidebarPosition": 0, "frontMatter": {"id": "koolux-rest-api", "title": "KooLux REST API", "description": "KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。", "sidebar_label": "Introduction", "sidebar_position": 0, "hide_title": true, "custom_edit_url": null}, "sidebar": "kooluxSidebar", "next": {"title": "KooLuxLight", "permalink": "/manycoreapi-demo/0.0.4/docs/api/koolux/koo-lux-light"}}