#!/bin/bash

# 主分支流水线脚本
# 用于主分支的完整 CI/CD 流程
# 包括构建、测试、部署等步骤
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 主流水线配置
PIPELINE_TIMEOUT="${PIPELINE_TIMEOUT:-7200}"  # 2小时超时
DEPLOY_ENVIRONMENT="${DEPLOY_ENVIRONMENT:-staging}"  # staging, production
SKIP_TESTS="${SKIP_TESTS:-false}"
SKIP_DEPLOYMENT="${SKIP_DEPLOYMENT:-false}"

# 执行环境检查
run_environment_check() {
    log_step "1" "环境检查"
    
    local env_script="$SCRIPT_DIR/../core/environment.sh"
    
    if [ ! -f "$env_script" ]; then
        log_error "环境检查脚本不存在: $env_script"
        return 1
    fi
    
    log_info "执行环境检查..."
    if "$env_script"; then
        log_success "环境检查通过"
        increment_counter "env_check_success"
        return 0
    else
        log_error "环境检查失败"
        increment_counter "env_check_failed"
        return 1
    fi
}

# 执行 API 变更检测
run_api_change_detection() {
    log_step "2" "API 变更检测"
    
    local api_changes_script="$SCRIPT_DIR/../core/api-changes.sh"
    
    if [ ! -f "$api_changes_script" ]; then
        log_error "API 变更检测脚本不存在: $api_changes_script"
        return 1
    fi
    
    log_info "执行 API 变更检测..."
    if source "$api_changes_script"; then
        log_success "API 变更检测完成"
        
        # 显示检测结果
        log_info "API 变更检测结果:"
        echo "  HAS_API_CHANGES: ${HAS_API_CHANGES:-false}"
        echo "  CHANGED_API_FILES: ${CHANGED_API_FILES:-'(无)'}"
        echo "  CHANGED_SERVICES: ${CHANGED_SERVICES:-'(无)'}"
        
        increment_counter "api_detection_success"
        return 0
    else
        log_error "API 变更检测失败"
        increment_counter "api_detection_failed"
        return 1
    fi
}

# 执行 OpenAPI 验证
run_openapi_validation() {
    log_step "3" "OpenAPI 验证"
    
    local validation_script="$SCRIPT_DIR/../core/validation.sh"
    
    if [ ! -f "$validation_script" ]; then
        log_error "OpenAPI 验证脚本不存在: $validation_script"
        return 1
    fi
    
    log_info "执行 OpenAPI 验证..."
    if "$validation_script" "all"; then
        log_success "OpenAPI 验证通过"
        increment_counter "validation_success"
        return 0
    else
        log_error "OpenAPI 验证失败"
        increment_counter "validation_failed"
        return 1
    fi
}

# 执行完整 SDK 流程
run_full_sdk_pipeline() {
    log_step "4" "完整 SDK 流程"
    
    local sdk_dir="$SCRIPT_DIR/../sdk"
    local overall_status=0
    
    # SDK 生成
    log_info "执行 SDK 生成..."
    if [ -f "$sdk_dir/generate.sh" ]; then
        if "$sdk_dir/generate.sh" "all"; then
            log_success "SDK 生成完成"
            increment_counter "sdk_generate_success"
        else
            log_error "SDK 生成失败"
            increment_counter "sdk_generate_failed"
            overall_status=1
        fi
    else
        log_error "SDK 生成脚本不存在: $sdk_dir/generate.sh"
        overall_status=1
    fi
    
    # SDK 构建
    if [ $overall_status -eq 0 ]; then
        log_info "执行 SDK 构建..."
        if [ -f "$sdk_dir/build.sh" ]; then
            if "$sdk_dir/build.sh" "all"; then
                log_success "SDK 构建完成"
                increment_counter "sdk_build_success"
            else
                log_error "SDK 构建失败"
                increment_counter "sdk_build_failed"
                overall_status=1
            fi
        else
            log_error "SDK 构建脚本不存在: $sdk_dir/build.sh"
            overall_status=1
        fi
    fi
    
    # SDK 测试
    if [ $overall_status -eq 0 ] && [ "$SKIP_TESTS" != "true" ]; then
        log_info "执行 SDK 测试..."
        if [ -f "$sdk_dir/test.sh" ]; then
            if "$sdk_dir/test.sh" "all"; then
                log_success "SDK 测试完成"
                increment_counter "sdk_test_success"
            else
                log_error "SDK 测试失败"
                increment_counter "sdk_test_failed"
                overall_status=1
            fi
        else
            log_warning "SDK 测试脚本不存在，跳过测试"
        fi
    elif [ "$SKIP_TESTS" = "true" ]; then
        log_info "跳过 SDK 测试 (SKIP_TESTS=true)"
    fi
    
    return $overall_status
}

# 执行 SDK 部署
run_sdk_deployment() {
    log_step "5" "SDK 部署"
    
    if [ "$SKIP_DEPLOYMENT" = "true" ]; then
        log_info "跳过 SDK 部署 (SKIP_DEPLOYMENT=true)"
        return 0
    fi
    
    local sdk_dir="$SCRIPT_DIR/../sdk"
    local deploy_type="snapshots"
    
    # 根据环境确定部署类型
    case "$DEPLOY_ENVIRONMENT" in
        "production")
            deploy_type="releases"
            ;;
        "staging")
            deploy_type="snapshots"
            ;;
        *)
            log_warning "未知的部署环境: $DEPLOY_ENVIRONMENT，使用 snapshots"
            deploy_type="snapshots"
            ;;
    esac
    
    log_info "执行 SDK 部署 ($deploy_type)..."
    if [ -f "$sdk_dir/deploy.sh" ]; then
        if "$sdk_dir/deploy.sh" "all" "$deploy_type"; then
            log_success "SDK 部署完成"
            increment_counter "sdk_deploy_success"
            return 0
        else
            log_error "SDK 部署失败"
            increment_counter "sdk_deploy_failed"
            return 1
        fi
    else
        log_error "SDK 部署脚本不存在: $sdk_dir/deploy.sh"
        return 1
    fi
}

# 执行文档构建和部署
run_docs_deployment() {
    log_step "6" "文档构建和部署"
    
    local docs_dir="$SCRIPT_DIR/../docs"
    
    # 文档部署
    log_info "执行文档部署..."
    if [ -f "$docs_dir/deploy.sh" ]; then
        if "$docs_dir/deploy.sh"; then
            log_success "文档部署完成"
            increment_counter "docs_deploy_success"
            return 0
        else
            log_error "文档部署失败"
            increment_counter "docs_deploy_failed"
            return 1
        fi
    else
        log_error "文档部署脚本不存在: $docs_dir/deploy.sh"
        return 1
    fi
}

# 执行清理工作
run_cleanup() {
    log_step "7" "清理工作"
    
    log_info "执行流水线清理..."
    
    # 清理临时文件
    local temp_dirs=("tmp" "temp" ".tmp")
    for dir in "${temp_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_info "清理临时目录: $dir"
            rm -rf "$dir" || true
        fi
    done
    
    # 清理构建缓存（可选）
    if [ "${CLEAN_BUILD_CACHE:-false}" = "true" ]; then
        log_info "清理构建缓存..."
        
        # Maven 缓存
        if [ -d "$HOME/.m2/repository" ]; then
            find "$HOME/.m2/repository" -name "*.tmp" -delete 2>/dev/null || true
        fi
        
        # npm 缓存
        if command -v npm >/dev/null 2>&1; then
            npm cache clean --force >/dev/null 2>&1 || true
        fi
    fi
    
    log_success "清理工作完成"
    increment_counter "cleanup_success"
    return 0
}

# 发送通知
send_notifications() {
    log_step "8" "发送通知"
    
    # 检查是否配置了通知
    if [ -z "$NOTIFICATION_WEBHOOK" ] && [ -z "$SLACK_WEBHOOK" ]; then
        log_info "未配置通知，跳过通知发送"
        return 0
    fi
    
    # 收集流水线结果
    local env_check=$(get_counter "env_check_success")
    local validation=$(get_counter "validation_success")
    local sdk_generate=$(get_counter "sdk_generate_success")
    local sdk_build=$(get_counter "sdk_build_success")
    local sdk_test=$(get_counter "sdk_test_success")
    local sdk_deploy=$(get_counter "sdk_deploy_success")
    local docs_deploy=$(get_counter "docs_deploy_success")
    
    # 构建通知消息
    local status="成功"
    local color="good"
    
    if [ $env_check -eq 0 ] || [ $validation -eq 0 ] || [ $sdk_generate -eq 0 ] || [ $sdk_build -eq 0 ] || [ $sdk_deploy -eq 0 ] || [ $docs_deploy -eq 0 ]; then
        status="失败"
        color="danger"
    fi
    
    local message="## 🚀 主分支流水线执行结果\n\n"
    message+="**状态**: $status\n"
    message+="**分支**: ${CI_COMMIT_BRANCH:-main}\n"
    message+="**提交**: ${CI_COMMIT_SHORT_SHA:-unknown}\n"
    message+="**环境**: $DEPLOY_ENVIRONMENT\n\n"
    message+="### 执行结果\n"
    message+="- 环境检查: $([ $env_check -gt 0 ] && echo "✅" || echo "❌")\n"
    message+="- OpenAPI 验证: $([ $validation -gt 0 ] && echo "✅" || echo "❌")\n"
    message+="- SDK 生成: $([ $sdk_generate -gt 0 ] && echo "✅" || echo "❌")\n"
    message+="- SDK 构建: $([ $sdk_build -gt 0 ] && echo "✅" || echo "❌")\n"
    message+="- SDK 测试: $([ $sdk_test -gt 0 ] && echo "✅" || echo "⚠️")\n"
    message+="- SDK 部署: $([ $sdk_deploy -gt 0 ] && echo "✅" || echo "❌")\n"
    message+="- 文档部署: $([ $docs_deploy -gt 0 ] && echo "✅" || echo "❌")\n"
    
    # 发送通知（这里只是示例，实际实现需要根据具体的通知系统）
    log_info "发送流水线通知..."
    log_info "通知内容: $message"
    
    # 实际的通知发送逻辑应该在这里实现
    # 例如: curl -X POST "$SLACK_WEBHOOK" -d "{'text': '$message'}"
    
    log_success "通知发送完成"
    increment_counter "notification_sent"
    return 0
}

# 显示主流水线统计
show_main_pipeline_stats() {
    log_step "9" "显示主流水线统计"
    
    echo ""
    log_info "🚀 主分支流水线执行统计:"
    
    local env_check=$(get_counter "env_check_success")
    local validation=$(get_counter "validation_success")
    local sdk_generate=$(get_counter "sdk_generate_success")
    local sdk_build=$(get_counter "sdk_build_success")
    local sdk_test=$(get_counter "sdk_test_success")
    local sdk_deploy=$(get_counter "sdk_deploy_success")
    local docs_deploy=$(get_counter "docs_deploy_success")
    local cleanup=$(get_counter "cleanup_success")
    local notification=$(get_counter "notification_sent")
    
    echo "  部署环境: $DEPLOY_ENVIRONMENT"
    echo "  环境检查: $([ $env_check -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  OpenAPI 验证: $([ $validation -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  SDK 生成: $([ $sdk_generate -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  SDK 构建: $([ $sdk_build -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  SDK 测试: $([ $sdk_test -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    echo "  SDK 部署: $([ $sdk_deploy -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  文档部署: $([ $docs_deploy -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  清理工作: $([ $cleanup -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  通知发送: $([ $notification -gt 0 ] && echo "✅ 发送" || echo "⚠️ 跳过")"
    
    show_stats "主流水线统计"
}

# 主函数
main() {
    init_script "主流水线" "执行主分支的完整 CI/CD 流程"
    
    # 设置超时
    if command -v timeout >/dev/null 2>&1; then
        log_info "设置流水线超时: ${PIPELINE_TIMEOUT}s"
    fi
    
    log_info "部署环境: $DEPLOY_ENVIRONMENT"
    log_info "跳过测试: $SKIP_TESTS"
    log_info "跳过部署: $SKIP_DEPLOYMENT"
    
    local pipeline_status=0
    local start_time=$(get_timestamp)
    
    # 执行主流水线步骤
    if ! run_environment_check; then
        pipeline_status=1
    elif ! run_api_change_detection; then
        pipeline_status=1
    elif ! run_openapi_validation; then
        pipeline_status=1
    elif ! run_full_sdk_pipeline; then
        pipeline_status=1
    elif ! run_sdk_deployment; then
        pipeline_status=1
    elif ! run_docs_deployment; then
        pipeline_status=1
    fi
    
    # 清理工作（不影响整体结果）
    run_cleanup || true
    
    # 发送通知（不影响整体结果）
    send_notifications || true
    
    local end_time=$(get_timestamp)
    local total_duration=$((end_time - start_time))
    
    show_main_pipeline_stats
    
    log_info "流水线总耗时: ${total_duration}s"
    
    if [ $pipeline_status -eq 0 ]; then
        log_success "🎉 主流水线执行完成！"
        finish_script "主流水线" "true"
        return 0
    else
        log_error "❌ 主流水线执行失败，请检查错误信息"
        finish_script "主流水线" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "主流水线模块已加载 ✅"
