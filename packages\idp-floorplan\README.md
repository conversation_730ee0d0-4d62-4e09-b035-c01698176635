# 小程序接口声明库（模板）

# 目录结构

- `index.d.ts` - 为最终的整合导出模块
- `api.d.ts` - 主要的类型声明文件，此文件的**所有导出**会作为该 package 提供的小程序接口声明
    > 当前内装小程序只提供了一个全局的 `IDP` 类型，因而 `api.d.ts` 中也应该仅导出一个 `IDP` 声明

以下为自动生成的文件（请不要手动修改）：

- `global-decl-public/` - 用于集成到 `@qunhe/idp-sdk` 中提供三方接口类型声明
- `global-decl-internal/` - 用于集成到 `@qunhe/idp-sdk-internal` 中提供二方接口类型声明

# 户型小程序

## 使用说明
### 接口提供方
户型小程序接口部分需要由户型实现
户型模块需要依赖@qunhe/idp-floorplan-impl并实现相关接口

### 接口使用方
二方、三方需要依赖@qunhe/idp-floorplan获取接口类型声明

## 开发说明
### 路径
/global-decl-internal 为二方接口声明
/global-decl-public 为三方接口声明

### 版本说明
0.1.x和0.2.x版本为小程序平台规范尚未建立时的版本，已废弃
1.0.x版本开始为正式版本

