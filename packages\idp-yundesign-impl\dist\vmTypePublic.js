var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_StartDragProductPromiseResult = injections.types["StartDragProductPromiseResult"];
    var var_injection_KPoint3d = injections.packages["@qunhe/math-apaas-api"]["KPoint3d"];
    var var_injection_KVector3d = injections.packages["@qunhe/math-apaas-api"]["KVector3d"];
    var var_injection_PromiseResultWithUuid = injections.types["PromiseResultWithUuid"];
    var var_injection_PromiseResult = injections.types["PromiseResult"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Interaction = {};
    var var_startDragProductAsync = {};
    var var_stringType = {};
    var var_injection_StartDragProductPromiseResult_Promise = {};
    var var_injection_StartDragProductPromiseResult_Promise_then = {};
    var var_injection_StartDragProductPromiseResult_Promise_then_onresolve = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_DB = {};
    var var_Types = {};
    var var_ModelDoorWindowType = {};
    var var_numberType = {};
    var var_Methods = {};
    var var_createFurniture = {};
    var var_FurnitureCreateInfo = {};
    var var_injection_PromiseResultWithUuid_Promise = {};
    var var_injection_PromiseResultWithUuid_Promise_then = {};
    var var_injection_PromiseResultWithUuid_Promise_then_onresolve = {};
    var var_deleteFurniture = {};
    var var_ElementId = {};
    var var_booleanType = {};
    var var_getFurniture = {};
    var var_Furniture = {};
    var var_getAllModelDoorWindowList = {};
    var var_ModelDoorWindow_Array = {};
    var var_ModelDoorWindow = {};
    var var_stringType_Array = {};
    var var_getModelDoorWindow = {};
    var var_deleteModelDoorWindow = {};
    var var_createModelDoorWindowAsync = {};
    var var_ModelDoorWindowCreateInfo = {};
    var var_injection_PromiseResult_Promise = {};
    var var_injection_PromiseResult_Promise_then = {};
    var var_injection_PromiseResult_Promise_then_onresolve = {};
    var var_UI = {};
    var var_Platform = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_StartDragProductPromiseResult, exportName: "StartDragProductPromiseResult" },
        { value: var_injection_KPoint3d, packageName: "@qunhe/math-apaas-api", exportName: "KPoint3d" },
        { value: var_injection_KVector3d, packageName: "@qunhe/math-apaas-api", exportName: "KVector3d" },
        { value: var_injection_PromiseResultWithUuid, exportName: "PromiseResultWithUuid" },
        { value: var_injection_PromiseResult, exportName: "PromiseResult" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Interaction": var_Interaction,
        "DB": var_DB,
        "UI": var_UI,
        "Platform": var_Platform,
    };
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "startDragProductAsync": var_startDragProductAsync,
    };
    var_startDragProductAsync.type = BasicType.Function;
    var_startDragProductAsync.name = "startDragProductAsync";
    var_startDragProductAsync.varying = false;
    var_startDragProductAsync.keepArgsHandle = false;
    var_startDragProductAsync.args = [var_stringType];
    var_startDragProductAsync.return = var_injection_StartDragProductPromiseResult_Promise;
    var_stringType.type = BasicType.String;
    var_injection_StartDragProductPromiseResult_Promise.type = BasicType.Object;
    var_injection_StartDragProductPromiseResult_Promise.properties = {
        "then": var_injection_StartDragProductPromiseResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_StartDragProductPromiseResult_Promise_then.type = BasicType.Function;
    var_injection_StartDragProductPromiseResult_Promise_then.name = "";
    var_injection_StartDragProductPromiseResult_Promise_then.varying = false;
    var_injection_StartDragProductPromiseResult_Promise_then.keepArgsHandle = true;
    var_injection_StartDragProductPromiseResult_Promise_then.args = [var_injection_StartDragProductPromiseResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_StartDragProductPromiseResult_Promise_then.return = var_undefinedType;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.type = BasicType.Function;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.name = "";
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.varying = false;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.args = [var_injection_StartDragProductPromiseResult];
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
        "ModelDoorWindowType": var_ModelDoorWindowType,
    };
    var_ModelDoorWindowType.type = BasicType.Object;
    var_ModelDoorWindowType.properties = {
        "Window": var_numberType,
        "FrenchWindow": var_numberType,
        "BayWindow": var_numberType,
        "Door": var_numberType,
        "DoubleDoor": var_numberType,
        "SlidingDoor": var_numberType,
    };
    var_numberType.type = BasicType.Number;
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "createFurniture": var_createFurniture,
        "deleteFurniture": var_deleteFurniture,
        "getFurniture": var_getFurniture,
        "getAllModelDoorWindowList": var_getAllModelDoorWindowList,
        "getModelDoorWindow": var_getModelDoorWindow,
        "deleteModelDoorWindow": var_deleteModelDoorWindow,
        "createModelDoorWindowAsync": var_createModelDoorWindowAsync,
    };
    var_createFurniture.type = BasicType.Function;
    var_createFurniture.name = "createFurniture";
    var_createFurniture.varying = false;
    var_createFurniture.keepArgsHandle = false;
    var_createFurniture.args = [var_FurnitureCreateInfo];
    var_createFurniture.return = var_injection_PromiseResultWithUuid_Promise;
    var_FurnitureCreateInfo.type = BasicType.Object;
    var_FurnitureCreateInfo.properties = {
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_injection_KPoint3d,
        "scale": var_injection_KVector3d,
    };
    var_injection_PromiseResultWithUuid_Promise.type = BasicType.Object;
    var_injection_PromiseResultWithUuid_Promise.properties = {
        "then": var_injection_PromiseResultWithUuid_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_PromiseResultWithUuid_Promise_then.type = BasicType.Function;
    var_injection_PromiseResultWithUuid_Promise_then.name = "";
    var_injection_PromiseResultWithUuid_Promise_then.varying = false;
    var_injection_PromiseResultWithUuid_Promise_then.keepArgsHandle = true;
    var_injection_PromiseResultWithUuid_Promise_then.args = [var_injection_PromiseResultWithUuid_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_PromiseResultWithUuid_Promise_then.return = var_undefinedType;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.type = BasicType.Function;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.name = "";
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.varying = false;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.args = [var_injection_PromiseResultWithUuid];
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.return = var_undefinedType;
    var_deleteFurniture.type = BasicType.Function;
    var_deleteFurniture.name = "deleteFurniture";
    var_deleteFurniture.varying = false;
    var_deleteFurniture.keepArgsHandle = false;
    var_deleteFurniture.args = [var_ElementId];
    var_deleteFurniture.return = var_booleanType;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_booleanType.type = BasicType.Boolean;
    var_getFurniture.type = BasicType.Function;
    var_getFurniture.name = "getFurniture";
    var_getFurniture.varying = false;
    var_getFurniture.keepArgsHandle = false;
    var_getFurniture.args = [var_ElementId];
    var_getFurniture.return = var_Furniture;
    var_Furniture.type = BasicType.Object;
    var_Furniture.properties = {
        "elementId": var_ElementId,
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_injection_KPoint3d,
        "size": var_injection_KPoint3d,
        "scale": var_injection_KPoint3d,
    };
    var_getAllModelDoorWindowList.type = BasicType.Function;
    var_getAllModelDoorWindowList.name = "getAllModelDoorWindowList";
    var_getAllModelDoorWindowList.varying = false;
    var_getAllModelDoorWindowList.keepArgsHandle = false;
    var_getAllModelDoorWindowList.args = [];
    var_getAllModelDoorWindowList.return = var_ModelDoorWindow_Array;
    var_ModelDoorWindow_Array.type = BasicType.Array;
    var_ModelDoorWindow_Array.value = var_ModelDoorWindow;
    var_ModelDoorWindow.type = BasicType.Object;
    var_ModelDoorWindow.properties = {
        "elementId": var_ElementId,
        "productId": var_stringType,
        "type": var_numberType,
        "position": var_injection_KPoint3d,
        "rotation": var_numberType,
        "size": var_injection_KPoint3d,
        "scale": var_injection_KPoint3d,
        "handleFlipped": var_booleanType,
        "facingFlipped": var_booleanType,
        "hostIds": var_stringType_Array,
        "openingId": var_stringType,
    };
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_getModelDoorWindow.type = BasicType.Function;
    var_getModelDoorWindow.name = "getModelDoorWindow";
    var_getModelDoorWindow.varying = false;
    var_getModelDoorWindow.keepArgsHandle = false;
    var_getModelDoorWindow.args = [var_ElementId];
    var_getModelDoorWindow.return = var_ModelDoorWindow;
    var_deleteModelDoorWindow.type = BasicType.Function;
    var_deleteModelDoorWindow.name = "deleteModelDoorWindow";
    var_deleteModelDoorWindow.varying = false;
    var_deleteModelDoorWindow.keepArgsHandle = false;
    var_deleteModelDoorWindow.args = [var_ElementId];
    var_deleteModelDoorWindow.return = var_booleanType;
    var_createModelDoorWindowAsync.type = BasicType.Function;
    var_createModelDoorWindowAsync.name = "createModelDoorWindowAsync";
    var_createModelDoorWindowAsync.varying = false;
    var_createModelDoorWindowAsync.keepArgsHandle = false;
    var_createModelDoorWindowAsync.args = [var_ModelDoorWindowCreateInfo];
    var_createModelDoorWindowAsync.return = var_injection_PromiseResult_Promise;
    var_ModelDoorWindowCreateInfo.type = BasicType.Object;
    var_ModelDoorWindowCreateInfo.properties = {
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_numberType,
        "size": var_injection_KPoint3d,
        "handleFlipped": var_booleanType,
        "facingFlipped": var_booleanType,
    };
    var_injection_PromiseResult_Promise.type = BasicType.Object;
    var_injection_PromiseResult_Promise.properties = {
        "then": var_injection_PromiseResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_PromiseResult_Promise_then.type = BasicType.Function;
    var_injection_PromiseResult_Promise_then.name = "";
    var_injection_PromiseResult_Promise_then.varying = false;
    var_injection_PromiseResult_Promise_then.keepArgsHandle = true;
    var_injection_PromiseResult_Promise_then.args = [var_injection_PromiseResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_PromiseResult_Promise_then.return = var_undefinedType;
    var_injection_PromiseResult_Promise_then_onresolve.type = BasicType.Function;
    var_injection_PromiseResult_Promise_then_onresolve.name = "";
    var_injection_PromiseResult_Promise_then_onresolve.varying = false;
    var_injection_PromiseResult_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_PromiseResult_Promise_then_onresolve.args = [var_injection_PromiseResult];
    var_injection_PromiseResult_Promise_then_onresolve.return = var_undefinedType;
    var_UI.type = BasicType.Object;
    var_UI.properties = {
    };
    var_Platform.type = BasicType.Object;
    var_Platform.properties = {
    };
    
    return var_sourceFile;
};
