{"id": "api/layout/户型图例管理api", "title": "户型图例管理API", "description": "户型图例管理服务REST API", "source": "@site/docs/api/layout/户型图例管理api.info.mdx", "sourceDirName": "api/layout", "slug": "/api/layout/户型图例管理api", "permalink": "/manycoreapi-demo/0.0.4/docs/api/layout/户型图例管理api", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "sidebarPosition": 0, "frontMatter": {"id": "户型图例管理api", "title": "户型图例管理API", "description": "户型图例管理服务REST API", "sidebar_label": "Introduction", "sidebar_position": 0, "hide_title": true, "custom_edit_url": null}, "sidebar": "layoutSidebar", "next": {"title": "图例管理接口", "permalink": "/manycoreapi-demo/0.0.4/docs/api/layout/图例管理接口"}}