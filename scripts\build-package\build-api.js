// 编译 package 使其合法，比如生成 vmType 生成函数、全局类型声明定义

// 当前不再需要编译 API package
process.exit(0);

const child_process = require('child_process');
const path = require('path');
const fse = require('fs-extra');

const PackageDir = process.cwd();
const ApiDtsFile = path.resolve(PackageDir, 'api.d.ts');

function execSync(cmd) {
    console.log(`$ ${cmd}`);
    child_process.execSync(cmd, { stdio: 'inherit' });
}

function cwdRelative(p) {
    return path.relative(process.cwd(), p) || '.';
}

// 清空旧文件
fse.removeSync(path.resolve(PackageDir, 'build'));
fse.removeSync(path.resolve(PackageDir, 'global-decl-public'));
fse.removeSync(path.resolve(PackageDir, 'global-decl-internal'));

// 首先生成 public 版 api
const PublicApiDtsFile = path.resolve(path.dirname(ApiDtsFile), 'api-public.d.ts');
const TempApiExtractorJsonFile = path.resolve(path.dirname(PublicApiDtsFile), 'api-extractor.json');
fse.outputFileSync(
    TempApiExtractorJsonFile,
    fse.readFileSync(path.resolve(__dirname, 'public-api-extractor.json'))
        .toString()
        .replace('{mainEntryPointFilePath}', path.relative(path.dirname(TempApiExtractorJsonFile), ApiDtsFile).replace(/\\/g, '/'))
        .replace('{publicTrimmedFilePath}', path.relative(path.dirname(TempApiExtractorJsonFile), PublicApiDtsFile).replace(/\\/g, '/'))
);

const apaasTypeGeneratorScript = path.resolve(__dirname, '../apaas-type-generator.js');
const dtsRollupScript = path.resolve(__dirname, '../dts-rollup.js');
execSync(`node ${dtsRollupScript} api-extractor run`);

function buildForApi(inputApiDtsFile, outputGlobalDeclareDir) {
    // 生成全局类型声明文件
    execSync(`node ${apaasTypeGeneratorScript} generate-global-declare-dts -d . --api-dts-file ${cwdRelative(inputApiDtsFile)} --output-dir ${cwdRelative(outputGlobalDeclareDir)}`);
}

// 分别生成正式版和internal版接口信息
buildForApi(PublicApiDtsFile, path.resolve(PackageDir, 'global-decl-public'));
buildForApi(ApiDtsFile, path.resolve(PackageDir, 'global-decl-internal'));

// 销毁临时创建的文件
// fse.removeSync(PublicApiDtsFile);
fse.removeSync(TempApiExtractorJsonFile);
