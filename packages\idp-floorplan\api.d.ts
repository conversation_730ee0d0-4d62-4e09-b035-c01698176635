// TODO: put yor non exported types here
import { KBoundedCurve2d, KGeomFace2d, KBoundedCurve3d, KFace3d, KPoint2d, KPlane, KVector3d, KVector2d } from '@qunhe/math-apaas-api'
import { CadImportParams, FloorPlanCadInfo, FloorPlanLayerInfo } from './type';
import { IDP as IDPCommon } from '@qunhe/idp-common';

/**
 * Cad导入类型
 * @internal
 */
declare enum CadImportType {
    IdentifyFloorPlan = 'IdentifyFloorPlan',
    OnlyBaseMap = 'OnlyBaseMap',
}

/**
 * @internal
 */
type DrawWallType = 'line' | 'arc' | 'rect';

/**
 * @internal
 */
type DrawColumnType = 'square' | 'circle';

declare namespace IDP {
    namespace DB {
        // Types
        namespace Types {
            /**
             * @internal
             */
            enum CourseType {
                OTHER = 'OTHER', // 其他
                SURFACE = 'SURFACE', // 面层
                COMBINED = 'COMBINED', // 结合层
                TROWELING = 'TROWELING', // 找平层
                ISOLATING = 'ISOLATING', // 隔离层
                DAMP = 'DAMP', // 防水层/防潮层
                FILLER = 'FILLER', // 填充层
                INSULATION = 'INSULATION', // 保温层
            }

            interface GrepFace {
                /**
                 * 建筑面Id
                 */
                readonly id: string;

                /**
                 * 3D几何
                 */
                readonly geometry: KFace3d;

                /**
                 * 参与围成的房间
                 */
                readonly roomIds: IDPCommon.DB.Types.ElementId[];
            }

            /**
             * @internal
             */
            enum StructuralWallUsage {
                NonBearing = 'NonBearing', // 非承重墙
                Bearing = 'Bearing', // 承重墙
            }

            /**
             * 房间分割线类型
             * @internal
             */
            enum RoomSeparatorType {
                OnlyFloor = 'OnlyFloor', // 仅地面
                AllTheWay = 'AllTheWay' // 全部
            }

            interface Wall {
                readonly id: IDPCommon.DB.Types.ElementId;

                /**
                 * @internal
                 */
                readonly location: KBoundedCurve2d;

                /**
                 * @internal
                 */
                readonly thickness: number;

                /**
                 * @internal
                 */
                readonly height: number;

                /**
                 * @internal
                 */
                readonly structuralUsage: StructuralWallUsage;

                readonly profile2d: KGeomFace2d[];

                readonly geometry3d: GrepFace[];
            }

            interface Opening {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly hostId: IDPCommon.DB.Types.ElementId;

                /**
                 * @internal
                 */
                readonly contour: KBoundedCurve3d[];

                readonly profile2d: KGeomFace2d[];

                /**
                 * 拱高
                 * @internal
                 */
                readonly archHeight: number;
            }

            interface Room {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly type: number;

                readonly name: string;

                readonly profile2d: KGeomFace2d[];

                /**
                 * @internal
                 */
                readonly hasRoof: boolean;
            }

            /**
             * 方柱
             * @internal
             */
            interface SquareColumn {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly width: number;

                readonly depth: number;

                readonly height: number;
            }

            /**
             * 楼板
             * @internal
             */
            interface Floor {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly profile2d: KGeomFace2d[];
            }

            /**
             * 梁
             * @internal
             */
            interface Beam {
                readonly id: IDPCommon.DB.Types.ElementId;

                /**
                 * @internal
                 */
                readonly location: KBoundedCurve2d;

                /**
                 * @internal
                 */
                readonly thickness: number;

                /**
                 * @internal
                 */
                readonly height: number;

                /**
                 * @internal
                 */
                readonly topElevation: number;
            }

            /**
             * 户型窗洞
             * @internal
             */
            interface WindowOpening {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly subElements: IDPCommon.DB.Types.ElementId[];
            }

            /**
             * 户型门洞
             * @internal
             */
            interface DoorOpening {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly subElements: IDPCommon.DB.Types.ElementId[];
            }

            /**
             * 房间分割线
             * @internal
             */
            interface RoomSeparator {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly type: RoomSeparatorType;
            }

            /**
             * 楼板洞
             * @internal
             */
            interface FloorOpening {
                readonly id: IDPCommon.DB.Types.ElementId;

                readonly depth: number;
            }

            /**
             * 户型CURD API类型
             * @internal
             */
            namespace Floorplan {
                enum DocumentView {
                    /**
                     * 基础视图
                        */
                    BASIC = 'BASIC',
                }

                enum ElementJoint {
                    /**
                     * 斜接
                     */
                    OBLIQUE = 'OBLIQUE',
                    /**
                     * 方接
                     */
                    ABUTMENT = 'ABUTMENT',
                }

                enum OpeningType {
                    /**
                     * 原始洞
                     */
                    OPENING = 'OPENING',
                    /**
                     * 门洞
                     */
                    DOOR = 'DOOR',
                    /**
                     * 窗洞
                     */
                    WINDOW = 'WINDOW',
                    /**
                     * 壁龛洞
                     */
                    NICHE = 'NICHE',
                }

                enum WallType {
                    /**
                     * 普通墙
                     */
                    NORMAL = 'NORMAL',
                    /**
                     * 隔墙
                     */
                    PARTITION = 'PARTITION',
                }

                enum OffgroundType {
                    CEILING = 'CEILING',
                    FLOOR = 'FLOOR',
                }

                enum OpeningSourceType {
                    MODEL_DOOR_WINDOW = 'MODEL_DOOR_WINDOW',
                    CUSTOM_DOOR_WINDOW = 'CUSTOM_DOOR_WINDOW',
                    FLOOR_PLAN = 'FLOOR_PLAN',
                }

                enum ElementType {
                    Wall = 'wall',
                    Beam = 'beam',
                    Room = 'room',
                    Pillar = 'pillar',
                    Floor = 'floor',
                    Opening = 'opening',
                    Chimney = 'chimney',
                    FloorOpening = 'floor_opening',
                    RoomSeparator = 'room_separator',
                }

                enum RoomType {
                    /** 未指定 */
                    UNSPECIFIED = -1,

                    /// 家装
                    /** 整套 */
                    COMPLETE_SET = 0,
                    /** 客厅 */
                    PARLOR = 1,
                    /** 走廊 */
                    CORRIDOR = 2,
                    /** 餐厅 */
                    DINING_ROOM = 3,
                    /** 主卧 */
                    MASTER_BEDROOM = 4,
                    /** 儿童房 */
                    CHILDREN_ROOM = 5,
                    /** 书房 */
                    STUDY = 6,
                    /** 厨房 */
                    KITCHEN = 7,
                    /** 卫生间 */
                    BATHROOM = 8,
                    /** 多功能室 */
                    MULTI_PURPOSE_ROOM = 9,
                    /** 次卧 */
                    SECONDARY_BEDROOM = 10,
                    /** 客卧 */
                    GUEST_BEDROOM = 11,
                    /** 老人房 */
                    ELDERLY_ROOM = 12,
                    /** 阳台 */
                    BALCONY = 13,
                    /** 玄关 */
                    ENTRANCE = 14,
                    /** 卧室 */
                    BEDROOM = 15,
                    /** 门厅 */
                    HALLWAY = 16,
                    /** 自定义 */
                    CUSTOM = 17,
                    /** 储物间 */
                    STORAGE_ROOM = 18,
                    /** 步入式衣柜 */
                    WALK_IN_CLOSET = 19,
                    /** 外景 */
                    EXTERIOR = 20,
                    /** 起居室 */
                    LIVING_ROOM = 21,
                    /** 客餐厅 */
                    LIVING_DINING_ROOM = 22,
                    /** 露台 */
                    TERRACE = 23,
                    /** 影视间 */
                    MOVIE_ROOM = 24,
                    /** 入户花园 */
                    ENTRANCE_GARDEN = 25,
                    /** 门厅/玄关 */
                    HALLWAY_ENTRANCE = 26,
                    /** 洗衣房 */
                    LAUNDRY_ROOM = 27,
                    /** 办公室 */
                    OFFICE = 28,
                    /** 地下室 */
                    BASEMENT = 29,
                    /** 车库 */
                    GARAGE = 30,
                    /** 未命名 */
                    UNNAMED = 31,
                    /** 户外 */
                    OUTDOOR = 32,
                    /** 保姆房 */
                    NANNY_ROOM = 33,
                    /** 健身房 */
                    GYM = 34,
                    /** 花房 */
                    FLOWER_ROOM = 35,
                    /** 楼梯间 */
                    STAIRWELL = 36,
                    /** 景观阳台 */
                    BALCONY_WITH_VIEW = 37,
                    /** 生活阳台 */
                    LIVING_BALCONY = 38,
                    /** 设备平台 */
                    DEVICE_PLATFORM = 39,
                    /** 主卫 */
                    MASTER_BATHROOM = 40,
                    /** 次卫 */
                    SECONDARY_BATHROOM = 41,
                    /** 中厨 */
                    CHINESE_KITCHEN = 42,
                    /** 西厨 */
                    WESTERN_KITCHEN = 43,

                    /// 公装
                    /** 商场专柜 */
                    SHOPPING_MALL_COUNTER = 44,
                    /** 专卖店 */
                    SPECIALTY_STORE = 45,
                    /** 商场店铺 */
                    SHOPPING_MALL_STORE = 46,
                    /** 母婴店 */
                    MATERNITY_AND_BABY_STORE = 47,
                    /** 商超 */
                    SUPERMARKET = 48,
                    /** 珠宝店 */
                    JEWELRY_STORE = 49,
                    /** 服装店 */
                    CLOTHING_STORE = 50,
                    /** 美容美发 */
                    BEAUTY_SALON = 51,
                    /** 数码店 */
                    DIGITAL_STORE = 52,
                    /** 水果店 */
                    FRUIT_SHOP = 53,
                    /** 婚纱店 */
                    BRIDAL_SHOP = 54,
                    /** 烟酒店 */
                    TOBACCO_SHOP = 55,
                    /** 售楼处 */
                    SALES_OFFICE = 56,
                    /** 潮玩店 */
                    FASHION_TOY_STORE = 57,
                    /** 书店 */
                    BOOKSTORE = 58,
                    /** 餐厅 */
                    RESTAURANT = 59,
                    /** 后厨 */
                    BACK_KITCHEN = 60,
                    /** 咖啡厅 */
                    CAFE = 61,
                    /** 饮品店 */
                    BEVERAGE_SHOP = 62,
                    /** 甜品店 */
                    DESSERT_SHOP = 63,
                    /** 火锅店 */
                    HOT_POT_RESTAURANT = 64,
                    /** 烧烤店 */
                    BBQ_RESTAURANT = 65,
                    /** 面包店 */
                    BAKERY = 66,
                    /** 茶馆 */
                    TEA_HOUSE = 67,
                    /** 包厢 */
                    PRIVATE_DINING_ROOM = 68,
                    /** 快餐店 */
                    FAST_FOOD_RESTAURANTS = 69,
                    /** 会展 */
                    EXHIBITION_HALL = 70,
                    /** 展台 */
                    BOOTH = 71,
                    /** 橱窗 */
                    SHOW_WINDOW = 72,
                    /** KTV */
                    KTV = 73,
                    /** 网咖 */
                    INTERNET_CAFE = 74,
                    /** 酒吧 */
                    BAR = 75,
                    /** 电影院 */
                    CINEMA = 76,
                    /** 游乐场 */
                    PLAYGROUND = 77,
                    /** 娱乐室 */
                    ENTERTAINMENT_ROOM = 78,
                    /** 客房 */
                    GUEST_ROOM = 79,
                    /** 酒店前台 */
                    HOTEL_RECEPTION = 80,
                    /** 宴会厅 */
                    BANQUET_HALL = 81,
                    /** 酒店茶水间 */
                    HOTEL_TEA_ROOM = 82,
                    /** 休闲空间 */
                    LEISURE_SPACE = 83,
                    /** 布草间 */
                    LINEN_ROOM = 84,
                    /** SPA桑拿 */
                    SPA = 85,
                    /** 棋牌室 */
                    CHESS_AND_CARD_ROOM = 86,
                    /** 经理室 */
                    MANAGER_OFFICE = 87,
                    /** 办公室 */
                    BUREAU = 88,
                    /** 办公区 */
                    OFFICE_AREA = 89,
                    /** 洽谈室 */
                    NEGOTIATION_ROOM = 90,
                    /** 会议室 */
                    MEETING_ROOM = 91,
                    /** 前台接待区 */
                    RECEPTION_AREA = 92,
                    /** 茶水间 */
                    PANTRY = 93,
                    /** 休闲区 */
                    RECREATION_AREA = 94,
                    /** 大堂前台 */
                    LOBBY_RECEPTION = 95,
                    /** 体育健身 */
                    SPORTS_AND_FITNESS = 96,
                    /** 实验室 */
                    LABORATORY = 97,
                    /** 教室 */
                    CLASSROOM = 98,
                    /** 医院门诊 */
                    HOSPITAL_OUTPATIENT = 99,
                    /** 游泳馆 */
                    SWIMMING_FACILITY = 100,
                    /** 球馆 */
                    ARENA = 101,
                    /** 图书室 */
                    LIBRARY = 102,
                    /** 培训室 */
                    TRAINING_ROOM = 103,
                    /** 剧院 */
                    THEATER = 104,
                    /** 阅览室 */
                    READING_ROOM = 105,
                    /** 走廊通道 */
                    AISLE = 106,
                    /** 电梯间 */
                    ELEVATOR_ROOM = 107,
                    /** 露台 */
                    PATIO = 108,
                    /** 卫生间 */
                    TOILET = 109,
                    /** 仓库 */
                    STOREHOUSE = 110,
                    /** 设备间 */
                    EQUIPMENT_ROOM = 111,
                    /** 机房 */
                    MACHINE_ROOM = 112,
                    /** 其他 */
                    OTHER = 113,
                }

                interface Compass {
                    readonly northDirection: number;
                    readonly latitude?: number;
                    readonly longitude?: number;
                }

                interface FloorplanConfig {
                    readonly elementJoint: ElementJoint;
                    readonly height: number;
                    readonly floorThickness: number;
                    readonly compass: Compass;
                }

                interface FloorplanDocument {
                    readonly floorplanElements: FloorplanElement[];
                    readonly floorplanConfig: FloorplanConfig;
                    readonly version: string;
                }

                /**
                 * 户型构件类型
                 * @vm-type FloorplanElement
                 */
                type FloorplanElement = Room | Wall | Beam | Pillar | Chimney | FloorOpening | RoomSeparator | Opening;

                interface Room {
                    readonly id: string;
                    readonly etp: ElementType.Room;
                    readonly location: KPoint2d;
                    readonly name: string;
                    readonly roomType: RoomType;
                    readonly roomTypeName: string;
                    readonly hasCeiling: boolean;
                }

                interface RoomSeparator {
                    readonly id: string;
                    readonly etp: ElementType.RoomSeparator;
                    readonly curve: KBoundedCurve2d;
                }

                interface FloorOpening {
                    readonly id: string;
                    readonly etp: ElementType.FloorOpening;
                    readonly geometricRepresentation: GeometricRepresentation;
                    readonly offgroundType: OffgroundType;
                }

                interface Opening {
                    readonly id: string;
                    readonly etp: ElementType.Opening;
                    readonly hostIds: string[];
                    readonly openingType: OpeningType;
                    readonly geometricRepresentation: GeometricRepresentation;
                }

                interface Wall {
                    readonly id: string;
                    readonly etp: ElementType.Wall;
                    readonly wallType: WallType;
                    readonly isBearing: boolean;
                    readonly geometricRepresentation: GeometricRepresentation;
                }

                interface Beam {
                    readonly id: string;
                    readonly etp: ElementType.Beam;
                    readonly offgroundType: OffgroundType;
                    readonly geometricRepresentation: GeometricRepresentation;
                }

                interface Pillar {
                    readonly id: string;
                    readonly etp: ElementType.Pillar;
                    readonly geometricRepresentation: GeometricRepresentation;
                }

                interface Chimney {
                    readonly id: string;
                    readonly etp: ElementType.Chimney;
                    readonly geometricRepresentation: GeometricRepresentation;
                }

                /**
                 * 户型几何建模方式
                 * @vm-type GeometricRepresentation
                 */
                type GeometricRepresentation = BoundedAxis | ExtrudeAreaSolid;

                interface BoundedAxis {
                    readonly gtp: 'boundedAxis';
                    readonly curve: KBoundedCurve2d;
                    readonly thickness: number;
                    readonly height: number;
                    readonly offGround: number;
                }

                interface ExtrudeAreaSolid {
                    readonly gtp: 'extrudeAreaSolid';
                    readonly plane: KPlane;
                    readonly depth: number;
                    readonly extrudeDirection: KVector3d;
                    readonly profile: ExtrudeProfile;
                }

                /**
                 * @vm-type ExtrudeProfile
                 */
                type ExtrudeProfile = RectangleProfile | CircleProfile | ArbitraryClosedProfile;

                interface RectangleProfile {
                    readonly ptp: 'rectangleProfile';
                    readonly center: KPoint2d;
                    readonly length: number;
                    readonly width: number;
                    readonly xDirection: KVector2d;
                }

                interface CircleProfile {
                    readonly ptp: 'circleProfile';
                    readonly center: KPoint2d;
                    readonly radius: number;
                }

                interface ArbitraryClosedProfile {
                    readonly ptp: 'arbitraryClosedProfile';
                    readonly faceOnPlane: KGeomFace2d;
                }

                /**
                 * 户型批量更新方案Requests
                 * @vm-type FloorplanDocumentBatchUpdateRequest
                 */
                interface FloorplanDocumentBatchUpdateRequest {
                    view?: DocumentView;
                    batchRequests: FloorplanDocumentOperateRequest[];
                }

                interface FloorplanDocumentBatchUpdateResponse {
                    /**
                     * 更新后的完整户型文档
                     */
                    readonly floorplanDocument: FloorplanDocument;
                    /**
                     * 每个 element 的更新结果，不包含对其他构件的影响（比如围成了新的房间）与 batchRequests 一一对应
                     */
                    readonly elements: Array<{
                        /**
                         * 新增、更新、删除的 element 对应的 id（新增墙同时挖洞时为多个）
                         */
                        readonly elementIds: string[];
                    }>;
                }

                /**
                 * 户型批量更新Request类型
                 * @vm-type FloorplanDocumentOperateRequest
                 */
                type FloorplanDocumentOperateRequest =
                    ElementDeleteRequest |
                    WallCreateRequest |
                    ChimneyCreateRequest |
                    RoomSeparatorCreateRequest |
                    FloorOpeningCreateRequest |
                    BeamCreateRequest |
                    PillarCreateRequest |
                    OpeningCreateRequest |
                    FloorplanConfigUpdateRequest |
                    WallUpdateRequest |
                    ChimneyUpdateRequest |
                    PillarUpdateRequest |
                    BeamUpdateRequest |
                    RoomSeparatorUpdateRequest |
                    FloorOpeningUpdateRequest |
                    RoomUpdateRequest |
                    OpeningUpdateRequest;

                interface BaseOperateRequest {
                    readonly rtp: string;
                }

                interface FloorplanConfigUpdateRequest extends BaseOperateRequest, Partial<FloorplanConfig> {
                    rtp: 'floorplan_config_update';
                }

                interface ElementOperateRequest extends BaseOperateRequest {
                }

                /**
                 * Delete requests
                 */
                interface ElementDeleteRequest extends ElementOperateRequest {
                    rtp: 'element_delete';
                    id: string;
                }

                /**
                 * Create requests
                 */
                interface ElementCreateRequest extends ElementOperateRequest {
                }

                interface GeometricElementCreateRequest extends ElementCreateRequest {
                    geometricRepresentation: GeometricRepresentation;
                }

                interface RoomSeparatorCreateRequest extends ElementCreateRequest {
                    rtp: 'room_separator_create';
                    curve: KBoundedCurve2d;
                }

                interface WallCreateRequest extends GeometricElementCreateRequest {
                    rtp: 'wall_create';
                    wallType?: WallType;
                    isBearing?: boolean;
                    openingCreateRequests?: OpeningCreateRequest[];
                }

                interface BeamCreateRequest extends GeometricElementCreateRequest {
                    rtp: 'beam_create';
                    offGroundType?: OffgroundType;
                }

                interface PillarCreateRequest extends GeometricElementCreateRequest {
                    rtp: 'pillar_create';
                }

                interface ChimneyCreateRequest extends GeometricElementCreateRequest {
                    rtp: 'chimney_create';
                }

                interface OpeningCreateRequest extends GeometricElementCreateRequest {
                    rtp: 'opening_create';
                    hostId: string;
                    openingType?: OpeningType;
                    openingSourceType?: OpeningSourceType;
                }

                interface FloorOpeningCreateRequest extends GeometricElementCreateRequest {
                    rtp: 'floor_opening_create';
                    name?: string;
                    offGroundType?: OffgroundType;
                }

                /**
                 * Update requests
                 */
                interface ElementUpdateRequest extends ElementOperateRequest {
                    id: string;
                }

                interface GeometricElementUpdateRequest extends ElementUpdateRequest {
                    geometricRepresentation?: GeometricRepresentation;
                }

                interface WallUpdateRequest extends GeometricElementUpdateRequest {
                    rtp: 'wall_update';
                    wallType?: WallType;
                    isBearing?: boolean;
                    openingCreateRequests?: OpeningCreateRequest[];
                }

                interface ChimneyUpdateRequest extends GeometricElementUpdateRequest {
                    rtp: 'chimney_update';
                }

                interface PillarUpdateRequest extends GeometricElementUpdateRequest {
                    rtp: 'pillar_update';
                }

                interface BeamUpdateRequest extends GeometricElementUpdateRequest {
                    rtp: 'beam_update';
                    offGroundType?: OffgroundType;
                }

                interface RoomSeparatorUpdateRequest extends ElementUpdateRequest {
                    rtp: 'room_separator_update';
                    curve?: KBoundedCurve2d;
                }

                interface FloorOpeningUpdateRequest extends ElementUpdateRequest {
                    rtp: 'floor_opening_update';
                    name?: string;
                    offGroundType?: OffgroundType;
                }

                interface RoomUpdateRequest extends ElementUpdateRequest {
                    rtp: 'room_update';
                    name?: string;
                    roomType?: RoomType;
                    hasCeiling?: boolean;
                }

                interface OpeningUpdateRequest extends GeometricElementUpdateRequest {
                    rtp: 'opening_update';
                    hostId?: string;
                    openingType?: OpeningType;
                    openingSourceType?: OpeningSourceType;
                }
            }
        }

        // Methods
        namespace Methods {
            /**
             * 获取所有墙
             * get all walls
             */
            function getAllWallList(): IDP.DB.Types.Wall[];

            /**
             * 获取所有房间
             * get all rooms
             */
            function getAllRoomList(): IDP.DB.Types.Room[];

            /**
             * 获取所有洞
             * get all openings
             */
            function getAllOpeningList(): IDP.DB.Types.Opening[];

            /**
             * 获取所有方形柱
             * @internal
             */
            function getAllSquareColumnList(): IDP.DB.Types.SquareColumn[];

            /**
             * 获取所有梁
             * @internal
             */
            function getAllBeamList(): IDP.DB.Types.Beam[];

            /**
             * 获取所有楼板
             * @internal
             */
            function getAllFloorList(): IDP.DB.Types.Floor[];

            /**
             * 获取所有户型门洞
             * @internal
             */
            function getAllDoorOpeningList(): IDP.DB.Types.DoorOpening[];

            /**
             * 获取所有户型窗洞
             * @internal
             */
            function getAllWindowOpeningList(): IDP.DB.Types.WindowOpening[];

            /**
             * 获取所有房间分割线
             * @internal
             */
            function getAllRoomSeparatorList(): IDP.DB.Types.RoomSeparator[];

            /**
             * 获取所有楼板洞
             * @internal
             */
            function getAllFloorOpeningList(): IDP.DB.Types.FloorOpening[];

            /**
             * 获取当前楼层层高
             * @internal
             */
            function getCurrentLevelHeight(): number;

            /**
             * 更新房间名称
             * @internal
             */
            function updateRoomName(roomId: IDPCommon.DB.Types.ElementId, name: string): void;

            /**
             * 更新房间类型
             * 目前仅是二方&type穷举有上百个且key会变更不稳定，暂不在idp提供RoomTypeEnum， 具体值可见：https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80991045367
             * @internal
             */
            function updateRoomType(roomId: IDPCommon.DB.Types.ElementId, type: number): void;

            /**
             * 更新房间屋顶显隐
             * @internal
             */
            function updateRoomRoofVisible(roomId: IDPCommon.DB.Types.ElementId, visible: boolean): void;

            /**
             * @internal
             */
            interface CreateConstructionLayerParam {
                requestList: RoomConstructionLayerRequest[];
            }

            /**
             * @internal
             */
            interface RoomConstructionLayerRequest {
                roomId: string;

                baseline: number;

                roomAreaLayers: AreaConstructionLayerRequest[];

                roomRootCourses: HorizontalConstructionCourseRequest[];

                verticalLayers: VerticalConstructionLayerRequest[];
            }

            /**
             * @internal
             */
            interface AreaConstructionLayerRequest {
                area: KGeomFace2d;

                relativeHeight: number;

                areaCourses: HorizontalConstructionCourseRequest[];
            }

            /**
             * @internal
             */
            interface HorizontalConstructionCourseRequest {
                courseType: IDP.DB.Types.CourseType;

                confId: string;

                thickness: number;

                relativeLine: number;
            }

            /**
             * @internal
             */
            interface VerticalConstructionLayerRequest {
                faceId: string;
                splitPoint: KPoint2d | null;
                pointInLayer: KPoint2d | null;
                verticalCourses: VerticalConstructionCourseRequest[];
            }

            /**
             * @internal
             */
            interface VerticalConstructionCourseRequest {
                courseType: IDP.DB.Types.CourseType;
                confId: string;
                height: number;
            }

            /**
             * @deprecated
             * @param param
             * @internal
             */
            function __deprecated__createConstructionLayer(param: CreateConstructionLayerParam): Promise<{ result: boolean, errorCode?: string, errorInfo?: string }>;

            /**
             * @internal
             */
            function getSpatialDataAsync(): Promise<string>;

            /**
             * 保存区域数据
             * https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80303018796
             *
             * @param json 序列化后的json
             * @internal
             */
            function saveSpatialDataAsync(json: string): Promise<void>;

            /**
             * 按墙面删除施工层
             *
             * @param faceIds
             * @internal
             */
            function deleteConstructionLayerBySourceFaceIds(faceIds: string[]): void;

            /**
             * 获取当前楼层户型基础数据
             * Get current floor floorplan base data
             * @param view 视图类型,默认BASIC
             * @returns 返回户型Document数据
             * @internal
             */
            function getFloorplanDocument(view?: IDP.DB.Types.Floorplan.DocumentView): Promise<IDP.DB.Types.Floorplan.FloorplanDocument>;

            /**
             * 更新当前楼层户型基础数据
             * Update current floor floorplan data
             * @param data 批量更新请求
             * @returns 批量更新结果
             * @internal
             */
            function updateFloorplanDocument(data: IDP.DB.Types.Floorplan.FloorplanDocumentBatchUpdateRequest): Promise<IDP.DB.Types.Floorplan.FloorplanDocumentBatchUpdateResponse>;
        }
    }

    // TODO
    // 暂时先放在这里
    namespace Design {
        /**
         * @internal
         */
        function getDesignConfig(): { designId: string | undefined; levelId: string | undefined };
    }

    namespace Interaction {

        /**
         * 导入Cad
         * @param importType Cad导入类型
         * @internal
         */
        function triggerCadImport(importType: CadImportType): void;

        /**
         * 获取Cad底图信息
         * @internal
         */
        function getCadInfo(): FloorPlanCadInfo;

        /**
         * 修改底图显示隐藏
         * @param layers 底图图层信息
         * @internal
         */
        function changeCadLayerVisible(layers: FloorPlanLayerInfo[]): void;

        /**
         * 户型墙体绘制
         *
         * @param type
         * @internal
         */
        function startDrawWall(type: DrawWallType): void;

        /**
         * 户型柱绘制
         *
         * @param type
         * @internal
         */
        function startDrawColumn(type: DrawColumnType): void;

        /**
         * 户型烟道绘制
         *
         * @internal
         */
        function startDrawFlue(): void;

        /**
         * 户型梁绘制
         *
         * @internal
         */
        function startDrawBeam(): void;

        /**
         * 户型门洞绘制
         *
         * @internal
         */
        function startDrawDoorOpening(): void;

        /**
         * 户型窗洞绘制
         *
         * @internal
         */
        function startDrawWindowOpening(): void;

        /**
         * 户型壁龛绘制
         *
         * @internal
         */
        function startDrawNiche(): void;

        /**
         * 户型屋顶绘制
         *
         * @internal
         */
        function startDrawRoof(): void;
    }

    /**
     * @internal
     */
    namespace Integration {
        namespace CADImport {
            /**
            * 导入CAD信息，加载户型数据
            * @param params CAD 导入参数
            * @internal
            */
            function loadFloorplanFromCad(params: CadImportParams): Promise<void>;
        }

    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
