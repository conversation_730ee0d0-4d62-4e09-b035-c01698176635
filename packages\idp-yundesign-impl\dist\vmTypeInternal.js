var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_StartDragProductPromiseResult = injections.types["StartDragProductPromiseResult"];
    var var_injection_KPoint3d = injections.packages["@qunhe/math-apaas-api"]["KPoint3d"];
    var var_injection_KVector3d = injections.packages["@qunhe/math-apaas-api"]["KVector3d"];
    var var_injection_PromiseResultWithUuid = injections.types["PromiseResultWithUuid"];
    var var_injection_KEuler = injections.packages["@qunhe/math-apaas-api"]["KEuler"];
    var var_injection_PromiseResult = injections.types["PromiseResult"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Interaction = {};
    var var_startDragProductAsync = {};
    var var_stringType = {};
    var var_injection_StartDragProductPromiseResult_Promise = {};
    var var_injection_StartDragProductPromiseResult_Promise_then = {};
    var var_injection_StartDragProductPromiseResult_Promise_then_onresolve = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_openCategoryInPublicLibrary = {};
    var var_openCategoryInPublicLibrary_categoryData_objectLiteral = {};
    var var_numberType = {};
    var var_DB = {};
    var var_Types = {};
    var var_ModelDoorWindowType = {};
    var var_Methods = {};
    var var_createFurniture = {};
    var var_FurnitureCreateInfo = {};
    var var_injection_PromiseResultWithUuid_Promise = {};
    var var_injection_PromiseResultWithUuid_Promise_then = {};
    var var_injection_PromiseResultWithUuid_Promise_then_onresolve = {};
    var var_createFurnitureList = {};
    var var_FurnitureCreateInfo_Array = {};
    var var_ElementId_Array_Promise = {};
    var var_ElementId_Array_Promise_then = {};
    var var_ElementId_Array_Promise_then_onresolve = {};
    var var_ElementId_Array = {};
    var var_ElementId = {};
    var var_deleteFurniture = {};
    var var_booleanType = {};
    var var_getFurniture = {};
    var var_Furniture = {};
    var var_createFurnitureGroup = {};
    var var_FurnitureGroupCreateInfo = {};
    var var_deleteFurnitureGroup = {};
    var var_getAllFurnitureList = {};
    var var_Furniture_Array = {};
    var var_getAllFurnitureGroupList = {};
    var var_FurnitureGroup_Array = {};
    var var_FurnitureGroup = {};
    var var_stringType_Promise = {};
    var var_stringType_Promise_then = {};
    var var_stringType_Promise_then_onresolve = {};
    var var_getAllModelMoldingList = {};
    var var_ModelMolding_Array = {};
    var var_ModelMolding = {};
    var var_getElementTypeByProductIdAsync = {};
    var var_stringType_Array_Promise = {};
    var var_stringType_Array_Promise_then = {};
    var var_stringType_Array_Promise_then_onresolve = {};
    var var_stringType_Array = {};
    var var_replaceModelMoldingAsync = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_getAllModelDoorWindowList = {};
    var var_ModelDoorWindow_Array = {};
    var var_ModelDoorWindow = {};
    var var_getModelDoorWindow = {};
    var var_deleteModelDoorWindow = {};
    var var_createModelDoorWindowAsync = {};
    var var_ModelDoorWindowCreateInfo = {};
    var var_injection_PromiseResult_Promise = {};
    var var_injection_PromiseResult_Promise_then = {};
    var var_injection_PromiseResult_Promise_then_onresolve = {};
    var var_UI = {};
    var var_renderLeftPanelProductResourcesContent = {};
    var var_renderLeftPanelProductResourcesContent_options_objectLiteral = {};
    var var_unmountLeftPanelProductResourcesContent = {};
    var var_Platform = {};
    var var_enterDefaultModeAsync = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_StartDragProductPromiseResult, exportName: "StartDragProductPromiseResult" },
        { value: var_injection_KPoint3d, packageName: "@qunhe/math-apaas-api", exportName: "KPoint3d" },
        { value: var_injection_KVector3d, packageName: "@qunhe/math-apaas-api", exportName: "KVector3d" },
        { value: var_injection_PromiseResultWithUuid, exportName: "PromiseResultWithUuid" },
        { value: var_injection_KEuler, packageName: "@qunhe/math-apaas-api", exportName: "KEuler" },
        { value: var_injection_PromiseResult, exportName: "PromiseResult" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Interaction": var_Interaction,
        "DB": var_DB,
        "UI": var_UI,
        "Platform": var_Platform,
    };
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "startDragProductAsync": var_startDragProductAsync,
        "openCategoryInPublicLibrary": var_openCategoryInPublicLibrary,
    };
    var_startDragProductAsync.type = BasicType.Function;
    var_startDragProductAsync.name = "startDragProductAsync";
    var_startDragProductAsync.varying = false;
    var_startDragProductAsync.keepArgsHandle = false;
    var_startDragProductAsync.args = [var_stringType];
    var_startDragProductAsync.return = var_injection_StartDragProductPromiseResult_Promise;
    var_stringType.type = BasicType.String;
    var_injection_StartDragProductPromiseResult_Promise.type = BasicType.Object;
    var_injection_StartDragProductPromiseResult_Promise.properties = {
        "then": var_injection_StartDragProductPromiseResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_StartDragProductPromiseResult_Promise_then.type = BasicType.Function;
    var_injection_StartDragProductPromiseResult_Promise_then.name = "";
    var_injection_StartDragProductPromiseResult_Promise_then.varying = false;
    var_injection_StartDragProductPromiseResult_Promise_then.keepArgsHandle = true;
    var_injection_StartDragProductPromiseResult_Promise_then.args = [var_injection_StartDragProductPromiseResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_StartDragProductPromiseResult_Promise_then.return = var_undefinedType;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.type = BasicType.Function;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.name = "";
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.varying = false;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.args = [var_injection_StartDragProductPromiseResult];
    var_injection_StartDragProductPromiseResult_Promise_then_onresolve.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_openCategoryInPublicLibrary.type = BasicType.Function;
    var_openCategoryInPublicLibrary.name = "openCategoryInPublicLibrary";
    var_openCategoryInPublicLibrary.varying = false;
    var_openCategoryInPublicLibrary.keepArgsHandle = false;
    var_openCategoryInPublicLibrary.args = [var_openCategoryInPublicLibrary_categoryData_objectLiteral];
    var_openCategoryInPublicLibrary.return = var_undefinedType;
    var_openCategoryInPublicLibrary_categoryData_objectLiteral.type = BasicType.Object;
    var_openCategoryInPublicLibrary_categoryData_objectLiteral.properties = {
        "treeId": var_numberType,
        "categoryId": var_numberType,
    };
    var_numberType.type = BasicType.Number;
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
        "ModelDoorWindowType": var_ModelDoorWindowType,
    };
    var_ModelDoorWindowType.type = BasicType.Object;
    var_ModelDoorWindowType.properties = {
        "Window": var_numberType,
        "FrenchWindow": var_numberType,
        "BayWindow": var_numberType,
        "Door": var_numberType,
        "DoubleDoor": var_numberType,
        "SlidingDoor": var_numberType,
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "createFurniture": var_createFurniture,
        "createFurnitureList": var_createFurnitureList,
        "deleteFurniture": var_deleteFurniture,
        "getFurniture": var_getFurniture,
        "createFurnitureGroup": var_createFurnitureGroup,
        "deleteFurnitureGroup": var_deleteFurnitureGroup,
        "getAllFurnitureList": var_getAllFurnitureList,
        "getAllFurnitureGroupList": var_getAllFurnitureGroupList,
        "getAllModelMoldingList": var_getAllModelMoldingList,
        "getElementTypeByProductIdAsync": var_getElementTypeByProductIdAsync,
        "replaceModelMoldingAsync": var_replaceModelMoldingAsync,
        "getAllModelDoorWindowList": var_getAllModelDoorWindowList,
        "getModelDoorWindow": var_getModelDoorWindow,
        "deleteModelDoorWindow": var_deleteModelDoorWindow,
        "createModelDoorWindowAsync": var_createModelDoorWindowAsync,
    };
    var_createFurniture.type = BasicType.Function;
    var_createFurniture.name = "createFurniture";
    var_createFurniture.varying = false;
    var_createFurniture.keepArgsHandle = false;
    var_createFurniture.args = [var_FurnitureCreateInfo];
    var_createFurniture.return = var_injection_PromiseResultWithUuid_Promise;
    var_FurnitureCreateInfo.type = BasicType.Object;
    var_FurnitureCreateInfo.properties = {
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_injection_KPoint3d,
        "scale": var_injection_KVector3d,
    };
    var_injection_PromiseResultWithUuid_Promise.type = BasicType.Object;
    var_injection_PromiseResultWithUuid_Promise.properties = {
        "then": var_injection_PromiseResultWithUuid_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_PromiseResultWithUuid_Promise_then.type = BasicType.Function;
    var_injection_PromiseResultWithUuid_Promise_then.name = "";
    var_injection_PromiseResultWithUuid_Promise_then.varying = false;
    var_injection_PromiseResultWithUuid_Promise_then.keepArgsHandle = true;
    var_injection_PromiseResultWithUuid_Promise_then.args = [var_injection_PromiseResultWithUuid_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_PromiseResultWithUuid_Promise_then.return = var_undefinedType;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.type = BasicType.Function;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.name = "";
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.varying = false;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.args = [var_injection_PromiseResultWithUuid];
    var_injection_PromiseResultWithUuid_Promise_then_onresolve.return = var_undefinedType;
    var_createFurnitureList.type = BasicType.Function;
    var_createFurnitureList.name = "createFurnitureList";
    var_createFurnitureList.varying = false;
    var_createFurnitureList.keepArgsHandle = false;
    var_createFurnitureList.args = [var_FurnitureCreateInfo_Array];
    var_createFurnitureList.return = var_ElementId_Array_Promise;
    var_FurnitureCreateInfo_Array.type = BasicType.Array;
    var_FurnitureCreateInfo_Array.value = var_FurnitureCreateInfo;
    var_ElementId_Array_Promise.type = BasicType.Object;
    var_ElementId_Array_Promise.properties = {
        "then": var_ElementId_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ElementId_Array_Promise_then.type = BasicType.Function;
    var_ElementId_Array_Promise_then.name = "";
    var_ElementId_Array_Promise_then.varying = false;
    var_ElementId_Array_Promise_then.keepArgsHandle = true;
    var_ElementId_Array_Promise_then.args = [var_ElementId_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_ElementId_Array_Promise_then.return = var_undefinedType;
    var_ElementId_Array_Promise_then_onresolve.type = BasicType.Function;
    var_ElementId_Array_Promise_then_onresolve.name = "";
    var_ElementId_Array_Promise_then_onresolve.varying = false;
    var_ElementId_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_ElementId_Array_Promise_then_onresolve.args = [var_ElementId_Array];
    var_ElementId_Array_Promise_then_onresolve.return = var_undefinedType;
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_deleteFurniture.type = BasicType.Function;
    var_deleteFurniture.name = "deleteFurniture";
    var_deleteFurniture.varying = false;
    var_deleteFurniture.keepArgsHandle = false;
    var_deleteFurniture.args = [var_ElementId];
    var_deleteFurniture.return = var_booleanType;
    var_booleanType.type = BasicType.Boolean;
    var_getFurniture.type = BasicType.Function;
    var_getFurniture.name = "getFurniture";
    var_getFurniture.varying = false;
    var_getFurniture.keepArgsHandle = false;
    var_getFurniture.args = [var_ElementId];
    var_getFurniture.return = var_Furniture;
    var_Furniture.type = BasicType.Object;
    var_Furniture.properties = {
        "elementId": var_ElementId,
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_injection_KPoint3d,
        "size": var_injection_KPoint3d,
        "scale": var_injection_KPoint3d,
        "groupId": var_ElementId,
        "uuid": var_stringType,
    };
    var_createFurnitureGroup.type = BasicType.Function;
    var_createFurnitureGroup.name = "createFurnitureGroup";
    var_createFurnitureGroup.varying = false;
    var_createFurnitureGroup.keepArgsHandle = false;
    var_createFurnitureGroup.args = [var_FurnitureGroupCreateInfo];
    var_createFurnitureGroup.return = var_injection_PromiseResultWithUuid_Promise;
    var_FurnitureGroupCreateInfo.type = BasicType.Object;
    var_FurnitureGroupCreateInfo.properties = {
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_injection_KEuler,
        "size": var_injection_KVector3d,
    };
    var_deleteFurnitureGroup.type = BasicType.Function;
    var_deleteFurnitureGroup.name = "deleteFurnitureGroup";
    var_deleteFurnitureGroup.varying = false;
    var_deleteFurnitureGroup.keepArgsHandle = false;
    var_deleteFurnitureGroup.args = [var_ElementId];
    var_deleteFurnitureGroup.return = var_booleanType;
    var_getAllFurnitureList.type = BasicType.Function;
    var_getAllFurnitureList.name = "getAllFurnitureList";
    var_getAllFurnitureList.varying = false;
    var_getAllFurnitureList.keepArgsHandle = false;
    var_getAllFurnitureList.args = [];
    var_getAllFurnitureList.return = var_Furniture_Array;
    var_Furniture_Array.type = BasicType.Array;
    var_Furniture_Array.value = var_Furniture;
    var_getAllFurnitureGroupList.type = BasicType.Function;
    var_getAllFurnitureGroupList.name = "getAllFurnitureGroupList";
    var_getAllFurnitureGroupList.varying = false;
    var_getAllFurnitureGroupList.keepArgsHandle = false;
    var_getAllFurnitureGroupList.args = [];
    var_getAllFurnitureGroupList.return = var_FurnitureGroup_Array;
    var_FurnitureGroup_Array.type = BasicType.Array;
    var_FurnitureGroup_Array.value = var_FurnitureGroup;
    var_FurnitureGroup.type = BasicType.Object;
    var_FurnitureGroup.properties = {
        "elementId": var_ElementId,
        "productId": var_stringType_Promise,
        "position": var_injection_KPoint3d,
        "rotation": var_injection_KEuler,
        "size": var_injection_KVector3d,
        "children": var_Furniture_Array,
        "uuid": var_stringType,
    };
    var_stringType_Promise.type = BasicType.Object;
    var_stringType_Promise.properties = {
        "then": var_stringType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_stringType_Promise_then.type = BasicType.Function;
    var_stringType_Promise_then.name = "";
    var_stringType_Promise_then.varying = false;
    var_stringType_Promise_then.keepArgsHandle = true;
    var_stringType_Promise_then.args = [var_stringType_Promise_then_onresolve, var_Promise_then_onreject];
    var_stringType_Promise_then.return = var_undefinedType;
    var_stringType_Promise_then_onresolve.type = BasicType.Function;
    var_stringType_Promise_then_onresolve.name = "";
    var_stringType_Promise_then_onresolve.varying = false;
    var_stringType_Promise_then_onresolve.keepArgsHandle = false;
    var_stringType_Promise_then_onresolve.args = [var_stringType];
    var_stringType_Promise_then_onresolve.return = var_undefinedType;
    var_getAllModelMoldingList.type = BasicType.Function;
    var_getAllModelMoldingList.name = "getAllModelMoldingList";
    var_getAllModelMoldingList.varying = false;
    var_getAllModelMoldingList.keepArgsHandle = false;
    var_getAllModelMoldingList.args = [];
    var_getAllModelMoldingList.return = var_ModelMolding_Array;
    var_ModelMolding_Array.type = BasicType.Array;
    var_ModelMolding_Array.value = var_ModelMolding;
    var_ModelMolding.type = BasicType.Object;
    var_ModelMolding.properties = {
        "elementId": var_ElementId,
        "productId": var_stringType,
    };
    var_getElementTypeByProductIdAsync.type = BasicType.Function;
    var_getElementTypeByProductIdAsync.name = "getElementTypeByProductIdAsync";
    var_getElementTypeByProductIdAsync.varying = false;
    var_getElementTypeByProductIdAsync.keepArgsHandle = false;
    var_getElementTypeByProductIdAsync.args = [var_stringType];
    var_getElementTypeByProductIdAsync.return = var_stringType_Array_Promise;
    var_stringType_Array_Promise.type = BasicType.Object;
    var_stringType_Array_Promise.properties = {
        "then": var_stringType_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_stringType_Array_Promise_then.type = BasicType.Function;
    var_stringType_Array_Promise_then.name = "";
    var_stringType_Array_Promise_then.varying = false;
    var_stringType_Array_Promise_then.keepArgsHandle = true;
    var_stringType_Array_Promise_then.args = [var_stringType_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_stringType_Array_Promise_then.return = var_undefinedType;
    var_stringType_Array_Promise_then_onresolve.type = BasicType.Function;
    var_stringType_Array_Promise_then_onresolve.name = "";
    var_stringType_Array_Promise_then_onresolve.varying = false;
    var_stringType_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_stringType_Array_Promise_then_onresolve.args = [var_stringType_Array];
    var_stringType_Array_Promise_then_onresolve.return = var_undefinedType;
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_replaceModelMoldingAsync.type = BasicType.Function;
    var_replaceModelMoldingAsync.name = "replaceModelMoldingAsync";
    var_replaceModelMoldingAsync.varying = false;
    var_replaceModelMoldingAsync.keepArgsHandle = false;
    var_replaceModelMoldingAsync.args = [var_stringType, var_stringType];
    var_replaceModelMoldingAsync.return = var_undefinedType_Promise;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_getAllModelDoorWindowList.type = BasicType.Function;
    var_getAllModelDoorWindowList.name = "getAllModelDoorWindowList";
    var_getAllModelDoorWindowList.varying = false;
    var_getAllModelDoorWindowList.keepArgsHandle = false;
    var_getAllModelDoorWindowList.args = [];
    var_getAllModelDoorWindowList.return = var_ModelDoorWindow_Array;
    var_ModelDoorWindow_Array.type = BasicType.Array;
    var_ModelDoorWindow_Array.value = var_ModelDoorWindow;
    var_ModelDoorWindow.type = BasicType.Object;
    var_ModelDoorWindow.properties = {
        "elementId": var_ElementId,
        "productId": var_stringType,
        "type": var_numberType,
        "position": var_injection_KPoint3d,
        "rotation": var_numberType,
        "size": var_injection_KPoint3d,
        "scale": var_injection_KPoint3d,
        "handleFlipped": var_booleanType,
        "facingFlipped": var_booleanType,
        "hostIds": var_stringType_Array,
        "openingId": var_stringType,
    };
    var_getModelDoorWindow.type = BasicType.Function;
    var_getModelDoorWindow.name = "getModelDoorWindow";
    var_getModelDoorWindow.varying = false;
    var_getModelDoorWindow.keepArgsHandle = false;
    var_getModelDoorWindow.args = [var_ElementId];
    var_getModelDoorWindow.return = var_ModelDoorWindow;
    var_deleteModelDoorWindow.type = BasicType.Function;
    var_deleteModelDoorWindow.name = "deleteModelDoorWindow";
    var_deleteModelDoorWindow.varying = false;
    var_deleteModelDoorWindow.keepArgsHandle = false;
    var_deleteModelDoorWindow.args = [var_ElementId];
    var_deleteModelDoorWindow.return = var_booleanType;
    var_createModelDoorWindowAsync.type = BasicType.Function;
    var_createModelDoorWindowAsync.name = "createModelDoorWindowAsync";
    var_createModelDoorWindowAsync.varying = false;
    var_createModelDoorWindowAsync.keepArgsHandle = false;
    var_createModelDoorWindowAsync.args = [var_ModelDoorWindowCreateInfo];
    var_createModelDoorWindowAsync.return = var_injection_PromiseResult_Promise;
    var_ModelDoorWindowCreateInfo.type = BasicType.Object;
    var_ModelDoorWindowCreateInfo.properties = {
        "productId": var_stringType,
        "position": var_injection_KPoint3d,
        "rotation": var_numberType,
        "size": var_injection_KPoint3d,
        "handleFlipped": var_booleanType,
        "facingFlipped": var_booleanType,
    };
    var_injection_PromiseResult_Promise.type = BasicType.Object;
    var_injection_PromiseResult_Promise.properties = {
        "then": var_injection_PromiseResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_injection_PromiseResult_Promise_then.type = BasicType.Function;
    var_injection_PromiseResult_Promise_then.name = "";
    var_injection_PromiseResult_Promise_then.varying = false;
    var_injection_PromiseResult_Promise_then.keepArgsHandle = true;
    var_injection_PromiseResult_Promise_then.args = [var_injection_PromiseResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_injection_PromiseResult_Promise_then.return = var_undefinedType;
    var_injection_PromiseResult_Promise_then_onresolve.type = BasicType.Function;
    var_injection_PromiseResult_Promise_then_onresolve.name = "";
    var_injection_PromiseResult_Promise_then_onresolve.varying = false;
    var_injection_PromiseResult_Promise_then_onresolve.keepArgsHandle = false;
    var_injection_PromiseResult_Promise_then_onresolve.args = [var_injection_PromiseResult];
    var_injection_PromiseResult_Promise_then_onresolve.return = var_undefinedType;
    var_UI.type = BasicType.Object;
    var_UI.properties = {
        "renderLeftPanelProductResourcesContent": var_renderLeftPanelProductResourcesContent,
        "unmountLeftPanelProductResourcesContent": var_unmountLeftPanelProductResourcesContent,
    };
    var_renderLeftPanelProductResourcesContent.type = BasicType.Function;
    var_renderLeftPanelProductResourcesContent.name = "renderLeftPanelProductResourcesContent";
    var_renderLeftPanelProductResourcesContent.varying = false;
    var_renderLeftPanelProductResourcesContent.keepArgsHandle = false;
    var_renderLeftPanelProductResourcesContent.args = [var_stringType, var_renderLeftPanelProductResourcesContent_options_objectLiteral];
    var_renderLeftPanelProductResourcesContent.return = var_undefinedType;
    var_renderLeftPanelProductResourcesContent_options_objectLiteral.type = BasicType.Object;
    var_renderLeftPanelProductResourcesContent_options_objectLiteral.properties = {
        "currentLibrary": var_stringType,
        "rootContainerId": var_stringType,
    };
    var_unmountLeftPanelProductResourcesContent.type = BasicType.Function;
    var_unmountLeftPanelProductResourcesContent.name = "unmountLeftPanelProductResourcesContent";
    var_unmountLeftPanelProductResourcesContent.varying = false;
    var_unmountLeftPanelProductResourcesContent.keepArgsHandle = false;
    var_unmountLeftPanelProductResourcesContent.args = [];
    var_unmountLeftPanelProductResourcesContent.return = var_undefinedType;
    var_Platform.type = BasicType.Object;
    var_Platform.properties = {
        "enterDefaultModeAsync": var_enterDefaultModeAsync,
    };
    var_enterDefaultModeAsync.type = BasicType.Function;
    var_enterDefaultModeAsync.name = "enterDefaultModeAsync";
    var_enterDefaultModeAsync.varying = false;
    var_enterDefaultModeAsync.keepArgsHandle = false;
    var_enterDefaultModeAsync.args = [];
    var_enterDefaultModeAsync.return = var_undefinedType_Promise;
    
    return var_sourceFile;
};
