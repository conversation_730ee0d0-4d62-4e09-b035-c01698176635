#!/bin/bash

# 通用函数库
# 提供所有 CI/CD 脚本共用的函数和工具
# 作者: Backend API Team

set -e

# 颜色定义
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export PURPLE='\033[0;35m'
export CYAN='\033[0;36m'
export WHITE='\033[1;37m'
export NC='\033[0m' # No Color

# 日志级别
export LOG_LEVEL_DEBUG=0
export LOG_LEVEL_INFO=1
export LOG_LEVEL_WARN=2
export LOG_LEVEL_ERROR=3

# 默认日志级别
export CURRENT_LOG_LEVEL=${CURRENT_LOG_LEVEL:-$LOG_LEVEL_INFO}

# 日志函数
log_debug() {
    if [ "$CURRENT_LOG_LEVEL" -le "$LOG_LEVEL_DEBUG" ]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1" >&2
    fi
}

log_info() {
    if [ "$CURRENT_LOG_LEVEL" -le "$LOG_LEVEL_INFO" ]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

log_success() {
    if [ "$CURRENT_LOG_LEVEL" -le "$LOG_LEVEL_INFO" ]; then
        echo -e "${GREEN}[SUCCESS]${NC} $1"
    fi
}

log_warning() {
    if [ "$CURRENT_LOG_LEVEL" -le "$LOG_LEVEL_WARN" ]; then
        echo -e "${YELLOW}[WARNING]${NC} $1" >&2
    fi
}

log_error() {
    if [ "$CURRENT_LOG_LEVEL" -le "$LOG_LEVEL_ERROR" ]; then
        echo -e "${RED}[ERROR]${NC} $1" >&2
    fi
}

# 步骤日志函数
log_step() {
    local step_num="$1"
    local step_desc="$2"
    echo ""
    echo -e "${CYAN}=== 步骤 $step_num: $step_desc ===${NC}"
}

# 检查必需的环境变量
check_required_env() {
    local var_name="$1"
    local var_value="${!var_name}"
    
    if [ -z "$var_value" ]; then
        log_error "缺少必需的环境变量: $var_name"
        return 1
    fi
    
    log_debug "环境变量 $var_name 已设置"
    return 0
}

# 检查文件是否存在
check_file_exists() {
    local file_path="$1"
    local description="${2:-文件}"
    
    if [ ! -f "$file_path" ]; then
        log_error "$description 不存在: $file_path"
        return 1
    fi
    
    log_debug "$description 存在: $file_path"
    return 0
}

# 检查目录是否存在
check_dir_exists() {
    local dir_path="$1"
    local description="${2:-目录}"
    
    if [ ! -d "$dir_path" ]; then
        log_error "$description 不存在: $dir_path"
        return 1
    fi
    
    log_debug "$description 存在: $dir_path"
    return 0
}

# 创建目录（如果不存在）
ensure_dir() {
    local dir_path="$1"
    local description="${2:-目录}"

    if [ ! -d "$dir_path" ]; then
        log_info "创建 $description: $dir_path"
        mkdir -p "$dir_path"
    else
        log_debug "$description 已存在: $dir_path"
    fi
}

# 确保目录存在（兼容函数）
ensure_dir_exists() {
    ensure_dir "$@"
}

# 执行命令并记录
execute_cmd() {
    local cmd="$1"
    local description="${2:-命令}"
    
    log_info "执行 $description: $cmd"
    
    if eval "$cmd"; then
        log_success "$description 执行成功"
        return 0
    else
        local exit_code=$?
        log_error "$description 执行失败 (退出码: $exit_code)"
        return $exit_code
    fi
}

# 安全地执行命令（失败时不退出）
safe_execute() {
    local cmd="$1"
    local description="${2:-命令}"
    
    log_info "安全执行 $description: $cmd"
    
    if eval "$cmd"; then
        log_success "$description 执行成功"
        return 0
    else
        local exit_code=$?
        log_warning "$description 执行失败 (退出码: $exit_code)，继续执行"
        return $exit_code
    fi
}

# 计时器函数
start_timer() {
    export TIMER_START=$(date +%s)
    log_debug "计时器已启动"
}

end_timer() {
    local description="${1:-操作}"
    
    if [ -z "$TIMER_START" ]; then
        log_warning "计时器未启动"
        return 1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - TIMER_START))
    local minutes=$((duration / 60))
    local seconds=$((duration % 60))
    
    if [ $minutes -gt 0 ]; then
        log_info "$description 耗时: ${minutes}分${seconds}秒"
    else
        log_info "$description 耗时: ${seconds}秒"
    fi
    
    unset TIMER_START
}

# 获取服务列表
get_services() {
    echo "camera doorwindow furniture koolux layout pdm"
}

# 检查服务是否有效
is_valid_service() {
    local service="$1"
    local services=$(get_services)
    
    for valid_service in $services; do
        if [ "$service" = "$valid_service" ]; then
            return 0
        fi
    done
    
    return 1
}

# 获取服务的 OpenAPI 规范文件路径
get_service_openapi_path() {
    local service="$1"
    echo "specifications/services/$service/openapi.yaml"
}

# 获取服务的 Java SDK 配置文件路径
get_service_java_config_path() {
    local service="$1"
    echo "generators/sdks/configs/java/$service.yaml"
}

# 获取服务的 Java SDK 输出路径
get_service_java_output_path() {
    local service="$1"
    echo "build/sdks/$service/java"
}

# 检查服务的必需文件
check_service_files() {
    local service="$1"
    local errors=0
    
    log_info "检查服务 $service 的文件..."
    
    # 检查 OpenAPI 规范
    local openapi_path=$(get_service_openapi_path "$service")
    if ! check_file_exists "$openapi_path" "OpenAPI 规范"; then
        errors=$((errors + 1))
    fi
    
    # 检查 Java SDK 配置
    local java_config_path=$(get_service_java_config_path "$service")
    if ! check_file_exists "$java_config_path" "Java SDK 配置"; then
        errors=$((errors + 1))
    fi
    
    if [ $errors -eq 0 ]; then
        log_success "服务 $service 的文件检查通过"
        return 0
    else
        log_error "服务 $service 的文件检查失败 ($errors 个错误)"
        return 1
    fi
}

# 显示脚本头部信息
show_script_header() {
    local script_name="$1"
    local script_desc="$2"
    local script_version="${3:-1.0.0}"
    
    echo ""
    echo -e "${WHITE}================================================${NC}"
    echo -e "${WHITE} $script_name${NC}"
    echo -e "${WHITE} $script_desc${NC}"
    echo -e "${WHITE} 版本: $script_version${NC}"
    echo -e "${WHITE} 时间: $(date)${NC}"
    echo -e "${WHITE}================================================${NC}"
    echo ""
}

# 显示脚本尾部信息
show_script_footer() {
    local script_name="$1"
    local success="${2:-true}"
    
    echo ""
    echo -e "${WHITE}================================================${NC}"
    if [ "$success" = "true" ]; then
        echo -e "${GREEN} $script_name 执行完成 ✅${NC}"
    else
        echo -e "${RED} $script_name 执行失败 ❌${NC}"
    fi
    echo -e "${WHITE} 时间: $(date)${NC}"
    echo -e "${WHITE}================================================${NC}"
    echo ""
}

# 初始化脚本环境
init_script() {
    local script_name="$1"
    local script_desc="$2"
    
    # 显示头部信息
    show_script_header "$script_name" "$script_desc"
    
    # 启动计时器
    start_timer
    
    # 设置错误处理
    set -e
    
    # 设置调试模式（如果启用）
    if [ "${DEBUG:-false}" = "true" ]; then
        export CURRENT_LOG_LEVEL=$LOG_LEVEL_DEBUG
        set -x
    fi
}

# 完成脚本执行
finish_script() {
    local script_name="$1"
    local success="${2:-true}"
    
    # 结束计时器
    end_timer "$script_name"
    
    # 显示尾部信息
    show_script_footer "$script_name" "$success"
}

# ==========================================
# 计数器和统计功能
# ==========================================

# 全局计数器数组
declare -A COUNTERS

# 增加计数器
increment_counter() {
    local counter_name="$1"
    local increment="${2:-1}"

    if [[ -z "$counter_name" ]]; then
        log_error "计数器名称不能为空"
        return 1
    fi

    # 初始化计数器（如果不存在）
    if [[ -z "${COUNTERS[$counter_name]}" ]]; then
        COUNTERS[$counter_name]=0
    fi

    # 增加计数器
    COUNTERS[$counter_name]=$((COUNTERS[$counter_name] + increment))

    log_debug "计数器 '$counter_name' 增加 $increment，当前值: ${COUNTERS[$counter_name]}"
}

# 获取计数器值
get_counter() {
    local counter_name="$1"

    if [[ -z "$counter_name" ]]; then
        log_error "计数器名称不能为空"
        return 1
    fi

    # 返回计数器值（如果不存在则返回0）
    echo "${COUNTERS[$counter_name]:-0}"
}

# 重置计数器
reset_counter() {
    local counter_name="$1"

    if [[ -z "$counter_name" ]]; then
        log_error "计数器名称不能为空"
        return 1
    fi

    COUNTERS[$counter_name]=0
    log_debug "计数器 '$counter_name' 已重置为 0"
}

# 显示所有计数器
show_counters() {
    log_info "📊 当前计数器状态:"

    if [[ ${#COUNTERS[@]} -eq 0 ]]; then
        log_info "  无计数器数据"
        return
    fi

    for counter_name in "${!COUNTERS[@]}"; do
        log_info "  $counter_name: ${COUNTERS[$counter_name]}"
    done
}

# 显示统计信息
show_stats() {
    local title="${1:-执行统计}"

    log_info "📈 $title:"
    show_counters
}

# ==========================================
# 时间函数
# ==========================================

# 获取当前时间戳
get_current_time() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 获取时间戳（兼容函数）
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 获取简短时间戳（用于文件名）
get_short_timestamp() {
    date '+%Y%m%d-%H%M%S'
}

log_info "通用函数库已加载 ✅"
