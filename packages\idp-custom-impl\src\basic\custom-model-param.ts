import { BasicType } from '@qunhe/kls-abstraction';

/**
 * ParamModelLite-param
 */
export const ParamModelLiteParamTypes = {
    type: BasicType.Object,
    properties: {
        ref: undefined!,
        data: {
            type: BasicType.Unknown,
        },
        getName: {
            name: 'getName',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.String,
            },
        },
        getDescription: {
            name: 'getDescription',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.String,
            },
        },
        getSimpleName: {
            name: 'getSimpleName',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.String,
            },
        },
        getDisplayName: {
            name: 'getDisplayName',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.String,
            },
        },
        getVisible: {
            name: 'getVisible',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Boolean,
            },
        },
        isOverridden: {
            name: 'isOverridden',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Boolean,
            },
        },
        cancelOverride: {
            name: 'cancelOverride',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        getUnitType: {
            name: 'getUnitType',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Number,
            },
        },
        getParamForm: {
            name: 'getParamForm',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Number,
            },
        },
        isUsingMixFormula: {
            name: 'isUsingMixFormula',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Boolean,
            },
        },
        useMixFormula: {
            name: 'useMixFormula',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        getValue: {
            name: 'getValue',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        setValue: {
            name: 'setValue',
            varying: false,
            keepArgsHandle: false,
            args: [
                {
                    type: BasicType.Unknown,
                },
            ],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        getType: {
            name: 'getType',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.String,
            },
        },
        getMax: {
            name: 'getMax',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Number,
            },
        },
        getMin: {
            name: 'getMin',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Number,
            },
        },
        getStep: {
            name: 'getStep',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Number,
            },
        },
        getOptionValues: {
            name: 'getOptionValues',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Array,
                value: {
                    type: BasicType.Unknown,
                },
            },
        },
        getEditable: {
            name: 'getEditable',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Boolean,
            },
        },
        getRecommendValues: {
            name: 'getRecommendValues',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Array,
                value: {
                    type: BasicType.Unknown,
                },
            },
        },
        getValueDisplayNames: {
            name: 'getValueDisplayNames',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Array,
                value: {
                    type: BasicType.String,
                },
            },
        },
        getPrimitiveOverride: {
            name: 'getPrimitiveOverride',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Boolean,
            },
        },
        setPrimitiveOverride: {
            name: 'setPrimitiveOverride',
            varying: false,
            keepArgsHandle: false,
            args: [
                {
                    type: BasicType.Boolean,
                },
            ],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        getParsedValue: {
            name: 'getParsedValue',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        setParsedValue: {
            name: 'setParsedValue',
            varying: false,
            keepArgsHandle: false,
            args: [{ type: BasicType.Unknown }],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        // 读取公式
        getFormula: {
            name: 'getFormula',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        // 设置公式
        setFormula: {
            name: 'setFormula',
            varying: false,
            keepArgsHandle: false,
            args: [{ type: BasicType.Unknown }],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        toJSON: {
            name: 'toJSON',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        getPackageId: {
            name: 'getPackageId',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.String,
            },
        },
    },
};
