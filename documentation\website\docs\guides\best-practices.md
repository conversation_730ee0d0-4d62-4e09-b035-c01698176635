---
id: best-practices
title: API 最佳实践
sidebar_label: 最佳实践
---

# API 最佳实践

遵循这些最佳实践，可以帮助您更高效、安全地使用群核科技 API，并确保应用程序的稳定性和性能。

## 🔐 安全性最佳实践

### API 密钥管理

**✅ 推荐做法：**
- 将 API 密钥存储在环境变量中，不要硬编码到代码里
- 使用配置文件或密钥管理服务
- 定期轮换 API 密钥
- 为不同环境使用不同的密钥

```javascript
// ✅ 正确：使用环境变量
const apiKey = process.env.KUJIALE_API_KEY;

// ❌ 错误：硬编码密钥
const apiKey = "kj_live_abc123def456";
```

**❌ 避免做法：**
- 在前端代码中暴露 API 密钥
- 将密钥提交到版本控制系统
- 与他人共享生产环境密钥

### 请求安全

```javascript
// ✅ 正确的请求头设置
const headers = {
  'Authorization': `Bearer ${apiKey}`,
  'Content-Type': 'application/json',
  'User-Agent': 'YourApp/1.0.0',
  'X-Request-ID': generateRequestId() // 用于请求追踪
};
```

## ⚡ 性能优化

### 1. 使用批量 API

当需要处理多个资源时，优先使用批量 API：

```javascript
// ✅ 推荐：批量获取家具
const furnitureList = await api.furniture.batchGet({
  ids: ['furniture1', 'furniture2', 'furniture3']
});

// ❌ 避免：多次单独请求
const furniture1 = await api.furniture.get('furniture1');
const furniture2 = await api.furniture.get('furniture2');
const furniture3 = await api.furniture.get('furniture3');
```

### 2. 合理使用分页

对于大量数据，使用分页参数：

```javascript
// ✅ 推荐：分页获取
const response = await api.furniture.list({
  page: 1,
  pageSize: 50, // 建议 10-100 之间
  filters: {
    category: 'chair'
  }
});
```

### 3. 缓存策略

实现合理的缓存机制：

```javascript
class ApiClient {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟
  }

  async getWithCache(endpoint, params) {
    const cacheKey = `${endpoint}:${JSON.stringify(params)}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    
    const data = await this.api.get(endpoint, params);
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  }
}
```

## 🔄 错误处理和重试

### 实现指数退避重试

```javascript
async function retryWithBackoff(fn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      if (error.status >= 500 || error.status === 429) {
        const delay = Math.pow(2, i) * 1000; // 指数退避
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error; // 客户端错误不重试
      }
    }
  }
}

// 使用示例
const furniture = await retryWithBackoff(() => 
  api.furniture.get('furniture_id')
);
```

### 优雅的错误处理

```javascript
class ApiError extends Error {
  constructor(message, status, code, details) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

async function handleApiCall(apiCall) {
  try {
    return await apiCall();
  } catch (error) {
    if (error.status === 401) {
      // 处理认证错误
      throw new ApiError('认证失败，请检查 API 密钥', 401, 'AUTH_FAILED');
    } else if (error.status === 429) {
      // 处理速率限制
      throw new ApiError('请求频率过高，请稍后重试', 429, 'RATE_LIMITED');
    } else if (error.status >= 500) {
      // 处理服务器错误
      throw new ApiError('服务暂时不可用', error.status, 'SERVER_ERROR');
    } else {
      // 处理其他错误
      throw new ApiError(error.message, error.status, 'CLIENT_ERROR', error.details);
    }
  }
}
```

## 📊 监控和调试

### 请求日志

```javascript
class ApiClient {
  async request(method, endpoint, data) {
    const requestId = generateRequestId();
    const startTime = Date.now();
    
    console.log(`[${requestId}] ${method} ${endpoint} - 开始请求`);
    
    try {
      const response = await this.httpClient.request({
        method,
        url: endpoint,
        data,
        headers: {
          'X-Request-ID': requestId
        }
      });
      
      const duration = Date.now() - startTime;
      console.log(`[${requestId}] ${method} ${endpoint} - 成功 (${duration}ms)`);
      
      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[${requestId}] ${method} ${endpoint} - 失败 (${duration}ms)`, error);
      throw error;
    }
  }
}
```

### 性能监控

```javascript
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0
    };
  }
  
  recordRequest(duration, success) {
    this.metrics.requestCount++;
    if (!success) this.metrics.errorCount++;
    
    // 计算平均响应时间
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (this.metrics.requestCount - 1) + duration) / 
      this.metrics.requestCount;
  }
  
  getHealthCheck() {
    const errorRate = this.metrics.errorCount / this.metrics.requestCount;
    return {
      ...this.metrics,
      errorRate,
      status: errorRate < 0.05 ? 'healthy' : 'degraded'
    };
  }
}
```

## 🚀 开发效率

### 使用 SDK

我们强烈推荐使用官方 SDK，而不是直接调用 REST API：

```javascript
// ✅ 推荐：使用 SDK
import { KujialeSdk } from '@kujiale/api-sdk';

const client = new KujialeSdk({
  apiKey: process.env.KUJIALE_API_KEY,
  environment: 'production'
});

const furniture = await client.furniture.get('furniture_id');

// ❌ 不推荐：直接 HTTP 调用
const response = await fetch('https://api.kujiale.com/furniture-design/v1/furniture/furniture_id', {
  headers: {
    'Authorization': `Bearer ${apiKey}`
  }
});
```

### 类型安全

如果使用 TypeScript，充分利用类型定义：

```typescript
import { FurnitureData, CreateFurnitureRequest } from '@kujiale/api-types';

async function createFurniture(data: CreateFurnitureRequest): Promise<FurnitureData> {
  return await client.furniture.create(data);
}
```

## 📋 Checklist

部署前检查清单：

- [ ] API 密钥安全存储
- [ ] 实现适当的错误处理
- [ ] 配置重试机制
- [ ] 添加请求日志
- [ ] 设置监控和告警
- [ ] 进行性能测试
- [ ] 实现缓存策略
- [ ] 编写单元测试

## 📞 技术支持

如果您在实施这些最佳实践时遇到问题：

- 查看 [错误处理指南](/docs/getting-started/error-handling)
- 访问 [开发者社区](https://forum.kujiale.com)
- 联系技术支持：<EMAIL> 