---
openapi: "3.1.0"
info:
  title: "户型图例管理API"
  description: "户型图例管理服务REST API\n\n提供图例数据的完整生命周期管理接口，图例是户型设计中用于标识空间元素的核心组件。支持单个和批\
    量操作，包括图例的创建、查询、更新、删除以及复杂的图例组合功能。\n\n**主要功能特性：**\n- 单个图例CRUD操作：创建、获取、更新、删除单个图例\n\
    - 批量操作支持：高效的批量创建、获取、更新、删除操作\n- 分页查询：支持大量图例数据的分页展示和管理\n- 图例组合：支持创建图例组合，实现复杂的空间布\
    局\n- 异步处理：批量操作采用异步处理机制，确保大数据量操作的性能\n- 权限控制：严格的读写权限控制，保障数据安全\n\n**业务场景应用：**\n-\
    \ 户型设计工具中的图例管理\n- 空间布局设计和优化\n- 图例模板和组合的快速应用\n- 户型数据的批量导入导出\n- 三维空间定位和图层管理\n\n\
    **技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 异步操作：支持长时间批量操作的异步处\
    理"
  contact:
    name: "群核设计服务开发团队"
    url: "https://wiki.manycore.com/design-services"
    email: "<EMAIL>"
  license:
    name: "群核科技专有许可证"
    url: "https://manycore.com/license"
  version: "1.0.0"
servers:
- url: "http://localhost:8080"
  description: "本地开发环境"
- url: "https://api-dev.qunhe.com"
  description: "开发测试环境"
- url: "https://api.qunhe.com"
  description: "生产环境"
tags:
- name: "图例管理接口"
  description: "提供图例数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询、图例组合等功能。图例是户型设计中用于标识空间元素的核心组件。"
paths:
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}:generatecoloredfloorplan:
    post:
      tags:
      - "colored-floor-plan-rest-api-controller"
      operationId: "getColoredFloorPlan"
      parameters:
      - name: "obsDesignId"
        in: "path"
        required: true
        schema:
          type: "string"
      - name: "levelId"
        in: "path"
        required: true
        schema:
          type: "string"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ColoredFloorPlanGetRequest"
        required: true
      responses:
        200:
          description: "OK"
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/OperationLegendImageResponse"
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legendgroup:batchcreate:
    post:
      tags:
      - "高级功能"
      - "批量操作"
      - "图例管理接口"
      summary: "批量创建图例组合"
      description: "根据图例组合信息批量创建图例。适用于套装图例创建、主题户型搭建等场景。可以一次性创建多个相关联的图例实例。"
      operationId: "batchCreateLegendGroup"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LegendGroupBatchCreateRequest"
        required: true
      responses:
        200:
          description: "批量创建图例组合操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                图例组合创建响应示例:
                  description: "图例组合创建响应示例"
                  value:
                    operationId: "op_group_create_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - id: "legend_001"
                        productId: "ObQ1gR2nF8kE"
                      - id: "legend_002"
                        productId: "ObR3hS4mG9lF"
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如图例组合信息错误、位置冲突等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:
    get:
      tags:
      - "图例查询"
      - "图例管理接口"
      summary: "获取图例列表"
      description: "分页获取指定设计方案和楼层下的图例列表。图例按照图层序号排序，适用于图例管理界面和户型展示场景。"
      operationId: "listLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID，用于标识特定的户型设计方案"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID，用于标识设计方案中的特定楼层"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "pageSize"
        in: "query"
        description: "每页记录数，控制单次返回的数据量。建议值：10-50，最大值：100"
        required: true
        schema:
          maximum: 100
          minimum: 1
          type: "integer"
          format: "int32"
        example: 20
      - name: "pageNum"
        in: "query"
        description: "页码，从0开始。实际返回数据范围：[pageNum*pageSize, pageNum*pageSize+pageSize)"
        required: true
        schema:
          minimum: 0
          type: "integer"
          format: "int32"
        example: 0
      responses:
        404:
          description: "指定的设计方案或楼层不存在，或用户无权限访问"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功获取图例列表"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LegendListResponse"
              examples:
                图例列表响应示例:
                  description: "图例列表响应示例"
                  value:
                    totalCount: 15
                    legendUnitDataList:
                    - id: "legend_001"
                      productId: "ObQ1gR2nF8kE"
                      size:
                        x: 1.2
                        y: 0.8
                        z: 2.4
                      transform:
                        position:
                          x: 2.5
                          y: 0.0
                          z: 3.2
                        rotate:
                          x: 0.0
                          y: 1.57
                          z: 0.0
                      index: 1.0
                      version: "v1.0"
        500:
          description: "服务器内部错误，请稍后重试"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数无效，如页码超出范围、页大小不合理等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
    post:
      tags:
      - "图例管理"
      - "图例管理接口"
      summary: "创建单个图例"
      description: "在指定位置创建一个新的图例实例。适用于用户添加图例、导入户型数据等场景。支持完整的图例属性配置。"
      operationId: "createLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SingleLegendCreateRequest"
        required: true
      responses:
        400:
          description: "请求数据无效，如产品ID不存在、尺寸参数错误、位置冲突等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "图例创建成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LegendUnitData"
              examples:
                创建成功响应示例:
                  description: "创建成功响应示例"
                  value:
                    id: "legend_002"
                    productId: "ObQ1gR2nF8kE"
                    size:
                      x: 1.2
                      y: 0.8
                      z: 2.4
                    transform:
                      position:
                        x: 2.5
                        y: 0.0
                        z: 3.2
                      rotate:
                        x: 0.0
                        y: 1.57
                        z: 0.0
                    index: 1.0
                    version: "v1.0"
        500:
          description: "服务器内部错误，创建失败"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:batchupdate:
    post:
      tags:
      - "批量操作"
      - "图例管理接口"
      summary: "批量更新图例"
      description: "根据提供的图例信息列表批量更新图例。支持异步处理，适用于批量编辑、批量调整等场景。每个图例必须包含有效的ID。"
      operationId: "batchUpdateLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LegendBatchUpdateRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如图例ID缺失、更新数据格式错误等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量更新操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量更新响应示例:
                  description: "批量更新响应示例"
                  value:
                    operationId: "op_batch_update_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - id: "legend_001"
                        productId: "ObQ1gR2nF8kE"
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:batchget:
    post:
      tags:
      - "批量操作"
      - "图例管理接口"
      summary: "批量获取图例信息"
      description: "根据图例ID列表批量获取图例信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个图例。"
      operationId: "batchGetLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LegendBatchGetRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如图例ID列表为空、ID格式错误等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量获取操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量获取响应示例:
                  description: "批量获取响应示例"
                  value:
                    operationId: "op_batch_get_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - id: "legend_001"
                        productId: "ObQ1gR2nF8kE"
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:batchdelete:
    post:
      tags:
      - "批量操作"
      - "图例管理接口"
      summary: "批量删除图例"
      description: "根据图例ID列表批量删除图例。删除操作不可逆，适用于批量清理、户型重置等场景。返回成功删除的图例ID列表。"
      operationId: "batchDeleteLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LegendBatchDeleteRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如图例ID列表为空、ID格式错误等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量删除操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量删除响应示例:
                  description: "批量删除响应示例"
                  value:
                    operationId: "op_batch_delete_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - "legend_001"
                      - "legend_002"
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend:batchcreate:
    post:
      tags:
      - "批量操作"
      - "图例管理接口"
      summary: "批量创建图例"
      description: "批量创建多个图例实例。异步接口，支持大量数据处理。初次请求时operationId为空，后续可通过返回的operationId轮\
        询结果。适用于批量导入、户型复制等场景。"
      operationId: "batchCreateLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/LegendBatchCreateRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如图例数据格式错误、数量超出限制等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量创建操作已启动，请通过operationId查询进度"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量创建响应示例:
                  description: "批量创建响应示例"
                  value:
                    operationId: "op_batch_create_001"
                    status: "PROCESSING"
                    progress: 30
  /layout/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/legend/{legendId}:
    get:
      tags:
      - "图例查询"
      - "图例管理接口"
      summary: "获取单个图例信息"
      description: "根据图例ID获取特定图例的详细信息。适用于图例详情展示、编辑前数据加载等场景。"
      operationId: "getSingleLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "legendId"
        in: "path"
        description: "图例的唯一标识符，由系统生成"
        required: true
        schema:
          type: "string"
        example: "legend_001"
      responses:
        400:
          description: "请求参数格式错误，如图例ID格式不正确"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，请稍后重试"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        404:
          description: "指定ID的图例不存在，或用户无权限访问"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功获取图例信息"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LegendUnitData"
              examples:
                图例详情响应示例:
                  description: "图例详情响应示例"
                  value:
                    id: "legend_001"
                    productId: "ObQ1gR2nF8kE"
                    size:
                      x: 1.2
                      y: 0.8
                      z: 2.4
                    transform:
                      position:
                        x: 2.5
                        y: 0.0
                        z: 3.2
                      rotate:
                        x: 0.0
                        y: 1.57
                        z: 0.0
                    index: 1.0
                    version: "v1.0"
    put:
      tags:
      - "图例管理"
      - "图例管理接口"
      summary: "更新单个图例"
      description: "更新指定ID的图例信息，支持位置调整、尺寸修改、图层调整等操作。适用于图例编辑、户型调整等场景。"
      operationId: "updateLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "legendId"
        in: "path"
        description: "要更新的图例ID"
        required: true
        schema:
          type: "string"
        example: "legend_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SingleLegendUpdateRequest"
        required: true
      responses:
        200:
          description: "图例更新成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LegendUnitData"
              examples:
                更新成功响应示例:
                  description: "更新成功响应示例"
                  value:
                    id: "legend_001"
                    productId: "ObQ1gR2nF8kE"
                    size:
                      x: 1.5
                      y: 0.8
                      z: 2.4
                    transform:
                      position:
                        x: 3.0
                        y: 0.0
                        z: 3.2
                      rotate:
                        x: 0.0
                        y: 3.14
                        z: 0.0
                    index: 2.0
                    version: "v1.1"
        404:
          description: "指定ID的图例不存在"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，更新失败"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如图例ID格式错误、更新参数不合理等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
    delete:
      tags:
      - "图例管理"
      - "图例管理接口"
      summary: "删除单个图例"
      description: "从指定位置删除一个图例实例。删除操作不可逆，适用于用户移除图例、清理户型等场景。"
      operationId: "deleteLegend"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "legendId"
        in: "path"
        description: "要删除的图例ID"
        required: true
        schema:
          type: "string"
        example: "legend_001"
      responses:
        200:
          description: "图例删除成功"
          content:
            application/json:
              schema:
                type: "boolean"
              examples:
                删除成功响应:
                  description: "删除成功响应"
                  value: true
        404:
          description: "指定ID的图例不存在或已被删除"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，删除失败"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数无效，如图例ID格式错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
components:
  schemas:
    ColoredFloorPlanGetRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
      description: "彩色户型图获取请求，用于生成带有颜色和图例的完整户型图。采用异步处理模式，适用于高质量户型图渲染。"
      example:
        operationId: "render_plan_001"
        requestId: "req_render_001"
    LegendImageResponse:
      type: "object"
      properties:
        layoutLegendImg:
          type: "string"
          description: "户型图例综合图像URL，包含完整的户型布局和所有图例元素。适用于客户展示和方案确认。"
          format: "uri"
          example: "https://example.com/images/layout_legend_001.png"
        reviewImg:
          type: "string"
          description: "审核预览图像URL，用于方案审核和快速预览。包含关键设计要素，便于快速理解方案内容。"
          format: "uri"
          example: "https://example.com/images/review_001.png"
        wallOnlyImg:
          type: "string"
          description: "纯墙体图像URL，只包含墙体结构，不含图例元素。适用于结构分析和施工图纸。"
          format: "uri"
          example: "https://example.com/images/walls_001.png"
        legendOnlyImg:
          type: "string"
          description: "纯图例图像URL，只包含图例元素，不含墙体结构。适用于家具布置分析和装饰方案讨论。"
          format: "uri"
          example: "https://example.com/images/legends_001.png"
        layoutLegendImagePsd:
          type: "string"
          description: "户型图例PSD文件URL，Adobe Photoshop格式的分层设计文件。支持专业工具的深度编辑。"
          format: "uri"
          example: "https://example.com/files/layout_001.psd"
      description: "图例图像响应，包含户型设计的多种图像视图。提供从完整户型图到专项图例的全方位图像资源。"
      example:
        layoutLegendImg: "https://example.com/images/layout_legend_001.png"
        reviewImg: "https://example.com/images/review_001.png"
        wallOnlyImg: "https://example.com/images/walls_001.png"
        legendOnlyImg: "https://example.com/images/legends_001.png"
        layoutLegendImagePsd: "https://example.com/files/layout_001.psd"
    OperationLegendImageResponse:
      type: "object"
      properties:
        operationId:
          type: "string"
        metaData:
          type: "object"
          additionalProperties:
            type: "object"
        done:
          type: "boolean"
        result:
          $ref: "#/components/schemas/LegendImageResponse"
        error:
          $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
    LegendGroupBatchCreateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/LegendGroupUnitData"
      description: "图例组合批量创建请求，包含图例组合信息和位置数据"
      example:
        batchRequests:
        - productId: "ObQ1gR2nF8kE"
          size:
            x: 2.0
            y: 1.0
            z: 0.8
          transform:
            position:
              x: 3.0
              y: 0.0
              z: 2.0
            rotate:
              x: 0.0
              y: 0.0
              z: 0.0
          index: 1
        requestId: "req_group_create_001"
    LegendGroupUnitData:
      required:
      - "index"
      - "productId"
      - "size"
      - "transform"
      type: "object"
      properties:
        productId:
          pattern: "^[A-Za-z0-9+/=]{12,}$"
          type: "string"
          description: "图例组合关联的产品ID，必须是有效的加密产品ID。定义了组合单元的基本属性和外观。"
          example: "ObQ1gR2nF8kE"
        size:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        transform:
          $ref: "#/components/schemas/Transform"
        index:
          maximum: 999999
          minimum: 0
          type: "integer"
          description: "图例组合的图层序号，控制组合中所有图例的基础显示层级。值越大显示越靠前。"
          format: "int32"
          example: 1
      description: "图例组合单元数据模型，用于批量创建相关图例。支持预定义的组合模板和自定义组合创建。"
      example:
        productId: "ObQ1gR2nF8kE"
        size:
          x: 2.0
          y: 1.0
          z: 0.8
        transform:
          position:
            x: 3.0
            y: 0.0
            z: 2.0
          rotate:
            x: 0.0
            y: 0.0
            z: 0.0
        index: 1
    Transform:
      type: "object"
      properties:
        position:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        rotate:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
      description: "三维空间变换数据，包含位置和旋转信息。用于定义物体在空间中的精确位置和朝向。"
      example:
        position:
          x: 2.5
          y: 0.0
          z: 3.2
        rotate:
          x: 0.0
          y: 1.57
          z: 0.0
    LegendUnitData:
      required:
      - "index"
      - "productId"
      - "size"
      - "transform"
      type: "object"
      properties:
        id:
          maxLength: 64
          pattern: "^[a-zA-Z0-9_-]+$"
          type: "string"
          description: "图例的唯一标识符，由系统自动生成。创建时可为空，更新时必须提供。"
          example: "legend_001"
        productId:
          pattern: "^[A-Za-z0-9+/=]{12,}$"
          type: "string"
          description: "图例关联的产品ID，必须是有效的加密产品ID。产品定义了图例的基本属性和外观。"
          example: "ObQ1gR2nF8kE"
        size:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        transform:
          $ref: "#/components/schemas/Transform"
        index:
          maximum: 999999
          minimum: 0
          type: "number"
          description: "图例的图层序号，控制显示层级。值越大显示越靠前，支持小数以便精确控制层级。"
          format: "double"
          example: 1.0
        version:
          maxLength: 16
          type: "string"
          description: "图例数据的版本标识，用于数据版本控制和兼容性管理。"
          example: "v1.0"
      description: "图例单元数据模型，包含图例的完整属性信息。图例是户型设计中用于表示空间元素的核心数据结构。"
      example:
        id: "legend_001"
        productId: "ObQ1gR2nF8kE"
        size:
          x: 1.2
          y: 0.8
          z: 2.4
        transform:
          position:
            x: 2.5
            y: 0.0
            z: 3.2
          rotate:
            x: 0.0
            y: 1.57
            z: 0.0
        index: 1.0
        version: "v1.0"
    SingleLegendCreateRequest:
      required:
      - "legendUnitData"
      type: "object"
      properties:
        requestId:
          type: "string"
        legendUnitData:
          $ref: "#/components/schemas/LegendUnitData"
      description: "创建图例的请求数据，包含产品ID、尺寸、位置、图层等信息"
      example:
        legendUnitData:
          productId: "ObQ1gR2nF8kE"
          size:
            x: 1.2
            y: 0.8
            z: 2.4
          transform:
            position:
              x: 2.5
              y: 0.0
              z: 3.2
            rotate:
              x: 0.0
              y: 1.57
              z: 0.0
          index: 1.0
          version: "v1.0"
    LegendListResponse:
      required:
      - "legendUnitDataList"
      type: "object"
      properties:
        totalCount:
          type: "integer"
          format: "int32"
        legendUnitDataList:
          type: "array"
          description: "当前页的图例数据列表，包含每个图例的完整属性信息。通常按图层序号或创建时间排序。"
          items:
            $ref: "#/components/schemas/LegendUnitData"
      description: "图例列表响应，包含分页的图例数据列表和总数信息。用于图例管理界面的分页展示。"
      example:
        totalCount: 25
        legendUnitDataList:
        - id: "legend_001"
          productId: "ObQ1gR2nF8kE"
          size:
            x: 1.2
            y: 0.8
            z: 2.4
          transform:
            position:
              x: 2.5
              y: 0.0
              z: 3.2
            rotate:
              x: 0.0
              y: 1.57
              z: 0.0
          index: 1.0
          version: "v1.0"
    LegendBatchUpdateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/LegendUnitData"
      description: "批量更新请求，包含要更新的图例列表（每个图例必须包含有效ID）"
      example:
        batchRequests:
        - id: "legend_001"
          productId: "ObQ1gR2nF8kE"
          size:
            x: 1.5
            y: 0.8
            z: 2.4
          transform:
            position:
              x: 3.0
              y: 0.0
              z: 3.2
            rotate:
              x: 0.0
              y: 3.14
              z: 0.0
          index: 2.0
        requestId: "req_batch_update_001"
    LegendBatchGetRequest:
      required:
      - "batchRequests"
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          description: "要获取的图例ID列表，每个ID必须是有效的图例标识符。不存在的图例会在响应中标记。单次最多支持100个图例。"
          example:
          - "legend_001"
          - "legend_002"
          - "legend_003"
          items:
            type: "string"
            description: "要获取的图例ID列表，每个ID必须是有效的图例标识符。不存在的图例会在响应中标记。单次最多支持100个图例。"
            example: "[\"legend_001\",\"legend_002\",\"legend_003\"]"
      description: "批量获取请求，包含要获取的图例ID列表（最多100个）"
      example:
        batchRequests:
        - "legend_001"
        - "legend_002"
        - "legend_003"
    LegendBatchDeleteRequest:
      required:
      - "batchRequests"
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          description: "要删除的图例ID列表，每个ID必须是有效的图例标识符。系统会忽略不存在的图例ID。单次最多支持100个图例。"
          example:
          - "legend_001"
          - "legend_002"
          - "legend_003"
          items:
            type: "string"
            description: "要删除的图例ID列表，每个ID必须是有效的图例标识符。系统会忽略不存在的图例ID。单次最多支持100个图例。"
            example: "[\"legend_001\",\"legend_002\",\"legend_003\"]"
      description: "批量删除请求，包含要删除的图例ID列表"
      example:
        batchRequests:
        - "legend_001"
        - "legend_002"
        - "legend_003"
    LegendBatchCreateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/LegendUnitData"
      description: "批量创建请求，包含要创建的图例列表（最多100个）和requestId"
      example:
        batchRequests:
        - productId: "ObQ1gR2nF8kE"
          size:
            x: 1.2
            y: 0.8
            z: 2.4
          transform:
            position:
              x: 2.5
              y: 0.0
              z: 3.2
            rotate:
              x: 0.0
              y: 1.57
              z: 0.0
          index: 1.0
        requestId: "req_batch_create_001"
    SingleLegendUpdateRequest:
      required:
      - "legendUnitData"
      type: "object"
      properties:
        requestId:
          type: "string"
        legendUnitData:
          $ref: "#/components/schemas/LegendUnitData"
      description: "更新图例的请求数据，包含要修改的字段"
      example:
        legendUnitData:
          id: "legend_001"
          productId: "ObQ1gR2nF8kE"
          size:
            x: 1.5
            y: 0.8
            z: 2.4
          transform:
            position:
              x: 3.0
              y: 0.0
              z: 3.2
            rotate:
              x: 0.0
              y: 3.14
              z: 0.0
          index: 2.0
          version: "v1.1"
