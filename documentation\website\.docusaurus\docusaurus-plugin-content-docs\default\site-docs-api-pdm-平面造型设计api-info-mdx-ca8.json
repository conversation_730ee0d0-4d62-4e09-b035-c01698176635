{"id": "api/pdm/平面造型设计api", "title": "平面造型设计API", "description": "平面造型设计REST API", "source": "@site/docs/api/pdm/平面造型设计api.info.mdx", "sourceDirName": "api/pdm", "slug": "/api/pdm/平面造型设计api", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计api", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "sidebarPosition": 0, "frontMatter": {"id": "平面造型设计api", "title": "平面造型设计API", "description": "平面造型设计REST API", "sidebar_label": "Introduction", "sidebar_position": 0, "hide_title": true, "custom_edit_url": null}, "sidebar": "pdmSidebar", "next": {"title": "平面造型设计管理接口", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计管理接口"}}