{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"tutorialSidebar": [{"type": "link", "label": "开始使用", "href": "/manycoreapi-demo/0.0.4/docs/", "docId": "intro", "unlisted": false}, {"type": "category", "label": "快速开始", "items": [{"type": "link", "label": "身份认证", "href": "/manycoreapi-demo/0.0.4/docs/getting-started/authentication", "docId": "getting-started/authentication", "unlisted": false}, {"type": "link", "label": "发起请求", "href": "/manycoreapi-demo/0.0.4/docs/getting-started/making-requests", "docId": "getting-started/making-requests", "unlisted": false}, {"type": "link", "label": "错误处理", "href": "/manycoreapi-demo/0.0.4/docs/getting-started/error-handling", "docId": "getting-started/error-handling", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "开发指南", "items": [{"type": "link", "label": "最佳实践", "href": "/manycoreapi-demo/0.0.4/docs/guides/best-practices", "docId": "guides/best-practices", "unlisted": false}, {"type": "link", "label": "速率限制", "href": "/manycoreapi-demo/0.0.4/docs/guides/rate-limiting", "docId": "guides/rate-limiting", "unlisted": false}, {"type": "link", "label": "Webhooks", "href": "/manycoreapi-demo/0.0.4/docs/guides/webhooks", "docId": "guides/webhooks", "unlisted": false}, {"type": "link", "label": "SDK 开发包", "href": "/manycoreapi-demo/0.0.4/docs/guides/sdks", "docId": "guides/sdks", "unlisted": false}], "collapsed": true, "collapsible": true}], "cameraSidebar": [{"type": "link", "label": "Introduction", "href": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api", "docId": "api/camera/camera-infrastructure-api", "unlisted": false}, {"type": "category", "label": "Camera Infrastructure", "items": [{"type": "link", "label": "获取相机列表", "href": "/manycoreapi-demo/0.0.4/docs/api/camera/get-cameras", "className": "api-method get", "docId": "api/camera/get-cameras", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure"}], "doorwindowSidebar": [{"type": "link", "label": "Introduction", "href": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗设计管理api", "docId": "api/doorwindow/门窗设计管理api", "unlisted": false}, {"type": "category", "label": "门窗管理 API", "items": [{"type": "link", "label": "批量更新门窗", "href": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/batch-update", "className": "api-method post", "docId": "api/doorwindow/batch-update", "unlisted": false}, {"type": "link", "label": "获取门窗文档", "href": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/get-doc", "className": "api-method get", "docId": "api/doorwindow/get-doc", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗管理-api"}], "furnitureSidebar": [{"type": "link", "label": "Introduction", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具设计管理api", "docId": "api/furniture/家具设计管理api", "unlisted": false}, {"type": "category", "label": "家具管理接口", "items": [{"type": "link", "label": "获取家具列表", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/get-furniture-list", "className": "api-method get", "docId": "api/furniture/get-furniture-list", "unlisted": false}, {"type": "link", "label": "创建单个家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/create-furniture", "className": "api-method post", "docId": "api/furniture/create-furniture", "unlisted": false}, {"type": "link", "label": "批量更新家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-update-furniture", "className": "api-method post", "docId": "api/furniture/batch-update-furniture", "unlisted": false}, {"type": "link", "label": "批量获取家具信息", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-get-furniture", "className": "api-method post", "docId": "api/furniture/batch-get-furniture", "unlisted": false}, {"type": "link", "label": "批量删除家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-delete-furniture", "className": "api-method post", "docId": "api/furniture/batch-delete-furniture", "unlisted": false}, {"type": "link", "label": "批量创建家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture", "className": "api-method post", "docId": "api/furniture/batch-create-furniture", "unlisted": false}, {"type": "link", "label": "通过组合商品批量创建家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture-by-group-product", "className": "api-method post", "docId": "api/furniture/batch-create-furniture-by-group-product", "unlisted": false}, {"type": "link", "label": "获取单个家具信息", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/get-single-furniture", "className": "api-method get", "docId": "api/furniture/get-single-furniture", "unlisted": false}, {"type": "link", "label": "更新单个家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/update-furniture", "className": "api-method put", "docId": "api/furniture/update-furniture", "unlisted": false}, {"type": "link", "label": "删除单个家具", "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/delete-furniture", "className": "api-method delete", "docId": "api/furniture/delete-furniture", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具管理接口"}], "kooluxSidebar": [{"type": "link", "label": "Introduction", "href": "/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api", "docId": "api/koolux/koolux-rest-api", "unlisted": false}, {"type": "category", "label": "KooLuxLight", "items": [{"type": "link", "label": "创建KooLux方案", "href": "/manycoreapi-demo/0.0.4/docs/api/koolux/import-data-source", "className": "api-method post", "docId": "api/koolux/import-data-source", "unlisted": false}, {"type": "link", "label": "查询照明场景数据（建设中，暂不可用）", "href": "/manycoreapi-demo/0.0.4/docs/api/koolux/get-scene-document", "className": "api-method get", "docId": "api/koolux/get-scene-document", "unlisted": false}, {"type": "link", "label": "批量更新照明场景数据", "href": "/manycoreapi-demo/0.0.4/docs/api/koolux/batch-update", "className": "api-method put", "docId": "api/koolux/batch-update", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/manycoreapi-demo/0.0.4/docs/api/koolux/koo-lux-light"}], "layoutSidebar": [{"type": "link", "label": "Introduction", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/户型图例管理api", "docId": "api/layout/户型图例管理api", "unlisted": false}, {"type": "category", "label": "图例管理接口", "items": [{"type": "link", "label": "批量创建图例组合", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend-group", "className": "api-method post", "docId": "api/layout/batch-create-legend-group", "unlisted": false}, {"type": "link", "label": "获取图例列表", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/list-legend", "className": "api-method get", "docId": "api/layout/list-legend", "unlisted": false}, {"type": "link", "label": "创建单个图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/create-legend", "className": "api-method post", "docId": "api/layout/create-legend", "unlisted": false}, {"type": "link", "label": "批量更新图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-update-legend", "className": "api-method post", "docId": "api/layout/batch-update-legend", "unlisted": false}, {"type": "link", "label": "批量获取图例信息", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-get-legend", "className": "api-method post", "docId": "api/layout/batch-get-legend", "unlisted": false}, {"type": "link", "label": "批量删除图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-delete-legend", "className": "api-method post", "docId": "api/layout/batch-delete-legend", "unlisted": false}, {"type": "link", "label": "批量创建图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend", "className": "api-method post", "docId": "api/layout/batch-create-legend", "unlisted": false}, {"type": "link", "label": "获取单个图例信息", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/get-single-legend", "className": "api-method get", "docId": "api/layout/get-single-legend", "unlisted": false}, {"type": "link", "label": "更新单个图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/update-legend", "className": "api-method put", "docId": "api/layout/update-legend", "unlisted": false}, {"type": "link", "label": "删除单个图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/delete-legend", "className": "api-method delete", "docId": "api/layout/delete-legend", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/manycoreapi-demo/0.0.4/docs/api/layout/图例管理接口"}, {"type": "category", "label": "colored-floor-plan-rest-api-controller", "items": [{"type": "link", "label": "getColoredFloorPlan", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/get-colored-floor-plan", "className": "api-method post", "docId": "api/layout/get-colored-floor-plan", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "高级功能", "items": [{"type": "link", "label": "批量创建图例组合", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend-group", "className": "api-method post", "docId": "api/layout/batch-create-legend-group", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "批量操作", "items": [{"type": "link", "label": "批量创建图例组合", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend-group", "className": "api-method post", "docId": "api/layout/batch-create-legend-group", "unlisted": false}, {"type": "link", "label": "批量更新图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-update-legend", "className": "api-method post", "docId": "api/layout/batch-update-legend", "unlisted": false}, {"type": "link", "label": "批量获取图例信息", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-get-legend", "className": "api-method post", "docId": "api/layout/batch-get-legend", "unlisted": false}, {"type": "link", "label": "批量删除图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-delete-legend", "className": "api-method post", "docId": "api/layout/batch-delete-legend", "unlisted": false}, {"type": "link", "label": "批量创建图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend", "className": "api-method post", "docId": "api/layout/batch-create-legend", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "图例查询", "items": [{"type": "link", "label": "获取图例列表", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/list-legend", "className": "api-method get", "docId": "api/layout/list-legend", "unlisted": false}, {"type": "link", "label": "获取单个图例信息", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/get-single-legend", "className": "api-method get", "docId": "api/layout/get-single-legend", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "图例管理", "items": [{"type": "link", "label": "创建单个图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/create-legend", "className": "api-method post", "docId": "api/layout/create-legend", "unlisted": false}, {"type": "link", "label": "更新单个图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/update-legend", "className": "api-method put", "docId": "api/layout/update-legend", "unlisted": false}, {"type": "link", "label": "删除单个图例", "href": "/manycoreapi-demo/0.0.4/docs/api/layout/delete-legend", "className": "api-method delete", "docId": "api/layout/delete-legend", "unlisted": false}], "collapsed": true, "collapsible": true}], "pdmSidebar": [{"type": "link", "label": "Introduction", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计api", "docId": "api/pdm/平面造型设计api", "unlisted": false}, {"type": "category", "label": "平面造型设计管理接口", "items": [{"type": "link", "label": "保存单个平面造型数据", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/save-planar-model-v-2", "className": "api-method post", "docId": "api/pdm/save-planar-model-v-2", "unlisted": false}, {"type": "link", "label": "批量删除平面造型数据", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-delete-planar-model", "className": "api-method post", "docId": "api/pdm/batch-delete-planar-model", "unlisted": false}, {"type": "link", "label": "批量保存平面造型数据", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-save-planar-model-v-2", "className": "api-method post", "docId": "api/pdm/batch-save-planar-model-v-2", "unlisted": false}, {"type": "link", "label": "根据平面造型ID删除单个平面造型数据", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/delete-planar-model", "className": "api-method delete", "docId": "api/pdm/delete-planar-model", "unlisted": false}, {"type": "link", "label": "分页获取平面造型数据列表", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model-list", "className": "api-method get", "docId": "api/pdm/fetch-planar-model-list", "unlisted": false}, {"type": "link", "label": "批量获取平面造型建模结果", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model-build-result", "className": "api-method post", "docId": "api/pdm/batch-fetch-planar-model-build-result", "unlisted": false}, {"type": "link", "label": "批量获取平面造型数据", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model", "className": "api-method post", "docId": "api/pdm/batch-fetch-planar-model", "unlisted": false}, {"type": "link", "label": "获取平面造型数据", "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model", "className": "api-method get", "docId": "api/pdm/fetch-planar-model", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计管理接口"}]}, "docs": {"api/camera/camera-infrastructure": {"id": "api/camera/camera-infrastructure", "title": "Camera Infrastructure", "description": "Camera Infrastructure", "sidebar": "cameraSidebar"}, "api/camera/camera-infrastructure-api": {"id": "api/camera/camera-infrastructure-api", "title": "Camera Infrastructure API", "description": "相机基础设施 REST API", "sidebar": "cameraSidebar"}, "api/camera/get-cameras": {"id": "api/camera/get-cameras", "title": "获取相机列表", "description": "根据指定的设计工具和相应参数获取相机列表。", "sidebar": "cameraSidebar"}, "api/doorwindow/batch-update": {"id": "api/doorwindow/batch-update", "title": "批量更新门窗", "description": "对指定设计和楼层下的门窗进行批量更新操作。支持replace（替换）、sdAttach/swAttach（简单门/窗附加）、detach（分离）、sdUpdate/swUpdate（简单门/窗更新）等多种操作类型。单次操作最多支持20个门窗，采用异步处理确保大批量操作的性能。", "sidebar": "doorwindowSidebar"}, "api/doorwindow/get-doc": {"id": "api/doorwindow/get-doc", "title": "获取门窗文档", "description": "根据方案ID和楼层ID获取门窗文档信息。返回指定楼层下的所有门窗数据，包括简单门窗（单一门窗单元）和复杂门窗（组合型门窗）。支持不同数据视图以控制返回数据的详细程度，优化查询性能。", "sidebar": "doorwindowSidebar"}, "api/doorwindow/门窗管理-api": {"id": "api/doorwindow/门窗管理-api", "title": "门窗管理 API", "description": "门窗管理 API", "sidebar": "doorwindowSidebar"}, "api/doorwindow/门窗设计管理api": {"id": "api/doorwindow/门窗设计管理api", "title": "门窗设计管理API", "description": "门窗设计服务REST API", "sidebar": "doorwindowSidebar"}, "api/furniture/batch-create-furniture": {"id": "api/furniture/batch-create-furniture", "title": "批量创建家具", "description": "批量创建多个家具实例。异步接口，支持大量数据处理。不保证幂等性，请在请求中包含requestId。初次请求时operationId为空，后续可通过返回的operationId轮询结果。适用于批量导入、场景复制等场景。", "sidebar": "furnitureSidebar"}, "api/furniture/batch-create-furniture-by-group-product": {"id": "api/furniture/batch-create-furniture-by-group-product", "title": "通过组合商品批量创建家具", "description": "根据商品组合信息批量创建家具。适用于套装家具创建、主题场景搭建等场景。可以一次性创建多个相关联的家具实例。", "sidebar": "furnitureSidebar"}, "api/furniture/batch-delete-furniture": {"id": "api/furniture/batch-delete-furniture", "title": "批量删除家具", "description": "根据家具ID列表批量删除家具。删除操作不可逆，适用于批量清理、场景重置等场景。返回成功删除的家具ID列表。", "sidebar": "furnitureSidebar"}, "api/furniture/batch-get-furniture": {"id": "api/furniture/batch-get-furniture", "title": "批量获取家具信息", "description": "根据家具ID列表批量获取家具信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个家具。", "sidebar": "furnitureSidebar"}, "api/furniture/batch-update-furniture": {"id": "api/furniture/batch-update-furniture", "title": "批量更新家具", "description": "根据提供的家具信息列表批量更新家具。支持异步处理，适用于批量编辑、批量调整等场景。每个家具必须包含有效的ID。", "sidebar": "furnitureSidebar"}, "api/furniture/create-furniture": {"id": "api/furniture/create-furniture", "title": "创建单个家具", "description": "在指定位置创建一个新的家具实例。支持幂等性控制，通过requestId防止重复创建。适用于用户添加家具、导入家具数据等场景。", "sidebar": "furnitureSidebar"}, "api/furniture/delete-furniture": {"id": "api/furniture/delete-furniture", "title": "删除单个家具", "description": "从指定位置删除一个家具实例。删除操作不可逆，适用于用户移除家具、清理场景等场景。", "sidebar": "furnitureSidebar"}, "api/furniture/get-furniture-list": {"id": "api/furniture/get-furniture-list", "title": "获取家具列表", "description": "分页获取指定设计方案和楼层下的家具列表。支持不同数据视图以优化性能，适用于家具展示和管理场景。", "sidebar": "furnitureSidebar"}, "api/furniture/get-single-furniture": {"id": "api/furniture/get-single-furniture", "title": "获取单个家具信息", "description": "根据家具ID获取特定家具的详细信息。适用于家具详情展示、编辑前数据加载等场景。", "sidebar": "furnitureSidebar"}, "api/furniture/update-furniture": {"id": "api/furniture/update-furniture", "title": "更新单个家具", "description": "更新指定ID的家具信息，支持位置调整、尺寸修改、材质替换等操作。适用于家具编辑、个性化定制等场景。", "sidebar": "furnitureSidebar"}, "api/furniture/家具管理接口": {"id": "api/furniture/家具管理接口", "title": "家具管理接口", "description": "家具管理接口", "sidebar": "furnitureSidebar"}, "api/furniture/家具设计管理api": {"id": "api/furniture/家具设计管理api", "title": "家具设计管理API", "description": "家具设计服务REST API", "sidebar": "furnitureSidebar"}, "api/koolux/batch-update": {"id": "api/koolux/batch-update", "title": "批量更新照明场景数据", "description": "批量更新照明场景中的模型、ies灯光数据，只针对照明对象生效", "sidebar": "kooluxSidebar"}, "api/koolux/get-scene-document": {"id": "api/koolux/get-scene-document", "title": "查询照明场景数据（建设中，暂不可用）", "description": "查询照明场景中的模型、ies灯光数据", "sidebar": "kooluxSidebar"}, "api/koolux/import-data-source": {"id": "api/koolux/import-data-source", "title": "创建KooLux方案", "description": "导入酷家乐方案到KooLux", "sidebar": "kooluxSidebar"}, "api/koolux/koo-lux-light": {"id": "api/koolux/koo-lux-light", "title": "KooLuxLight", "description": "KooLuxLight", "sidebar": "kooluxSidebar"}, "api/koolux/koolux-rest-api": {"id": "api/koolux/koolux-rest-api", "title": "KooLux REST API", "description": "KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。", "sidebar": "kooluxSidebar"}, "api/layout/batch-create-legend": {"id": "api/layout/batch-create-legend", "title": "批量创建图例", "description": "批量创建多个图例实例。异步接口，支持大量数据处理。初次请求时operationId为空，后续可通过返回的operationId轮询结果。适用于批量导入、户型复制等场景。", "sidebar": "layoutSidebar"}, "api/layout/batch-create-legend-group": {"id": "api/layout/batch-create-legend-group", "title": "批量创建图例组合", "description": "根据图例组合信息批量创建图例。适用于套装图例创建、主题户型搭建等场景。可以一次性创建多个相关联的图例实例。", "sidebar": "layoutSidebar"}, "api/layout/batch-delete-legend": {"id": "api/layout/batch-delete-legend", "title": "批量删除图例", "description": "根据图例ID列表批量删除图例。删除操作不可逆，适用于批量清理、户型重置等场景。返回成功删除的图例ID列表。", "sidebar": "layoutSidebar"}, "api/layout/batch-get-legend": {"id": "api/layout/batch-get-legend", "title": "批量获取图例信息", "description": "根据图例ID列表批量获取图例信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个图例。", "sidebar": "layoutSidebar"}, "api/layout/batch-update-legend": {"id": "api/layout/batch-update-legend", "title": "批量更新图例", "description": "根据提供的图例信息列表批量更新图例。支持异步处理，适用于批量编辑、批量调整等场景。每个图例必须包含有效的ID。", "sidebar": "layoutSidebar"}, "api/layout/create-legend": {"id": "api/layout/create-legend", "title": "创建单个图例", "description": "在指定位置创建一个新的图例实例。适用于用户添加图例、导入户型数据等场景。支持完整的图例属性配置。", "sidebar": "layoutSidebar"}, "api/layout/delete-legend": {"id": "api/layout/delete-legend", "title": "删除单个图例", "description": "从指定位置删除一个图例实例。删除操作不可逆，适用于用户移除图例、清理户型等场景。", "sidebar": "layoutSidebar"}, "api/layout/get-colored-floor-plan": {"id": "api/layout/get-colored-floor-plan", "title": "getColoredFloorPlan", "description": "getColoredFloorPlan", "sidebar": "layoutSidebar"}, "api/layout/get-single-legend": {"id": "api/layout/get-single-legend", "title": "获取单个图例信息", "description": "根据图例ID获取特定图例的详细信息。适用于图例详情展示、编辑前数据加载等场景。", "sidebar": "layoutSidebar"}, "api/layout/list-legend": {"id": "api/layout/list-legend", "title": "获取图例列表", "description": "分页获取指定设计方案和楼层下的图例列表。图例按照图层序号排序，适用于图例管理界面和户型展示场景。", "sidebar": "layoutSidebar"}, "api/layout/update-legend": {"id": "api/layout/update-legend", "title": "更新单个图例", "description": "更新指定ID的图例信息，支持位置调整、尺寸修改、图层调整等操作。适用于图例编辑、户型调整等场景。", "sidebar": "layoutSidebar"}, "api/layout/图例管理接口": {"id": "api/layout/图例管理接口", "title": "图例管理接口", "description": "图例管理接口", "sidebar": "layoutSidebar"}, "api/layout/户型图例管理api": {"id": "api/layout/户型图例管理api", "title": "户型图例管理API", "description": "户型图例管理服务REST API", "sidebar": "layoutSidebar"}, "api/pdm/batch-delete-planar-model": {"id": "api/pdm/batch-delete-planar-model", "title": "批量删除平面造型数据", "description": "根据平面造型ID批量删除平面造型数据", "sidebar": "pdmSidebar"}, "api/pdm/batch-fetch-planar-model": {"id": "api/pdm/batch-fetch-planar-model", "title": "批量获取平面造型数据", "description": "根据请求体中的平面造型ID批量获取平面造型数据", "sidebar": "pdmSidebar"}, "api/pdm/batch-fetch-planar-model-build-result": {"id": "api/pdm/batch-fetch-planar-model-build-result", "title": "批量获取平面造型建模结果", "description": "根据请求体中的平面造型ID批量获取平面造型的建模结果", "sidebar": "pdmSidebar"}, "api/pdm/batch-save-planar-model-v-2": {"id": "api/pdm/batch-save-planar-model-v-2", "title": "批量保存平面造型数据", "description": "批量保存平面造型数据", "sidebar": "pdmSidebar"}, "api/pdm/delete-planar-model": {"id": "api/pdm/delete-planar-model", "title": "根据平面造型ID删除单个平面造型数据", "description": "根据平面造型ID删除单个平面造型数据", "sidebar": "pdmSidebar"}, "api/pdm/fetch-planar-model": {"id": "api/pdm/fetch-planar-model", "title": "获取平面造型数据", "description": "根据平面造型ID获取单个平面造型数据", "sidebar": "pdmSidebar"}, "api/pdm/fetch-planar-model-list": {"id": "api/pdm/fetch-planar-model-list", "title": "分页获取平面造型数据列表", "description": "分页获取平面造型数据列表", "sidebar": "pdmSidebar"}, "api/pdm/save-planar-model-v-2": {"id": "api/pdm/save-planar-model-v-2", "title": "保存单个平面造型数据", "description": "保存单个平面造型数据", "sidebar": "pdmSidebar"}, "api/pdm/平面造型设计api": {"id": "api/pdm/平面造型设计api", "title": "平面造型设计API", "description": "平面造型设计REST API", "sidebar": "pdmSidebar"}, "api/pdm/平面造型设计管理接口": {"id": "api/pdm/平面造型设计管理接口", "title": "平面造型设计管理接口", "description": "平面造型设计管理接口", "sidebar": "pdmSidebar"}, "getting-started/authentication": {"id": "getting-started/authentication", "title": "身份认证", "description": "酷家乐 API 使用 API Key 进行身份认证。", "sidebar": "tutorialSidebar"}, "getting-started/error-handling": {"id": "getting-started/error-handling", "title": "错误处理", "description": "HTTP 状态码", "sidebar": "tutorialSidebar"}, "getting-started/making-requests": {"id": "getting-started/making-requests", "title": "发起请求", "description": "基础 URL", "sidebar": "tutorialSidebar"}, "guides/best-practices": {"id": "guides/best-practices", "title": "API 最佳实践", "description": "遵循这些最佳实践，可以帮助您更高效、安全地使用群核科技 API，并确保应用程序的稳定性和性能。", "sidebar": "tutorialSidebar"}, "guides/rate-limiting": {"id": "guides/rate-limiting", "title": "速率限制", "description": "为了确保服务的稳定性和公平使用，群核科技 API 实施了速率限制机制。了解这些限制并在您的应用中正确处理，是构建稳定应用的关键。", "sidebar": "tutorialSidebar"}, "guides/sdks": {"id": "guides/sdks", "title": "SDK 使用指南", "description": "群核科技提供多种编程语言的官方 SDK，帮助开发者快速集成我们的 API 服务。使用 SDK 可以大大简化开发流程，并提供更好的类型安全和错误处理。", "sidebar": "tutorialSidebar"}, "guides/webhooks": {"id": "guides/webhooks", "title": "Webhooks 事件通知", "description": "Webhooks 允许群核科技在特定事件发生时主动向您的应用发送 HTTP 请求，而无需您持续轮询我们的 API。这是一种高效的实时通知机制。", "sidebar": "tutorialSidebar"}, "intro": {"id": "intro", "title": "群核科技 API 文档", "description": "欢迎来到群核科技 API 文档中心！这里为开发者提供完整的 API 参考文档、开发指南和示例代码，帮助您快速集成群核科技的全栈家居云设计能力。", "sidebar": "tutorialSidebar"}}}}