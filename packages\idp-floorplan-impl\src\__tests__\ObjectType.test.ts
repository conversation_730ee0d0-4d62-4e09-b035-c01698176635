import { ObjectType } from '@qunhe/kls-abstraction';
import { serialize, validateType } from './ObjectTypeUtils';
import { describe, test, expect } from '@jest/globals';
import { createTypeBuilders } from '@qunhe/kls-runtime';

const { ObjectTypeBuilder } = createTypeBuilders({
    defaultRequired: true
});

/**
 * 验证基础类型的定义和序列化结果
 */
describe('ObjectType Builder BasicTypes Test', () => {
    interface SimpleType {
        a: number;
        b: boolean;
        c: string;
        d: number[];
        e: { f: number; g: boolean; h: string };
        f?: number;
        g?: number;
        h?: number;
    }
    // 测试数据和类型定义
    const validValue: SimpleType = {
        a: 1,
        b: true,
        c: 'hello',
        d: [1, 2, 3],
        e: {
            f: 1,
            g: true,
            h: 'world'
        },
        f: undefined,
        g: 2
    };

    const invalidValue: Record<string, any> = {
        a: 'not a number', // 应该是数字
        b: true,
        c: 'hello',
        d: ['not a number'], // 数组元素应该是数字
        e: {
            f: 1,
            g: 'not a boolean', // 应该是布尔值
            h: 'world'
        },
        f: undefined,
        g: 2
    };

    const type: ObjectType = new ObjectTypeBuilder()
        .addNumber('a')
        .addBoolean('b')
        .addString('c')
        .addArray('d', arrayTypeBuilder => {
            arrayTypeBuilder.withNumber();
        })
        .addObject('e', objectTypeBuilder => {
            objectTypeBuilder.addNumber('f');
            objectTypeBuilder.addBoolean('g');
            objectTypeBuilder.addString('h');
        })
        .addOptional('f', optionalTypeBuilder => {
            optionalTypeBuilder.withNumber();
        })
        .addOptional('g', optionalTypeBuilder => {
            optionalTypeBuilder.withNumber();
        })
        .addOptional('h', optionalTypeBuilder => {
            optionalTypeBuilder.withNumber();
        })
        .build();

    const expectedResult = {
        a: 1,
        b: true,
        c: 'hello',
        d: [1, 2, 3],
        e: {
            f: 1,
            g: true,
            h: 'world'
        },
        g: 2
    };

    describe('Type Validation', () => {
        test('Valid data should pass type validation', () => {
            expect(() => validateType(validValue, type)).not.toThrow();
        });

        test('Invalid data should throw type validation error', () => {
            expect(() => validateType(invalidValue, type)).toThrow();
        });

        test('Invalid data should provide detailed error information', () => {
            try {
                validateType(invalidValue, type);
                expect('should not reach here').toBe('should throw error');
            } catch (error) {
                if (error instanceof Error) {
                    expect(error.message).toContain('root.a');
                    expect(error.message).toContain('expect number');
                } else {
                    expect('should throw TypeValidationError').toBe(
                        'but throw other type error'
                    );
                }
            }
        });
    });

    describe('Serialization', () => {
        test('Valid data serialization result should be as expected', () => {
            const result = serialize(validValue, type);
            expect(result).toEqual(expectedResult);
        });

        test('Invalid data serialization should throw error when validation is enabled', () => {
            expect(() =>
                serialize(invalidValue, type, { validateBeforeSerialize: true })
            ).toThrow();
        });
    });
});
