var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_IDP_Events_on = injections.types["IDP_Events_on"];
    var var_injection_IDP_Events_once = injections.types["IDP_Events_once"];
    var var_injection_IDP_Events_off = injections.types["IDP_Events_off"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Platform = {};
    var var_getAppMode = {};
    var var_stringType = {};
    var var_getLocale = {};
    var var_getGlobalLengthUnit = {};
    var var_numberType = {};
    var var_getGlobalLengthPrecision = {};
    var var_AppMode = {};
    var var_LengthUnitType = {};
    var var_UI = {};
    var var_Design = {};
    var var_getDesignId = {};
    var var_getName = {};
    var var_getAllLevels = {};
    var var_Level_Array = {};
    var var_Level = {};
    var var_getCurrentLevel = {};
    var var_changeCurrentLevelAsync = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_User = {};
    var var_getUserId = {};
    var var_DB = {};
    var var_Types = {};
    var var_ElementType = {};
    var var_Interaction = {};
    var var_getSelectedElements = {};
    var var_ElementId_Array = {};
    var var_ElementId = {};
    var var_setSelectedElements = {};
    var var_Events = {};
    var var_Miniapp = {};
    var var_getUploadedDataAsync = {};
    var var_unknownType_Promise = {};
    var var_unknownType_Promise_then = {};
    var var_unknownType_Promise_then_onresolve = {};
    var var_uploadDataAsync = {};
    var var_MiniappUploadDataOption = {};
    var var_MiniappUploadDataResult_Promise = {};
    var var_MiniappUploadDataResult_Promise_then = {};
    var var_MiniappUploadDataResult_Promise_then_onresolve = {};
    var var_MiniappUploadDataResult = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_IDP_Events_on, exportName: "IDP_Events_on" },
        { value: var_injection_IDP_Events_once, exportName: "IDP_Events_once" },
        { value: var_injection_IDP_Events_off, exportName: "IDP_Events_off" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Platform": var_Platform,
        "UI": var_UI,
        "Design": var_Design,
        "User": var_User,
        "DB": var_DB,
        "Interaction": var_Interaction,
        "Events": var_Events,
        "Miniapp": var_Miniapp,
    };
    var_Platform.type = BasicType.Object;
    var_Platform.properties = {
        "getAppMode": var_getAppMode,
        "getLocale": var_getLocale,
        "getGlobalLengthUnit": var_getGlobalLengthUnit,
        "getGlobalLengthPrecision": var_getGlobalLengthPrecision,
        "AppMode": var_AppMode,
        "LengthUnitType": var_LengthUnitType,
    };
    var_getAppMode.type = BasicType.Function;
    var_getAppMode.name = "getAppMode";
    var_getAppMode.varying = false;
    var_getAppMode.keepArgsHandle = false;
    var_getAppMode.args = [];
    var_getAppMode.return = var_stringType;
    var_stringType.type = BasicType.String;
    var_getLocale.type = BasicType.Function;
    var_getLocale.name = "getLocale";
    var_getLocale.varying = false;
    var_getLocale.keepArgsHandle = false;
    var_getLocale.args = [];
    var_getLocale.return = var_stringType;
    var_getGlobalLengthUnit.type = BasicType.Function;
    var_getGlobalLengthUnit.name = "getGlobalLengthUnit";
    var_getGlobalLengthUnit.varying = false;
    var_getGlobalLengthUnit.keepArgsHandle = false;
    var_getGlobalLengthUnit.args = [];
    var_getGlobalLengthUnit.return = var_numberType;
    var_numberType.type = BasicType.Number;
    var_getGlobalLengthPrecision.type = BasicType.Function;
    var_getGlobalLengthPrecision.name = "getGlobalLengthPrecision";
    var_getGlobalLengthPrecision.varying = false;
    var_getGlobalLengthPrecision.keepArgsHandle = false;
    var_getGlobalLengthPrecision.args = [];
    var_getGlobalLengthPrecision.return = var_stringType;
    var_AppMode.type = BasicType.Object;
    var_AppMode.properties = {
        "DefaultMode": var_stringType,
        "SuperFloorplanMode": var_stringType,
        "CustomMode": var_stringType,
        "UnknownMode": var_stringType,
    };
    var_LengthUnitType.type = BasicType.Object;
    var_LengthUnitType.properties = {
        "mm": var_numberType,
        "m": var_numberType,
        "ft": var_numberType,
        "in": var_numberType,
    };
    var_UI.type = BasicType.Object;
    var_UI.properties = {
    };
    var_Design.type = BasicType.Object;
    var_Design.properties = {
        "getDesignId": var_getDesignId,
        "getName": var_getName,
        "getAllLevels": var_getAllLevels,
        "getCurrentLevel": var_getCurrentLevel,
        "changeCurrentLevelAsync": var_changeCurrentLevelAsync,
    };
    var_getDesignId.type = BasicType.Function;
    var_getDesignId.name = "getDesignId";
    var_getDesignId.varying = false;
    var_getDesignId.keepArgsHandle = false;
    var_getDesignId.args = [];
    var_getDesignId.return = var_stringType;
    var_getName.type = BasicType.Function;
    var_getName.name = "getName";
    var_getName.varying = false;
    var_getName.keepArgsHandle = false;
    var_getName.args = [];
    var_getName.return = var_stringType;
    var_getAllLevels.type = BasicType.Function;
    var_getAllLevels.name = "getAllLevels";
    var_getAllLevels.varying = false;
    var_getAllLevels.keepArgsHandle = false;
    var_getAllLevels.args = [];
    var_getAllLevels.return = var_Level_Array;
    var_Level_Array.type = BasicType.Array;
    var_Level_Array.value = var_Level;
    var_Level.type = BasicType.Object;
    var_Level.properties = {
        "id": var_stringType,
        "name": var_stringType,
        "index": var_numberType,
    };
    var_getCurrentLevel.type = BasicType.Function;
    var_getCurrentLevel.name = "getCurrentLevel";
    var_getCurrentLevel.varying = false;
    var_getCurrentLevel.keepArgsHandle = false;
    var_getCurrentLevel.args = [];
    var_getCurrentLevel.return = var_Level;
    var_changeCurrentLevelAsync.type = BasicType.Function;
    var_changeCurrentLevelAsync.name = "changeCurrentLevelAsync";
    var_changeCurrentLevelAsync.varying = false;
    var_changeCurrentLevelAsync.keepArgsHandle = false;
    var_changeCurrentLevelAsync.args = [var_stringType];
    var_changeCurrentLevelAsync.return = var_undefinedType_Promise;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_User.type = BasicType.Object;
    var_User.properties = {
        "getUserId": var_getUserId,
    };
    var_getUserId.type = BasicType.Function;
    var_getUserId.name = "getUserId";
    var_getUserId.varying = false;
    var_getUserId.keepArgsHandle = false;
    var_getUserId.args = [];
    var_getUserId.return = var_stringType;
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
        "ElementType": var_ElementType,
    };
    var_ElementType.type = BasicType.Object;
    var_ElementType.properties = {
        "Furniture": var_stringType,
        "FurnitureGroup": var_stringType,
        "Wardrobe": var_stringType,
        "WardrobeCopy": var_stringType,
        "Cabinet": var_stringType,
        "CabinetCopy": var_stringType,
        "DoorWindow": var_stringType,
        "DoorWindowCopy": var_stringType,
        "Skirting": var_stringType,
        "Paving": var_stringType,
        "DecoMolding": var_stringType,
        "PlanarModelingDesign": var_stringType,
        "StripLight": var_stringType,
        "DecoFreeStyleModel": var_stringType,
        "StripLightPlaster": var_stringType,
        "ParamCeiling": var_stringType,
        "CustomGroup": var_stringType,
        "ModelDoorWindow": var_stringType,
        "ModelMolding": var_stringType,
        "MixGroup": var_stringType,
        "Wall": var_stringType,
        "Opening": var_stringType,
        "Room": var_stringType,
        "SquareColumn": var_stringType,
        "Floor": var_stringType,
        "Beam": var_stringType,
        "DoorOpening": var_stringType,
        "WindowOpening": var_stringType,
        "RoomSeparator": var_stringType,
        "FloorOpening": var_stringType,
        "FurnitureLegend": var_stringType,
        "ParamLegend": var_stringType,
        "LegendGroup": var_stringType,
        "Zone": var_stringType,
        "CabinetRef": var_stringType,
        "CabinetCopyRef": var_stringType,
        "WardrobeRef": var_stringType,
        "WardrobeCopyRef": var_stringType,
        "CabinetSnapper": var_stringType,
        "CabinetCopySnapper": var_stringType,
        "WardrobeSnapper": var_stringType,
        "WardrobeCopySnapper": var_stringType,
        "CabinetSnapperGroup": var_stringType,
        "CabinetCopySnapperGroup": var_stringType,
        "WardrobeSnapperGroup": var_stringType,
        "WardrobeCopySnapperGroup": var_stringType,
        "GenericModel": var_stringType,
        "FrozenModel": var_stringType,
        "RoutingFace": var_stringType,
        "RoutingFaceGroup": var_stringType,
        "RoutingCube": var_stringType,
        "RoutingSocket": var_stringType,
    };
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "getSelectedElements": var_getSelectedElements,
        "setSelectedElements": var_setSelectedElements,
    };
    var_getSelectedElements.type = BasicType.Function;
    var_getSelectedElements.name = "getSelectedElements";
    var_getSelectedElements.varying = false;
    var_getSelectedElements.keepArgsHandle = false;
    var_getSelectedElements.args = [];
    var_getSelectedElements.return = var_ElementId_Array;
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_setSelectedElements.type = BasicType.Function;
    var_setSelectedElements.name = "setSelectedElements";
    var_setSelectedElements.varying = false;
    var_setSelectedElements.keepArgsHandle = false;
    var_setSelectedElements.args = [var_ElementId_Array];
    var_setSelectedElements.return = var_ElementId_Array;
    var_Events.type = BasicType.Object;
    var_Events.properties = {
        "on": var_injection_IDP_Events_on,
        "once": var_injection_IDP_Events_once,
        "off": var_injection_IDP_Events_off,
    };
    var_Miniapp.type = BasicType.Object;
    var_Miniapp.properties = {
        "getUploadedDataAsync": var_getUploadedDataAsync,
        "uploadDataAsync": var_uploadDataAsync,
    };
    var_getUploadedDataAsync.type = BasicType.Function;
    var_getUploadedDataAsync.name = "getUploadedDataAsync";
    var_getUploadedDataAsync.varying = false;
    var_getUploadedDataAsync.keepArgsHandle = false;
    var_getUploadedDataAsync.args = [];
    var_getUploadedDataAsync.return = var_unknownType_Promise;
    var_unknownType_Promise.type = BasicType.Object;
    var_unknownType_Promise.properties = {
        "then": var_unknownType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_unknownType_Promise_then.type = BasicType.Function;
    var_unknownType_Promise_then.name = "";
    var_unknownType_Promise_then.varying = false;
    var_unknownType_Promise_then.keepArgsHandle = true;
    var_unknownType_Promise_then.args = [var_unknownType_Promise_then_onresolve, var_Promise_then_onreject];
    var_unknownType_Promise_then.return = var_undefinedType;
    var_unknownType_Promise_then_onresolve.type = BasicType.Function;
    var_unknownType_Promise_then_onresolve.name = "";
    var_unknownType_Promise_then_onresolve.varying = false;
    var_unknownType_Promise_then_onresolve.keepArgsHandle = false;
    var_unknownType_Promise_then_onresolve.args = [var_unknownType];
    var_unknownType_Promise_then_onresolve.return = var_undefinedType;
    var_uploadDataAsync.type = BasicType.Function;
    var_uploadDataAsync.name = "uploadDataAsync";
    var_uploadDataAsync.varying = false;
    var_uploadDataAsync.keepArgsHandle = false;
    var_uploadDataAsync.args = [var_MiniappUploadDataOption];
    var_uploadDataAsync.return = var_MiniappUploadDataResult_Promise;
    var_MiniappUploadDataOption.type = BasicType.Object;
    var_MiniappUploadDataOption.properties = {
        "miniappId": var_stringType,
        "data": var_stringType,
    };
    var_MiniappUploadDataResult_Promise.type = BasicType.Object;
    var_MiniappUploadDataResult_Promise.properties = {
        "then": var_MiniappUploadDataResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_MiniappUploadDataResult_Promise_then.type = BasicType.Function;
    var_MiniappUploadDataResult_Promise_then.name = "";
    var_MiniappUploadDataResult_Promise_then.varying = false;
    var_MiniappUploadDataResult_Promise_then.keepArgsHandle = true;
    var_MiniappUploadDataResult_Promise_then.args = [var_MiniappUploadDataResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_MiniappUploadDataResult_Promise_then.return = var_undefinedType;
    var_MiniappUploadDataResult_Promise_then_onresolve.type = BasicType.Function;
    var_MiniappUploadDataResult_Promise_then_onresolve.name = "";
    var_MiniappUploadDataResult_Promise_then_onresolve.varying = false;
    var_MiniappUploadDataResult_Promise_then_onresolve.keepArgsHandle = false;
    var_MiniappUploadDataResult_Promise_then_onresolve.args = [var_MiniappUploadDataResult];
    var_MiniappUploadDataResult_Promise_then_onresolve.return = var_undefinedType;
    var_MiniappUploadDataResult.type = BasicType.Object;
    var_MiniappUploadDataResult.properties = {
        "uniqueId": var_stringType,
    };
    
    return var_sourceFile;
};
