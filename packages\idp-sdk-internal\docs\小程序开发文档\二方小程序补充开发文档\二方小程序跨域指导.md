主要介绍二方小程序访问酷家乐接口的一些做法和要求

## 现状&推荐做法

### 现状

鉴于二方对访问酷家乐接口的强需求，二方小程序页面计划统一采用`https://miniapp-cos.kujiale.com`域名（与其他酷家乐应用同站不同域），以便于获取部分`cookie`。但跨域限制仍在，访问酷家乐的相关服务接口会受到浏览器跨域策略的限制
  > 小程序对隔离性有硬性要求，推荐所有小程序按照三方小程序要求开发，同站实际上已经破坏了一部分隔离性

### 推荐做法

- 目标接口对来自`miniapp-cos.kujiale.com`的请求需要开放跨域访问
- 针对小程序页面需要读取的`cookie`，应将域设置为`.kujiale.com`，以确保小程序页面`js`能读取，不影响发送至`*.kujiale.com`的请求
  > 注意：针对不需要前端读取的`cookie`，可以不用额外设置域，但仍然需要完全符合`CORS`要求（参考[SameSite](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite#none)），以确保请求发送时携带相关`cookie`
- 跨域访问支持，由各自前端、后端协商实现，小程序组无法提供支持，实现方式自定
> 参考资料：[MDN_CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)，`CORS`的实现请务必严格按照上述内容实现，任何不满足要求的请求都会被浏览器阻止或者造成其他报错，包括且不仅限于无法获取`response`、请求被阻止、无法获得认证信息等问题，当遇见相关问题时可以查看`network`和`console`获取错误信息，并对照文档修正

### 其他注意事项

- 非`coohom`环境，小程序页面（`*.kujiale.com`域名）会与承载方位于同一站下，此时，浏览器会默认将`iframe`运行于主线程，而不是独立线程，因此`iframe`行为会影响承载方。请注意不要造成云图、`BIM`应用出现卡死等相关问题
- 严格禁止修改`document.domain`的行为
- `Chrome`在未来会修改三方`cookie`的一些行为，但是不会对`CORS`造成很大影响，但是会影响`cookie`的一些绑定行为，可能会造成一些认证相关信息的问题，具体可以参考[`privacysandbox`](https://privacysandbox.com/)获取相关内容
