// TODO: put yor non exported types here
declare namespace IDP {
    // TODO: put your exported apis here
    namespace Interaction {

        /**
         * 进入智能助手布局&风格
         * @internal
         */
        function goIntelligentLayoutAndMatch(): void;

        /**
         * 进入智能助手-布局助手
         * @internal
         */
        function goIntelligentWholeLayout(): void;

        /**
         * 进入智能助手-风格助手
         * @internal
         */
        function goIntelligentMatch(): void;

        /**
         * 进入智能助手-智能吊顶
         * @internal
         */
        function goIntelligentCeiling(): void;

        /**
         * 应用风格匹配
         * @param link 一个oss链接，里面是需要应用到方案的数据
         * @internal
         */
        function applyIntelligentMatchAsync(link: string): Promise<void>;

        /**
         * 开启/关闭应用样板间时的loading
         * @param status 开启/关闭
         * @internal
         */
        function toggleApplySampleRoomLoading(status: boolean): void;
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
