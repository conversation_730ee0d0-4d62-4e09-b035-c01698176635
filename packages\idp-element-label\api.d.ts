import { IDP as IDPCommon } from '@qunhe/idp-common';

/**
 * 设计对象标签配置
 */
interface ElementLabelConfig {
    /**
     * 全局唯一标识
     */
    id: number;
    /**
     * 标签键(商家维度唯一, 不同商家之间可能有相同)
     */
    key: string;
    /**
     * 标签名
     */
    name: string;
    /**
     * 标签类型
     */
    type: IDP.DB.Types.ElementLabelType;
    /**
     * 标签选项 仅ElementLabelType.option有
     */
    options?: string[];
}

/**
 * 设计对象标签数据
 */
interface ElementLabelData {
    /**
     * ElementLabelConfig id
     */
    id: number;

    /**
     * 标签值
     */
    value: string;
}

declare namespace IDP {
    namespace Integration {
        /**
         * 商家后台配置
         */
        namespace Catalog {
            /**
             * 获取当前商家的设计对象标签配置
             */
            function getElementLabelConfigsAsync(): Promise<ElementLabelConfig[]>;
        }
    }

    namespace DB {
        namespace Types {
            /**
             * 设计对象标签类型
             */
            enum ElementLabelType {
                /**
                 * 数字
                 */
                number = 0,
                /**
                 * 字符
                 */
                string = 1,
                /**
                 * 单选(字符)
                 */
                option = 2
            }

            /**
             * 设计对象标签写入错误类型
             */
            enum ElementLabelWriteErrorType {
                /**
                 * 标签配置校验失败: id不存在、标签超过数量上限(30)、key重复(如复制其它商家的方案已有相同key)等
                 */
                config = 'ElementLabel.ConfigError',
                /**
                 * 标签值校验失败: 非数字(自动转换)、值不在可选项列表等
                 */
                value = 'ElementLabel.ValueError',
                /**
                 * 设计对象校验失败: 待更新标签对象不存在、不支持、不可写入等
                 */
                element = 'ElementLabel.ElementError'
            }
        }

        namespace Methods {
            /**
             * 添加或更新指定设计对象标签
             * @param elementId 设计对象ID
             * @param updateData 待更新设计对象标签数据
             * @throws { Error } Error.name 见 {@link IDP.DB.Types.ElementLabelWriteErrorType}, Error.message 指出具体失败原因
             */
            function putElementLabelsAsync(
                elementId: IDPCommon.DB.Types.ElementId,
                updateData: ElementLabelData[]
            ): Promise<void>;
        }
    }
}

export { IDP };
// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
