var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_UnknownType = injections.types["UnknownType"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Custom = {};
    var var_Kada = {};
    var var_findCabinetSnapperList = {};
    var var_injection_UnknownType_Array = {};
    var var_findCabinetCopySnapperList = {};
    var var_findWardrobeSnapperList = {};
    var var_findWardrobeCopySnapperList = {};
    var var_isInKada = {};
    var var_stringType = {};
    var var_Kada_1 = {};
    var var_getSnapperUniquenessAsync = {};
    var var_getSnapperUniquenessAsync_option_objectLiteral = {};
    var var_ElementId_Array = {};
    var var_ElementId = {};
    var var_SnapperUniquenessResult_Promise = {};
    var var_SnapperUniquenessResult_Promise_then = {};
    var var_SnapperUniquenessResult_Promise_then_onresolve = {};
    var var_SnapperUniquenessResult = {};
    var var_SnapperUniquenessResult_result_objectLiteral_Array = {};
    var var_SnapperUniquenessResult_result_objectLiteral = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_openBudgetAsync = {};
    var var_openBudgetAsync_options_objectLiteral = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_RoutingFace = {};
    var var_enter = {};
    var var_EnterRoutingFaceOption = {};
    var var_stringType_Array = {};
    var var_EnterRoutingFaceOption_createAdsorptionData = {};
    var var_RoutingFaceForCreateAdsorption = {};
    var var_RoutingFaceForCreateAdsorption_dragRoutingSocket_objectLiteral = {};
    var var_Number2 = {};
    var var_numberType = {};
    var var_exit = {};
    var var_booleanType = {};
    var var_startDragSocketAsync = {};
    var var_ElementId_Promise = {};
    var var_ElementId_Promise_then = {};
    var var_ElementId_Promise_then_onresolve = {};
    var var_attachSocketToModel = {};
    var var_getRoutingSnapperGroupList = {};
    var var_findRoutingSnapperGroupById = {};
    var var_RoutingSnapperGroup = {};
    var var_Number3 = {};
    var var_RoutingSnapper_Array = {};
    var var_RoutingSnapper = {};
    var var_Connector_Array = {};
    var var_Connector = {};
    var var_RoutingFace_Array = {};
    var var_RoutingFace_1 = {};
    var var_Number3_Array_Array = {};
    var var_Number3_Array = {};
    var var_RoutingSocket_Array = {};
    var var_RoutingSocket = {};
    var var_RoutingCube_Array = {};
    var var_RoutingCube = {};
    var var_RoutingSnapperConnection_Array = {};
    var var_RoutingSnapperConnection = {};
    var var_getRoutingSocketList = {};
    var var_deleteRoutingSocket = {};
    var var_Detection = {};
    var var_detectRoutingFaceConnectivityAsync = {};
    var var_RoutingFaceConnectivityDetectionResult_Promise = {};
    var var_RoutingFaceConnectivityDetectionResult_Promise_then = {};
    var var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve = {};
    var var_RoutingFaceConnectivityDetectionResult = {};
    var var_forceConnectionDetectionAsync = {};
    var var_DetectionResult_Promise = {};
    var var_DetectionResult_Promise_then = {};
    var var_DetectionResult_Promise_then_onresolve = {};
    var var_DetectionResult = {};
    var var_spatialProximityDetectionAsync = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_UnknownType, exportName: "UnknownType" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Custom": var_Custom,
        "Kada": var_Kada_1,
    };
    var_Custom.type = BasicType.Object;
    var_Custom.properties = {
        "Kada": var_Kada,
    };
    var_Kada.type = BasicType.Object;
    var_Kada.properties = {
        "findCabinetSnapperList": var_findCabinetSnapperList,
        "findCabinetCopySnapperList": var_findCabinetCopySnapperList,
        "findWardrobeSnapperList": var_findWardrobeSnapperList,
        "findWardrobeCopySnapperList": var_findWardrobeCopySnapperList,
        "isInKada": var_isInKada,
    };
    var_findCabinetSnapperList.type = BasicType.Function;
    var_findCabinetSnapperList.name = "findCabinetSnapperList";
    var_findCabinetSnapperList.varying = false;
    var_findCabinetSnapperList.keepArgsHandle = false;
    var_findCabinetSnapperList.args = [];
    var_findCabinetSnapperList.return = var_injection_UnknownType_Array;
    var_injection_UnknownType_Array.type = BasicType.Array;
    var_injection_UnknownType_Array.value = var_injection_UnknownType;
    var_findCabinetCopySnapperList.type = BasicType.Function;
    var_findCabinetCopySnapperList.name = "findCabinetCopySnapperList";
    var_findCabinetCopySnapperList.varying = false;
    var_findCabinetCopySnapperList.keepArgsHandle = false;
    var_findCabinetCopySnapperList.args = [];
    var_findCabinetCopySnapperList.return = var_injection_UnknownType_Array;
    var_findWardrobeSnapperList.type = BasicType.Function;
    var_findWardrobeSnapperList.name = "findWardrobeSnapperList";
    var_findWardrobeSnapperList.varying = false;
    var_findWardrobeSnapperList.keepArgsHandle = false;
    var_findWardrobeSnapperList.args = [];
    var_findWardrobeSnapperList.return = var_injection_UnknownType_Array;
    var_findWardrobeCopySnapperList.type = BasicType.Function;
    var_findWardrobeCopySnapperList.name = "findWardrobeCopySnapperList";
    var_findWardrobeCopySnapperList.varying = false;
    var_findWardrobeCopySnapperList.keepArgsHandle = false;
    var_findWardrobeCopySnapperList.args = [];
    var_findWardrobeCopySnapperList.return = var_injection_UnknownType_Array;
    var_isInKada.type = BasicType.Function;
    var_isInKada.name = "isInKada";
    var_isInKada.varying = false;
    var_isInKada.keepArgsHandle = false;
    var_isInKada.args = [];
    var_isInKada.return = var_stringType;
    var_stringType.type = BasicType.String;
    var_Kada_1.type = BasicType.Object;
    var_Kada_1.properties = {
        "getSnapperUniquenessAsync": var_getSnapperUniquenessAsync,
        "openBudgetAsync": var_openBudgetAsync,
        "RoutingFace": var_RoutingFace,
        "Detection": var_Detection,
    };
    var_getSnapperUniquenessAsync.type = BasicType.Function;
    var_getSnapperUniquenessAsync.name = "getSnapperUniquenessAsync";
    var_getSnapperUniquenessAsync.varying = false;
    var_getSnapperUniquenessAsync.keepArgsHandle = false;
    var_getSnapperUniquenessAsync.args = [var_getSnapperUniquenessAsync_option_objectLiteral];
    var_getSnapperUniquenessAsync.return = var_SnapperUniquenessResult_Promise;
    var_getSnapperUniquenessAsync_option_objectLiteral.type = BasicType.Object;
    var_getSnapperUniquenessAsync_option_objectLiteral.properties = {
        "elements": var_ElementId_Array,
        "toolType": var_stringType,
    };
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_SnapperUniquenessResult_Promise.type = BasicType.Object;
    var_SnapperUniquenessResult_Promise.properties = {
        "then": var_SnapperUniquenessResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_SnapperUniquenessResult_Promise_then.type = BasicType.Function;
    var_SnapperUniquenessResult_Promise_then.name = "";
    var_SnapperUniquenessResult_Promise_then.varying = false;
    var_SnapperUniquenessResult_Promise_then.keepArgsHandle = true;
    var_SnapperUniquenessResult_Promise_then.args = [var_SnapperUniquenessResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_SnapperUniquenessResult_Promise_then.return = var_undefinedType;
    var_SnapperUniquenessResult_Promise_then_onresolve.type = BasicType.Function;
    var_SnapperUniquenessResult_Promise_then_onresolve.name = "";
    var_SnapperUniquenessResult_Promise_then_onresolve.varying = false;
    var_SnapperUniquenessResult_Promise_then_onresolve.keepArgsHandle = false;
    var_SnapperUniquenessResult_Promise_then_onresolve.args = [var_SnapperUniquenessResult];
    var_SnapperUniquenessResult_Promise_then_onresolve.return = var_undefinedType;
    var_SnapperUniquenessResult.type = BasicType.Object;
    var_SnapperUniquenessResult.properties = {
        "result": var_SnapperUniquenessResult_result_objectLiteral_Array,
    };
    var_SnapperUniquenessResult_result_objectLiteral_Array.type = BasicType.Array;
    var_SnapperUniquenessResult_result_objectLiteral_Array.value = var_SnapperUniquenessResult_result_objectLiteral;
    var_SnapperUniquenessResult_result_objectLiteral.type = BasicType.Object;
    var_SnapperUniquenessResult_result_objectLiteral.properties = {
        "elementId": var_ElementId,
        "uniqueValue": var_stringType,
    };
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_openBudgetAsync.type = BasicType.Function;
    var_openBudgetAsync.name = "openBudgetAsync";
    var_openBudgetAsync.varying = false;
    var_openBudgetAsync.keepArgsHandle = false;
    var_openBudgetAsync.args = [var_openBudgetAsync_options_objectLiteral];
    var_openBudgetAsync.return = var_undefinedType_Promise;
    var_openBudgetAsync_options_objectLiteral.type = BasicType.Object;
    var_openBudgetAsync_options_objectLiteral.properties = {
        "elements": var_ElementId_Array,
        "toolType": var_stringType,
    };
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_RoutingFace.type = BasicType.Object;
    var_RoutingFace.properties = {
        "enter": var_enter,
        "exit": var_exit,
        "startDragSocketAsync": var_startDragSocketAsync,
        "attachSocketToModel": var_attachSocketToModel,
        "getRoutingSnapperGroupList": var_getRoutingSnapperGroupList,
        "findRoutingSnapperGroupById": var_findRoutingSnapperGroupById,
        "getRoutingSocketList": var_getRoutingSocketList,
        "deleteRoutingSocket": var_deleteRoutingSocket,
    };
    var_enter.type = BasicType.Function;
    var_enter.name = "enter";
    var_enter.varying = false;
    var_enter.keepArgsHandle = false;
    var_enter.args = [var_EnterRoutingFaceOption];
    var_enter.return = var_undefinedType;
    var_EnterRoutingFaceOption.type = BasicType.Object;
    var_EnterRoutingFaceOption.properties = {
        "socketProductIds": var_stringType_Array,
        "createAdsorptionData": var_EnterRoutingFaceOption_createAdsorptionData,
    };
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_EnterRoutingFaceOption_createAdsorptionData.type = BasicType.Function;
    var_EnterRoutingFaceOption_createAdsorptionData.name = "createAdsorptionData";
    var_EnterRoutingFaceOption_createAdsorptionData.varying = false;
    var_EnterRoutingFaceOption_createAdsorptionData.keepArgsHandle = false;
    var_EnterRoutingFaceOption_createAdsorptionData.args = [var_RoutingFaceForCreateAdsorption];
    var_EnterRoutingFaceOption_createAdsorptionData.return = var_injection_UnknownType_Array;
    var_RoutingFaceForCreateAdsorption.type = BasicType.Object;
    var_RoutingFaceForCreateAdsorption.properties = {
        "belongsToSnapperElementId": var_ElementId,
        "dragRoutingSocket": var_RoutingFaceForCreateAdsorption_dragRoutingSocket_objectLiteral,
        "routingFace2d": var_injection_UnknownType,
    };
    var_RoutingFaceForCreateAdsorption_dragRoutingSocket_objectLiteral.type = BasicType.Object;
    var_RoutingFaceForCreateAdsorption_dragRoutingSocket_objectLiteral.properties = {
        "productId": var_stringType,
        "elementId": var_ElementId,
        "size": var_Number2,
    };
    var_Number2.type = BasicType.Object;
    var_Number2.properties = {
        "x": var_numberType,
        "y": var_numberType,
    };
    var_numberType.type = BasicType.Number;
    var_exit.type = BasicType.Function;
    var_exit.name = "exit";
    var_exit.varying = false;
    var_exit.keepArgsHandle = false;
    var_exit.args = [var_booleanType];
    var_exit.return = var_undefinedType;
    var_booleanType.type = BasicType.Boolean;
    var_startDragSocketAsync.type = BasicType.Function;
    var_startDragSocketAsync.name = "startDragSocketAsync";
    var_startDragSocketAsync.varying = false;
    var_startDragSocketAsync.keepArgsHandle = false;
    var_startDragSocketAsync.args = [var_stringType];
    var_startDragSocketAsync.return = var_ElementId_Promise;
    var_ElementId_Promise.type = BasicType.Object;
    var_ElementId_Promise.properties = {
        "then": var_ElementId_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ElementId_Promise_then.type = BasicType.Function;
    var_ElementId_Promise_then.name = "";
    var_ElementId_Promise_then.varying = false;
    var_ElementId_Promise_then.keepArgsHandle = true;
    var_ElementId_Promise_then.args = [var_ElementId_Promise_then_onresolve, var_Promise_then_onreject];
    var_ElementId_Promise_then.return = var_undefinedType;
    var_ElementId_Promise_then_onresolve.type = BasicType.Function;
    var_ElementId_Promise_then_onresolve.name = "";
    var_ElementId_Promise_then_onresolve.varying = false;
    var_ElementId_Promise_then_onresolve.keepArgsHandle = false;
    var_ElementId_Promise_then_onresolve.args = [var_ElementId];
    var_ElementId_Promise_then_onresolve.return = var_undefinedType;
    var_attachSocketToModel.type = BasicType.Function;
    var_attachSocketToModel.name = "attachSocketToModel";
    var_attachSocketToModel.varying = false;
    var_attachSocketToModel.keepArgsHandle = false;
    var_attachSocketToModel.args = [var_ElementId_Array];
    var_attachSocketToModel.return = var_undefinedType_Promise;
    var_getRoutingSnapperGroupList.type = BasicType.Function;
    var_getRoutingSnapperGroupList.name = "getRoutingSnapperGroupList";
    var_getRoutingSnapperGroupList.varying = false;
    var_getRoutingSnapperGroupList.keepArgsHandle = false;
    var_getRoutingSnapperGroupList.args = [];
    var_getRoutingSnapperGroupList.return = var_injection_UnknownType_Array;
    var_findRoutingSnapperGroupById.type = BasicType.Function;
    var_findRoutingSnapperGroupById.name = "findRoutingSnapperGroupById";
    var_findRoutingSnapperGroupById.varying = false;
    var_findRoutingSnapperGroupById.keepArgsHandle = false;
    var_findRoutingSnapperGroupById.args = [var_ElementId];
    var_findRoutingSnapperGroupById.return = var_RoutingSnapperGroup;
    var_RoutingSnapperGroup.type = BasicType.Object;
    var_RoutingSnapperGroup.properties = {
        "elementId": var_ElementId,
        "position": var_Number3,
        "rotation": var_Number3,
        "size": var_Number3,
        "children": var_RoutingSnapper_Array,
        "connection": var_RoutingSnapperConnection_Array,
    };
    var_Number3.type = BasicType.Object;
    var_Number3.properties = {
        "x": var_numberType,
        "y": var_numberType,
        "z": var_numberType,
    };
    var_RoutingSnapper_Array.type = BasicType.Array;
    var_RoutingSnapper_Array.value = var_RoutingSnapper;
    var_RoutingSnapper.type = BasicType.Object;
    var_RoutingSnapper.properties = {
        "productId": var_stringType,
        "elementId": var_ElementId,
        "position": var_Number3,
        "rotation": var_Number3,
        "size": var_Number3,
        "connectors": var_Connector_Array,
        "routingFaces": var_RoutingFace_Array,
        "routingCubes": var_RoutingCube_Array,
    };
    var_Connector_Array.type = BasicType.Array;
    var_Connector_Array.value = var_Connector;
    var_Connector.type = BasicType.Object;
    var_Connector.properties = {
        "id": var_stringType,
        "name": var_stringType,
        "type": var_numberType,
    };
    var_RoutingFace_Array.type = BasicType.Array;
    var_RoutingFace_Array.value = var_RoutingFace_1;
    var_RoutingFace_1.type = BasicType.Object;
    var_RoutingFace_1.properties = {
        "linkedProductId": var_stringType,
        "elementId": var_ElementId,
        "type": var_stringType,
        "name": var_stringType,
        "outLoop": var_Number3_Array_Array,
        "active": var_booleanType,
        "linkedSockets": var_RoutingSocket_Array,
    };
    var_Number3_Array_Array.type = BasicType.Array;
    var_Number3_Array_Array.value = var_Number3_Array;
    var_Number3_Array.type = BasicType.Array;
    var_Number3_Array.value = var_Number3;
    var_RoutingSocket_Array.type = BasicType.Array;
    var_RoutingSocket_Array.value = var_RoutingSocket;
    var_RoutingSocket.type = BasicType.Object;
    var_RoutingSocket.properties = {
        "productId": var_stringType,
        "elementId": var_ElementId,
        "name": var_stringType,
        "position": var_Number3,
        "rotation": var_Number3,
        "size": var_Number3,
        "isCreated": var_booleanType,
    };
    var_RoutingCube_Array.type = BasicType.Array;
    var_RoutingCube_Array.value = var_RoutingCube;
    var_RoutingCube.type = BasicType.Object;
    var_RoutingCube.properties = {
        "linkedProductId": var_stringType,
        "elementId": var_ElementId,
        "name": var_stringType,
        "active": var_booleanType,
    };
    var_RoutingSnapperConnection_Array.type = BasicType.Array;
    var_RoutingSnapperConnection_Array.value = var_RoutingSnapperConnection;
    var_RoutingSnapperConnection.type = BasicType.Object;
    var_RoutingSnapperConnection.properties = {
        "firstId": var_stringType,
        "firstSnapperId": var_stringType,
        "firstConnectorId": var_stringType,
        "secondId": var_stringType,
        "secondSnapperId": var_stringType,
        "secondConnectorId": var_stringType,
    };
    var_getRoutingSocketList.type = BasicType.Function;
    var_getRoutingSocketList.name = "getRoutingSocketList";
    var_getRoutingSocketList.varying = false;
    var_getRoutingSocketList.keepArgsHandle = false;
    var_getRoutingSocketList.args = [];
    var_getRoutingSocketList.return = var_RoutingSocket_Array;
    var_deleteRoutingSocket.type = BasicType.Function;
    var_deleteRoutingSocket.name = "deleteRoutingSocket";
    var_deleteRoutingSocket.varying = false;
    var_deleteRoutingSocket.keepArgsHandle = false;
    var_deleteRoutingSocket.args = [var_ElementId];
    var_deleteRoutingSocket.return = var_undefinedType;
    var_Detection.type = BasicType.Object;
    var_Detection.properties = {
        "detectRoutingFaceConnectivityAsync": var_detectRoutingFaceConnectivityAsync,
        "forceConnectionDetectionAsync": var_forceConnectionDetectionAsync,
        "spatialProximityDetectionAsync": var_spatialProximityDetectionAsync,
    };
    var_detectRoutingFaceConnectivityAsync.type = BasicType.Function;
    var_detectRoutingFaceConnectivityAsync.name = "detectRoutingFaceConnectivityAsync";
    var_detectRoutingFaceConnectivityAsync.varying = false;
    var_detectRoutingFaceConnectivityAsync.keepArgsHandle = false;
    var_detectRoutingFaceConnectivityAsync.args = [var_ElementId_Array];
    var_detectRoutingFaceConnectivityAsync.return = var_RoutingFaceConnectivityDetectionResult_Promise;
    var_RoutingFaceConnectivityDetectionResult_Promise.type = BasicType.Object;
    var_RoutingFaceConnectivityDetectionResult_Promise.properties = {
        "then": var_RoutingFaceConnectivityDetectionResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_RoutingFaceConnectivityDetectionResult_Promise_then.type = BasicType.Function;
    var_RoutingFaceConnectivityDetectionResult_Promise_then.name = "";
    var_RoutingFaceConnectivityDetectionResult_Promise_then.varying = false;
    var_RoutingFaceConnectivityDetectionResult_Promise_then.keepArgsHandle = true;
    var_RoutingFaceConnectivityDetectionResult_Promise_then.args = [var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_RoutingFaceConnectivityDetectionResult_Promise_then.return = var_undefinedType;
    var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve.type = BasicType.Function;
    var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve.name = "";
    var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve.varying = false;
    var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve.keepArgsHandle = false;
    var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve.args = [var_RoutingFaceConnectivityDetectionResult];
    var_RoutingFaceConnectivityDetectionResult_Promise_then_onresolve.return = var_undefinedType;
    var_RoutingFaceConnectivityDetectionResult.type = BasicType.Object;
    var_RoutingFaceConnectivityDetectionResult.properties = {
        "status": var_stringType,
        "requestElementId": var_ElementId_Array,
        "successed": var_ElementId_Array,
        "failed": var_ElementId_Array,
    };
    var_forceConnectionDetectionAsync.type = BasicType.Function;
    var_forceConnectionDetectionAsync.name = "forceConnectionDetectionAsync";
    var_forceConnectionDetectionAsync.varying = false;
    var_forceConnectionDetectionAsync.keepArgsHandle = false;
    var_forceConnectionDetectionAsync.args = [var_ElementId_Array];
    var_forceConnectionDetectionAsync.return = var_DetectionResult_Promise;
    var_DetectionResult_Promise.type = BasicType.Object;
    var_DetectionResult_Promise.properties = {
        "then": var_DetectionResult_Promise_then,
        "catch": var_Promise_catch,
    };
    var_DetectionResult_Promise_then.type = BasicType.Function;
    var_DetectionResult_Promise_then.name = "";
    var_DetectionResult_Promise_then.varying = false;
    var_DetectionResult_Promise_then.keepArgsHandle = true;
    var_DetectionResult_Promise_then.args = [var_DetectionResult_Promise_then_onresolve, var_Promise_then_onreject];
    var_DetectionResult_Promise_then.return = var_undefinedType;
    var_DetectionResult_Promise_then_onresolve.type = BasicType.Function;
    var_DetectionResult_Promise_then_onresolve.name = "";
    var_DetectionResult_Promise_then_onresolve.varying = false;
    var_DetectionResult_Promise_then_onresolve.keepArgsHandle = false;
    var_DetectionResult_Promise_then_onresolve.args = [var_DetectionResult];
    var_DetectionResult_Promise_then_onresolve.return = var_undefinedType;
    var_DetectionResult.type = BasicType.Object;
    var_DetectionResult.properties = {
        "pass": var_ElementId_Array,
        "fail": var_ElementId_Array,
        "untouched": var_ElementId_Array,
    };
    var_spatialProximityDetectionAsync.type = BasicType.Function;
    var_spatialProximityDetectionAsync.name = "spatialProximityDetectionAsync";
    var_spatialProximityDetectionAsync.varying = false;
    var_spatialProximityDetectionAsync.keepArgsHandle = false;
    var_spatialProximityDetectionAsync.args = [var_ElementId_Array];
    var_spatialProximityDetectionAsync.return = var_DetectionResult_Promise;
    
    return var_sourceFile;
};
