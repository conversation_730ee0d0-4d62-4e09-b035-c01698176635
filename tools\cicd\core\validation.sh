#!/bin/bash

# OpenAPI 规范验证脚本
# 验证 OpenAPI 规范文件的语法和结构
# 重构自 tools/ci-cd/ci/validation/validate-openapi.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# OpenAPI Generator 配置
OPENAPI_GENERATOR_JAR="/opt/openapi-generator/openapi-generator-cli-7.12.0.jar"

# 验证单个 OpenAPI 文件
validate_openapi_file() {
    local file="$1"
    local file_desc="${2:-OpenAPI 文件}"
    
    log_info "🔍 验证 $file_desc: $file"
    
    # 检查文件是否存在
    if ! check_file_exists "$file" "$file_desc"; then
        increment_counter "validation_failed"
        return 1
    fi
    
    # 检查文件是否为空
    if [ ! -s "$file" ]; then
        log_error "$file_desc 为空"
        increment_counter "validation_failed"
        return 1
    fi
    
    local validation_status=0
    
    # OpenAPI Generator 验证
    log_info "执行 OpenAPI Generator 验证..."
    if java -jar "$OPENAPI_GENERATOR_JAR" validate -i "$file" >/dev/null 2>&1; then
        log_success "OpenAPI Generator 验证通过"
        increment_counter "openapi_generator_passed"
    else
        log_error "OpenAPI Generator 验证失败"
        increment_counter "openapi_generator_failed"
        validation_status=1
        
        # 显示详细错误信息
        log_info "详细错误信息:"
        java -jar "$OPENAPI_GENERATOR_JAR" validate -i "$file" 2>&1 | head -20
    fi
    
    # 基本 YAML 语法检查
    log_info "执行 YAML 语法检查..."

    # 尝试多种 YAML 检查方法
    local yaml_check_success=false

    # 方法1: 使用 Python3 + PyYAML
    if command -v python3 >/dev/null 2>&1; then
        local yaml_error
        if yaml_error=$(python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>&1); then
            log_success "YAML 语法检查通过 (Python3)"
            increment_counter "yaml_syntax_passed"
            yaml_check_success=true
        else
            log_debug "Python3 YAML 检查失败: $yaml_error"
        fi
    fi

    # 方法2: 使用 yq (如果可用)
    if [ "$yaml_check_success" = false ] && command -v yq >/dev/null 2>&1; then
        if yq eval '.' "$file" >/dev/null 2>&1; then
            log_success "YAML 语法检查通过 (yq)"
            increment_counter "yaml_syntax_passed"
            yaml_check_success=true
        else
            log_debug "yq YAML 检查失败"
        fi
    fi

    # 方法3: 使用 yamllint (如果可用)
    if [ "$yaml_check_success" = false ] && command -v yamllint >/dev/null 2>&1; then
        if yamllint "$file" >/dev/null 2>&1; then
            log_success "YAML 语法检查通过 (yamllint)"
            increment_counter "yaml_syntax_passed"
            yaml_check_success=true
        else
            log_debug "yamllint YAML 检查失败"
        fi
    fi

    # 如果所有方法都失败
    if [ "$yaml_check_success" = false ]; then
        log_warning "YAML 语法检查跳过 (无可用的 YAML 检查工具)"
        log_info "提示: OpenAPI Generator 已验证文件语法正确性"
        # 不增加失败计数器，因为 OpenAPI Generator 已经验证了语法
    fi
    
    # 可选：Spectral 验证（如果可用）
    if command -v spectral >/dev/null 2>&1; then
        log_info "执行 Spectral 规范验证..."
        if spectral lint "$file" >/dev/null 2>&1; then
            log_success "Spectral 验证通过"
            increment_counter "spectral_passed"
        else
            log_warning "Spectral 验证失败（非阻塞）"
            increment_counter "spectral_failed"
            # Spectral 失败不阻塞流程
        fi
    else
        log_debug "Spectral 不可用，跳过验证"
    fi
    
    if [ $validation_status -eq 0 ]; then
        log_success "✅ $file_desc 验证通过"
        increment_counter "validation_passed"
        return 0
    else
        log_error "❌ $file_desc 验证失败"
        increment_counter "validation_failed"
        return 1
    fi
}

# 验证指定服务的 OpenAPI 文件
validate_service() {
    local service="$1"
    
    if ! is_valid_service "$service"; then
        log_error "无效的服务名: $service"
        return 1
    fi
    
    local openapi_path=$(get_service_openapi_path "$service")
    validate_openapi_file "$openapi_path" "服务 $service 的 OpenAPI 规范"
}

# 验证所有服务的 OpenAPI 文件
validate_all_services() {
    log_step "1" "验证所有服务的 OpenAPI 规范"
    
    local services=$(get_services)
    local overall_status=0
    
    log_info "开始验证服务: $services"
    
    for service in $services; do
        log_info "验证服务: $service"
        
        if validate_service "$service"; then
            log_success "服务 $service 验证通过"
        else
            log_error "服务 $service 验证失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 验证变更的文件
validate_changed_files() {
    log_step "1" "验证变更的 OpenAPI 文件"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过验证"
        return 0
    fi
    
    if [ -z "$CHANGED_API_FILES" ]; then
        log_warning "变更文件列表为空"
        return 0
    fi
    
    log_info "验证变更的文件: $CHANGED_API_FILES"
    
    local overall_status=0
    
    # 处理文件列表
    for file in $CHANGED_API_FILES; do
        if [ -n "$file" ]; then
            if validate_openapi_file "$file" "变更文件"; then
                log_success "文件 $file 验证通过"
            else
                log_error "文件 $file 验证失败"
                overall_status=1
            fi
            
            echo ""  # 添加空行分隔
        fi
    done
    
    return $overall_status
}

# 验证指定的文件列表
validate_file_list() {
    local files="$1"
    local description="${2:-指定文件}"
    
    log_step "1" "验证 $description"
    
    if [ -z "$files" ]; then
        log_warning "文件列表为空"
        return 0
    fi
    
    local overall_status=0
    
    for file in $files; do
        if [ -n "$file" ]; then
            if validate_openapi_file "$file" "$description"; then
                log_success "文件 $file 验证通过"
            else
                log_error "文件 $file 验证失败"
                overall_status=1
            fi
            
            echo ""  # 添加空行分隔
        fi
    done
    
    return $overall_status
}

# 显示验证统计
show_validation_stats() {
    log_step "2" "显示验证统计"
    
    echo ""
    log_info "🔍 验证结果统计:"
    
    local passed=$(get_counter "validation_passed")
    local failed=$(get_counter "validation_failed")
    local total=$((passed + failed))
    
    echo "  总文件数: $total"
    echo "  验证通过: $passed"
    echo "  验证失败: $failed"
    
    if [ $total -gt 0 ]; then
        local success_rate=$((passed * 100 / total))
        echo "  成功率: $success_rate%"
    fi
    
    echo ""
    echo "  OpenAPI Generator 通过: $(get_counter "openapi_generator_passed")"
    echo "  OpenAPI Generator 失败: $(get_counter "openapi_generator_failed")"
    echo "  YAML 语法检查通过: $(get_counter "yaml_syntax_passed")"
    echo "  YAML 语法检查失败: $(get_counter "yaml_syntax_failed")"
    
    if command -v spectral >/dev/null 2>&1; then
        echo "  Spectral 验证通过: $(get_counter "spectral_passed")"
        echo "  Spectral 验证失败: $(get_counter "spectral_failed")"
    fi
    
    show_stats "OpenAPI 验证统计"
}

# 主函数
main() {
    local mode="${1:-changed}"  # changed, all, service
    local target="${2:-}"       # 服务名或文件列表
    
    init_script "OpenAPI 验证" "验证 OpenAPI 规范文件的语法和结构"
    
    # 检查 OpenAPI Generator 是否可用
    if [ ! -f "$OPENAPI_GENERATOR_JAR" ]; then
        log_error "OpenAPI Generator 不可用: $OPENAPI_GENERATOR_JAR"
        finish_script "OpenAPI 验证" "false"
        exit 1
    fi
    
    local validation_status=0
    
    case "$mode" in
        "changed")
            log_info "验证模式: 变更文件"
            if ! validate_changed_files; then
                validation_status=1
            fi
            ;;
        "all")
            log_info "验证模式: 所有服务"
            if ! validate_all_services; then
                validation_status=1
            fi
            ;;
        "service")
            if [ -z "$target" ]; then
                log_error "服务验证模式需要指定服务名"
                finish_script "OpenAPI 验证" "false"
                exit 1
            fi
            log_info "验证模式: 单个服务 ($target)"
            if ! validate_service "$target"; then
                validation_status=1
            fi
            ;;
        "files")
            if [ -z "$target" ]; then
                log_error "文件验证模式需要指定文件列表"
                finish_script "OpenAPI 验证" "false"
                exit 1
            fi
            log_info "验证模式: 指定文件"
            if ! validate_file_list "$target" "指定文件"; then
                validation_status=1
            fi
            ;;
        *)
            log_error "无效的验证模式: $mode"
            log_info "支持的模式: changed, all, service, files"
            finish_script "OpenAPI 验证" "false"
            exit 1
            ;;
    esac
    
    show_validation_stats
    
    if [ $validation_status -eq 0 ]; then
        log_success "🎉 OpenAPI 验证完成，所有文件都通过验证！"
        finish_script "OpenAPI 验证" "true"
        return 0
    else
        log_error "❌ OpenAPI 验证失败，请修复错误后重试"
        finish_script "OpenAPI 验证" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "OpenAPI 验证模块已加载 ✅"
