import { KVector3d, KBoundingBox3d, KPoint3d } from '@qunhe/math-apaas-api';
import {} from '@qunhe/kls-abstraction';
import { IDP as IDPCommon } from '@qunhe/idp-common';

/**
 * 创建参数化吊顶时的入参类型
 * @internal
 */
interface IParamCeilingParam {
    /** 参数化吊顶的族类型 id */
    symbolId: string;
    /** 该参数化吊顶对应的参数值键值对 */
    /** @vm-type Parameters */
    parameters: {[key: string]: string | boolean | number};
    /** 位置信息 */
    position: KVector3d;
    /** 旋转信息 */
    rotation?: KVector3d;
}

/**
 * 返回的参数化吊顶数据结果
 * @internal
 */
interface IParamCeilingResult {
    /** 参数化吊顶的族类型 id */
    symbolId: string;
    /** 参数化吊顶的实例 id */
    elementId: string;
    /** 位置信息 */
    position: KVector3d;
    /** 旋转信息 */
    rotation: KVector3d;
    /** 该参数化吊顶对应的参数值键值对 */
    /** @vm-type Parameters */
    parameters: {[key: string]: string | boolean | number};
    /** 参数化吊顶包围盒的 Point3d 表达 */
    size: KPoint3d;
    /** 参数化吊顶包围盒的 BBox3d 表达 */
    bbox3d: KBoundingBox3d;
}

// TODO: put yor non exported types here
declare namespace IDP {
    namespace DB {
        namespace Methods {
            /**
             * 批量创建参数化吊顶
             * @param ceilingParams 批量创建参数化吊顶所需要的入参列表
             * @returns 包含了返回包含了返回参数化吊顶 elementId 的 promise
             * @internal
             */
            function createParamCeilingListAsync(ceilingParams: IParamCeilingParam[]): Promise<IDPCommon.DB.Types.ElementId[]>;
            /**
             * 批量查询参数化吊顶
             * @param elementIds
             * @returns 返回的参数化吊顶结果
             * @internal
             */
            function getParamCeilingList(elementIds: IDPCommon.DB.Types.ElementId[]): IParamCeilingResult[];
            /**
             * 批量删除参数化吊顶
             * @param elementIds
             * @returns 批量删除的结果: true-成功，false-失败
             * @internal
             */
            function deleteParamCeilingList(elementIds: IDPCommon.DB.Types.ElementId[]): boolean;
        }
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
