---
id: get-cameras
title: "获取相机列表"
description: "根据指定的设计工具和相应参数获取相机列表。"
sidebar_label: "获取相机列表"
hide_title: true
hide_table_of_contents: true
api: {"tags":["Camera Infrastructure"],"description":"根据指定的设计工具和相应参数获取相机列表。\n\n**酷家乐 (kujiale)**：\n- 路径参数：app=kujiale\n- 必需查询参数：designId, levelId\n- 可选查询参数：type\n","operationId":"getCameras","parameters":[{"name":"app","in":"path","description":"设计工具标识符，用于区分不同的群核设计工具\n","required":true,"schema":{"type":"string","description":"设计工具标识符枚举，定义群核旗下支持的设计工具类型\n- `kujiale`: 酷家乐 - 家装设计平台\n","enum":["kujiale"],"example":"kujiale","title":"ToolAppType"}},{"name":"type","in":"query","description":"相机类型过滤器，如果不指定则返回所有类型的相机\n- `normal`: 漫游视图\n- `panorama`: 全景图\n- `view3d`: 3D鸟瞰视图\n","required":false,"schema":{"type":"string","enum":["normal","panorama","view3d"]},"example":"normal"},{"name":"designId","in":"query","description":"设计ID，用于标识具体的设计方案\n**适用工具**: kujiale\n**使用条件**: 当app为kujiale时必需\n","required":false,"schema":{"type":"string"},"example":"3FONSDIEWO34"},{"name":"levelId","in":"query","description":"楼层ID，用于标识设计中的具体楼层\n**适用工具**: kujiale\n**使用条件**: 当app为kujiale时必需\n","required":false,"schema":{"type":"string"},"example":"M5HPLGIKTJME2AABAAAAADQ8"}],"responses":{"200":{"description":"成功获取相机列表","content":{"application/json":{"schema":{"title":"CameraListResponse","required":["elements"],"type":"object","properties":{"elements":{"type":"array","description":"相机列表，包含指定条件下的所有相机信息","items":{"title":"Camera","required":["id","type","imgUrl","source","app","cameraParameter"],"type":"object","properties":{"id":{"type":"string","description":"相机唯一标识符","example":"camera_001"},"imgUrl":{"type":"string","description":"预览图片URL地址，用于在列表中显示相机的缩略图","example":"https://example.com/preview/camera_001.jpg"},"source":{"title":"CameraSource","type":"string","description":"相机来源，标识相机创建的上下文环境","enum":["CAMERA_SCENE_UNSPECIFIED","RENDER_PAGE","ALBUM","DESIGN_PAGE"]},"type":{"title":"CameraType","type":"string","description":"相机类型，定义视角的观察模式","enum":["CAMERA_TYPE_UNSPECIFIED","NORMAL","PANORAMA","VIEW_3D"]},"app":{"type":"string","description":"设计工具标识符枚举，定义群核旗下支持的设计工具类型\n- `kujiale`: 酷家乐 - 家装设计平台\n","enum":["kujiale"],"example":"kujiale","title":"ToolAppType"},"cameraParameter":{"title":"CameraParameter","required":["position","lookAt","hfov","near"],"type":"object","properties":{"position":{"description":"相机在3D空间中的位置坐标","type":"object","required":["x","y","z"],"properties":{"x":{"type":"number","format":"double"},"y":{"type":"number","format":"double"},"z":{"type":"number","format":"double"}},"title":"Point3d"},"lookAt":{"description":"相机朝向目标点，相机镜头指向的3D空间位置","type":"object","required":["x","y","z"],"properties":{"x":{"type":"number","format":"double"},"y":{"type":"number","format":"double"},"z":{"type":"number","format":"double"}},"title":"Point3d"},"hfov":{"maximum":179,"minimum":1,"type":"number","description":"相机水平视场角，单位：度，控制相机的视野宽度","format":"double","example":60},"near":{"minimum":0.01,"type":"number","description":"近裁剪面距离，相机能够渲染的最近距离","format":"double","example":0.1}},"description":"相机参数配置，包含完整的3D相机渲染所需参数"},"createdTime":{"type":"integer","description":"创建时间戳，Unix时间戳格式，单位为毫秒","format":"int64","example":1640995200000},"metadata":{"type":"object","description":"工具特定的元数据信息","additionalProperties":true,"example":{"designId":"3FONSDIEWO34","levelId":"M5HPLGIKTJME2AABAAAAADQ8","toolVersion":"v2.1.0"}}},"description":"相机信息，包含完整的相机参数和元数据"}},"pagination":{"type":"object","description":"分页信息（预留字段，当前版本可能为空）","properties":{"total":{"type":"integer","description":"总记录数"},"page":{"type":"integer","description":"当前页码"},"pageSize":{"type":"integer","description":"每页大小"}}}},"description":"相机列表响应对象，支持跨设计工具的统一格式返回"},"examples":{"kujialeExample":{"summary":"酷家乐相机列表示例","value":{"elements":[{"id":"kj_camera_001","type":"NORMAL","imgUrl":"https://example.com/preview/kj_camera_001.jpg","source":"DESIGN_PAGE","app":"kujiale","cameraParameter":{"position":{"x":1.5,"y":1.8,"z":2},"lookAt":{"x":0,"y":0,"z":0},"hfov":60,"near":0.1},"createdTime":1640995200000}]}}}}}},"400":{"description":"请求参数错误，可能的原因：\n- 缺少必需的查询参数\n- 设计工具标识符不支持\n- 参数组合无效\n","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"unsupportedAppType":{"summary":"不支持的设计工具类型","value":{"code":400,"message":"Unsupported app type","status":"INVALID_ARGUMENT","details":{"reason":"UNSUPPORTED_APP_TYPE","message":"指定的设计工具类型不支持","domain":"camera.restapi.qunhe.com","metaData":{"providedApp":"unknown_app","supportedApps":"kujiale"}},"localizedMessage":{"locale":"zh-CN","message":"不支持的设计工具类型"}}},"invalidParameterCombination":{"summary":"参数组合无效","value":{"code":400,"message":"Invalid parameter combination for app","status":"INVALID_ARGUMENT","details":{"reason":"INVALID_PARAMETER_COMBINATION","message":"酷家乐需要designId和levelId参数","domain":"camera.restapi.qunhe.com","metaData":{"app":"kujiale","requiredParameters":"designId,levelId"}},"localizedMessage":{"locale":"zh-CN","message":"参数组合与指定的设计工具不匹配"}}}}}}},"403":{"description":"无权限访问资源，请确认用户具有相应的读取权限","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"permissionDenied":{"summary":"权限拒绝示例","value":{"code":403,"message":"Permission denied","status":"PERMISSION_DENIED","details":{"reason":"INSUFFICIENT_PERMISSIONS","message":"用户没有访问该资源的权限","domain":"camera.restapi.qunhe.com","metaData":{"app":"kujiale","resourceId":"3FONSDIEWO34","requiredPermission":"design:read"}},"localizedMessage":{"locale":"zh-CN","message":"权限不足，无法访问该资源"}}}}}}},"404":{"description":"资源不存在，请检查提供的参数是否正确","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"resourceNotFound":{"summary":"资源不存在示例","value":{"code":404,"message":"Resource not found","status":"NOT_FOUND","details":{"reason":"RESOURCE_NOT_FOUND","message":"指定的资源不存在","domain":"camera.restapi.qunhe.com","metaData":{"app":"kujiale","designId":"3FONSDIEWO34","levelId":"M5HPLGIKTJME2AABAAAAADQ8"}},"localizedMessage":{"locale":"zh-CN","message":"未找到指定的资源"}}}}}}},"500":{"description":"服务器内部错误，请稍后重试或联系技术支持","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"internalError":{"summary":"内部服务器错误示例","value":{"code":500,"message":"Internal server error","status":"INTERNAL","details":{"reason":"SERVICE_UNAVAILABLE","message":"相机服务暂时不可用","domain":"camera.restapi.qunhe.com","metaData":{"errorId":"ERR_CAMERA_001","timestamp":"2024-01-01T12:00:00Z"}},"localizedMessage":{"locale":"zh-CN","message":"服务器内部错误，请稍后重试"},"help":{"desc":"联系技术支持","url":"https://support.qunhe.com"}}}}}}}},"method":"get","path":"/camera/openapi/v1/apps/{app}/cameras","servers":[{"url":"http://localhost:8083","description":"本地开发环境"},{"url":"https://api-dev.qunhe.com","description":"开发测试环境"},{"url":"https://api.qunhe.com","description":"生产环境"}],"info":{"title":"Camera Infrastructure API","version":"0.0.1","description":"相机基础设施 REST API\n\n为群核旗下多个设计工具提供统一的相机查询和管理功能，支持多种相机类型的预设，\n帮助用户在设计过程中快速切换和保存不同的观察角度。\n\n**支持的设计工具：**\n详见 `ToolAppType` 枚举定义\n\n**支持的功能：**\n- 根据不同设计工具的标识和参数查询相机\n- 支持漫游视图、全景图、3D鸟瞰视图\n- 相机参数配置管理\n- 跨工具的相机数据统一格式\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：待定\n","contact":{"name":"设计服务开发团队","email":"<EMAIL>","url":"https://wiki.qunhe.com/design-api"},"license":{"name":"Proprietary","url":"https://qunhe.com/license"}},"postman":{"name":"获取相机列表","description":{"content":"根据指定的设计工具和相应参数获取相机列表。\n\n**酷家乐 (kujiale)**：\n- 路径参数：app=kujiale\n- 必需查询参数：designId, levelId\n- 可选查询参数：type\n","type":"text/plain"},"url":{"path":["camera","openapi","v1","apps",":app","cameras"],"host":["{{baseUrl}}"],"query":[{"disabled":false,"description":{"content":"相机类型过滤器，如果不指定则返回所有类型的相机\n- `normal`: 漫游视图\n- `panorama`: 全景图\n- `view3d`: 3D鸟瞰视图\n","type":"text/plain"},"key":"type","value":""},{"disabled":false,"description":{"content":"设计ID，用于标识具体的设计方案\n**适用工具**: kujiale\n**使用条件**: 当app为kujiale时必需\n","type":"text/plain"},"key":"designId","value":""},{"disabled":false,"description":{"content":"楼层ID，用于标识设计中的具体楼层\n**适用工具**: kujiale\n**使用条件**: 当app为kujiale时必需\n","type":"text/plain"},"key":"levelId","value":""}],"variable":[{"disabled":false,"description":{"content":"(Required) 设计工具标识符，用于区分不同的群核设计工具\n","type":"text/plain"},"type":"any","value":"","key":"app"}]},"header":[{"key":"Accept","value":"application/json"}],"method":"GET"}}
sidebar_class_name: "get api-method"
info_path: docs/api/camera/camera-infrastructure-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"获取相机列表"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/camera/openapi/v1/apps/{app}/cameras"}
  context={"endpoint"}
>
  
</MethodEndpoint>



根据指定的设计工具和相应参数获取相机列表。

**酷家乐 (kujiale)**：
- 路径参数：app=kujiale
- 必需查询参数：designId, levelId
- 可选查询参数：type


<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"app","in":"path","description":"设计工具标识符，用于区分不同的群核设计工具\n","required":true,"schema":{"type":"string","description":"设计工具标识符枚举，定义群核旗下支持的设计工具类型\n- `kujiale`: 酷家乐 - 家装设计平台\n","enum":["kujiale"],"example":"kujiale","title":"ToolAppType"}},{"name":"type","in":"query","description":"相机类型过滤器，如果不指定则返回所有类型的相机\n- `normal`: 漫游视图\n- `panorama`: 全景图\n- `view3d`: 3D鸟瞰视图\n","required":false,"schema":{"type":"string","enum":["normal","panorama","view3d"]},"example":"normal"},{"name":"designId","in":"query","description":"设计ID，用于标识具体的设计方案\n**适用工具**: kujiale\n**使用条件**: 当app为kujiale时必需\n","required":false,"schema":{"type":"string"},"example":"3FONSDIEWO34"},{"name":"levelId","in":"query","description":"楼层ID，用于标识设计中的具体楼层\n**适用工具**: kujiale\n**使用条件**: 当app为kujiale时必需\n","required":false,"schema":{"type":"string"},"example":"M5HPLGIKTJME2AABAAAAADQ8"}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"成功获取相机列表","content":{"application/json":{"schema":{"title":"CameraListResponse","required":["elements"],"type":"object","properties":{"elements":{"type":"array","description":"相机列表，包含指定条件下的所有相机信息","items":{"title":"Camera","required":["id","type","imgUrl","source","app","cameraParameter"],"type":"object","properties":{"id":{"type":"string","description":"相机唯一标识符","example":"camera_001"},"imgUrl":{"type":"string","description":"预览图片URL地址，用于在列表中显示相机的缩略图","example":"https://example.com/preview/camera_001.jpg"},"source":{"title":"CameraSource","type":"string","description":"相机来源，标识相机创建的上下文环境","enum":["CAMERA_SCENE_UNSPECIFIED","RENDER_PAGE","ALBUM","DESIGN_PAGE"]},"type":{"title":"CameraType","type":"string","description":"相机类型，定义视角的观察模式","enum":["CAMERA_TYPE_UNSPECIFIED","NORMAL","PANORAMA","VIEW_3D"]},"app":{"type":"string","description":"设计工具标识符枚举，定义群核旗下支持的设计工具类型\n- `kujiale`: 酷家乐 - 家装设计平台\n","enum":["kujiale"],"example":"kujiale","title":"ToolAppType"},"cameraParameter":{"title":"CameraParameter","required":["position","lookAt","hfov","near"],"type":"object","properties":{"position":{"description":"相机在3D空间中的位置坐标","type":"object","required":["x","y","z"],"properties":{"x":{"type":"number","format":"double"},"y":{"type":"number","format":"double"},"z":{"type":"number","format":"double"}},"title":"Point3d"},"lookAt":{"description":"相机朝向目标点，相机镜头指向的3D空间位置","type":"object","required":["x","y","z"],"properties":{"x":{"type":"number","format":"double"},"y":{"type":"number","format":"double"},"z":{"type":"number","format":"double"}},"title":"Point3d"},"hfov":{"maximum":179,"minimum":1,"type":"number","description":"相机水平视场角，单位：度，控制相机的视野宽度","format":"double","example":60},"near":{"minimum":0.01,"type":"number","description":"近裁剪面距离，相机能够渲染的最近距离","format":"double","example":0.1}},"description":"相机参数配置，包含完整的3D相机渲染所需参数"},"createdTime":{"type":"integer","description":"创建时间戳，Unix时间戳格式，单位为毫秒","format":"int64","example":1640995200000},"metadata":{"type":"object","description":"工具特定的元数据信息","additionalProperties":true,"example":{"designId":"3FONSDIEWO34","levelId":"M5HPLGIKTJME2AABAAAAADQ8","toolVersion":"v2.1.0"}}},"description":"相机信息，包含完整的相机参数和元数据"}},"pagination":{"type":"object","description":"分页信息（预留字段，当前版本可能为空）","properties":{"total":{"type":"integer","description":"总记录数"},"page":{"type":"integer","description":"当前页码"},"pageSize":{"type":"integer","description":"每页大小"}}}},"description":"相机列表响应对象，支持跨设计工具的统一格式返回"},"examples":{"kujialeExample":{"summary":"酷家乐相机列表示例","value":{"elements":[{"id":"kj_camera_001","type":"NORMAL","imgUrl":"https://example.com/preview/kj_camera_001.jpg","source":"DESIGN_PAGE","app":"kujiale","cameraParameter":{"position":{"x":1.5,"y":1.8,"z":2},"lookAt":{"x":0,"y":0,"z":0},"hfov":60,"near":0.1},"createdTime":1640995200000}]}}}}}},"400":{"description":"请求参数错误，可能的原因：\n- 缺少必需的查询参数\n- 设计工具标识符不支持\n- 参数组合无效\n","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"unsupportedAppType":{"summary":"不支持的设计工具类型","value":{"code":400,"message":"Unsupported app type","status":"INVALID_ARGUMENT","details":{"reason":"UNSUPPORTED_APP_TYPE","message":"指定的设计工具类型不支持","domain":"camera.restapi.qunhe.com","metaData":{"providedApp":"unknown_app","supportedApps":"kujiale"}},"localizedMessage":{"locale":"zh-CN","message":"不支持的设计工具类型"}}},"invalidParameterCombination":{"summary":"参数组合无效","value":{"code":400,"message":"Invalid parameter combination for app","status":"INVALID_ARGUMENT","details":{"reason":"INVALID_PARAMETER_COMBINATION","message":"酷家乐需要designId和levelId参数","domain":"camera.restapi.qunhe.com","metaData":{"app":"kujiale","requiredParameters":"designId,levelId"}},"localizedMessage":{"locale":"zh-CN","message":"参数组合与指定的设计工具不匹配"}}}}}}},"403":{"description":"无权限访问资源，请确认用户具有相应的读取权限","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"permissionDenied":{"summary":"权限拒绝示例","value":{"code":403,"message":"Permission denied","status":"PERMISSION_DENIED","details":{"reason":"INSUFFICIENT_PERMISSIONS","message":"用户没有访问该资源的权限","domain":"camera.restapi.qunhe.com","metaData":{"app":"kujiale","resourceId":"3FONSDIEWO34","requiredPermission":"design:read"}},"localizedMessage":{"locale":"zh-CN","message":"权限不足，无法访问该资源"}}}}}}},"404":{"description":"资源不存在，请检查提供的参数是否正确","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"resourceNotFound":{"summary":"资源不存在示例","value":{"code":404,"message":"Resource not found","status":"NOT_FOUND","details":{"reason":"RESOURCE_NOT_FOUND","message":"指定的资源不存在","domain":"camera.restapi.qunhe.com","metaData":{"app":"kujiale","designId":"3FONSDIEWO34","levelId":"M5HPLGIKTJME2AABAAAAADQ8"}},"localizedMessage":{"locale":"zh-CN","message":"未找到指定的资源"}}}}}}},"500":{"description":"服务器内部错误，请稍后重试或联系技术支持","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"examples":{"internalError":{"summary":"内部服务器错误示例","value":{"code":500,"message":"Internal server error","status":"INTERNAL","details":{"reason":"SERVICE_UNAVAILABLE","message":"相机服务暂时不可用","domain":"camera.restapi.qunhe.com","metaData":{"errorId":"ERR_CAMERA_001","timestamp":"2024-01-01T12:00:00Z"}},"localizedMessage":{"locale":"zh-CN","message":"服务器内部错误，请稍后重试"},"help":{"desc":"联系技术支持","url":"https://support.qunhe.com"}}}}}}}}}
>
  
</StatusCodes>


      