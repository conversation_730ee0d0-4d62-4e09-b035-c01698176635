import * as fse from 'fs-extra';
import * as child_process from 'child_process';
import * as path from 'path';
import { mergeApis } from './mergeApis';
import * as yargs from 'yargs';
import { cwdRelativePath } from './utils';

function execSync(cmd: string, options?: { cwd?: string; }) {
    console.log(`$ ${cmd}`);
    child_process.execSync(cmd, { stdio: 'inherit', cwd: options?.cwd });
}

const args = yargs
    .strict()
    .option('output-api-json', { type: 'boolean', default: false })
    .option('trim-internal-api', { type: 'boolean', default: false })
    .option('build-global-decl', { type: 'boolean', default: true })
    .argv as {
        'output-api-json': boolean;
        'trim-internal-api': boolean;
        'build-global-decl': boolean;
    };

const includePackages: string[] = fse.readJsonSync('include_packages.json');
const BuildDir = path.resolve('build');
const TempBuildDir = path.resolve(BuildDir, 'temp-build');

const apaasTypeGeneratorScript = path.resolve(__dirname, '../apaas-type-generator.js');
const dtsRollupScript = path.resolve(__dirname, '../dts-rollup.js');

fse.emptyDirSync(BuildDir);

const TempEntryFile = path.resolve('__temp_entry__.d.ts');
fse.outputFileSync(TempEntryFile, includePackages.map(p => `import "${p}/api";\n`).join(''));
execSync(`node ${apaasTypeGeneratorScript} merge-dts-files --entry-dts ${cwdRelativePath(TempEntryFile)} --output-dir ${cwdRelativePath(TempBuildDir)}`);

mergeApis(
    includePackages.map(p => path.resolve(TempBuildDir, `src/${p}/api.d.ts`)),
    path.resolve(TempBuildDir, 'api-merged.d.ts')
);

const apiExtractorJson = require('hjson').parse(fse.readFileSync(path.resolve(__dirname, 'api-extractor.json')).toString());
if (args['output-api-json']) {
    apiExtractorJson.docModel.enabled = true;
}
if (args['trim-internal-api']) {
    apiExtractorJson.dtsRollup.betaTrimmedFilePath = apiExtractorJson.dtsRollup.untrimmedFilePath;
    apiExtractorJson.dtsRollup.untrimmedFilePath = '';
}
fse.outputFileSync(path.resolve(TempBuildDir, 'api-extractor.json'), JSON.stringify(apiExtractorJson, undefined, 2));

execSync(`node ${dtsRollupScript} ae run -c api-extractor.json`, { cwd: TempBuildDir });

if (args['build-global-decl']) {
    execSync(`node ${apaasTypeGeneratorScript} generate-global-declare-dts -d . --api-dts-file index.d.ts --output-dir global-decl`, { cwd: BuildDir });
    fse.moveSync('build/global-decl/index.d.ts', 'build/index.d.ts', { overwrite: true });
    fse.removeSync('build/global-decl');
}

fse.removeSync(TempEntryFile);
fse.removeSync(TempBuildDir);
