---
openapi: "3.1.0"
info:
  title: "家具设计管理API"
  description: "家具设计服务REST API\n\n为群核旗下设计工具提供家具数据的完整生命周期管理接口，支持家具模型的创建、编辑、查询和管理。提供\
    单个和批量操作，支持不同数据视图以优化性能，适用于各种家具设计和管理场景。\n\n**核心功能：**\n- 家具CRUD操作：创建、获取、更新、删除家具实例\
    \n- 批量操作支持：高效处理大量家具数据的批量操作\n- 多视图支持：BASIC和FULL视图，针对不同场景优化性能\n- 分页查询：支持大量家具数据的分\
    页展示和管理\n- 产品组合：支持通过组合商品批量创建相关家具\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 幂等性控制：通过requestId确\
    保操作的幂等性\n\n**业务应用场景：**\n- 室内设计软件的家具管理\n- 家具产品的三维展示和编辑\n- 场景模板和套装家具的快速应用\n- 家具数\
    据的批量导入和同步\n- 个性化家具定制和配置\n- 空间布局优化和家具摆放\n\n**技术特性：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n\
    - 认证方式：基于设计方案的权限验证\n- 异步支持：长时间批量操作的异步处理机制"
  contact:
    name: "群核科技开发团队"
    url: "https://wiki.manycore.com/furniture-design"
    email: "<EMAIL>"
  license:
    name: "群核科技专有许可证"
    url: "https://manycore.com/license"
  version: "1.0.0"
servers:
- url: "http://localhost:8083"
  description: "本地开发环境"
- url: "https://api-dev.qunhe.com"
  description: "开发测试环境"
- url: "https://api.qunhe.com"
  description: "生产环境"
tags:
- name: "家具管理接口"
  description: "提供家具数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询等功能。支持不同数据视图以优化性能。"
paths:
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture:
    get:
      tags:
      - "家具管理接口"
      summary: "获取家具列表"
      description: "分页获取指定设计方案和楼层下的家具列表。支持不同数据视图以优化性能，适用于家具展示和管理场景。"
      operationId: "getFurnitureList"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID，用于标识特定的设计方案"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID，用于标识设计方案中的特定楼层"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "pageSize"
        in: "query"
        description: "每页记录数，控制单次返回的数据量。建议值：10-50，最大值：100"
        required: true
        schema:
          maximum: 100
          minimum: 1
          type: "integer"
          format: "int32"
        example: 20
      - name: "pageNum"
        in: "query"
        description: "页码，从0开始。实际返回数据范围：[pageNum*pageSize, pageNum*pageSize+pageSize)"
        required: true
        schema:
          minimum: 0
          type: "integer"
          format: "int32"
        example: 0
      - name: "view"
        in: "query"
        description: "数据视图类型。BASIC：核心信息，性能更好；FULL：完整信息，包含编辑数据"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "BASIC"
      responses:
        404:
          description: "指定的设计方案或楼层不存在，或用户无权限访问"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，请稍后重试"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数无效，如页码超出范围、页大小不合理等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功获取家具列表"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FurnitureListResponse"
              examples:
                家具列表响应示例:
                  description: "家具列表响应示例"
                  value:
                    totalCount: 25
                    furnitureList:
                    - id: "furniture_001"
                      productId: "prod_sofa_001"
                      size:
                        x: 2.0
                        y: 0.8
                        z: 0.9
                      transform:
                        position:
                          x: 1.5
                          y: 0.0
                          z: 2.3
                        rotate:
                          x: 0.0
                          y: 1.57
                          z: 0.0
    post:
      tags:
      - "家具管理接口"
      summary: "创建单个家具"
      description: "在指定位置创建一个新的家具实例。支持幂等性控制，通过requestId防止重复创建。适用于用户添加家具、导入家具数据等场景。"
      operationId: "createFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "view"
        in: "query"
        description: "返回数据的视图类型"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "BASIC"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SingleFurnitureCreateRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误，创建失败"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "家具创建成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FurnitureUnitData"
              examples:
                创建成功响应示例:
                  description: "创建成功响应示例"
                  value:
                    id: "furniture_002"
                    productId: "prod_sofa_001"
                    size:
                      x: 2.0
                      y: 0.8
                      z: 0.9
                    transform:
                      position:
                        x: 1.5
                        y: 0.0
                        z: 2.3
                      rotate:
                        x: 0.0
                        y: 1.57
                        z: 0.0
        400:
          description: "请求数据无效，如产品ID不存在、尺寸参数错误、位置冲突等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture:batchupdate:
    post:
      tags:
      - "家具管理接口"
      summary: "批量更新家具"
      description: "根据提供的家具信息列表批量更新家具。支持异步处理，适用于批量编辑、批量调整等场景。每个家具必须包含有效的ID。"
      operationId: "batchUpdateFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "view"
        in: "query"
        description: "返回数据的视图类型"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "BASIC"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FurnitureBatchUpdateRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如家具ID缺失、更新数据格式错误等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量更新操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量更新响应示例:
                  description: "批量更新响应示例"
                  value:
                    operationId: "op_batch_update_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - id: "furniture_001"
                        productId: "prod_sofa_001"
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture:batchget:
    post:
      tags:
      - "家具管理接口"
      summary: "批量获取家具信息"
      description: "根据家具ID列表批量获取家具信息。支持异步处理，适用于大量数据查询、数据同步等场景。单次最多支持100个家具。"
      operationId: "batchGetFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "view"
        in: "query"
        description: "返回数据的视图类型"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "BASIC"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FurnitureBatchGetRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量获取操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量获取响应示例:
                  description: "批量获取响应示例"
                  value:
                    operationId: "op_batch_get_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - id: "furniture_001"
                        productId: "prod_sofa_001"
        400:
          description: "请求数据无效，如家具ID列表为空、ID格式错误等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture:batchdelete:
    post:
      tags:
      - "家具管理接口"
      summary: "批量删除家具"
      description: "根据家具ID列表批量删除家具。删除操作不可逆，适用于批量清理、场景重置等场景。返回成功删除的家具ID列表。"
      operationId: "batchDeleteFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FurnitureBatchDeleteRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "批量删除操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量删除响应示例:
                  description: "批量删除响应示例"
                  value:
                    operationId: "op_batch_delete_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - "furniture_001"
                      - "furniture_002"
        400:
          description: "请求数据无效，如家具ID列表为空、ID格式错误等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture:batchcreate:
    post:
      tags:
      - "家具管理接口"
      summary: "批量创建家具"
      description: "批量创建多个家具实例。异步接口，支持大量数据处理。不保证幂等性，请在请求中包含requestId。初次请求时operationId为\
        空，后续可通过返回的operationId轮询结果。适用于批量导入、场景复制等场景。"
      operationId: "batchCreateFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "view"
        in: "query"
        description: "返回数据的视图类型"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "BASIC"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FurnitureBatchCreateRequest"
        required: true
      responses:
        200:
          description: "批量创建操作已启动，请通过operationId查询进度"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                批量创建响应示例:
                  description: "批量创建响应示例"
                  value:
                    operationId: "op_batch_create_001"
                    status: "PROCESSING"
                    progress: 30
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如家具数据格式错误、数量超出限制等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture:batch-create-furniture-by-group-product:
    post:
      tags:
      - "家具管理接口"
      summary: "通过组合商品批量创建家具"
      description: "根据商品组合信息批量创建家具。适用于套装家具创建、主题场景搭建等场景。可以一次性创建多个相关联的家具实例。"
      operationId: "batchCreateFurnitureByGroupProduct"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "view"
        in: "query"
        description: "返回数据的视图类型"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "BASIC"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FurnitureProductGroupBatchCreateRequest"
        required: true
      responses:
        200:
          description: "通过组合商品批量创建操作成功"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/Operation"
              examples:
                组合商品创建响应示例:
                  description: "组合商品创建响应示例"
                  value:
                    operationId: "op_group_create_001"
                    status: "FINISHED"
                    data:
                      elements:
                      - id: "furniture_001"
                        productId: "prod_sofa_001"
                      - id: "furniture_002"
                        productId: "prod_table_001"
        400:
          description: "请求数据无效，如组合商品信息错误、位置冲突等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
  /fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture/{furnitureId}:
    get:
      tags:
      - "家具管理接口"
      summary: "获取单个家具信息"
      description: "根据家具ID获取特定家具的详细信息。适用于家具详情展示、编辑前数据加载等场景。"
      operationId: "getSingleFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "furnitureId"
        in: "path"
        description: "家具的唯一标识符，由系统生成"
        required: true
        schema:
          type: "string"
        example: "furniture_001"
      - name: "view"
        in: "query"
        description: "数据视图类型。BASIC：核心信息；FULL：包含组件编辑信息"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "FULL"
      responses:
        200:
          description: "成功获取家具信息"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FurnitureUnitData"
              examples:
                家具详情响应示例:
                  description: "家具详情响应示例"
                  value:
                    id: "furniture_001"
                    productId: "prod_sofa_001"
                    size:
                      x: 2.0
                      y: 0.8
                      z: 0.9
                    transform:
                      position:
                        x: 1.5
                        y: 0.0
                        z: 2.3
                      rotate:
                        x: 0.0
                        y: 1.57
                        z: 0.0
        400:
          description: "请求参数格式错误，如家具ID格式不正确"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        404:
          description: "指定ID的家具不存在，或用户无权限访问"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，请稍后重试"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
    put:
      tags:
      - "家具管理接口"
      summary: "更新单个家具"
      description: "更新指定ID的家具信息，支持位置调整、尺寸修改、材质替换等操作。适用于家具编辑、个性化定制等场景。"
      operationId: "updateFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "furnitureId"
        in: "path"
        description: "要更新的家具ID"
        required: true
        schema:
          type: "string"
        example: "furniture_001"
      - name: "view"
        in: "query"
        description: "返回数据的视图类型"
        required: false
        schema:
          type: "string"
          default: "BASIC"
          enum:
          - "BASIC"
          - "FULL"
        example: "FULL"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SingleFurnitureUpdateRequest"
        required: true
      responses:
        200:
          description: "家具更新成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FurnitureUnitData"
              examples:
                更新成功响应示例:
                  description: "更新成功响应示例"
                  value:
                    id: "furniture_001"
                    productId: "prod_sofa_001"
                    size:
                      x: 2.2
                      y: 0.8
                      z: 0.9
                    transform:
                      position:
                        x: 2.0
                        y: 0.0
                        z: 2.3
                      rotate:
                        x: 0.0
                        y: 3.14
                        z: 0.0
        500:
          description: "服务器内部错误，更新失败"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求数据无效，如家具ID格式错误、更新参数不合理等"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        404:
          description: "指定ID的家具不存在"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
    delete:
      tags:
      - "家具管理接口"
      summary: "删除单个家具"
      description: "从指定位置删除一个家具实例。删除操作不可逆，适用于用户移除家具、清理场景等场景。"
      operationId: "deleteFurniture"
      parameters:
      - name: "obsDesignId"
        in: "path"
        description: "设计方案的ID"
        required: true
        schema:
          type: "string"
        example: "ObQ1gR2nF8kE"
      - name: "levelId"
        in: "path"
        description: "楼层ID"
        required: true
        schema:
          type: "string"
        example: "level_001"
      - name: "furnitureId"
        in: "path"
        description: "要删除的家具ID"
        required: true
        schema:
          type: "string"
        example: "furniture_001"
      responses:
        404:
          description: "指定ID的家具不存在或已被删除"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        500:
          description: "服务器内部错误，删除失败"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数无效，如家具ID格式错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "家具删除成功"
          content:
            application/json:
              schema:
                type: "boolean"
              examples:
                删除成功响应:
                  description: "删除成功响应"
                  value: true
components:
  schemas:
    ComponentEditInfo:
      required:
      - "componentId"
      type: "object"
      properties:
        componentId:
          maxLength: 128
          minLength: 0
          pattern: "^[a-zA-Z0-9_-]+$"
          type: "string"
          description: "组件的唯一标识符，对应家具模型中的组件ID。通常等于 component materialId 的值"
          example: "comp_sofa_fabric_001"
        replaceInfo:
          $ref: "#/components/schemas/ComponentReplaceInfo"
        uvEditInfo:
          $ref: "#/components/schemas/ComponentUVEditInfo"
      description: "组件编辑信息，支持材质替换和UV编辑"
      example:
        componentId: "comp_sofa_fabric_001"
        replaceInfo:
          targetMaterialId: "mat_leather_brown_001"
        uvEditInfo:
          angle: 1.57
          scale:
            x: 1.2
            y: 1.0
          translation:
            x: 0.1
            y: 0.0
    ComponentReplaceInfo:
      type: "object"
      properties:
        targetMaterialId:
          maxLength: 128
          minLength: 0
          type: "string"
          description: "目标材质ID，用于材质级别的替换。只改变表面外观，不影响几何形状"
          example: "mat_leather_brown_001"
        targetProductId:
          maxLength: 128
          minLength: 0
          type: "string"
          description: "目标产品ID，用于产品级别的替换。可能改变组件的几何形状和功能"
          example: "prod_handle_gold_001"
        replacementType:
          type: "string"
      description: "组件替换信息，支持材质替换和产品替换"
      example:
        targetMaterialId: "mat_leather_brown_001"
        targetProductId: "prod_handle_gold_001"
    ComponentUVEditInfo:
      type: "object"
      properties:
        angle:
          maximum: 6.283185307179586
          exclusiveMaximum: false
          minimum: 0.0
          exclusiveMinimum: false
          type: "number"
          description: "材质旋转角度，单位：弧度。正值为逆时针旋转，范围[0, 2π]"
          format: "double"
          example: 1.5707963267948966
        scale:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point2d"
        translation:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point2d"
        editDescription:
          type: "string"
      description: "UV贴图编辑信息，控制材质在组件表面的显示效果"
      example:
        angle: 1.5707963267948966
        scale:
          x: 1.2
          y: 1.0
        translation:
          x: 0.1
          y: 0.0
    FurnitureUnitData:
      required:
      - "productId"
      - "size"
      - "transform"
      type: "object"
      properties:
        id:
          maxLength: 64
          pattern: "^[a-zA-Z0-9_-]+$"
          type: "string"
          description: "家具的唯一标识符，全局唯一。创建时由系统生成，更新和删除时必需"
          example: "furniture_001"
        productId:
          type: "string"
          description: "家具对应的产品ID，用于标识家具类型。必须是系统中存在且有权限访问的产品"
          example: "prod_sofa_001"
        size:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        transform:
          $ref: "#/components/schemas/Transform"
        componentEditInfos:
          maxItems: 50
          minItems: 0
          type: "array"
          description: "组件编辑信息列表，包含材质替换和UV编辑。仅在FULL视图下返回"
          items:
            $ref: "#/components/schemas/ComponentEditInfo"
      description: "家具单元数据，包含家具的完整信息"
      example:
        id: "furniture_001"
        productId: "prod_sofa_001"
        size:
          x: 2.0
          y: 0.8
          z: 0.9
        transform:
          position:
            x: 1.5
            y: 0.0
            z: 2.3
          rotate:
            x: 0.0
            y: 1.57
            z: 0.0
        componentEditInfos:
        - componentId: "comp_sofa_fabric_001"
          replaceInfo:
            targetMaterialId: "mat_leather_brown_001"
    SingleFurnitureCreateRequest:
      required:
      - "furnitureUnitData"
      type: "object"
      properties:
        requestId:
          type: "string"
        furnitureUnitData:
          $ref: "#/components/schemas/FurnitureUnitData"
      description: "创建家具的请求数据，包含产品ID、尺寸、位置等信息"
      example:
        requestId: "req_create_furniture_001"
        furnitureUnitData:
          productId: "prod_sofa_001"
          size:
            x: 2.0
            y: 0.8
            z: 0.9
          transform:
            position:
              x: 1.5
              y: 0.0
              z: 2.3
            rotate:
              x: 0.0
              y: 1.57
              z: 0.0
    Transform:
      required:
      - "position"
      - "rotate"
      type: "object"
      properties:
        position:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        rotate:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
      description: "3D变换信息，包含位置坐标和旋转角度"
      example:
        position:
          x: 1.5
          y: 0.0
          z: 2.3
        rotate:
          x: 0.0
          y: 1.5707963267948966
          z: 0.0
    FurnitureListResponse:
      type: "object"
      properties:
        totalCount:
          type: "integer"
          format: "int32"
        furnitureList:
          type: "array"
          description: "家具单元数据列表"
          items:
            $ref: "#/components/schemas/FurnitureUnitData"
      description: "家具列表的响应"
    FurnitureBatchUpdateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/FurnitureUnitData"
      description: "批量更新请求，包含要更新的家具列表（每个家具必须包含有效ID）"
    FurnitureBatchGetRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          description: "要获取的家具ID列表"
          items:
            type: "string"
            description: "要获取的家具ID列表"
        view:
          type: "string"
          description: "定义获取家具数据时返回的视图范围，控制数据详细程度以优化性能"
          default: "BASIC"
          enum:
          - "FURNITURE_DATA_VIEW_UNSPECIFIED"
          - "BASIC"
          - "FULL"
      description: "批量获取请求，包含要获取的家具ID列表（最多100个）"
    FurnitureBatchDeleteRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          description: "要删除的家具ID列表"
          items:
            type: "string"
            description: "要删除的家具ID列表"
      description: "批量删除请求，包含要删除的家具ID列表"
    FurnitureBatchCreateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/FurnitureUnitData"
      description: "批量创建请求，包含要创建的家具列表（最多100个）和requestId"
    FurnitureProductGroupBatchCreateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/FurnitureProductGroupUnit"
      description: "产品组合批量创建请求，包含组合商品信息和位置数据"
    FurnitureProductGroupUnit:
      type: "object"
      properties:
        size:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Point3d"
        transform:
          $ref: "#/components/schemas/Transform"
        productId:
          type: "string"
          description: "家具对应的商品Id"
      description: "家具商品组单元"
    SingleFurnitureUpdateRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        furnitureUnitData:
          $ref: "#/components/schemas/FurnitureUnitData"
      description: "更新家具的请求数据，包含要修改的字段"
