#!/bin/bash

# 演示如何添加新服务的脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "🎯 演示添加新服务的流程"
echo ""

# 1. 显示当前支持的服务
echo "📋 当前支持的服务："
"$SCRIPT_DIR/local-sdk-manager.sh" generate invalid 2>&1 | grep "支持的服务:" || echo "无法获取服务列表"
echo ""

# 2. 创建新服务目录
NEW_SERVICE="demo-service"
NEW_SERVICE_DIR="$PROJECT_ROOT/openapi/$NEW_SERVICE"

echo "📁 创建新服务目录: $NEW_SERVICE"
mkdir -p "$NEW_SERVICE_DIR"

# 3. 复制模板文件
echo "📋 复制模板文件..."
if [ -f "$PROJECT_ROOT/openapi/doorwindow/restapi.yaml" ]; then
    cp "$PROJECT_ROOT/openapi/doorwindow/restapi.yaml" "$NEW_SERVICE_DIR/"
    cp "$PROJECT_ROOT/openapi/doorwindow/config-java.yaml" "$NEW_SERVICE_DIR/"
    echo "✅ 模板文件复制完成"
else
    echo "⚠️ 模板文件不存在，创建空文件"
    touch "$NEW_SERVICE_DIR/restapi.yaml"
    touch "$NEW_SERVICE_DIR/config-java.yaml"
fi

# 4. 显示新的服务列表
echo ""
echo "🎉 新服务添加完成！现在支持的服务："
"$SCRIPT_DIR/local-sdk-manager.sh" generate invalid 2>&1 | grep "支持的服务:" || echo "无法获取服务列表"

echo ""
echo "🚀 现在可以使用以下命令操作新服务："
echo "  ./scripts/local/quick-sdk.sh gen $NEW_SERVICE"
echo "  ./scripts/local/quick-sdk.sh build $NEW_SERVICE"
echo ""
echo "⚠️ 注意：请根据实际需求编辑以下文件："
echo "  - $NEW_SERVICE_DIR/restapi.yaml"
echo "  - $NEW_SERVICE_DIR/config-java.yaml"
echo ""

# 5. 清理演示（可选）
read -p "🗑️ 是否删除演示服务目录？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -rf "$NEW_SERVICE_DIR"
    echo "✅ 演示服务目录已删除"
fi 