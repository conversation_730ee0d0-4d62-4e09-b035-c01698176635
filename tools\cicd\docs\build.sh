#!/bin/bash

# 文档构建脚本
# 支持普通和优化模式的 Docusaurus 文档构建
# 合并自 build-docusaurus.sh 和 build-docusaurus-optimized.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 文档构建配置
DOCS_DIR="${DOCS_DIR:-docs-site}"
BUILD_OUTPUT_DIR="${BUILD_OUTPUT_DIR:-build}"
NODE_MEMORY_LIMIT="${NODE_MEMORY_LIMIT:-8192}"

# 验证构建环境
verify_build_environment() {
    log_step "1" "验证构建环境"
    
    # 检查 Node.js
    if ! command -v node >/dev/null 2>&1; then
        log_error "Node.js 未安装"
        return 1
    fi
    
    # 检查 npm
    if ! command -v npm >/dev/null 2>&1; then
        log_error "npm 未安装"
        return 1
    fi
    
    # 显示环境信息
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    
    log_success "Node.js: $node_version"
    log_success "npm: $npm_version"
    
    # 显示系统信息
    if command -v free >/dev/null 2>&1; then
        local available_memory=$(free -h | grep '^Mem:' | awk '{print $7}' || echo 'Unknown')
        log_info "可用内存: $available_memory"
    fi
    
    log_success "构建环境验证通过"
    return 0
}

# 设置构建环境
setup_build_environment() {
    local mode="${1:-normal}"
    
    log_step "2" "设置构建环境 ($mode)"
    
    # 根据模式设置 Node.js 内存限制
    case "$mode" in
        "optimized")
            export NODE_OPTIONS="--max-old-space-size=$NODE_MEMORY_LIMIT"
            log_info "设置 Node.js 内存限制: ${NODE_MEMORY_LIMIT}MB"
            ;;
        "normal")
            # 使用默认内存设置
            log_info "使用默认 Node.js 内存设置"
            ;;
    esac
    
    # 设置其他环境变量
    export CI=true
    export NODE_ENV=production
    
    log_success "构建环境设置完成"
}

# 定位文档目录
locate_docs_directory() {
    log_step "3" "定位文档目录"
    
    local current_dir=$(pwd)
    log_info "当前目录: $current_dir"
    
    # 检查是否在项目根目录
    if [ ! -d "$DOCS_DIR" ]; then
        log_warning "在当前目录未找到 $DOCS_DIR，尝试查找项目根目录..."
        
        # 向上查找项目根目录
        while [ ! -d "$DOCS_DIR" ] && [ "$(pwd)" != "/" ]; do
            cd ..
            log_debug "检查目录: $(pwd)"
        done
        
        if [ ! -d "$DOCS_DIR" ]; then
            log_error "无法找到文档目录: $DOCS_DIR"
            return 1
        fi
    fi
    
    log_success "找到文档目录: $(pwd)/$DOCS_DIR"
    
    # 检查文档目录内容
    if [ -d "$DOCS_DIR" ]; then
        log_info "文档目录内容:"
        ls -la "$DOCS_DIR/" | head -10
    fi
    
    return 0
}

# 安装依赖
install_dependencies() {
    local mode="${1:-normal}"
    
    log_step "4" "安装依赖"
    
    # 进入文档目录
    if ! cd "$DOCS_DIR"; then
        log_error "无法进入文档目录: $DOCS_DIR"
        return 1
    fi
    
    log_info "当前目录: $(pwd)"
    
    # 检查 package.json
    if ! check_file_exists "package.json" "package.json"; then
        return 1
    fi
    
    # 显示 package.json 信息
    if command -v jq >/dev/null 2>&1; then
        local package_name=$(jq -r '.name // "unknown"' package.json)
        local package_version=$(jq -r '.version // "unknown"' package.json)
        log_info "项目: $package_name@$package_version"
    fi
    
    # 清理缓存（优化模式）
    if [ "$mode" = "optimized" ]; then
        log_info "清理 npm 缓存..."
        npm cache clean --force >/dev/null 2>&1 || true
    fi
    
    # 安装依赖
    log_info "安装 npm 依赖..."
    local start_time=$(date +%s)

    local npm_cmd="npm ci"
    if [ "$mode" = "optimized" ]; then
        npm_cmd="$npm_cmd --prefer-offline --no-audit"
    fi

    if $npm_cmd >/dev/null 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log_success "依赖安装完成 (耗时: ${duration}s)"
        increment_counter "dependencies_installed"
        return 0
    else
        log_error "依赖安装失败"
        increment_counter "dependencies_failed"
        
        # 显示错误详情
        log_info "错误详情:"
        $npm_cmd 2>&1 | tail -10
        return 1
    fi
}

# 构建文档
build_documentation() {
    local mode="${1:-normal}"
    
    log_step "5" "构建文档 ($mode)"
    
    # 检查构建脚本
    if ! check_file_exists "package.json" "package.json"; then
        return 1
    fi
    
    # 检查是否有构建脚本
    local build_script="build"
    if command -v jq >/dev/null 2>&1; then
        local has_build=$(jq -r '.scripts.build // empty' package.json)
        if [ -z "$has_build" ]; then
            log_error "package.json 中未找到 build 脚本"
            return 1
        fi
    fi
    
    # 清理旧的构建输出
    if [ -d "$BUILD_OUTPUT_DIR" ]; then
        log_info "清理旧的构建输出..."
        rm -rf "$BUILD_OUTPUT_DIR"
    fi
    
    # 执行构建
    log_info "开始构建文档..."
    local start_time=$(get_timestamp)
    
    local build_cmd="npm run build"
    if [ "$mode" = "optimized" ]; then
        # 优化模式的额外参数
        build_cmd="$build_cmd -- --no-minify"
    fi
    
    if $build_cmd >/dev/null 2>&1; then
        local end_time=$(get_timestamp)
        local duration=$((end_time - start_time))
        log_success "文档构建完成 (耗时: ${duration}s)"
        increment_counter "build_success"
        
        # 验证构建输出
        if [ -d "$BUILD_OUTPUT_DIR" ]; then
            local file_count=$(find "$BUILD_OUTPUT_DIR" -type f | wc -l)
            local dir_size=$(du -sh "$BUILD_OUTPUT_DIR" 2>/dev/null | cut -f1 || echo "Unknown")
            log_info "构建输出: $file_count 个文件, 大小: $dir_size"
            increment_counter "build_files" "$file_count"
        else
            log_warning "构建输出目录不存在: $BUILD_OUTPUT_DIR"
        fi
        
        return 0
    else
        log_error "文档构建失败"
        increment_counter "build_failed"
        
        # 显示错误详情
        log_info "错误详情:"
        $build_cmd 2>&1 | tail -20
        return 1
    fi
}

# 验证构建结果
verify_build_result() {
    log_step "6" "验证构建结果"
    
    if [ ! -d "$BUILD_OUTPUT_DIR" ]; then
        log_error "构建输出目录不存在: $BUILD_OUTPUT_DIR"
        return 1
    fi
    
    # 检查关键文件
    local required_files=("index.html")
    local missing_files=0
    
    for file in "${required_files[@]}"; do
        if [ -f "$BUILD_OUTPUT_DIR/$file" ]; then
            log_success "找到关键文件: $file"
        else
            log_error "缺少关键文件: $file"
            missing_files=$((missing_files + 1))
        fi
    done
    
    if [ $missing_files -eq 0 ]; then
        log_success "构建结果验证通过"
        return 0
    else
        log_error "构建结果验证失败，缺少 $missing_files 个关键文件"
        return 1
    fi
}

# 显示构建统计
show_build_stats() {
    log_step "7" "显示构建统计"
    
    echo ""
    log_info "📚 文档构建结果统计:"
    
    local success=$(get_counter "build_success")
    local failed=$(get_counter "build_failed")
    local deps_installed=$(get_counter "dependencies_installed")
    local deps_failed=$(get_counter "dependencies_failed")
    local build_files=$(get_counter "build_files")
    
    echo "  构建状态: $([ $success -gt 0 ] && echo "成功" || echo "失败")"
    echo "  依赖安装: $([ $deps_installed -gt 0 ] && echo "成功" || echo "失败")"
    echo "  构建文件: $build_files 个"
    echo "  构建时间: $(date)"
    
    if [ -d "$DOCS_DIR/$BUILD_OUTPUT_DIR" ]; then
        local output_size=$(du -sh "$DOCS_DIR/$BUILD_OUTPUT_DIR" 2>/dev/null | cut -f1 || echo "Unknown")
        echo "  输出大小: $output_size"
    fi
    
    show_stats "文档构建统计"
}

# 主函数
main() {
    local mode="${1:-normal}"  # normal, optimized
    
    init_script "文档构建" "构建 Docusaurus 文档"
    
    # 验证模式
    if [[ "$mode" != "normal" && "$mode" != "optimized" ]]; then
        log_error "无效的构建模式: $mode"
        log_info "支持的模式: normal, optimized"
        finish_script "文档构建" "false"
        exit 1
    fi
    
    log_info "构建模式: $mode"
    
    local build_status=0
    local project_dir=$(pwd)
    
    # 执行构建流程
    if ! verify_build_environment; then
        build_status=1
    elif ! setup_build_environment "$mode"; then
        build_status=1
    elif ! locate_docs_directory; then
        build_status=1
    elif ! install_dependencies "$mode"; then
        build_status=1
    elif ! build_documentation "$mode"; then
        build_status=1
    elif ! verify_build_result; then
        build_status=1
    fi
    
    # 返回项目根目录
    cd "$project_dir"
    
    show_build_stats
    
    if [ $build_status -eq 0 ]; then
        log_success "🎉 文档构建完成！"
        finish_script "文档构建" "true"
        return 0
    else
        log_error "❌ 文档构建失败，请检查错误信息"
        finish_script "文档构建" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "文档构建模块已加载 ✅"
