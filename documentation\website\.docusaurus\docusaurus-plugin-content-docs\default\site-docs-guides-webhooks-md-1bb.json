{"id": "guides/webhooks", "title": "Webhooks 事件通知", "description": "Webhooks 允许群核科技在特定事件发生时主动向您的应用发送 HTTP 请求，而无需您持续轮询我们的 API。这是一种高效的实时通知机制。", "source": "@site/docs/guides/webhooks.md", "sourceDirName": "guides", "slug": "/guides/webhooks", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/webhooks", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "webhooks", "title": "Webhooks 事件通知", "sidebar_label": "Webhooks"}, "sidebar": "tutorialSidebar", "previous": {"title": "速率限制", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/rate-limiting"}, "next": {"title": "SDK 开发包", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/sdks"}}