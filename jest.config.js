/** @type {import("jest").Config} **/
module.exports = {
  testEnvironment: "node",
  moduleFileExtensions: ['ts', 'js'],
  testPathIgnorePatterns: ['/node_modules/'],
  testMatch: ['**/__tests__/**/*.+(test).(ts|tsx|js)'],
  transform: {
    "^.+\\.(js|ts|tsx)$": "ts-jest"
  },
  transformIgnorePatterns: [
    // 不忽略这些需要转换的node_modules包
    'node_modules/(?!(@qunhe/math-apaas-impl|@qunhe/apaas-type-generator-lib|@qunhe/math-apaas-api|@qunhe/math)/)'
  ],
};