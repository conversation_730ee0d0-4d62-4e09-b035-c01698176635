{"name": "@qunhe/idp-decoration", "version": "1.70.0", "description": "api typings for idp-decoration", "keywords": [], "author": "yuecheng <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/idp-types.git"}, "scripts": {"build": "node ../../scripts/build-package/build-api", "build-package": "npm run build && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "dependencies": {"@qunhe/idp-common": "1.70.0", "@qunhe/math-apaas-api": "^3.0.2"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "devDependencies": {"copyfiles": "^2.4.1"}}