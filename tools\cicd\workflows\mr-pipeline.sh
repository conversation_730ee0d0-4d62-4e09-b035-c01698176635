#!/bin/bash

# MR 流水线脚本
# 整合所有 MR 相关的 CI 步骤
# 重构自 complete-mr-pipeline.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 流水线配置
PIPELINE_TIMEOUT="${PIPELINE_TIMEOUT:-3600}"  # 1小时超时
SKIP_DOCS="${SKIP_DOCS:-false}"
SKIP_SDKS="${SKIP_SDKS:-false}"

# 执行环境检查
run_environment_check() {
    log_step "1" "环境检查"
    
    local env_script="$SCRIPT_DIR/../core/environment.sh"
    
    if [ ! -f "$env_script" ]; then
        log_error "环境检查脚本不存在: $env_script"
        return 1
    fi
    
    log_info "执行环境检查..."
    if "$env_script"; then
        log_success "环境检查通过"
        increment_counter "env_check_success"
        return 0
    else
        log_error "环境检查失败"
        increment_counter "env_check_failed"
        return 1
    fi
}

# 执行 API 变更检测
run_api_change_detection() {
    log_step "2" "API 变更检测"
    
    local api_changes_script="$SCRIPT_DIR/../core/api-changes.sh"
    
    if [ ! -f "$api_changes_script" ]; then
        log_error "API 变更检测脚本不存在: $api_changes_script"
        return 1
    fi
    
    log_info "执行 API 变更检测..."
    if source "$api_changes_script"; then
        log_success "API 变更检测完成"
        
        # 显示检测结果
        log_info "API 变更检测结果:"
        echo "  HAS_API_CHANGES: ${HAS_API_CHANGES:-false}"
        echo "  CHANGED_API_FILES: ${CHANGED_API_FILES:-'(无)'}"
        echo "  CHANGED_SERVICES: ${CHANGED_SERVICES:-'(无)'}"
        
        increment_counter "api_detection_success"
        return 0
    else
        log_error "API 变更检测失败"
        increment_counter "api_detection_failed"
        return 1
    fi
}

# 执行 OpenAPI 验证
run_openapi_validation() {
    log_step "3" "OpenAPI 验证"
    
    local validation_script="$SCRIPT_DIR/../core/validation.sh"
    
    if [ ! -f "$validation_script" ]; then
        log_error "OpenAPI 验证脚本不存在: $validation_script"
        return 1
    fi
    
    log_info "执行 OpenAPI 验证..."
    if "$validation_script" "changed"; then
        log_success "OpenAPI 验证通过"
        increment_counter "validation_success"
        return 0
    else
        log_error "OpenAPI 验证失败"
        increment_counter "validation_failed"
        return 1
    fi
}

# 执行 SDK 生成和构建
run_sdk_pipeline() {
    log_step "4" "SDK 生成和构建"
    
    if [ "$SKIP_SDKS" = "true" ]; then
        log_info "跳过 SDK 生成和构建 (SKIP_SDKS=true)"
        return 0
    fi
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过 SDK 生成和构建"
        return 0
    fi
    
    local sdk_dir="$SCRIPT_DIR/../sdk"
    local overall_status=0
    
    # SDK 生成
    log_info "执行 SDK 生成..."
    if [ -f "$sdk_dir/generate.sh" ]; then
        if "$sdk_dir/generate.sh" "changed"; then
            log_success "SDK 生成完成"
            increment_counter "sdk_generate_success"
        else
            log_error "SDK 生成失败"
            increment_counter "sdk_generate_failed"
            overall_status=1
        fi
    else
        log_error "SDK 生成脚本不存在: $sdk_dir/generate.sh"
        overall_status=1
    fi
    
    # SDK 构建
    if [ $overall_status -eq 0 ]; then
        log_info "执行 SDK 构建..."
        if [ -f "$sdk_dir/build.sh" ]; then
            if "$sdk_dir/build.sh" "changed"; then
                log_success "SDK 构建完成"
                increment_counter "sdk_build_success"
            else
                log_error "SDK 构建失败"
                increment_counter "sdk_build_failed"
                overall_status=1
            fi
        else
            log_error "SDK 构建脚本不存在: $sdk_dir/build.sh"
            overall_status=1
        fi
    fi
    
    # SDK 测试
    if [ $overall_status -eq 0 ]; then
        log_info "执行 SDK 测试..."
        if [ -f "$sdk_dir/test.sh" ]; then
            if "$sdk_dir/test.sh" "changed"; then
                log_success "SDK 测试完成"
                increment_counter "sdk_test_success"
            else
                log_error "SDK 测试失败"
                increment_counter "sdk_test_failed"
                overall_status=1
            fi
        else
            log_warning "SDK 测试脚本不存在，跳过测试"
        fi
    fi
    
    return $overall_status
}

# 执行文档预览生成
run_docs_preview() {
    log_step "5" "文档预览生成"
    
    if [ "$SKIP_DOCS" = "true" ]; then
        log_info "跳过文档预览生成 (SKIP_DOCS=true)"
        return 0
    fi
    
    local docs_script="$SCRIPT_DIR/../docs/preview.sh"
    
    if [ ! -f "$docs_script" ]; then
        log_error "文档预览脚本不存在: $docs_script"
        return 1
    fi
    
    log_info "执行文档预览生成..."
    if "$docs_script" "optimized"; then
        log_success "文档预览生成完成"
        increment_counter "docs_preview_success"
        return 0
    else
        log_error "文档预览生成失败"
        increment_counter "docs_preview_failed"
        return 1
    fi
}

# 生成 CI 状态报告
generate_ci_status() {
    log_step "6" "生成 CI 状态报告"
    
    local gitlab_api_script="$SCRIPT_DIR/../utils/gitlab-api.sh"
    
    if [ ! -f "$gitlab_api_script" ]; then
        log_warning "GitLab API 脚本不存在，跳过状态报告"
        return 0
    fi
    
    # 加载 GitLab API 工具
    source "$gitlab_api_script"
    
    # 收集统计信息
    local env_check=$(get_counter "env_check_success")
    local api_detection=$(get_counter "api_detection_success")
    local validation=$(get_counter "validation_success")
    local sdk_generate=$(get_counter "sdk_generate_success")
    local sdk_build=$(get_counter "sdk_build_success")
    local sdk_test=$(get_counter "sdk_test_success")
    local docs_preview=$(get_counter "docs_preview_success")
    
    # 生成状态报告
    local status_message="## 🚀 MR 流水线执行结果\n\n"
    status_message+="### 📊 执行统计\n"
    status_message+="- ✅ 环境检查: $([ $env_check -gt 0 ] && echo "通过" || echo "失败")\n"
    status_message+="- ✅ API 变更检测: $([ $api_detection -gt 0 ] && echo "完成" || echo "失败")\n"
    status_message+="- ✅ OpenAPI 验证: $([ $validation -gt 0 ] && echo "通过" || echo "失败")\n"
    
    if [ "${HAS_API_CHANGES:-false}" = "true" ]; then
        status_message+="- ✅ SDK 生成: $([ $sdk_generate -gt 0 ] && echo "完成" || echo "失败")\n"
        status_message+="- ✅ SDK 构建: $([ $sdk_build -gt 0 ] && echo "完成" || echo "失败")\n"
        status_message+="- ✅ SDK 测试: $([ $sdk_test -gt 0 ] && echo "完成" || echo "跳过")\n"
    else
        status_message+="- ⚠️ SDK 处理: 跳过 (无 API 变更)\n"
    fi
    
    status_message+="- ✅ 文档预览: $([ $docs_preview -gt 0 ] && echo "完成" || echo "失败")\n"
    
    # 添加变更信息
    if [ "${HAS_API_CHANGES:-false}" = "true" ]; then
        status_message+="### 📋 变更信息\n"
        status_message+="- 变更服务: ${CHANGED_SERVICES:-'(无)'}\n"
        status_message+="- 变更文件数: $(echo "$CHANGED_API_FILES" | wc -w)\n"
    fi
    
    # 添加预览链接
    local preview_url=$(generate_manual_preview_url)
    if [ -n "$preview_url" ]; then
        status_message+="### 🔗 预览链接\n"
        status_message+="- [📖 文档预览]($preview_url)\n"
    fi
    
    status_message+="### ⏰ 执行时间\n"
    status_message+="- 完成时间: $(date)\n"
    
    # 发送状态评论
    log_info "发送 CI 状态报告到 MR..."
    if add_ci_status_comment "$status_message"; then
        log_success "CI 状态报告发送成功"
        increment_counter "status_report_success"
        return 0
    else
        log_warning "CI 状态报告发送失败"
        return 0  # 不影响整体流程
    fi
}

# 显示流水线统计
show_pipeline_stats() {
    log_step "7" "显示流水线统计"
    
    echo ""
    log_info "🚀 MR 流水线执行统计:"
    
    local env_check_success=$(get_counter "env_check_success")
    local env_check_failed=$(get_counter "env_check_failed")
    local api_detection_success=$(get_counter "api_detection_success")
    local validation_success=$(get_counter "validation_success")
    local sdk_generate_success=$(get_counter "sdk_generate_success")
    local sdk_build_success=$(get_counter "sdk_build_success")
    local sdk_test_success=$(get_counter "sdk_test_success")
    local docs_preview_success=$(get_counter "docs_preview_success")
    local status_report_success=$(get_counter "status_report_success")
    
    echo "  环境检查: $([ $env_check_success -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  API 检测: $([ $api_detection_success -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  OpenAPI 验证: $([ $validation_success -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  SDK 生成: $([ $sdk_generate_success -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    echo "  SDK 构建: $([ $sdk_build_success -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    echo "  SDK 测试: $([ $sdk_test_success -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    echo "  文档预览: $([ $docs_preview_success -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  状态报告: $([ $status_report_success -gt 0 ] && echo "✅ 发送" || echo "⚠️ 跳过")"
    
    show_stats "MR 流水线统计"
}

# 主函数
main() {
    init_script "MR 流水线" "执行完整的 MR CI/CD 流程"
    
    # 设置超时
    if command -v timeout >/dev/null 2>&1; then
        log_info "设置流水线超时: ${PIPELINE_TIMEOUT}s"
    fi
    
    local pipeline_status=0
    local start_time=$(get_timestamp)
    
    # 执行流水线步骤
    if ! run_environment_check; then
        pipeline_status=1
    elif ! run_api_change_detection; then
        pipeline_status=1
    elif ! run_openapi_validation; then
        pipeline_status=1
    elif ! run_sdk_pipeline; then
        pipeline_status=1
    elif ! run_docs_preview; then
        pipeline_status=1
    fi
    
    # 生成状态报告（不影响整体结果）
    generate_ci_status
    
    local end_time=$(get_timestamp)
    local total_duration=$((end_time - start_time))
    
    show_pipeline_stats
    
    log_info "流水线总耗时: ${total_duration}s"
    
    if [ $pipeline_status -eq 0 ]; then
        log_success "🎉 MR 流水线执行完成！"
        finish_script "MR 流水线" "true"
        return 0
    else
        log_error "❌ MR 流水线执行失败，请检查错误信息"
        finish_script "MR 流水线" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "MR 流水线模块已加载 ✅"
