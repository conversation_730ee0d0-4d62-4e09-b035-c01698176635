#!/bin/bash

# 修复 OpenAPI 规范文件中的已知问题

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "🔧 修复 OpenAPI 规范问题..."

# 备份原文件
cp "$PROJECT_ROOT/openapi/common/api-error.yaml" "$PROJECT_ROOT/openapi/common/api-error.yaml.backup"

# 创建修复版本的 api-error.yaml
cat > "$PROJECT_ROOT/openapi/common/api-error.yaml" << 'EOF'
schemas:
  ApiError:
    type: object
    description: REST API错误响应的主要数据结构
    required:
      - code
      - message
      - status
      - details
    properties:
      code:
        type: integer
        description: HTTP状态码（数字），例如 403、404、500等
        example: 403
        minimum: 100
        maximum: 599
      message:
        type: string
        description: 面向开发者的错误消息，应该使用英文
        example: "Permission denied"
        minLength: 1
        maxLength: 1000
      status:
        $ref: '#/schemas/Code'
        description: RestAPI对应的状态码枚举值
      details:
        $ref: '#/schemas/ErrorDetails'
        description: 错误的详细信息
      localizedMessage:
        $ref: '#/schemas/LocalizedMessage'
        description: 错误的本地化消息，可选字段
      help:
        $ref: '#/schemas/Help'
        description: 错误的帮助信息，可选字段
    additionalProperties: false

  Code:
    type: string
    description: 错误状态码枚举
    enum:
      - OK
      - CANCELLED
      - UNKNOWN
      - INVALID_ARGUMENT
      - DEADLINE_EXCEEDED
      - NOT_FOUND
      - ALREADY_EXISTS
      - PERMISSION_DENIED
      - UNAUTHENTICATED
      - RESOURCE_EXHAUSTED
      - FAILED_PRECONDITION
      - ABORTED
      - OUT_OF_RANGE
      - UNIMPLEMENTED
      - INTERNAL
      - UNAVAILABLE
      - DATA_LOSS
      - PARTIAL_ELEMENT_UPDATE_FAILED

  ErrorDetails:
    type: object
    description: 错误的详细信息，提供结构化的错误描述
    required:
      - reason
    properties:
      reason:
        type: string
        description: 错误的原因
        example: "INVALID_USER_INPUT"
        pattern: "^[A-Z][A-Z0-9_]*[A-Z0-9]$"
        maxLength: 63
      message:
        type: string
        description: 针对此错误出现的人类可读的具体解释
        example: "用户输入的邮箱格式不正确"
        maxLength: 500
      domain:
        type: string
        description: 错误域
        example: "restapi.manycore.com"
        maxLength: 100
      metaData:
        type: object
        description: 关于此错误的其他结构化详细信息
        additionalProperties:
          type: string
          maxLength: 200

  LocalizedMessage:
    type: object
    description: 本地化消息，用于提供特定语言环境的错误信息
    required:
      - locale
      - message
    properties:
      locale:
        type: string
        description: 消息所使用的语言环境
        example: "zh-CN"
        pattern: "^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"
      message:
        type: string
        description: 本地化的错误消息内容
        example: "权限不足，无法访问该资源"
        minLength: 1
        maxLength: 1000

  Help:
    type: object
    description: 帮助信息，提供解决错误的指导链接
    required:
      - desc
      - url
    properties:
      desc:
        type: string
        description: 描述链接提供的内容或帮助信息
        example: "查看用户权限配置指南"
        minLength: 1
        maxLength: 200
      url:
        type: string
        description: 帮助链接的URL地址
        format: uri
        example: "https://docs.manycore.com/api/permissions"
        maxLength: 500

  ErrorDescribe:
    type: object
    description: 错误描述接口的标记
    additionalProperties: true
EOF

echo "✅ api-error.yaml 已修复"

# 如果用户提供了参数，测试特定服务
if [ $# -gt 0 ]; then
    SERVICE=$1
    echo ""
    echo "🧪 测试 $SERVICE 服务..."
    
    # 运行快速测试
    "$SCRIPT_DIR/local-sdk-manager.sh" --dry-run generate "$SERVICE"
fi

echo ""
echo "🎉 修复完成！现在可以尝试重新生成 SDK：" 
echo "  ./scripts/local/quick-sdk.sh gen doorwindow" 