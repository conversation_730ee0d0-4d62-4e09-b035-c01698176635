const yargs = require('yargs/yargs');
const helper = require('yargs/helpers');
const fs = require('fs-extra');
const path = require('path');
const child_process = require('child_process');
const rimraf = require('rimraf');
const { EOL } = require('os');

const argv = yargs(helper.hideBin(process.argv))
    .scriptName("create-package")
    .usage('$0 <path>')
    // .option('internal', { alias: 'i', type: 'boolean', default: false, description: 'wether mark as internal api, will not add to sdk package' })
    .argv;

const pkg = argv._[0];

const reg = /@qunhe\/(idp-.+)/;
const match = reg.exec(pkg);
if (!match) {
    throw new Error('package name must start with @qunhe/idp-');
}

const name = match[1];

function createPackage(dirName, packageName, templateDir) {
    const base = path.resolve(`packages/${dirName}`);

    child_process.execSync(`npx lerna create ${packageName} --description "api typings for ${dirName}" --yes `);

    rimraf.sync(path.join(base, 'lib'));
    rimraf.sync(path.join(base, '__tests__'));

    const package = require(path.join(base, 'package.json'));
    delete package.directories;
    delete package.files;
    delete package.main;

    Object.assign(package, fs.readJsonSync(path.resolve(templateDir, 'package.json')));
    fs.copySync(templateDir, base);
    // merge the package.json
    fs.writeFileSync(path.join(base, 'package.json'), JSON.stringify(package, null, 2));
}

createPackage(name, pkg, 'template/package');
const pkgVersion = fs.readJsonSync(`packages/${name}/package.json`).version;
createPackage(`${name}-impl`, `${pkg}-impl`, 'template/package-impl');
const implPackageJson = fs.readJsonSync(`packages/${name}-impl/package.json`);
implPackageJson.dependencies[pkg] = pkgVersion;
fs.writeFileSync(`packages/${name}-impl/package.json`, JSON.stringify(implPackageJson, null, 2));

function appendingPackageIntoSdk(sdkDir, isInternal) {
    console.log(`Appending package into ${path.basename(sdkDir)}`);
    // write include_packages.json
    fs.outputJsonSync(
        path.resolve(sdkDir, 'include_packages.json'),
        fs.readJsonSync(path.resolve(sdkDir, 'include_packages.json')).concat([pkg]),
        { spaces: 2 }
    );
    // write package.json
    const sdk = require(path.resolve(sdkDir, 'package.json'));
    sdk.dependencies[pkg] = pkgVersion;
    fs.writeFileSync(path.resolve(sdkDir, 'package.json'), JSON.stringify(sdk, null, 2));
}
appendingPackageIntoSdk('packages/idp-sdk', false);
appendingPackageIntoSdk('packages/idp-sdk-internal', true);

console.log(`Package created, see packages/${name} and packages/${name}-impl`);
