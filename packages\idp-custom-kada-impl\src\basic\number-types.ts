import { BasicType } from '@qunhe/kls-abstraction';
import { NumberType, StringType } from './base-types';

/**
 * Number3 对象的声明
 */
export const Number3Types = {
    type: BasicType.Object,
    properties: {
        x: NumberType,
        y: NumberType,
        z: NumberType,
    },
};

/**
 * Number3公式类型
 */
export const Number3FormulaTypes = {
    type: BasicType.Object,
    properties: {
        x: StringType,
        y: StringType,
        z: StringType,
    },
}
