var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Interaction = {};
    var var_goIntelligentLayoutAndMatch = {};
    var var_undefinedType = {};
    var var_goIntelligentWholeLayout = {};
    var var_goIntelligentMatch = {};
    var var_goIntelligentCeiling = {};
    var var_applyIntelligentMatchAsync = {};
    var var_stringType = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_toggleApplySampleRoomLoading = {};
    var var_booleanType = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Interaction": var_Interaction,
    };
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "goIntelligentLayoutAndMatch": var_goIntelligentLayoutAndMatch,
        "goIntelligentWholeLayout": var_goIntelligentWholeLayout,
        "goIntelligentMatch": var_goIntelligentMatch,
        "goIntelligentCeiling": var_goIntelligentCeiling,
        "applyIntelligentMatchAsync": var_applyIntelligentMatchAsync,
        "toggleApplySampleRoomLoading": var_toggleApplySampleRoomLoading,
    };
    var_goIntelligentLayoutAndMatch.type = BasicType.Function;
    var_goIntelligentLayoutAndMatch.name = "goIntelligentLayoutAndMatch";
    var_goIntelligentLayoutAndMatch.varying = false;
    var_goIntelligentLayoutAndMatch.keepArgsHandle = false;
    var_goIntelligentLayoutAndMatch.args = [];
    var_goIntelligentLayoutAndMatch.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_goIntelligentWholeLayout.type = BasicType.Function;
    var_goIntelligentWholeLayout.name = "goIntelligentWholeLayout";
    var_goIntelligentWholeLayout.varying = false;
    var_goIntelligentWholeLayout.keepArgsHandle = false;
    var_goIntelligentWholeLayout.args = [];
    var_goIntelligentWholeLayout.return = var_undefinedType;
    var_goIntelligentMatch.type = BasicType.Function;
    var_goIntelligentMatch.name = "goIntelligentMatch";
    var_goIntelligentMatch.varying = false;
    var_goIntelligentMatch.keepArgsHandle = false;
    var_goIntelligentMatch.args = [];
    var_goIntelligentMatch.return = var_undefinedType;
    var_goIntelligentCeiling.type = BasicType.Function;
    var_goIntelligentCeiling.name = "goIntelligentCeiling";
    var_goIntelligentCeiling.varying = false;
    var_goIntelligentCeiling.keepArgsHandle = false;
    var_goIntelligentCeiling.args = [];
    var_goIntelligentCeiling.return = var_undefinedType;
    var_applyIntelligentMatchAsync.type = BasicType.Function;
    var_applyIntelligentMatchAsync.name = "applyIntelligentMatchAsync";
    var_applyIntelligentMatchAsync.varying = false;
    var_applyIntelligentMatchAsync.keepArgsHandle = false;
    var_applyIntelligentMatchAsync.args = [var_stringType];
    var_applyIntelligentMatchAsync.return = var_undefinedType_Promise;
    var_stringType.type = BasicType.String;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_toggleApplySampleRoomLoading.type = BasicType.Function;
    var_toggleApplySampleRoomLoading.name = "toggleApplySampleRoomLoading";
    var_toggleApplySampleRoomLoading.varying = false;
    var_toggleApplySampleRoomLoading.keepArgsHandle = false;
    var_toggleApplySampleRoomLoading.args = [var_booleanType];
    var_toggleApplySampleRoomLoading.return = var_undefinedType;
    var_booleanType.type = BasicType.Boolean;
    
    return var_sourceFile;
};
