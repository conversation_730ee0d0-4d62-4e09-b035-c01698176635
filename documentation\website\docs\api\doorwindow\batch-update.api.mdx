---
id: batch-update
title: "批量更新门窗"
description: "对指定设计和楼层下的门窗进行批量更新操作。支持replace（替换）、sdAttach/swAttach（简单门/窗附加）、detach（分离）、sdUpdate/swUpdate（简单门/窗更新）等多种操作类型。单次操作最多支持20个门窗，采用异步处理确保大批量操作的性能。"
sidebar_label: "批量更新门窗"
hide_title: true
hide_table_of_contents: true
api: {"tags":["门窗管理 API"],"description":"对指定设计和楼层下的门窗进行批量更新操作。支持replace（替换）、sdAttach/swAttach（简单门/窗附加）、detach（分离）、sdUpdate/swUpdate（简单门/窗更新）等多种操作类型。单次操作最多支持20个门窗，采用异步处理确保大批量操作的性能。","operationId":"batchUpdate","parameters":[{"name":"obsDesignId","in":"path","description":"设计方案的唯一标识符","required":true,"schema":{"type":"integer","format":"int64"},"example":"3FO3N47AJ4FR"},{"name":"levelId","in":"path","description":"楼层的唯一标识符，用于标识设计方案中的具体楼层","required":true,"schema":{"type":"string"},"example":"M7AAU7AKTLKBCAABAAAAAAI8"}],"requestBody":{"content":{"application/json":{"schema":{"type":"object","properties":{"requestId":{"type":"string"},"operationId":{"type":"string"},"batchRequests":{"type":"array","items":{"oneOf":[{"required":["id","type"],"type":"object","description":"门窗分离请求，用于将门窗从设计方案中分离","example":{"id":"door_123456","type":"MODEL","designId":"design_789","writeControl":{"versionCheck":true,"version":1}},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"type":"string","description":"要分离的门窗唯一标识符","example":"door_123456"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}}}],"title":"DoorWindowDetachRequest"},{"required":["id","productId","type"],"type":"object","description":"门窗替换请求，用于将现有门窗替换为新产品","example":{"id":"door_123456","productId":"new_product_789","type":"MODEL","designId":"design_789","writeControl":{"versionCheck":true,"version":1}},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"type":"string","description":"要替换的门窗唯一标识符","example":"door_123456"},"productId":{"type":"string","description":"新产品的唯一标识符，支持OBS存储的产品ID","example":"new_product_789"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}}}],"title":"DoorWindowReplaceRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单门附加请求，用于将门产品附加到墙体开口","example":{"productId":"door_product_456","openingRelation":{"hostId":"wall_123","openingId":"opening_456"},"openDirection":{"x":1,"y":0,"z":0},"openSide":"LEFT","designId":"design_789","index":1},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"maxLength":64,"type":"string","description":"产品ID，指定要附加的门窗产品。产品ID来自产品库，包含门窗的详细规格和属性信息。","example":"product_door_001"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"openSide":{"type":"string","description":"门开启方向枚举，定义门扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleDoorAttachRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单门创建请求，用于创建单开口关联的标准门型","example":{"productId":"door_product_123","openingRelation":{"hostId":"wall_456","openingId":"opening_789"},"openDirection":{"x":1,"y":0,"z":0},"position":{"x":100,"y":200,"z":0},"size":{"x":800,"y":2000,"z":50},"openSide":"LEFT","designId":"design_123"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"type":"string","description":"门窗产品的唯一标识符，支持OBS存储的产品ID","example":"product_123456"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"门开启方向枚举，定义门扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleDoorCreateRequest"},{"required":["id","type"],"type":"object","description":"简单门更新请求，用于更新已存在的简单门的属性","example":{"id":"door_123","type":"MODEL","openDirection":{"x":1,"y":0,"z":0},"position":{"x":150,"y":250,"z":0},"size":{"x":900,"y":2100,"z":60},"openSide":"RIGHT","designId":"design_456"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗ID，用于标识要更新的门窗对象","example":"door_001"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"门开启方向枚举，定义门扇的开启方向和旋转规则","example":"RIGHT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleDoorUpdateRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单窗附加请求，用于将窗产品附加到墙体开口","example":{"productId":"window_product_456","openingRelation":{"hostId":"wall_123","openingId":"opening_456"},"openDirection":{"x":1,"y":0,"z":0},"openSide":"LEFT","designId":"design_789","index":1},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"maxLength":64,"type":"string","description":"产品ID，指定要附加的门窗产品。产品ID来自产品库，包含门窗的详细规格和属性信息。","example":"product_door_001"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"openSide":{"type":"string","description":"窗开启方向枚举，定义窗扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleWindowAttachRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单窗创建请求，用于创建单开口关联的标准窗型","example":{"productId":"window_product_123","openingRelation":{"hostId":"wall_456","openingId":"opening_789"},"openDirection":{"x":1,"y":0,"z":0},"position":{"x":100,"y":200,"z":50},"size":{"x":1200,"y":1500,"z":20},"openSide":"LEFT","designId":"design_123"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"type":"string","description":"门窗产品的唯一标识符，支持OBS存储的产品ID","example":"product_123456"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"窗开启方向枚举，定义窗扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleWindowCreateRequest"},{"required":["id","type"],"type":"object","description":"简单窗更新请求，用于更新已存在的简单窗的属性","example":{"id":"window_123","type":"MODEL","openDirection":{"x":1,"y":0,"z":0},"position":{"x":200,"y":100,"z":50},"size":{"x":1500,"y":1200,"z":25},"openSide":"RIGHT","designId":"design_456"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗ID，用于标识要更新的门窗对象","example":"door_001"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"窗开启方向枚举，定义窗扇的开启方向和旋转规则","example":"RIGHT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleWindowUpdateRequest"}]}}},"description":"批量更新请求对象，包含待操作的门窗列表和操作配置。单次最多支持20个门窗的批量操作。","title":"DoorWindowBatchUpdateRequest"}}},"required":true},"responses":{"200":{"description":"批量更新操作已成功提交，返回异步操作信息","content":{"application/json":{"schema":{"type":"object","title":"Operation","description":"操作类，表示异步操作的状态和结果，用于批量处理场景","required":["operationId","done"],"properties":{"operationId":{"type":"string","description":"操作标识符，用于唯一标识一个操作实例","example":"op_12345678","pattern":"^[a-zA-Z0-9_-]+$","maxLength":64},"metaData":{"type":"object","description":"操作的元数据信息，包含操作的附加信息","additionalProperties":true,"example":{"startTime":"2025-01-01T10:00:00Z","priority":"high"}},"done":{"type":"boolean","description":"操作是否已完成，true 表示已完成（成功或失败），false 表示仍在处理中","example":true},"result":{"type":"object","description":"操作成功时的结果数据，仅当 done=true 且无错误时有值","additionalProperties":true},"error":{"description":"操作失败时的错误信息，仅当 done=true 且操作失败时有值","type":"object","title":"ApiError","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"x-module":"operation","x-category":"操作管理"},"examples":{"批量更新响应示例":{"description":"批量更新响应示例","value":{"operationId":"batch_update_20241201_001","done":true,"result":{"elements":[{"id":"door_001","productId":"prod_door_single_001","position":{"x":1,"y":0,"z":2},"size":{"x":0.8,"y":0.1,"z":2.1},"elementType":"SIMPLE_DOOR","doorType":"SINGLE_DOOR","moduleType":"MODEL"}]}}}}}}},"400":{"description":"请求参数错误，如批量大小超过限制、操作类型不正确、必需字段缺失等","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"资源未找到，指定的设计方案或楼层不存在，或门窗ID不存在","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误，批量操作处理失败或系统异常","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}},"method":"post","path":"/dw/openapi/v1/rest/designs/{obsDesignId}/levels/{levelId}/document:batchupdate","servers":[{"url":"http://localhost:8080","description":"本地开发环境"},{"url":"https://api-dev.qunhe.com","description":"开发测试环境"},{"url":"https://api.qunhe.com","description":"生产环境"}],"jsonRequestBodyExample":{"requestId":"string","operationId":"string","batchRequests":[{"id":"door_123456","type":"MODEL","designId":"design_789","writeControl":{"versionCheck":true,"version":1}},{"id":"door_123456","productId":"new_product_789","type":"MODEL","designId":"design_789","writeControl":{"versionCheck":true,"version":1}},{"productId":"door_product_456","openingRelation":{"hostId":"wall_123","openingId":"opening_456"},"openDirection":{"x":1,"y":0,"z":0},"openSide":"LEFT","designId":"design_789","index":1},{"productId":"door_product_123","openingRelation":{"hostId":"wall_456","openingId":"opening_789"},"openDirection":{"x":1,"y":0,"z":0},"position":{"x":100,"y":200,"z":0},"size":{"x":800,"y":2000,"z":50},"openSide":"LEFT","designId":"design_123"},{"id":"door_123","type":"MODEL","openDirection":{"x":1,"y":0,"z":0},"position":{"x":150,"y":250,"z":0},"size":{"x":900,"y":2100,"z":60},"openSide":"RIGHT","designId":"design_456"},{"productId":"window_product_456","openingRelation":{"hostId":"wall_123","openingId":"opening_456"},"openDirection":{"x":1,"y":0,"z":0},"openSide":"LEFT","designId":"design_789","index":1},{"productId":"window_product_123","openingRelation":{"hostId":"wall_456","openingId":"opening_789"},"openDirection":{"x":1,"y":0,"z":0},"position":{"x":100,"y":200,"z":50},"size":{"x":1200,"y":1500,"z":20},"openSide":"LEFT","designId":"design_123"},{"id":"window_123","type":"MODEL","openDirection":{"x":1,"y":0,"z":0},"position":{"x":200,"y":100,"z":50},"size":{"x":1500,"y":1200,"z":25},"openSide":"RIGHT","designId":"design_456"}]},"info":{"title":"门窗设计管理API","description":"门窗设计服务REST API\n\n为群核旗下设计工具提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。提供高效的批量操作和异步处理机制，确保大规模门窗数据处理的性能和稳定性。\n\n**核心功能：**\n- 门窗文档查询：获取指定楼层的完整门窗信息\n- 批量更新操作：支持多种门窗操作类型的批量处理\n- 多种门窗类型：简单门、简单窗、复杂门窗组合\n- 多视图支持：BASIC、ADVANCED、FULL等不同详细程度的数据视图\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 操作类型丰富：replace（替换）、attach（附加）、detach（分离）、update（更新）\n\n**门窗类型支持：**\n- 简单门窗：单一门窗单元，如普通门、普通窗\n- 复杂门窗：组合型门窗，如L型窗、U型窗、转角门等\n- 门类型：单开门、双开门、推拉门、折叠门等\n- 窗类型：平开窗、推拉窗、百叶窗、落地窗等\n\n**业务应用场景：**\n- 建筑设计软件的门窗配置\n- 门窗产品的三维建模和展示\n- 建筑开口与门窗的关联管理\n- 门窗规格和参数的批量调整\n- 复杂门窗组合的快速创建\n- 门窗数据的批量导入和同步\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 批量限制：单次批量操作最多20个门窗\n- 异步支持：长时间批量操作的异步处理机制","contact":{"name":"群核科技开发团队","url":"https://wiki.manycore.com/doorwindow-design","email":"<EMAIL>"},"license":{"name":"群核科技专有许可证","url":"https://manycore.com/license"},"version":"v1.0.0"},"postman":{"name":"批量更新门窗","description":{"content":"对指定设计和楼层下的门窗进行批量更新操作。支持replace（替换）、sdAttach/swAttach（简单门/窗附加）、detach（分离）、sdUpdate/swUpdate（简单门/窗更新）等多种操作类型。单次操作最多支持20个门窗，采用异步处理确保大批量操作的性能。","type":"text/plain"},"url":{"path":["dw","openapi","v1","rest","designs",":obsDesignId","levels",":levelId","document:batchupdate"],"host":["{{baseUrl}}"],"query":[],"variable":[{"disabled":false,"description":{"content":"(Required) 设计方案的唯一标识符","type":"text/plain"},"type":"any","value":"","key":"obsDesignId"},{"disabled":false,"description":{"content":"(Required) 楼层的唯一标识符，用于标识设计方案中的具体楼层","type":"text/plain"},"type":"any","value":"","key":"levelId"}]},"header":[{"key":"Content-Type","value":"application/json"},{"key":"Accept","value":"application/json"}],"method":"POST","body":{"mode":"raw","raw":"","options":{"raw":{"language":"json"}}}}}
sidebar_class_name: "post api-method"
info_path: docs/api/doorwindow/门窗设计管理api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"批量更新门窗"}
>
</Heading>

<MethodEndpoint
  method={"post"}
  path={"/dw/openapi/v1/rest/designs/{obsDesignId}/levels/{levelId}/document:batchupdate"}
  context={"endpoint"}
>
  
</MethodEndpoint>



对指定设计和楼层下的门窗进行批量更新操作。支持replace（替换）、sdAttach/swAttach（简单门/窗附加）、detach（分离）、sdUpdate/swUpdate（简单门/窗更新）等多种操作类型。单次操作最多支持20个门窗，采用异步处理确保大批量操作的性能。

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"obsDesignId","in":"path","description":"设计方案的唯一标识符","required":true,"schema":{"type":"integer","format":"int64"},"example":"3FO3N47AJ4FR"},{"name":"levelId","in":"path","description":"楼层的唯一标识符，用于标识设计方案中的具体楼层","required":true,"schema":{"type":"string"},"example":"M7AAU7AKTLKBCAABAAAAAAI8"}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"content":{"application/json":{"schema":{"type":"object","properties":{"requestId":{"type":"string"},"operationId":{"type":"string"},"batchRequests":{"type":"array","items":{"oneOf":[{"required":["id","type"],"type":"object","description":"门窗分离请求，用于将门窗从设计方案中分离","example":{"id":"door_123456","type":"MODEL","designId":"design_789","writeControl":{"versionCheck":true,"version":1}},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"type":"string","description":"要分离的门窗唯一标识符","example":"door_123456"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}}}],"title":"DoorWindowDetachRequest"},{"required":["id","productId","type"],"type":"object","description":"门窗替换请求，用于将现有门窗替换为新产品","example":{"id":"door_123456","productId":"new_product_789","type":"MODEL","designId":"design_789","writeControl":{"versionCheck":true,"version":1}},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"type":"string","description":"要替换的门窗唯一标识符","example":"door_123456"},"productId":{"type":"string","description":"新产品的唯一标识符，支持OBS存储的产品ID","example":"new_product_789"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}}}],"title":"DoorWindowReplaceRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单门附加请求，用于将门产品附加到墙体开口","example":{"productId":"door_product_456","openingRelation":{"hostId":"wall_123","openingId":"opening_456"},"openDirection":{"x":1,"y":0,"z":0},"openSide":"LEFT","designId":"design_789","index":1},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"maxLength":64,"type":"string","description":"产品ID，指定要附加的门窗产品。产品ID来自产品库，包含门窗的详细规格和属性信息。","example":"product_door_001"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"openSide":{"type":"string","description":"门开启方向枚举，定义门扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleDoorAttachRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单门创建请求，用于创建单开口关联的标准门型","example":{"productId":"door_product_123","openingRelation":{"hostId":"wall_456","openingId":"opening_789"},"openDirection":{"x":1,"y":0,"z":0},"position":{"x":100,"y":200,"z":0},"size":{"x":800,"y":2000,"z":50},"openSide":"LEFT","designId":"design_123"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"type":"string","description":"门窗产品的唯一标识符，支持OBS存储的产品ID","example":"product_123456"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"门开启方向枚举，定义门扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleDoorCreateRequest"},{"required":["id","type"],"type":"object","description":"简单门更新请求，用于更新已存在的简单门的属性","example":{"id":"door_123","type":"MODEL","openDirection":{"x":1,"y":0,"z":0},"position":{"x":150,"y":250,"z":0},"size":{"x":900,"y":2100,"z":60},"openSide":"RIGHT","designId":"design_456"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗ID，用于标识要更新的门窗对象","example":"door_001"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"门开启方向枚举，定义门扇的开启方向和旋转规则","example":"RIGHT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleDoorUpdateRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单窗附加请求，用于将窗产品附加到墙体开口","example":{"productId":"window_product_456","openingRelation":{"hostId":"wall_123","openingId":"opening_456"},"openDirection":{"x":1,"y":0,"z":0},"openSide":"LEFT","designId":"design_789","index":1},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"maxLength":64,"type":"string","description":"产品ID，指定要附加的门窗产品。产品ID来自产品库，包含门窗的详细规格和属性信息。","example":"product_door_001"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"openSide":{"type":"string","description":"窗开启方向枚举，定义窗扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleWindowAttachRequest"},{"required":["openingRelation","productId"],"type":"object","description":"简单窗创建请求，用于创建单开口关联的标准窗型","example":{"productId":"window_product_123","openingRelation":{"hostId":"wall_456","openingId":"opening_789"},"openDirection":{"x":1,"y":0,"z":0},"position":{"x":100,"y":200,"z":50},"size":{"x":1200,"y":1500,"z":20},"openSide":"LEFT","designId":"design_123"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"productId":{"type":"string","description":"门窗产品的唯一标识符，支持OBS存储的产品ID","example":"product_123456"},"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"窗开启方向枚举，定义窗扇的开启方向和旋转规则","example":"LEFT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleWindowCreateRequest"},{"required":["id","type"],"type":"object","description":"简单窗更新请求，用于更新已存在的简单窗的属性","example":{"id":"window_123","type":"MODEL","openDirection":{"x":1,"y":0,"z":0},"position":{"x":200,"y":100,"z":50},"size":{"x":1500,"y":1200,"z":25},"openSide":"RIGHT","designId":"design_456"},"allOf":[{"required":["opType"],"type":"object","properties":{"requestId":{"type":"string"},"changeOpening":{"type":"boolean","description":"是否联动修改门窗关联的户型门洞。true表示修改门窗时同时更新关联的开口信息，false表示只修改门窗本身。","example":true,"default":true},"opType":{"type":"string"}},"description":"门窗更新请求基类，定义所有门窗更新操作的通用请求结构","discriminator":{"propertyName":"opType"},"title":"DoorWindowUpdateRequest"},{"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗ID，用于标识要更新的门窗对象","example":"door_001"},"type":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"openSide":{"type":"string","description":"窗开启方向枚举，定义窗扇的开启方向和旋转规则","example":"RIGHT","enum":["OPEN_SIDE_UNSPECIFIED","LEFT","RIGHT","UP","DOWN"]}}}],"title":"SimpleWindowUpdateRequest"}]}}},"description":"批量更新请求对象，包含待操作的门窗列表和操作配置。单次最多支持20个门窗的批量操作。","title":"DoorWindowBatchUpdateRequest"}}},"required":true}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"批量更新操作已成功提交，返回异步操作信息","content":{"application/json":{"schema":{"type":"object","title":"Operation","description":"操作类，表示异步操作的状态和结果，用于批量处理场景","required":["operationId","done"],"properties":{"operationId":{"type":"string","description":"操作标识符，用于唯一标识一个操作实例","example":"op_12345678","pattern":"^[a-zA-Z0-9_-]+$","maxLength":64},"metaData":{"type":"object","description":"操作的元数据信息，包含操作的附加信息","additionalProperties":true,"example":{"startTime":"2025-01-01T10:00:00Z","priority":"high"}},"done":{"type":"boolean","description":"操作是否已完成，true 表示已完成（成功或失败），false 表示仍在处理中","example":true},"result":{"type":"object","description":"操作成功时的结果数据，仅当 done=true 且无错误时有值","additionalProperties":true},"error":{"description":"操作失败时的错误信息，仅当 done=true 且操作失败时有值","type":"object","title":"ApiError","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"x-module":"operation","x-category":"操作管理"},"examples":{"批量更新响应示例":{"description":"批量更新响应示例","value":{"operationId":"batch_update_20241201_001","done":true,"result":{"elements":[{"id":"door_001","productId":"prod_door_single_001","position":{"x":1,"y":0,"z":2},"size":{"x":0.8,"y":0.1,"z":2.1},"elementType":"SIMPLE_DOOR","doorType":"SINGLE_DOOR","moduleType":"MODEL"}]}}}}}}},"400":{"description":"请求参数错误，如批量大小超过限制、操作类型不正确、必需字段缺失等","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"资源未找到，指定的设计方案或楼层不存在，或门窗ID不存在","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误，批量操作处理失败或系统异常","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}}}
>
  
</StatusCodes>


      