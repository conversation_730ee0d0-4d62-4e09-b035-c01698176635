import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { injections } from '@qunhe/apaas-type-generator-lib';

export const getVMBindingType = once(() => {
    return createVMBindingType();
});
export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal({
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        }
    });
});

/**
 * 硬装造型类型
 * @internal
 */
export enum PlanarModelingDesignType {
    Floor = 'Floor', // 地台
    Ceiling = 'Ceiling', // 吊顶
}
