# Backend API SDK

这是一个基于 OpenAPI 规范的 API SDK 生成和管理项目，支持完整的 API 设计、评审、生成和发布流程。

**🆕 新特性：完全动态化的文档系统！** 现在添加新的 API 服务只需放置 OpenAPI 文件，所有配置自动生成。

## 🌟 核心特性

### 🔄 完全动态化文档系统 (NEW!)
- **零配置添加新服务**: 只需放置 `restapi.yaml` 文件，所有配置自动生成
- **自动扫描 OpenAPI 规范**: 动态发现所有 API 服务
- **智能内容生成**: Sidebar、导航菜单、文档页面全部自动生成
- **一键更新**: 单个脚本更新所有动态内容

### 📚 统一文档中心
- **现代化界面**: 基于 Docusaurus 构建的美观文档站点
- **完整 API 参考**: 从 OpenAPI 规范自动生成交互式文档
- **多语言支持**: 支持中英文双语文档
- **强大搜索**: 集成搜索功能，快速定位内容

### 🚀 自动化流程
- **MR 文档预览**: Pull Request 中自动生成文档预览
- **CI/CD 集成**: 自动校验、构建、部署
- **评审流程**: 完整的 API 评审工作流
- **SDK 自动生成**: 多语言 SDK 自动生成和发布

## 🏗️ 项目结构

```
backend-api-sdk/
├── .gitlab-ci.yml              # CI/CD 流水线配置
├── .spectral.yml               # OpenAPI 规范校验规则
├── docs/                       # 文档目录
│   ├── DYNAMIC_DOCS_GUIDE.md   # 🆕 动态文档系统指南
│   ├── api-review-process.md   # 详细评审流程文档
│   └── quick-start-guide.md    # 快速开始指南
├── docs-site/                  # 📖 Docusaurus 文档站点
│   ├── docs/                   # 手写文档 (Markdown)
│   │   ├── intro.md            # 🔄 动态生成的首页
│   │   ├── api/overview.md     # 🔄 动态生成的 API 概览
│   │   ├── getting-started/    # 快速开始指南
│   │   ├── guides/             # 开发指南
│   │   └── examples/           # 示例代码
│   ├── blog/                   # 更新日志博客
│   ├── src/                    # 自定义组件和页面
│   ├── docusaurus.config.ts    # 🔄 支持动态配置
│   ├── sidebars.ts             # 🔄 动态生成的侧边栏
│   ├── openapi-config.json     # 🔄 动态生成的 OpenAPI 配置
│   ├── navbar-config.json      # 🔄 动态生成的导航配置
│   └── package.json            # 依赖配置
├── openapi/                    # OpenAPI 规范文件
│   ├── diymodeldw-service/
│   │   ├── restapi.yaml        # API 规范定义
│   │   └── config-java.yaml    # Java SDK 生成配置
│   ├── furniture-design-service/
│   │   ├── restapi.yaml
│   │   └── config-java.yaml
│   └── designinfoservice/
│       ├── restapi.yaml
│       └── config-java.yaml
├── output/                     # 生成的 SDK 输出目录
├── generator/                  # 自定义生成器模板
└── scripts/                    # 构建和部署脚本
    ├── generate-openapi-config.js      # 🆕 OpenAPI 配置动态生成器
    ├── generate-docs-content.js        # 🆕 文档内容动态生成器
    ├── generate-all-dynamic-content.sh # 🆕 一键生成所有动态内容
    ├── setup-docs.sh                   # 📖 一键初始化文档环境
    └── ci/docs/                         # 文档构建脚本
```

## 🚀 零配置添加新 API 服务

### 超简单！只需 3 步：

1. **放置 OpenAPI 文件**
   ```bash
   # 创建新服务目录
   mkdir openapi/user-service
   # 放置你的 OpenAPI 规范文件
   cp your-api.yaml openapi/user-service/restapi.yaml
   ```

2. **生成动态内容**
   ```bash
   # 一键生成所有配置和文档
   ./scripts/generate-all-dynamic-content.sh
   ```

3. **预览和部署**
   ```bash
   cd docs-site
   npm run start  # 本地预览
   npm run build  # 构建生产版本
   ```

**就这么简单！** 🎉 你的新 API 服务文档已经自动集成到整个文档站点中。

### 自动生成的内容包括：
- ✅ OpenAPI 配置 (`openapi-config.json`)
- ✅ 侧边栏结构 (`sidebars.ts`)
- ✅ 导航菜单项 (`navbar-config.json`)
- ✅ 首页服务卡片 (`docs/intro.md`)
- ✅ 完整的交互式 API 文档

## 📖 文档体系

本项目提供了完整的 API 文档解决方案：

### 🌟 特性

- **🔄 完全动态化**: 新的零配置体验，添加服务无需手动配置
- **📚 统一文档中心**: 基于 Docusaurus 构建的现代化文档站点
- **🤖 自动生成**: 从 OpenAPI 规范自动生成 API 参考文档
- **👀 预览功能**: MR 中自动生成文档预览，支持评审流程
- **🌍 多语言支持**: 支持中英文双语文档
- **🔍 强大搜索**: 集成 Algolia 搜索功能
- **📱 移动适配**: 响应式设计，完美支持移动端
- **🚀 自动部署**: 集成 GitLab CI/CD，自动部署到内外网环境

### 📖 文档访问

- **内部预览**: MR 中的临时预览链接 (仅项目成员可访问)
- **内部生产**: `https://your-company.gitlab.io/backend-api-sdk/`
- **外网发布**: `https://docs.kujiale.com` (配置自定义域名后)

### 🚀 快速开始文档环境

```bash
# 1. 一键初始化文档环境
chmod +x scripts/setup-docs.sh
./scripts/setup-docs.sh

# 2. 生成所有动态内容 (新功能!)
./scripts/generate-all-dynamic-content.sh

# 3. 启动本地开发
cd docs-site
npm start  # 访问 http://localhost:3000

# 4. 构建生产版本
npm run build
```

## 🚀 快速开始

### 对于 API 设计者/开发者 (更新的流程)

1. **创建 API 变更**
   ```bash
   git checkout -b feature/your-api-change
   # 编辑或新增 openapi/your-service/restapi.yaml
   
   # 🆕 生成动态内容 (如果添加了新服务)
   ./scripts/generate-all-dynamic-content.sh
   
   git add .
   git commit -m "feat: 新增用户管理 API"
   git push origin feature/your-api-change
   ```

2. **创建 Merge Request**
   - 目标分支：`develop`
   - 添加标签：`API-Review::Pending-Pre-Review` 或 `API-Review::Pending-Final-Review`

3. **等待 CI 和评审**
   - CI 自动校验 OpenAPI 规范
   - **📖 自动生成文档预览** (包含你的新服务)
   - API 小组进行评审

### 对于 API 评审者

1. **查找待评审 MR**
   - 筛选带有 `API-Review::Pending-*` 标签的 MR

2. **执行评审**
   - 查看 OpenAPI 规范变更
   - 下载文档预览（CI artifacts）
   - 检查新服务是否正确集成到文档站点
   - 在 MR 中提供反馈

3. **确认评审结果**
   - 更新标签：`API-Review::Approved` 或 `API-Review::Changes-Requested`
   - 手动触发 `api_review_mr_checkpoint` job

## 🆕 动态文档系统详细指南

想了解更多关于新的动态文档系统？查看详细指南：

📋 **[动态文档系统完整指南](./docs/DYNAMIC_DOCS_GUIDE.md)**

包含内容：
- 系统架构和工作原理
- 自定义服务信息（名称、图标等）
- 高级配置选项
- 故障排除指南
- 最佳实践建议

## 🔄 工作流程

```mermaid
graph LR
    A[创建特性分支] --> B[编辑 OpenAPI 规范]
    B --> B1[🆕 运行动态生成脚本]
    B1 --> C[创建 MR]
    C --> D[CI 自动校验]
    D --> E[生成文档预览]
    E --> F[API 小组评审]
    F --> G{评审结果}
    G -->|需要修改| H[修改规范]
    H --> B1
    G -->|评审通过| I[合并 MR]
    I --> J[自动生成 SDK]
    J --> K[部署到仓库]
```

## 🛠️ 技术栈

- **OpenAPI 规范**: 3.x (YAML 格式)
- **规范校验**: Stoplight Spectral
- **文档生成**: Docusaurus + OpenAPI 插件
- **动态配置**: Node.js 脚本 (新增)
- **SDK 生成**: OpenAPI Generator
- **CI/CD**: GitLab CI/CD
- **构建工具**: Maven (Java SDK)

## 📚 文档

- **🆕 [动态文档系统指南](./docs/DYNAMIC_DOCS_GUIDE.md)** - 新的零配置文档系统完整指南
- [详细评审流程文档](./docs/api-review-process.md)
- [快速开始指南](./docs/quick-start-guide.md)

## 🏷️ GitLab 标签规范

| 标签 | 含义 | 使用场景 |
|------|------|----------|
| `API-Review::Pending-Pre-Review` | 等待预审 | API 设计阶段 |
| `API-Review::Pending-Final-Review` | 等待终审 | 开发完成阶段 |
| `API-Review::Changes-Requested` | 需要修改 | 评审发现问题 |
| `API-Review::Approved` | 评审通过 | 可以合并 |
| `API-Review::On-Hold` | 评审暂停 | 等待外部依赖 |

## 🔧 本地开发

### 🆕 动态文档系统开发
```bash
# 测试动态内容生成
./scripts/generate-all-dynamic-content.sh

# 检查生成的文件
cat docs-site/openapi-config.json
cat docs-site/navbar-config.json

# 预览动态生成的文档
cd docs-site && npm start
```
``

### 安装工具
```bash
# 安装 Spectral CLI（OpenAPI 校验）
npm install -g @stoplight/spectral-cli

# 安装 Docusaurus 相关依赖（在 docs-site 目录中）
cd docs-site && npm install
```

### 本地校验
```bash
# 校验 OpenAPI 规范
spectral lint openapi/your-service/restapi.yaml

# 生成动态配置并预览
./scripts/generate-all-dynamic-content.sh
cd docs-site && npm start
```

### 本地生成 SDK
```bash
# 使用 OpenAPI Generator 生成 Java SDK
java -jar /path/to/openapi-generator-cli.jar generate \
  -i openapi/your-service/restapi.yaml \
  -g java \
  -o output/your-service/java \
  --config openapi/your-service/config-java.yaml
```

## 🚨 CI认证问题快速解决

如果您在CI环境中遇到以下错误：
```
[error][CosUploadService] 文件上传失败： 未通过授权
[error][TaskService] manual:upload 执行失败!
```

**快速解决步骤：**

1. **配置GitLab CI环境变量**：
   - 访问项目 `Settings` > `CI/CD` > `Variables`
   - 添加以下变量：
     
     ```
     KJL_USERNAME = 你的LDAP用户名
     KJL_PASSWORD = 你的LDAP密码 (设置为Protected和Masked)
     ```

2. **设置步骤**：
   - 点击 `Add variable`
   - Key: `KJL_USERNAME`, Value: 你的LDAP用户名
   - 取消勾选 `Protect variable` (如果需要在非protected分支使用)
   - 点击 `Add variable`
   
   - 再次点击 `Add variable` 
   - Key: `KJL_PASSWORD`, Value: 你的LDAP密码
   - 勾选 `Mask variable` (隐藏密码显示)
   - 勾选 `Protect variable` (推荐，只在protected分支使用)
   - 点击 `Add variable`

3. **重新运行CI**：配置完成后，重新运行失败的Pipeline即可

> **安全提示**: 密码变量请务必设置为Masked，避免在日志中泄露

详细说明请查看：[TROUBLESHOOTING.md#ci-kjl-auth-failed](./TROUBLESHOOTING.md#ci-kjl-auth-failed)

---

**🎉 享受零配置的 API 文档体验！** 有任何问题请查看 [动态文档系统指南](./docs/DYNAMIC_DOCS_GUIDE.md) 或提交 Issue。 