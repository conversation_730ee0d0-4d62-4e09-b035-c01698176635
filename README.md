# IDP.Types
IDP 命名空间接口包，最终会合并发布为`@qunhe/idp-sdk`(内网)和`@manycore/idp-sdk`(外网)

## CHANGELOG 规范
- 任意修改都需要添加修改日志
- 未发布前一律将修改日志写到 DEV 条目下，未发布前不修改版本号，发布时根据[接口包发布周期](./packages/idp-sdk-internal/docs/接口包发布周期/README.md)更新版本号，并将 DEV 条目内容移动到对应版本号上
- CHANGELOG 文件
    - [idp-sdk](./packages/idp-sdk/CHANGELOG.md)
    - [idp-sdk-internal](./packages/idp-sdk-internal/CHANGELOG.md)

## 新增一个子包
```bash
# 需要导出到公网的包，将会包含于 idp-sdk
# 包名必须为 @qunhe/idp-{...}
node scripts/create-package {YOUR_PACKAGE_NAME}
yarn

# 如果不需要发布到公网，即不包含与 idp-sdk，可以通用包名引入
# 后续需要公开，可以手动添加至 idp-sdk
node scripts/create-package --internal {YOUR_PACKAGE_NAME}
yarn
```
**注意**
- 所有声明必须写在`api.d.ts`内，不可分文件目前

## 如何发布一个测试包（内网）

> 可在 feature 分支操作。

1. 通过 lerna 更新版本号（必须指定为 prerelease）
    ```bash
    npx lerna version prerelease --preid <foo>
    ```
1. 等待 pipeline 通过 test 后，手动触发 `publish-prerelease` job，发布到内网

## 如何发布一个正式包（内网）

1. 从 master 拉取 feature 分支，通过 lerna 更新版本号；提交修改，新建到 master 的 MR
    ```bash
    npx lerna version
    ```
1. 通知仓库 maintainer 合并
1. **maintainer**：使用 idp-sdk 版本号作为 tag，创建 tag 后自动发布

> 针对常规的两周一次的中版本发布，由 **maintainer** 触发 `create-public-release` 脚本发布（自动升级 minor 版本及创建 tag）

## 如何发布一个正式包/测试包（外网）

参考 [小程序包外网发布流程](https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80597111587)

## 如何发布文档网站

发布文档网站之前，应当**首先发布对应版本的小程序包**。

文档发布目前仅在特定分支可用。在小程序包发布之后：

1. 前往 **master 分支** 或 **Tag** 上的 ci job
1. （针对二方接口包，仅内网可见）触发 `publish-internal-docs`
1. （针对三方接口包）触发 `publish-sdk-docs`，发布对应版本的文档 (示例: https://manual.qunhequnhe.com/idp-sdk/1.17.0)
1. 发布完**三方接口包的正式版**文档后，通常还需要更新对应的 latest 版本文档：
    1. 前往 **Tag** 上的 ci job
    1. 触发 `publish-sdk-docs-latest`，发布对应版本的文档 (https://manual.qunhequnhe.com/idp-sdk/latest)

注：三方接口包的外网文档和内网文档保持同步。(示例: 内网 https://manual.qunhequnhe.com/idp-sdk/latest -> 外网 https://manual.kujiale.com/idp-sdk/latest)

## 如何使用自动生成的 VMBinding (试验阶段，可能不稳定)
1. 在本仓库内创建一个子包，并完成接口导出
2. 通过临时分支发布子包（参考[发布流程](#如何发布一个测试包)），或者自己通过`npm run build-package`构建子包并使用
3. 在使用时引用新发布的包，并使用 `createVMBindingType` 创建完整的VM绑定信息
4. 可选：使用 `@qunhe/idp-vmbinding-utils` 从生成的binding信息中获取部分namespace的绑定信息
5. 如需使用外部库已有的绑定信息，如Math，可以参考 [idp-math](./packages/idp-math)中`build-vm-type`脚本和`index.js`
6. 更多生成工具功能可以参考 [apaas-type-generator](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/apaas-type-generator), 可以自由修改由模板生成的包内的构建脚本和文件，其中 `impl/vmTypeInternal.js`、`impl/vmTypeInternal.d.ts`、`impl/vmTypePublic.js`、`impl/vmTypePublic.d.ts`、`global-decl-internal`及`global-decl-public`，为自动编译生成，请不要修改

简易使用样例如下
```ts
import { getVMBindingType, getVMBindingTypeInternal } from '@qunhe/idp-my_api_pkg/impl';
import { getVMBindingTypeForNamespace } from '@qunhe/idp-vmbinding-utils';

// TODO: ...... plugin or other...

appService.miniappManager.registerApi(() => { // 有许多入参，参考app-core类型声明
    const vmType = getVMBindingType(/* TODO: 可选入参 */); // 或者 getVMBindingTypeInternal 按需选择

    return {
         namespace: 'MY_NAMESPACE1',
         value: MY_API_OBJECT1
         type: getVMBindingTypeForNamespace('IDP.MY_NAMESPACE1', vmType)
    }
});
appService.miniappManager.registerApi(() => { // 有许多入参，参考app-core类型声明
    const vmType = getVMBindingType(/* TODO: 可选入参 */); // 或者 getVMBindingTypeInternal 按需选择

    return {
         namespace: 'MY_NAMESPACE2',
         value: MY_API_OBJECT2
         type: getVMBindingTypeForNamespace('IDP.MY_NAMESPACE2', vmType)
    }
});

// TODO: ... more
```
