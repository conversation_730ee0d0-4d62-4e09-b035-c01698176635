import { Type } from '@qunhe/kls-abstraction';
import * as APIType from '@qunhe/idp-math/api';
export declare function createVMBindingType(typeInjections: {
    packages: {
        "@qunhe/math-apaas-api": {
            "KGeomLib": Type;
            "KCurve2dType": Type;
            "KCurve3dType": Type;
            "KPtInLoopType": Type;
            "KCurveSurfaceIntersectType": Type;
            "KSurfaceType": Type;
            "KCurveInLoopType": Type;
            "KFaceBooleanType": Type;
        };
    };
}): Type & { VALUE_TYPE: typeof APIType; };
