import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/pdm/平面造型设计api",
    },
    {
      type: "category",
      label: "平面造型设计管理接口",
      link: {
        type: "doc",
        id: "api/pdm/平面造型设计管理接口",
      },
      items: [
        {
          type: "doc",
          id: "api/pdm/save-planar-model-v-2",
          label: "保存单个平面造型数据",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/pdm/batch-delete-planar-model",
          label: "批量删除平面造型数据",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/pdm/batch-save-planar-model-v-2",
          label: "批量保存平面造型数据",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/pdm/delete-planar-model",
          label: "根据平面造型ID删除单个平面造型数据",
          className: "api-method delete",
        },
        {
          type: "doc",
          id: "api/pdm/fetch-planar-model-list",
          label: "分页获取平面造型数据列表",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/pdm/batch-fetch-planar-model-build-result",
          label: "批量获取平面造型建模结果",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/pdm/batch-fetch-planar-model",
          label: "批量获取平面造型数据",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/pdm/fetch-planar-model",
          label: "获取平面造型数据",
          className: "api-method get",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
