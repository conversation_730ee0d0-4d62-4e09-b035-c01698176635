import { Type } from '@qunhe/kls-abstraction';
import * as APIType from '@qunhe/idp-floorplan/api';
export declare function createVMBindingType(typeInjections: {
    types: {
        "FloorplanElement": Type;
        "FloorplanDocumentBatchUpdateRequest": Type;
    };
    packages: {
        "@qunhe/math-apaas-api": {
            "KBoundedCurve2d": Type;
            "KGeomFace2d": Type;
            "KFace3d": Type;
            "KBoundedCurve3d": Type;
            "KPoint2d": Type;
            "KBoundingBox2d": Type;
            "KVector2d": Type;
        };
    };
}): Type & { VALUE_TYPE: typeof APIType; };
