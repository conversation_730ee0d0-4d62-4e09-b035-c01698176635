# SDK 配置文件
# 定义 SDK 生成、构建和部署的配置参数

# =============================================================================
# 支持的 SDK 语言配置
# =============================================================================

# 支持的语言列表
SUPPORTED_LANGUAGES=("java" "typescript" "python" "go")

# 默认生成的语言
DEFAULT_LANGUAGES="java,typescript"

# =============================================================================
# OpenAPI Generator 模板配置
# =============================================================================

# Java SDK 配置
JAVA_TEMPLATE="java"
JAVA_LIBRARY="okhttp-gson"
JAVA_GROUP_ID="com.qunhe.manycoreapi"
JAVA_ARTIFACT_ID="manycore-api-sdk"
JAVA_PACKAGE_NAME="com.qunhe.manycoreapi.client"
JAVA_INVOKER_PACKAGE="com.qunhe.manycoreapi.client"
JAVA_MODEL_PACKAGE="com.qunhe.manycoreapi.client.model"
JAVA_API_PACKAGE="com.qunhe.manycoreapi.client.api"
JAVA_SOURCE_FOLDER="src/main/java"
JAVA_TEST_FOLDER="src/test/java"

# TypeScript SDK 配置
TYPESCRIPT_TEMPLATE="typescript-fetch"
TYPESCRIPT_NPM_NAME="@qunhe/manycore-api-sdk"
TYPESCRIPT_NPM_VERSION="1.0.0"
TYPESCRIPT_NPM_REPOSITORY="http://npm-registry.qunhequnhe.com"
TYPESCRIPT_MODEL_PROPERTY_NAMING="camelCase"
TYPESCRIPT_SUPPORTS_ES6="true"
TYPESCRIPT_WITH_INTERFACES="true"

# Python SDK 配置
PYTHON_TEMPLATE="python"
PYTHON_PACKAGE_NAME="qunhe_manycore_api"
PYTHON_PROJECT_NAME="qunhe-manycore-api-sdk"
PYTHON_PACKAGE_VERSION="1.0.0"
PYTHON_PACKAGE_URL="https://github.com/qunhe/manycore-api-sdk-python"

# Go SDK 配置
GO_TEMPLATE="go"
GO_PACKAGE_NAME="manycoreapi"
GO_PACKAGE_VERSION="1.0.0"
GO_PACKAGE_URL="github.com/qunhe/manycore-api-sdk-go"
GO_MODULE_NAME="github.com/qunhe/manycore-api-sdk-go"

# =============================================================================
# 构建工具配置
# =============================================================================

# Java 构建配置
JAVA_BUILD_TOOL="maven"
JAVA_MAVEN_GOALS="clean compile package"
JAVA_TEST_GOALS="test"
JAVA_DEPLOY_GOALS="deploy"
JAVA_JDK_VERSION="11"

# TypeScript 构建配置
TYPESCRIPT_BUILD_TOOL="npm"
TYPESCRIPT_BUILD_SCRIPT="build"
TYPESCRIPT_TEST_SCRIPT="test"
TYPESCRIPT_LINT_SCRIPT="lint"
TYPESCRIPT_NODE_VERSION="16"

# Python 构建配置
PYTHON_BUILD_TOOL="setuptools"
PYTHON_BUILD_COMMAND="python setup.py sdist bdist_wheel"
PYTHON_TEST_COMMAND="python -m pytest"
PYTHON_LINT_COMMAND="flake8"
PYTHON_VERSION="3.8"

# Go 构建配置
GO_BUILD_COMMAND="go build ./..."
GO_TEST_COMMAND="go test ./..."
GO_LINT_COMMAND="golangci-lint run"
GO_VERSION="1.19"

# =============================================================================
# 部署仓库配置
# =============================================================================

# Maven 仓库配置
MAVEN_SNAPSHOTS_REPOSITORY_ID="qunhe-snapshots"
MAVEN_SNAPSHOTS_REPOSITORY_URL="http://nexus.qunhequnhe.com/repository/maven-snapshots/"
MAVEN_RELEASES_REPOSITORY_ID="qunhe-releases"
MAVEN_RELEASES_REPOSITORY_URL="http://nexus.qunhequnhe.com/repository/maven-releases/"

# npm 仓库配置
NPM_REGISTRY_URL="http://npm-registry.qunhequnhe.com"
NPM_SCOPE="@qunhe"

# Python 仓库配置
PYTHON_REPOSITORY_URL="http://pypi.qunhequnhe.com/simple/"
PYTHON_REPOSITORY_NAME="qunhe-pypi"

# Go 模块配置
GO_PROXY="https://goproxy.cn,direct"
GO_SUMDB="sum.golang.org"

# =============================================================================
# 版本管理配置
# =============================================================================

# 版本号格式
VERSION_FORMAT="MAJOR.MINOR.PATCH"
SNAPSHOT_SUFFIX="-SNAPSHOT"
RELEASE_SUFFIX=""

# 版本号生成策略
VERSION_STRATEGY="git-based"  # git-based, timestamp, manual

# Git 标签格式
GIT_TAG_PREFIX="v"
GIT_TAG_FORMAT="v{version}"

# =============================================================================
# 测试配置
# =============================================================================

# 测试类型
TEST_TYPES=("unit" "integration")
DEFAULT_TEST_TYPE="unit"

# 测试超时时间（秒）
TEST_TIMEOUT_UNIT="300"
TEST_TIMEOUT_INTEGRATION="1800"

# 测试覆盖率要求
MIN_COVERAGE_UNIT="80"
MIN_COVERAGE_INTEGRATION="70"

# =============================================================================
# 文档生成配置
# =============================================================================

# 是否生成 SDK 文档
GENERATE_SDK_DOCS="true"

# 文档格式
SDK_DOC_FORMATS=("html" "markdown")
DEFAULT_DOC_FORMAT="markdown"

# 文档输出目录
SDK_DOCS_OUTPUT_DIR="docs/sdk"

# =============================================================================
# 质量检查配置
# =============================================================================

# 代码质量检查工具
JAVA_QUALITY_TOOLS=("checkstyle" "spotbugs" "pmd")
TYPESCRIPT_QUALITY_TOOLS=("eslint" "prettier")
PYTHON_QUALITY_TOOLS=("flake8" "black" "mypy")
GO_QUALITY_TOOLS=("golangci-lint" "gofmt")

# 质量门禁
QUALITY_GATE_ENABLED="true"
FAIL_ON_QUALITY_ISSUES="false"

# =============================================================================
# 缓存配置
# =============================================================================

# 构建缓存
ENABLE_BUILD_CACHE="true"
BUILD_CACHE_DIR=".cache/build"
BUILD_CACHE_TTL="86400"  # 24小时

# 依赖缓存
ENABLE_DEPENDENCY_CACHE="true"
DEPENDENCY_CACHE_DIR=".cache/dependencies"
DEPENDENCY_CACHE_TTL="604800"  # 7天

# =============================================================================
# 并行构建配置
# =============================================================================

# 并行构建
ENABLE_PARALLEL_BUILD="true"
MAX_PARALLEL_JOBS="4"

# 并行测试
ENABLE_PARALLEL_TEST="true"
MAX_PARALLEL_TEST_JOBS="2"

# =============================================================================
# 配置函数
# =============================================================================

# 获取语言特定的配置
get_language_config() {
    local language="$1"
    local config_name="$2"
    
    # 转换语言名称为大写
    local lang_upper=$(echo "$language" | tr '[:lower:]' '[:upper:]')
    
    # 构建变量名
    local var_name="${lang_upper}_${config_name}"
    
    # 获取变量值
    local value="${!var_name}"
    
    echo "$value"
}

# 验证语言是否支持
is_language_supported() {
    local language="$1"
    
    for supported_lang in "${SUPPORTED_LANGUAGES[@]}"; do
        if [ "$supported_lang" = "$language" ]; then
            return 0
        fi
    done
    
    return 1
}

# 获取语言列表
get_language_list() {
    local languages_str="${1:-$DEFAULT_LANGUAGES}"
    
    # 将逗号分隔的字符串转换为数组
    IFS=',' read -ra languages <<< "$languages_str"
    
    # 验证每个语言是否支持
    local valid_languages=()
    for lang in "${languages[@]}"; do
        lang=$(echo "$lang" | xargs)  # 去除空格
        if is_language_supported "$lang"; then
            valid_languages+=("$lang")
        else
            echo "警告: 不支持的语言 '$lang'" >&2
        fi
    done
    
    printf '%s\n' "${valid_languages[@]}"
}

# 显示 SDK 配置
show_sdk_config() {
    echo "SDK 配置信息:"
    echo "  支持的语言: ${SUPPORTED_LANGUAGES[*]}"
    echo "  默认语言: $DEFAULT_LANGUAGES"
    echo "  版本策略: $VERSION_STRATEGY"
    echo "  版本格式: $VERSION_FORMAT"
    echo "  并行构建: $ENABLE_PARALLEL_BUILD"
    echo "  最大并行任务: $MAX_PARALLEL_JOBS"
    echo "  构建缓存: $ENABLE_BUILD_CACHE"
    echo "  质量门禁: $QUALITY_GATE_ENABLED"
    echo "  生成文档: $GENERATE_SDK_DOCS"
    
    echo ""
    echo "语言特定配置:"
    for lang in "${SUPPORTED_LANGUAGES[@]}"; do
        echo "  $lang:"
        echo "    模板: $(get_language_config "$lang" "TEMPLATE")"
        echo "    构建工具: $(get_language_config "$lang" "BUILD_TOOL")"
        echo "    版本: $(get_language_config "$lang" "VERSION")"
    done
}

# 验证 SDK 配置
validate_sdk_config() {
    local errors=0
    
    # 检查必需的配置
    if [ ${#SUPPORTED_LANGUAGES[@]} -eq 0 ]; then
        echo "错误: SUPPORTED_LANGUAGES 为空"
        ((errors++))
    fi
    
    if [ -z "$DEFAULT_LANGUAGES" ]; then
        echo "错误: DEFAULT_LANGUAGES 未设置"
        ((errors++))
    fi
    
    if [ -z "$VERSION_STRATEGY" ]; then
        echo "错误: VERSION_STRATEGY 未设置"
        ((errors++))
    fi
    
    # 验证默认语言是否都支持
    IFS=',' read -ra default_langs <<< "$DEFAULT_LANGUAGES"
    for lang in "${default_langs[@]}"; do
        lang=$(echo "$lang" | xargs)
        if ! is_language_supported "$lang"; then
            echo "错误: 默认语言 '$lang' 不在支持列表中"
            ((errors++))
        fi
    done
    
    if [ $errors -gt 0 ]; then
        echo "SDK 配置验证失败，发现 $errors 个错误"
        return 1
    else
        echo "SDK 配置验证通过"
        return 0
    fi
}

# 生成版本号
generate_version() {
    local version_type="${1:-snapshot}"
    local base_version="${2:-1.0.0}"
    
    case "$VERSION_STRATEGY" in
        "git-based")
            if [ "$version_type" = "release" ]; then
                echo "$base_version"
            else
                local commit_sha=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
                local timestamp=$(date +%Y%m%d%H%M%S)
                echo "${base_version}-${timestamp}-${commit_sha}${SNAPSHOT_SUFFIX}"
            fi
            ;;
        "timestamp")
            local timestamp=$(date +%Y%m%d%H%M%S)
            if [ "$version_type" = "release" ]; then
                echo "${base_version}-${timestamp}"
            else
                echo "${base_version}-${timestamp}${SNAPSHOT_SUFFIX}"
            fi
            ;;
        "manual")
            if [ "$version_type" = "release" ]; then
                echo "$base_version"
            else
                echo "${base_version}${SNAPSHOT_SUFFIX}"
            fi
            ;;
        *)
            echo "错误: 未知的版本策略 '$VERSION_STRATEGY'" >&2
            return 1
            ;;
    esac
}

# 如果直接执行此配置文件，显示帮助信息
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "SDK 配置文件"
    echo ""
    echo "用法:"
    echo "  source config/sdk-config.conf"
    echo "  show_sdk_config"
    echo "  validate_sdk_config"
    echo "  get_language_list \"java,typescript\""
    echo "  generate_version snapshot 1.0.0"
    echo ""
    echo "支持的语言:"
    printf '  %s\n' "${SUPPORTED_LANGUAGES[@]}"
fi
