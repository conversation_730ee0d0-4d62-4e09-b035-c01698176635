#!/bin/bash

# SDK 测试脚本
# 支持单个服务或所有服务的 SDK 测试
# 重构自 tools/ci-cd/ci/deployment/test-sdks.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# Maven 配置
MAVEN_SETTINGS="${MAVEN_SETTINGS:-config/ci-cd/ci-settings.xml}"

# 测试单个服务的 SDK
test_service_sdk() {
    local service="$1"
    local language="${2:-java}"
    local test_phase="${3:-test}"
    
    log_info "🧪 测试 $service SDK ($language)..."
    
    # 验证服务
    if ! is_valid_service "$service"; then
        log_error "无效的服务名: $service"
        increment_counter "test_failed"
        return 1
    fi
    
    # 获取输出目录
    local output_dir=$(get_service_output_path "$service" "$language")
    
    # 检查输出目录
    if ! check_dir_exists "$output_dir" "SDK 输出目录"; then
        log_error "$service SDK 输出目录不存在，请先生成和构建 SDK"
        increment_counter "test_failed"
        return 1
    fi
    
    # 检查是否有 pom.xml (Java 项目)
    if [ "$language" = "java" ]; then
        if ! check_file_exists "$output_dir/pom.xml" "Maven POM 文件"; then
            log_error "$service SDK 缺少 Maven POM 文件"
            increment_counter "test_failed"
            return 1
        fi
    fi
    
    # 保存当前目录
    local project_dir=$(pwd)
    
    # 测试 SDK
    log_info "执行 SDK 测试..."
    local start_time=$(get_timestamp)
    
    if cd "$output_dir"; then
        local test_success=false
        
        case "$language" in
            "java")
                # Maven 测试
                local maven_cmd="mvn $test_phase"
                if [ -f "$project_dir/$MAVEN_SETTINGS" ]; then
                    maven_cmd="$maven_cmd -s $project_dir/$MAVEN_SETTINGS"
                fi
                
                log_debug "执行命令: $maven_cmd"
                
                if $maven_cmd >/dev/null 2>&1; then
                    test_success=true
                    
                    # 检查测试报告
                    if [ -d "target/surefire-reports" ]; then
                        local test_count=$(find target/surefire-reports -name "*.xml" | wc -l)
                        log_debug "生成了 $test_count 个测试报告"
                    fi
                else
                    log_error "Maven 测试失败"
                    # 显示错误详情
                    log_info "错误详情:"
                    $maven_cmd 2>&1 | tail -20
                fi
                ;;
            *)
                log_error "不支持的语言: $language"
                ;;
        esac
        
        # 返回项目目录
        cd "$project_dir"
        
        local end_time=$(get_timestamp)
        local duration=$((end_time - start_time))
        
        if [ "$test_success" = true ]; then
            log_success "$service SDK 测试通过 (耗时: ${duration}s)"
            increment_counter "test_success"
            return 0
        else
            log_error "$service SDK 测试失败"
            increment_counter "test_failed"
            return 1
        fi
    else
        log_error "无法进入 SDK 目录: $output_dir"
        increment_counter "test_failed"
        return 1
    fi
}

# 测试所有服务的 SDK
test_all_sdks() {
    local language="${1:-java}"
    local test_phase="${2:-test}"
    
    log_step "1" "测试所有服务的 SDK ($language)"
    
    local services=$(get_services)
    local overall_status=0
    
    if [ -z "$services" ]; then
        log_warning "未发现任何服务"
        return 0
    fi
    
    log_info "发现的服务: $services"
    
    for service in $services; do
        if test_service_sdk "$service" "$language" "$test_phase"; then
            log_success "服务 $service SDK 测试完成"
        else
            log_error "服务 $service SDK 测试失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 测试变更服务的 SDK
test_changed_sdks() {
    local language="${1:-java}"
    local test_phase="${2:-test}"
    
    log_step "1" "测试变更服务的 SDK ($language)"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过 SDK 测试"
        return 0
    fi
    
    if [ -z "$CHANGED_SERVICES" ]; then
        log_warning "变更服务列表为空，测试所有服务的 SDK"
        test_all_sdks "$language" "$test_phase"
        return $?
    fi
    
    log_info "变更的服务: $CHANGED_SERVICES"
    
    local overall_status=0
    
    for service in $CHANGED_SERVICES; do
        if test_service_sdk "$service" "$language" "$test_phase"; then
            log_success "变更服务 $service SDK 测试完成"
        else
            log_error "变更服务 $service SDK 测试失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 测试指定服务列表的 SDK
test_service_list() {
    local service_list="$1"
    local language="${2:-java}"
    local test_phase="${3:-test}"
    
    log_step "1" "测试指定服务的 SDK ($language)"
    
    if [ -z "$service_list" ]; then
        log_error "服务列表为空"
        return 1
    fi
    
    log_info "指定的服务: $service_list"
    
    local overall_status=0
    
    for service in $service_list; do
        if test_service_sdk "$service" "$language" "$test_phase"; then
            log_success "服务 $service SDK 测试完成"
        else
            log_error "服务 $service SDK 测试失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 验证测试环境
verify_test_environment() {
    log_step "0" "验证测试环境"
    
    # 检查 Maven
    if ! mvn -version >/dev/null 2>&1; then
        log_error "Maven 不可用"
        return 1
    fi
    
    # 检查 Java
    if ! java -version >/dev/null 2>&1; then
        log_error "Java 不可用"
        return 1
    fi
    
    # 检查 Maven 配置文件
    if [ -n "$MAVEN_SETTINGS" ] && [ ! -f "$MAVEN_SETTINGS" ]; then
        log_warning "Maven 配置文件不存在: $MAVEN_SETTINGS"
        log_info "将使用默认 Maven 配置"
        MAVEN_SETTINGS=""
    fi
    
    log_success "测试环境验证通过"
    return 0
}

# 显示测试统计
show_test_stats() {
    log_step "2" "显示测试统计"
    
    echo ""
    log_info "🧪 SDK 测试结果统计:"
    
    local success=$(get_counter "test_success")
    local failed=$(get_counter "test_failed")
    local total=$((success + failed))
    
    echo "  总服务数: $total"
    echo "  测试通过: $success"
    echo "  测试失败: $failed"
    
    if [ $total -gt 0 ]; then
        local success_rate=$((success * 100 / total))
        echo "  成功率: $success_rate%"
    fi
    
    show_stats "SDK 测试统计"
}

# 主函数
main() {
    local mode="${1:-changed}"     # changed, all, service, list
    local target="${2:-}"          # 服务名或服务列表
    local language="${3:-java}"    # 编程语言
    local test_phase="${4:-test}"  # 测试阶段
    
    init_script "SDK 测试" "测试 OpenAPI SDK"
    
    # 验证测试环境
    if ! verify_test_environment; then
        finish_script "SDK 测试" "false"
        exit 1
    fi
    
    local test_status=0
    
    case "$mode" in
        "changed")
            log_info "测试模式: 变更服务"
            if ! test_changed_sdks "$language" "$test_phase"; then
                test_status=1
            fi
            ;;
        "all")
            log_info "测试模式: 所有服务"
            if ! test_all_sdks "$language" "$test_phase"; then
                test_status=1
            fi
            ;;
        "service")
            if [ -z "$target" ]; then
                log_error "服务测试模式需要指定服务名"
                finish_script "SDK 测试" "false"
                exit 1
            fi
            log_info "测试模式: 单个服务 ($target)"
            if ! test_service_sdk "$target" "$language" "$test_phase"; then
                test_status=1
            fi
            ;;
        "list")
            if [ -z "$target" ]; then
                log_error "列表测试模式需要指定服务列表"
                finish_script "SDK 测试" "false"
                exit 1
            fi
            log_info "测试模式: 服务列表"
            if ! test_service_list "$target" "$language" "$test_phase"; then
                test_status=1
            fi
            ;;
        *)
            log_error "无效的测试模式: $mode"
            log_info "支持的模式: changed, all, service, list"
            finish_script "SDK 测试" "false"
            exit 1
            ;;
    esac
    
    show_test_stats
    
    if [ $test_status -eq 0 ]; then
        log_success "🎉 SDK 测试完成！"
        finish_script "SDK 测试" "true"
        return 0
    else
        log_error "❌ SDK 测试失败，请检查错误信息"
        finish_script "SDK 测试" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "SDK 测试模块已加载 ✅"
