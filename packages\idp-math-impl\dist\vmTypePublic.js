var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_KGeomLib = injections.packages["@qunhe/math-apaas-api"]["KGeomLib"];
    var var_injection_KCurve2dType = injections.packages["@qunhe/math-apaas-api"]["KCurve2dType"];
    var var_injection_KCurve3dType = injections.packages["@qunhe/math-apaas-api"]["KCurve3dType"];
    var var_injection_KPtInLoopType = injections.packages["@qunhe/math-apaas-api"]["KPtInLoopType"];
    var var_injection_KCurveSurfaceIntersectType = injections.packages["@qunhe/math-apaas-api"]["KCurveSurfaceIntersectType"];
    var var_injection_KSurfaceType = injections.packages["@qunhe/math-apaas-api"]["KSurfaceType"];
    var var_injection_KCurveInLoopType = injections.packages["@qunhe/math-apaas-api"]["KCurveInLoopType"];
    var var_injection_KFaceBooleanType = injections.packages["@qunhe/math-apaas-api"]["KFaceBooleanType"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Math = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_KGeomLib, packageName: "@qunhe/math-apaas-api", exportName: "KGeomLib" },
        { value: var_injection_KCurve2dType, packageName: "@qunhe/math-apaas-api", exportName: "KCurve2dType" },
        { value: var_injection_KCurve3dType, packageName: "@qunhe/math-apaas-api", exportName: "KCurve3dType" },
        { value: var_injection_KPtInLoopType, packageName: "@qunhe/math-apaas-api", exportName: "KPtInLoopType" },
        { value: var_injection_KCurveSurfaceIntersectType, packageName: "@qunhe/math-apaas-api", exportName: "KCurveSurfaceIntersectType" },
        { value: var_injection_KSurfaceType, packageName: "@qunhe/math-apaas-api", exportName: "KSurfaceType" },
        { value: var_injection_KCurveInLoopType, packageName: "@qunhe/math-apaas-api", exportName: "KCurveInLoopType" },
        { value: var_injection_KFaceBooleanType, packageName: "@qunhe/math-apaas-api", exportName: "KFaceBooleanType" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Math": var_Math,
    };
    var_Math.type = BasicType.Object;
    var_Math.properties = {
        "KGeomLib": var_injection_KGeomLib,
        "KCurve2dType": var_injection_KCurve2dType,
        "KCurve3dType": var_injection_KCurve3dType,
        "KPtInLoopType": var_injection_KPtInLoopType,
        "KCurveSurfaceIntersectType": var_injection_KCurveSurfaceIntersectType,
        "KSurfaceType": var_injection_KSurfaceType,
        "KCurveInLoopType": var_injection_KCurveInLoopType,
        "KFaceBooleanType": var_injection_KFaceBooleanType,
    };
    
    return var_sourceFile;
};
