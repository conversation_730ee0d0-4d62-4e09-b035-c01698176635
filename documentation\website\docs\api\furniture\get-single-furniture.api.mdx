---
id: get-single-furniture
title: "获取单个家具信息"
description: "根据家具ID获取特定家具的详细信息。适用于家具详情展示、编辑前数据加载等场景。"
sidebar_label: "获取单个家具信息"
hide_title: true
hide_table_of_contents: true
api: {"tags":["家具管理接口"],"description":"根据家具ID获取特定家具的详细信息。适用于家具详情展示、编辑前数据加载等场景。","operationId":"getSingleFurniture","parameters":[{"name":"obsDesignId","in":"path","description":"设计方案的ID","required":true,"schema":{"type":"string"},"example":"ObQ1gR2nF8kE"},{"name":"levelId","in":"path","description":"楼层ID","required":true,"schema":{"type":"string"},"example":"level_001"},{"name":"furnitureId","in":"path","description":"家具的唯一标识符，由系统生成","required":true,"schema":{"type":"string"},"example":"furniture_001"},{"name":"view","in":"query","description":"数据视图类型。BASIC：核心信息；FULL：包含组件编辑信息","required":false,"schema":{"type":"string","default":"BASIC","enum":["BASIC","FULL"]},"example":"FULL"}],"responses":{"200":{"description":"成功获取家具信息","content":{"application/json":{"schema":{"required":["productId","size","transform"],"type":"object","properties":{"id":{"maxLength":64,"pattern":"^[a-zA-Z0-9_-]+$","type":"string","description":"家具的唯一标识符，全局唯一。创建时由系统生成，更新和删除时必需","example":"furniture_001"},"productId":{"type":"string","description":"家具对应的产品ID，用于标识家具类型。必须是系统中存在且有权限访问的产品","example":"prod_sofa_001"},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"transform":{"required":["position","rotate"],"type":"object","properties":{"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"rotate":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"3D变换信息，包含位置坐标和旋转角度","example":{"position":{"x":1.5,"y":0,"z":2.3},"rotate":{"x":0,"y":1.5707963267948966,"z":0}},"title":"Transform"},"componentEditInfos":{"maxItems":50,"minItems":0,"type":"array","description":"组件编辑信息列表，包含材质替换和UV编辑。仅在FULL视图下返回","items":{"required":["componentId"],"type":"object","properties":{"componentId":{"maxLength":128,"minLength":0,"pattern":"^[a-zA-Z0-9_-]+$","type":"string","description":"组件的唯一标识符，对应家具模型中的组件ID。通常等于 component materialId 的值","example":"comp_sofa_fabric_001"},"replaceInfo":{"type":"object","properties":{"targetMaterialId":{"maxLength":128,"minLength":0,"type":"string","description":"目标材质ID，用于材质级别的替换。只改变表面外观，不影响几何形状","example":"mat_leather_brown_001"},"targetProductId":{"maxLength":128,"minLength":0,"type":"string","description":"目标产品ID，用于产品级别的替换。可能改变组件的几何形状和功能","example":"prod_handle_gold_001"},"replacementType":{"type":"string"}},"description":"组件替换信息，支持材质替换和产品替换","example":{"targetMaterialId":"mat_leather_brown_001","targetProductId":"prod_handle_gold_001"},"title":"ComponentReplaceInfo"},"uvEditInfo":{"type":"object","properties":{"angle":{"maximum":6.283185307179586,"exclusiveMaximum":false,"minimum":0,"exclusiveMinimum":false,"type":"number","description":"材质旋转角度，单位：弧度。正值为逆时针旋转，范围[0, 2π]","format":"double","example":1.5707963267948966},"scale":{"type":"object","title":"Point2d","description":"二维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"}}},"translation":{"type":"object","title":"Point2d","description":"二维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"}}},"editDescription":{"type":"string"}},"description":"UV贴图编辑信息，控制材质在组件表面的显示效果","example":{"angle":1.5707963267948966,"scale":{"x":1.2,"y":1},"translation":{"x":0.1,"y":0}},"title":"ComponentUVEditInfo"}},"description":"组件编辑信息，支持材质替换和UV编辑","example":{"componentId":"comp_sofa_fabric_001","replaceInfo":{"targetMaterialId":"mat_leather_brown_001"},"uvEditInfo":{"angle":1.57,"scale":{"x":1.2,"y":1},"translation":{"x":0.1,"y":0}}},"title":"ComponentEditInfo"}}},"description":"家具单元数据，包含家具的完整信息","example":{"id":"furniture_001","productId":"prod_sofa_001","size":{"x":2,"y":0.8,"z":0.9},"transform":{"position":{"x":1.5,"y":0,"z":2.3},"rotate":{"x":0,"y":1.57,"z":0}},"componentEditInfos":[{"componentId":"comp_sofa_fabric_001","replaceInfo":{"targetMaterialId":"mat_leather_brown_001"}}]},"title":"FurnitureUnitData"},"examples":{"家具详情响应示例":{"description":"家具详情响应示例","value":{"id":"furniture_001","productId":"prod_sofa_001","size":{"x":2,"y":0.8,"z":0.9},"transform":{"position":{"x":1.5,"y":0,"z":2.3},"rotate":{"x":0,"y":1.57,"z":0}}}}}}}},"400":{"description":"请求参数格式错误，如家具ID格式不正确","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"指定ID的家具不存在，或用户无权限访问","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误，请稍后重试","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}},"method":"get","path":"/fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture/{furnitureId}","servers":[{"url":"http://localhost:8083","description":"本地开发环境"},{"url":"https://api-dev.qunhe.com","description":"开发测试环境"},{"url":"https://api.qunhe.com","description":"生产环境"}],"info":{"title":"家具设计管理API","description":"家具设计服务REST API\n\n为群核旗下设计工具提供家具数据的完整生命周期管理接口，支持家具模型的创建、编辑、查询和管理。提供单个和批量操作，支持不同数据视图以优化性能，适用于各种家具设计和管理场景。\n\n**核心功能：**\n- 家具CRUD操作：创建、获取、更新、删除家具实例\n- 批量操作支持：高效处理大量家具数据的批量操作\n- 多视图支持：BASIC和FULL视图，针对不同场景优化性能\n- 分页查询：支持大量家具数据的分页展示和管理\n- 产品组合：支持通过组合商品批量创建相关家具\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 幂等性控制：通过requestId确保操作的幂等性\n\n**业务应用场景：**\n- 室内设计软件的家具管理\n- 家具产品的三维展示和编辑\n- 场景模板和套装家具的快速应用\n- 家具数据的批量导入和同步\n- 个性化家具定制和配置\n- 空间布局优化和家具摆放\n\n**技术特性：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 异步支持：长时间批量操作的异步处理机制","contact":{"name":"群核科技开发团队","url":"https://wiki.manycore.com/furniture-design","email":"<EMAIL>"},"license":{"name":"群核科技专有许可证","url":"https://manycore.com/license"},"version":"1.0.0"},"postman":{"name":"获取单个家具信息","description":{"content":"根据家具ID获取特定家具的详细信息。适用于家具详情展示、编辑前数据加载等场景。","type":"text/plain"},"url":{"path":["fds","api","v1","rest","designs",":obsDesignId","levels",":levelId","furniture",":furnitureId"],"host":["{{baseUrl}}"],"query":[{"disabled":false,"description":{"content":"数据视图类型。BASIC：核心信息；FULL：包含组件编辑信息","type":"text/plain"},"key":"view","value":""}],"variable":[{"disabled":false,"description":{"content":"(Required) 设计方案的ID","type":"text/plain"},"type":"any","value":"","key":"obsDesignId"},{"disabled":false,"description":{"content":"(Required) 楼层ID","type":"text/plain"},"type":"any","value":"","key":"levelId"},{"disabled":false,"description":{"content":"(Required) 家具的唯一标识符，由系统生成","type":"text/plain"},"type":"any","value":"","key":"furnitureId"}]},"header":[{"key":"Accept","value":"application/json"}],"method":"GET"}}
sidebar_class_name: "get api-method"
info_path: docs/api/furniture/家具设计管理api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"获取单个家具信息"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/fds/api/v1/rest/designs/{obsDesignId}/levels/{levelId}/furniture/{furnitureId}"}
  context={"endpoint"}
>
  
</MethodEndpoint>



根据家具ID获取特定家具的详细信息。适用于家具详情展示、编辑前数据加载等场景。

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"obsDesignId","in":"path","description":"设计方案的ID","required":true,"schema":{"type":"string"},"example":"ObQ1gR2nF8kE"},{"name":"levelId","in":"path","description":"楼层ID","required":true,"schema":{"type":"string"},"example":"level_001"},{"name":"furnitureId","in":"path","description":"家具的唯一标识符，由系统生成","required":true,"schema":{"type":"string"},"example":"furniture_001"},{"name":"view","in":"query","description":"数据视图类型。BASIC：核心信息；FULL：包含组件编辑信息","required":false,"schema":{"type":"string","default":"BASIC","enum":["BASIC","FULL"]},"example":"FULL"}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"成功获取家具信息","content":{"application/json":{"schema":{"required":["productId","size","transform"],"type":"object","properties":{"id":{"maxLength":64,"pattern":"^[a-zA-Z0-9_-]+$","type":"string","description":"家具的唯一标识符，全局唯一。创建时由系统生成，更新和删除时必需","example":"furniture_001"},"productId":{"type":"string","description":"家具对应的产品ID，用于标识家具类型。必须是系统中存在且有权限访问的产品","example":"prod_sofa_001"},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"transform":{"required":["position","rotate"],"type":"object","properties":{"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"rotate":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"3D变换信息，包含位置坐标和旋转角度","example":{"position":{"x":1.5,"y":0,"z":2.3},"rotate":{"x":0,"y":1.5707963267948966,"z":0}},"title":"Transform"},"componentEditInfos":{"maxItems":50,"minItems":0,"type":"array","description":"组件编辑信息列表，包含材质替换和UV编辑。仅在FULL视图下返回","items":{"required":["componentId"],"type":"object","properties":{"componentId":{"maxLength":128,"minLength":0,"pattern":"^[a-zA-Z0-9_-]+$","type":"string","description":"组件的唯一标识符，对应家具模型中的组件ID。通常等于 component materialId 的值","example":"comp_sofa_fabric_001"},"replaceInfo":{"type":"object","properties":{"targetMaterialId":{"maxLength":128,"minLength":0,"type":"string","description":"目标材质ID，用于材质级别的替换。只改变表面外观，不影响几何形状","example":"mat_leather_brown_001"},"targetProductId":{"maxLength":128,"minLength":0,"type":"string","description":"目标产品ID，用于产品级别的替换。可能改变组件的几何形状和功能","example":"prod_handle_gold_001"},"replacementType":{"type":"string"}},"description":"组件替换信息，支持材质替换和产品替换","example":{"targetMaterialId":"mat_leather_brown_001","targetProductId":"prod_handle_gold_001"},"title":"ComponentReplaceInfo"},"uvEditInfo":{"type":"object","properties":{"angle":{"maximum":6.283185307179586,"exclusiveMaximum":false,"minimum":0,"exclusiveMinimum":false,"type":"number","description":"材质旋转角度，单位：弧度。正值为逆时针旋转，范围[0, 2π]","format":"double","example":1.5707963267948966},"scale":{"type":"object","title":"Point2d","description":"二维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"}}},"translation":{"type":"object","title":"Point2d","description":"二维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"}}},"editDescription":{"type":"string"}},"description":"UV贴图编辑信息，控制材质在组件表面的显示效果","example":{"angle":1.5707963267948966,"scale":{"x":1.2,"y":1},"translation":{"x":0.1,"y":0}},"title":"ComponentUVEditInfo"}},"description":"组件编辑信息，支持材质替换和UV编辑","example":{"componentId":"comp_sofa_fabric_001","replaceInfo":{"targetMaterialId":"mat_leather_brown_001"},"uvEditInfo":{"angle":1.57,"scale":{"x":1.2,"y":1},"translation":{"x":0.1,"y":0}}},"title":"ComponentEditInfo"}}},"description":"家具单元数据，包含家具的完整信息","example":{"id":"furniture_001","productId":"prod_sofa_001","size":{"x":2,"y":0.8,"z":0.9},"transform":{"position":{"x":1.5,"y":0,"z":2.3},"rotate":{"x":0,"y":1.57,"z":0}},"componentEditInfos":[{"componentId":"comp_sofa_fabric_001","replaceInfo":{"targetMaterialId":"mat_leather_brown_001"}}]},"title":"FurnitureUnitData"},"examples":{"家具详情响应示例":{"description":"家具详情响应示例","value":{"id":"furniture_001","productId":"prod_sofa_001","size":{"x":2,"y":0.8,"z":0.9},"transform":{"position":{"x":1.5,"y":0,"z":2.3},"rotate":{"x":0,"y":1.57,"z":0}}}}}}}},"400":{"description":"请求参数格式错误，如家具ID格式不正确","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"指定ID的家具不存在，或用户无权限访问","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误，请稍后重试","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}}}
>
  
</StatusCodes>


      