#!/usr/bin/env node

// 动态生成 OpenAPI 配置脚本
// 自动扫描 openapi 文件夹中的 restapi.yaml 文件

const fs = require('node:fs');
const path = require('node:path');
const yaml = require('js-yaml');

/**
 * 扫描 openapi 文件夹，自动生成 Docusaurus OpenAPI 配置
 */
function generateOpenApiConfig() {
    // 从当前脚本位置计算 openapi 目录路径
    // 脚本可能从不同位置运行，需要灵活处理
    const scriptDir = __dirname;
    const projectRoot = path.join(scriptDir, '..');

    // 如果在 docs-site 目录中运行，需要向上两级
    const currentDir = process.cwd();
    let openApiDir;

    if (currentDir.endsWith('docs-site') || currentDir.endsWith('website')) {
        // 从 docs-site 或 website 目录运行
        openApiDir = path.join(currentDir, '..', '..', 'specifications', 'services');
    } else {
        // 从项目根目录运行
        openApiDir = path.join(currentDir, 'specifications', 'services');
    }

    // 特殊处理：如果脚本被复制到 docs-site 目录临时运行
    if (currentDir.endsWith('docs-site') && __filename.includes('temp-generate-config.js')) {
        openApiDir = path.join(currentDir, '..', 'specifications', 'services');
    }

    const config = {};

    console.log('🔍 当前工作目录:', currentDir);
    console.log('🔍 脚本目录:', scriptDir);
    console.log('🔍 扫描 OpenAPI 文件夹:', openApiDir);

    if (!fs.existsSync(openApiDir)) {
        console.error('❌ openapi 文件夹不存在:', openApiDir);
        return {};
    }

    // 读取所有子目录
    const subdirs = fs.readdirSync(openApiDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name)
        .filter(name => !['common', 'shared'].includes(name)); // 排除通用文件夹

    console.log('📁 发现服务目录:', subdirs);

    for (const serviceDir of subdirs) {
        const servicePath = path.join(openApiDir, serviceDir);
        const openapiPath = path.join(servicePath, 'openapi.yaml');

        if (fs.existsSync(openapiPath)) {
            console.log(`✅ 发现 API 规范: ${serviceDir}/openapi.yaml`);

            // 直接使用原始文件夹名称作为配置 ID，零转换
            const configId = serviceDir;

            config[configId] = {
                specPath: `../../specifications/services/${serviceDir}/openapi.yaml`,
                outputDir: `docs/api/${serviceDir}`,  // 直接使用文件夹名称
                sidebarOptions: {
                    groupPathsBy: "tag",
                    categoryLinkSource: "tag",  // 生成标签页面以获得完整文档结构
                },
                downloadUrl: `/specifications/services/${serviceDir}/openapi.yaml`,
                hideSendButton: false,
                showSchemas: false,
                disableCompression: false
               
            };
        } else {
            console.log(`⚠️ 未找到 API 规范: ${serviceDir}/openapi.yaml`);
        }
    }

    console.log(`🎉 生成了 ${Object.keys(config).length} 个 OpenAPI 配置`);
    return config;
}

/**
 * 从 OpenAPI 规范文件中提取服务信息
 */
function getServiceInfo(yamlPath, serviceDir) {
    try {
        const yamlContent = fs.readFileSync(yamlPath, 'utf8');
        const spec = yaml.load(yamlContent);

        return {
            // 从规范中提取标题和描述
            title: spec.info?.title || formatServiceName(serviceDir),
            description: spec.info?.description || `${formatServiceName(serviceDir)} API 接口文档`,
            version: spec.info?.version || '1.0.0',
        };
    } catch (error) {
        console.warn(`⚠️ 无法解析 ${yamlPath}:`, error.message);
        return {
            title: formatServiceName(serviceDir),
            description: `${formatServiceName(serviceDir)} API 接口文档`,
            version: '1.0.0',
        };
    }
}

/**
 * 格式化服务名称 - 完全基于原始名称
 */
function formatServiceName(serviceDir) {
    // 直接基于原始文件夹名称格式化，不做任何截取
    return serviceDir
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ') + ' 服务';
}

/**
 * 获取服务显示名称 - 完全动态化
 */
function getServiceDisplayName(configId) {
    // 移除硬编码映射，改为基于 configId 的智能格式化
    return configId
        .replace(/([a-z])([A-Z])/g, '$1 $2') // 驼峰转空格
        .split(/[-_]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ') + ' 服务';
}

/**
 * 获取服务图标 - 智能匹配
 */
function getServiceIcon(configId) {
    // 移除硬编码映射，使用智能匹配
    if (configId.includes('design')) return '📐';
    if (configId.includes('model') || configId.includes('diy')) return '🏠';
    if (configId.includes('furniture')) return '🪑';
    if (configId.includes('user')) return '👤';
    if (configId.includes('auth')) return '🔐';
    if (configId.includes('payment')) return '💳';
    return '🔧'; // 默认图标
}

/**
 * 将连字符分隔的字符串转换为驼峰命名（用于 JavaScript 变量名）
 */
function toCamelCase(str) {
    return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * 生成侧边栏配置
 */
function generateSidebarConfig(openApiConfig) {
    const sidebarEntries = Object.keys(openApiConfig).map(configId => {
        // 将 configId 转换为驼峰命名用于 JavaScript 变量名
        const camelCaseId = toCamelCase(configId);

        return {
            type: 'docSidebar',
            sidebarId: `${camelCaseId}Sidebar`,
            label: `${getServiceIcon(configId)} ${getServiceDisplayName(configId)}`,
        };
    });

    return sidebarEntries;
}

/**
 * 主函数
 */
function main() {
    console.log('📋 生成 OpenAPI 配置...\n');

    const openApiConfig = generateOpenApiConfig();

    if (Object.keys(openApiConfig).length === 0) {
        console.error('❌ 没有找到任何 OpenAPI 规范文件');
        process.exit(1);
    }

    // 生成侧边栏条目
    const sidebarEntries = generateSidebarConfig(openApiConfig);

    // 输出配置对象
    const output = {
        openApiConfig,
        sidebarEntries,
        summary: {
            totalServices: Object.keys(openApiConfig).length,
            services: Object.keys(openApiConfig).map(id => ({
                id,
                name: getServiceDisplayName(id),
                path: openApiConfig[id].specPath
            }))
        }
    };

    // 写入到文件
    // 根据运行位置确定输出路径
    let outputPath;
    if (process.cwd().endsWith('docs-site') || process.cwd().endsWith('website')) {
        // 从 docs-site 或 website 目录运行，直接写入当前目录
        outputPath = path.join(process.cwd(), 'openapi-config.json');
    } else {
        // 从项目根目录运行，写入 documentation/website 目录
        outputPath = path.join(process.cwd(), 'documentation', 'website', 'openapi-config.json');
    }

    console.log('📁 输出路径:', outputPath);
    fs.writeFileSync(outputPath, JSON.stringify(output, null, 2));

    console.log('\n✅ 配置生成完成!');
    console.log('📄 配置文件:', outputPath);
    console.log('\n📊 服务统计:');
    for (const service of output.summary.services) {
        console.log(`  - ${service.name} (${service.id})`);
    }

    return output;
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = { generateOpenApiConfig, generateSidebarConfig };