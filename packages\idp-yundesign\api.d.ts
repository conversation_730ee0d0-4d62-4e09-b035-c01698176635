import { IDP as IDPCommon } from '@qunhe/idp-common';
import {
    Furniture,
    FurnitureCreateInfo,
    FurnitureGroup,
    FurnitureGroupCreateInfo,
    LeftPanelProductLibrary,
    ModelDoorWindow,
    ModelDoorWindowCreateInfo,
    ModelMolding,
    PromiseResult,
    StartDragProductPromiseResult,
    PromiseResultWithUuid
} from './type';

declare namespace IDP {
    namespace Interaction {
        /**
         * 拖拽创建设计对象到场景
         * @param productId 商品 ID
         */
        function startDragProductAsync(productId: string): Promise<StartDragProductPromiseResult>;

        /**
         * 打开公库下具体类目(不传或传入错误的treeId/categoryId，默认打开公库,并打开左侧栏)
         * @param categoryData.treeId 指定公库下类目树的treeId
         * @param categoryData.categoryId 指定公库下类目树下具体的类目Id
         * @internal
         */
        function openCategoryInPublicLibrary(categoryData?: { treeId: number, categoryId: number }): void
    }

    namespace DB {

        namespace Types {
            /**
             * 门窗类型定义，值和模型门窗中一致
             */
            enum ModelDoorWindowType {
                /** 普通窗 */
                Window = 1,
                /** 落地窗 */
                FrenchWindow,
                /** 飘窗 */
                BayWindow,
                /** 单开门 */
                Door,
                /** 双开门 */
                DoubleDoor,
                /** 移门 */
                SlidingDoor
            }
        }

        namespace Methods {
            /**
             * 直接创建软装家具
             * @param furnitureInfo 创建软装家具所需的信息
             */
            function createFurniture(furnitureInfo: FurnitureCreateInfo): Promise<PromiseResultWithUuid>;

            /**
             * 直接创建多件软装家具
             * @internal
             * @param furnitureInfoList 创建软装家具所需的信息列表
             * @returns 创建失败的家具会用 `undefined` 占位，保证索引稳定
             */
            function createFurnitureList(furnitureInfoList: FurnitureCreateInfo[]): Promise<Array<IDPCommon.DB.Types.ElementId | undefined>>;

            /**
             * 根据设计对象 ID 删除软装家具
             * @param elementId 设计对象 ID
             */
            function deleteFurniture(elementId: IDPCommon.DB.Types.ElementId): boolean;

            /**
             * 根据设计对象 ID 查询软装家具详细信息
             */
            function getFurniture(elementId: IDPCommon.DB.Types.ElementId): Furniture | undefined;

            /**
             * 直接创建软装组合家具
             * @param furnitureGroupInfo 创建软装组合家具所需的信息
             * @internal
             */
            function createFurnitureGroup(furnitureGroupInfo: FurnitureGroupCreateInfo): Promise<PromiseResultWithUuid>;

            /**
             * 根据设计对象 ID 删除软装组合家具
             * @param elementId 设计对象 ID
             * @internal
             */
            function deleteFurnitureGroup(elementId: IDPCommon.DB.Types.ElementId): boolean;

            /**
             * 查询所有软装家具详细信息
             * @internal
             */
            function getAllFurnitureList(): Furniture[];

            /**
             * 查询所有软装组合家具详细信息
             * @internal
             */
            function getAllFurnitureGroupList(): FurnitureGroup[];

            /**
             * 查询所有模型踢脚线详细信息
             * @internal
             */
            function getAllModelMoldingList(): ModelMolding[];

            /**
             * 根据商品 ID 获取对应的设计对象类型列表
             * @internal
             * @description 支持（组合）家具/模型门窗/模型踢脚线
             * @param productId 商品 ID
             * @returns 不支持的类型会返回空数组，非法输入则 reject
             */
            function getElementTypeByProductIdAsync(productId: string): Promise<IDPCommon.DB.Types.ElementType[]>;

            /**
             * 批量替换一种模型踢脚线商品
             * @internal
             * @param oldProductId 被替换的商品 ID
             * @param newProductId 替换后的商品 ID
             * @returns 如果全部替换成功正常 resolve，否则 reject（包括部分替换成功）
             */
            function replaceModelMoldingAsync(oldProductId: string, newProductId: string): Promise<void>;

            /**
             * 查询所有模型门窗详细信息
             */
            function getAllModelDoorWindowList(): ModelDoorWindow[];

            /**
             * 根据设计对象 ID 查询模型门窗详细信息
             * @param elementId 设计对象 ID
             */
            function getModelDoorWindow(elementId: IDPCommon.DB.Types.ElementId): ModelDoorWindow | undefined;

            /**
             * 根据设计对象 ID 删除模型门窗
             * @param elementId 设计对象 ID
             */
            function deleteModelDoorWindow(elementId: IDPCommon.DB.Types.ElementId): boolean;

            /**
             * 创建模型门窗
             * @param modelDoorWindowInfo 创建模型门窗所需信息
             */
            function createModelDoorWindowAsync(modelDoorWindowInfo: ModelDoorWindowCreateInfo): Promise<PromiseResult>;
        }
    }

    namespace UI {
        /**
         * 渲染左侧栏content到指定的容器中, title由业务方自己负责渲染和管理
         * title同级的功能模块会降级到content做适配
         * @see https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80834575038
         * @param containerId content容器的DOM ID
         * @param options.currentLibrary 指定当前展示库 包括素材库、企业库和个人库
         * @param options.rootContainerId 左侧栏容器的DOM ID，商品替换的唤起面板尺寸会根据容器适配
         * @internal
         */
        function renderLeftPanelProductResourcesContent(
            containerId: string, options: { currentLibrary: LeftPanelProductLibrary, rootContainerId?: string }): void;
        /**
         * 卸载左侧栏容器挂载
         * @internal
         */
        function unmountLeftPanelProductResourcesContent(): void;
    }

    namespace Platform {
        /**
         * 进入云图默认模式
         * @internal
         */
        function enterDefaultModeAsync(): Promise<void>;
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
