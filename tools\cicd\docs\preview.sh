#!/bin/bash

# 文档预览脚本
# 支持普通和优化模式的 Docusaurus 文档预览
# 合并自 preview-docusaurus.sh 和 preview-docusaurus-optimized.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 预览配置
PREVIEW_OUTPUT_DIR="${PREVIEW_OUTPUT_DIR:-public}"
NO_CHANGES_TEMPLATE="${NO_CHANGES_TEMPLATE:-generators/docs/templates/no-api-changes.html}"

# 检查是否需要完整构建
check_build_requirement() {
    log_step "1" "检查构建需求"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，将创建简单预览页面"
        return 1  # 不需要完整构建
    fi
    
    # 显示变更文件
    if [ -n "$CHANGED_API_FILES" ]; then
        log_info "📋 检测到以下 API 变更文件:"
        for file in $CHANGED_API_FILES; do
            if [[ "$file" =~ \.(yaml|yml)$ ]]; then
                echo "  - $file"
                increment_counter "changed_api_files"
            fi
        done
    else
        log_warning "变更文件列表为空"
    fi
    
    local changed_count=$(get_counter "changed_api_files")
    if [ "$changed_count" -gt 0 ]; then
        log_success "需要完整构建，发现 $changed_count 个 API 变更文件"
        return 0  # 需要完整构建
    else
        log_info "未发现 API 变更文件，将创建简单预览页面"
        return 1  # 不需要完整构建
    fi
}

# 创建简单预览页面
create_simple_preview() {
    log_step "2" "创建简单预览页面"
    
    # 创建输出目录
    if ! ensure_dir_exists "$PREVIEW_OUTPUT_DIR" "预览输出目录"; then
        return 1
    fi
    
    # 检查模板文件
    if [ -f "$NO_CHANGES_TEMPLATE" ]; then
        log_info "使用模板文件: $NO_CHANGES_TEMPLATE"
        if cp "$NO_CHANGES_TEMPLATE" "$PREVIEW_OUTPUT_DIR/index.html"; then
            log_success "简单预览页面已创建"
            increment_counter "simple_preview_created"
            return 0
        else
            log_error "复制模板文件失败"
            return 1
        fi
    else
        log_warning "模板文件不存在，创建默认预览页面"
        
        # 创建默认的简单预览页面
        cat > "$PREVIEW_OUTPUT_DIR/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 文档预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API 文档预览</h1>
            <p>Backend API SDK 项目</p>
        </div>
        
        <div class="status">
            <h3>✅ 构建状态</h3>
            <p>此次提交未检测到 API 变更，无需重新构建文档。</p>
        </div>
        
        <div class="info">
            <h3>📋 说明</h3>
            <p>当检测到 OpenAPI 规范文件变更时，系统会自动构建完整的 API 文档。</p>
            <p>如需查看完整文档，请访问主分支的文档站点。</p>
        </div>
        
        <div class="info">
            <h3>🔗 相关链接</h3>
            <ul>
                <li><a href="https://manual.qunhequnhe.com/manycoreapi/">生产环境文档</a></li>
                <li><a href="https://manual.qunhequnhe.com/manycoreapi-staging/">预发布环境文档</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF
        
        if [ $? -eq 0 ]; then
            log_success "默认预览页面已创建"
            increment_counter "simple_preview_created"
            return 0
        else
            log_error "创建默认预览页面失败"
            return 1
        fi
    fi
}

# 执行完整文档构建
build_full_preview() {
    local mode="${1:-normal}"
    
    log_step "2" "执行完整文档构建 ($mode)"
    
    # 调用文档构建脚本
    local build_script="$SCRIPT_DIR/build.sh"
    
    if [ ! -f "$build_script" ]; then
        log_error "文档构建脚本不存在: $build_script"
        return 1
    fi
    
    log_info "调用文档构建脚本..."
    if "$build_script" "$mode"; then
        log_success "文档构建完成"
        increment_counter "full_build_success"
        return 0
    else
        log_error "文档构建失败"
        increment_counter "full_build_failed"
        return 1
    fi
}

# 复制构建结果到预览目录
copy_build_to_preview() {
    log_step "3" "复制构建结果到预览目录"
    
    local docs_dir="${DOCS_DIR:-docs-site}"
    local build_dir="$docs_dir/build"
    
    # 检查构建输出
    if [ ! -d "$build_dir" ]; then
        log_error "构建输出目录不存在: $build_dir"
        return 1
    fi
    
    # 创建预览输出目录
    if ! ensure_dir_exists "$PREVIEW_OUTPUT_DIR" "预览输出目录"; then
        return 1
    fi
    
    # 清理旧的预览内容
    log_info "清理旧的预览内容..."
    rm -rf "$PREVIEW_OUTPUT_DIR"/*
    
    # 复制构建结果
    log_info "复制构建结果..."
    if cp -r "$build_dir"/* "$PREVIEW_OUTPUT_DIR"/; then
        local file_count=$(find "$PREVIEW_OUTPUT_DIR" -type f | wc -l)
        local dir_size=$(du -sh "$PREVIEW_OUTPUT_DIR" 2>/dev/null | cut -f1 || echo "Unknown")
        
        log_success "预览内容复制完成"
        log_info "预览文件: $file_count 个, 大小: $dir_size"
        increment_counter "preview_files" "$file_count"
        return 0
    else
        log_error "复制构建结果失败"
        return 1
    fi
}

# 验证预览结果
verify_preview_result() {
    log_step "4" "验证预览结果"
    
    if [ ! -d "$PREVIEW_OUTPUT_DIR" ]; then
        log_error "预览输出目录不存在: $PREVIEW_OUTPUT_DIR"
        return 1
    fi
    
    # 检查关键文件
    if [ ! -f "$PREVIEW_OUTPUT_DIR/index.html" ]; then
        log_error "预览页面不存在: $PREVIEW_OUTPUT_DIR/index.html"
        return 1
    fi
    
    # 检查文件大小
    local index_size=$(stat -f%z "$PREVIEW_OUTPUT_DIR/index.html" 2>/dev/null || stat -c%s "$PREVIEW_OUTPUT_DIR/index.html" 2>/dev/null || echo "0")
    if [ "$index_size" -eq 0 ]; then
        log_error "预览页面为空"
        return 1
    fi
    
    log_success "预览结果验证通过"
    log_info "预览页面大小: $index_size 字节"
    return 0
}

# 显示预览统计
show_preview_stats() {
    log_step "5" "显示预览统计"
    
    echo ""
    log_info "👀 文档预览结果统计:"
    
    local simple_created=$(get_counter "simple_preview_created")
    local full_success=$(get_counter "full_build_success")
    local full_failed=$(get_counter "full_build_failed")
    local preview_files=$(get_counter "preview_files")
    local changed_files=$(get_counter "changed_api_files")
    
    if [ "$simple_created" -gt 0 ]; then
        echo "  预览类型: 简单预览页面"
        echo "  原因: 无 API 变更"
    elif [ "$full_success" -gt 0 ]; then
        echo "  预览类型: 完整文档预览"
        echo "  变更文件: $changed_files 个"
        echo "  预览文件: $preview_files 个"
    else
        echo "  预览类型: 构建失败"
    fi
    
    echo "  预览时间: $(date)"
    
    if [ -d "$PREVIEW_OUTPUT_DIR" ]; then
        local output_size=$(du -sh "$PREVIEW_OUTPUT_DIR" 2>/dev/null | cut -f1 || echo "Unknown")
        echo "  输出大小: $output_size"
    fi
    
    show_stats "文档预览统计"
}

# 主函数
main() {
    local mode="${1:-normal}"  # normal, optimized
    
    init_script "文档预览" "构建 Docusaurus 文档预览"
    
    # 验证模式
    if [[ "$mode" != "normal" && "$mode" != "optimized" ]]; then
        log_error "无效的预览模式: $mode"
        log_info "支持的模式: normal, optimized"
        finish_script "文档预览" "false"
        exit 1
    fi
    
    log_info "预览模式: $mode"
    
    local preview_status=0
    
    # 检查是否需要完整构建
    if check_build_requirement; then
        # 需要完整构建
        log_info "执行完整文档构建流程..."
        
        if ! build_full_preview "$mode"; then
            preview_status=1
        elif ! copy_build_to_preview; then
            preview_status=1
        fi
    else
        # 创建简单预览页面
        log_info "创建简单预览页面..."
        
        if ! create_simple_preview; then
            preview_status=1
        fi
    fi
    
    # 验证预览结果
    if [ $preview_status -eq 0 ]; then
        if ! verify_preview_result; then
            preview_status=1
        fi
    fi
    
    show_preview_stats
    
    if [ $preview_status -eq 0 ]; then
        log_success "🎉 文档预览完成！"
        finish_script "文档预览" "true"
        return 0
    else
        log_error "❌ 文档预览失败，请检查错误信息"
        finish_script "文档预览" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "文档预览模块已加载 ✅"
