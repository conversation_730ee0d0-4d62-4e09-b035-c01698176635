import { BasicType, Type } from '@qunhe/kls-abstraction';
import { getPromiseType } from './src';

/**
 * 异步函数类型
 */
export const AsyncFunctionType = {
    name: 'AsyncFunctionType',
    varying: false,
    keepArgsHandle: true,
    args: [
        {
            type: BasicType.Unknown,
        },
        {
            type: BasicType.Unknown,
        },
        {
            type: BasicType.Unknown,
        }
    ],
    type: BasicType.Function,
    return: getPromiseType({
        type: BasicType.Unknown,
    }),
}

/**
 * 生成一个event 对象
 *
 * @param type
 */
export function generateEventType(type: Type<any>) {
    return {
        type: BasicType.Object,
        properties: {
            on: {
                name: 'on',
                varying: false,
                keepArgsHandle: true,
                args: [
                    {
                        name: '',
                        type: BasicType.Function,
                        args: [type],
                        varying: false,
                        keepArgsHandle: false,
                        return: {
                            type: BasicType.Undefined,
                        },
                    },
                ],
                type: BasicType.Function,
                return: {
                    name: '',
                    varying: false,
                    keepArgsHandle: false,
                    args: [],
                    type: BasicType.Function,
                    return: {
                        type: BasicType.Undefined,
                    },
                },
            },
            once: {
                name: 'once',
                varying: false,
                keepArgsHandle: true,
                args: [
                    {
                        name: '',
                        type: BasicType.Function,
                        args: [type],
                        varying: false,
                        keepArgsHandle: false,
                        return: {
                            type: BasicType.Undefined,
                        },
                    },
                ],
                type: BasicType.Function,
                return: {
                    name: '',
                    varying: false,
                    args: [],
                    keepArgsHandle: false,
                    type: BasicType.Function,
                    return: {
                        type: BasicType.Undefined,
                    },
                },
            },
        },
    };
}
