#!/bin/bash

# SDK 生成脚本
# 支持单个服务或所有服务的 SDK 生成
# 合并自 generate-sdks.sh 和 generate-sdks-all-services.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# OpenAPI Generator 配置
OPENAPI_GENERATOR_JAR="/opt/openapi-generator/openapi-generator-cli-7.12.0.jar"

# 验证工具版本
verify_tools() {
    log_step "1" "验证工具版本"
    
    log_info "检查 Java 版本..."
    if java -version >/dev/null 2>&1; then
        local java_version=$(java -version 2>&1 | head -1)
        log_success "Java: $java_version"
    else
        log_error "Java 不可用"
        return 1
    fi
    
    log_info "检查 Maven 版本..."
    if mvn -version >/dev/null 2>&1; then
        local maven_version=$(mvn -version | head -1)
        log_success "Maven: $maven_version"
    else
        log_error "Maven 不可用"
        return 1
    fi
    
    log_info "检查 OpenAPI Generator..."
    if [ -f "$OPENAPI_GENERATOR_JAR" ] && java -jar "$OPENAPI_GENERATOR_JAR" version >/dev/null 2>&1; then
        local generator_version=$(java -jar "$OPENAPI_GENERATOR_JAR" version | head -1)
        log_success "OpenAPI Generator: $generator_version"
    else
        log_error "OpenAPI Generator 不可用"
        return 1
    fi
    
    log_success "所有工具验证通过"
    return 0
}

# 生成单个服务的 SDK
generate_service_sdk() {
    local service="$1"
    local language="${2:-java}"
    
    log_info "🔧 生成 $service SDK ($language)..."
    
    # 验证服务
    if ! is_valid_service "$service"; then
        log_error "无效的服务名: $service"
        increment_counter "generation_failed"
        return 1
    fi
    
    # 获取文件路径
    local api_spec=$(get_service_openapi_path "$service")
    local config_file=$(get_service_config_path "$service" "$language")
    local output_dir=$(get_service_output_path "$service" "$language")
    
    # 检查必要文件
    if ! check_file_exists "$api_spec" "API 规范文件"; then
        increment_counter "generation_failed"
        return 1
    fi
    
    if ! check_file_exists "$config_file" "配置文件"; then
        increment_counter "generation_failed"
        return 1
    fi
    
    # 创建输出目录
    if ! ensure_dir_exists "$output_dir" "SDK 输出目录"; then
        increment_counter "generation_failed"
        return 1
    fi
    
    # 清理旧的输出
    log_info "清理旧的 SDK 输出..."
    rm -rf "$output_dir"/*
    
    # 生成 SDK
    log_info "执行 SDK 生成..."
    local start_time=$(get_timestamp)
    
    if java -jar "$OPENAPI_GENERATOR_JAR" generate \
        -i "$api_spec" \
        -g "$language" \
        -o "$output_dir" \
        --config "$config_file" >/dev/null 2>&1; then
        
        local end_time=$(get_timestamp)
        local duration=$((end_time - start_time))
        
        log_success "$service SDK 生成成功 (耗时: ${duration}s)"
        increment_counter "generation_success"
        
        # 验证生成的文件
        if [ -d "$output_dir" ] && [ "$(ls -A "$output_dir" 2>/dev/null)" ]; then
            log_debug "SDK 文件已生成到: $output_dir"
            return 0
        else
            log_error "$service SDK 生成后输出目录为空"
            increment_counter "generation_failed"
            return 1
        fi
    else
        log_error "$service SDK 生成失败"
        increment_counter "generation_failed"
        
        # 显示错误详情
        log_info "错误详情:"
        java -jar "$OPENAPI_GENERATOR_JAR" generate \
            -i "$api_spec" \
            -g "$language" \
            -o "$output_dir" \
            --config "$config_file" 2>&1 | head -10
        
        return 1
    fi
}

# 生成所有服务的 SDK
generate_all_sdks() {
    local language="${1:-java}"
    
    log_step "2" "生成所有服务的 SDK ($language)"
    
    local services=$(get_services)
    local overall_status=0
    
    if [ -z "$services" ]; then
        log_warning "未发现任何服务"
        return 0
    fi
    
    log_info "发现的服务: $services"
    
    for service in $services; do
        if generate_service_sdk "$service" "$language"; then
            log_success "服务 $service SDK 生成完成"
        else
            log_error "服务 $service SDK 生成失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 生成变更服务的 SDK
generate_changed_sdks() {
    local language="${1:-java}"
    
    log_step "2" "生成变更服务的 SDK ($language)"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过 SDK 生成"
        return 0
    fi
    
    if [ -z "$CHANGED_SERVICES" ]; then
        log_warning "变更服务列表为空，生成所有服务的 SDK"
        generate_all_sdks "$language"
        return $?
    fi
    
    log_info "变更的服务: $CHANGED_SERVICES"
    
    local overall_status=0
    
    for service in $CHANGED_SERVICES; do
        if generate_service_sdk "$service" "$language"; then
            log_success "变更服务 $service SDK 生成完成"
        else
            log_error "变更服务 $service SDK 生成失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 生成指定服务列表的 SDK
generate_service_list() {
    local service_list="$1"
    local language="${2:-java}"
    
    log_step "2" "生成指定服务的 SDK ($language)"
    
    if [ -z "$service_list" ]; then
        log_error "服务列表为空"
        return 1
    fi
    
    log_info "指定的服务: $service_list"
    
    local overall_status=0
    
    for service in $service_list; do
        if generate_service_sdk "$service" "$language"; then
            log_success "服务 $service SDK 生成完成"
        else
            log_error "服务 $service SDK 生成失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 显示生成统计
show_generation_stats() {
    log_step "3" "显示生成统计"
    
    echo ""
    log_info "🔧 SDK 生成结果统计:"
    
    local success=$(get_counter "generation_success")
    local failed=$(get_counter "generation_failed")
    local total=$((success + failed))
    
    echo "  总服务数: $total"
    echo "  生成成功: $success"
    echo "  生成失败: $failed"
    
    if [ $total -gt 0 ]; then
        local success_rate=$((success * 100 / total))
        echo "  成功率: $success_rate%"
    fi
    
    show_stats "SDK 生成统计"
}

# 主函数
main() {
    local mode="${1:-changed}"     # changed, all, service, list
    local target="${2:-}"          # 服务名或服务列表
    local language="${3:-java}"    # 编程语言
    
    init_script "SDK 生成" "生成 OpenAPI SDK"
    
    # 验证工具
    if ! verify_tools; then
        finish_script "SDK 生成" "false"
        exit 1
    fi
    
    local generation_status=0
    
    case "$mode" in
        "changed")
            log_info "生成模式: 变更服务"
            if ! generate_changed_sdks "$language"; then
                generation_status=1
            fi
            ;;
        "all")
            log_info "生成模式: 所有服务"
            if ! generate_all_sdks "$language"; then
                generation_status=1
            fi
            ;;
        "service")
            if [ -z "$target" ]; then
                log_error "服务生成模式需要指定服务名"
                finish_script "SDK 生成" "false"
                exit 1
            fi
            log_info "生成模式: 单个服务 ($target)"
            if ! generate_service_sdk "$target" "$language"; then
                generation_status=1
            fi
            ;;
        "list")
            if [ -z "$target" ]; then
                log_error "列表生成模式需要指定服务列表"
                finish_script "SDK 生成" "false"
                exit 1
            fi
            log_info "生成模式: 服务列表"
            if ! generate_service_list "$target" "$language"; then
                generation_status=1
            fi
            ;;
        *)
            log_error "无效的生成模式: $mode"
            log_info "支持的模式: changed, all, service, list"
            finish_script "SDK 生成" "false"
            exit 1
            ;;
    esac
    
    show_generation_stats
    
    if [ $generation_status -eq 0 ]; then
        log_success "🎉 SDK 生成完成！"
        finish_script "SDK 生成" "true"
        return 0
    else
        log_error "❌ SDK 生成失败，请检查错误信息"
        finish_script "SDK 生成" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "SDK 生成模块已加载 ✅"
