{"name": "@qunhe/idp-yunrender", "version": "1.70.0", "description": "api typings for idp-yunrender", "keywords": [], "author": "linze <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/idp-types.git"}, "scripts": {"build": "node ../../scripts/build-package/build-api", "build-package": "npm run build && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "devDependencies": {"copyfiles": "^2.4.1"}}