# CI/CD 脚本 API 文档

## 📋 概述

本文档描述了 CI/CD 脚本的接口规范、参数说明和返回值定义。

## 🔧 通用接口规范

### 脚本调用格式
```bash
./script-name.sh [参数1] [参数2] ...
```

### 返回值规范
- **0**: 成功
- **1**: 一般错误
- **2**: 参数错误
- **3**: 环境错误
- **4**: 依赖错误
- **5**: 网络错误

### 环境变量输出
脚本执行后会设置相关的环境变量，供后续脚本使用。

### 日志格式
```
[时间戳] [级别] [模块] 消息内容
```

## 🏗️ 核心模块 API

### environment.sh - 环境检查

#### 调用方式
```bash
./core/environment.sh
```

#### 功能描述
检查构建环境的完整性，验证必需的工具和依赖。

#### 检查项目
- Git 环境
- Docker 环境（可选）
- Node.js 环境
- Java 环境
- Maven 环境
- Python 环境（可选）

#### 环境变量输出
```bash
ENVIRONMENT_CHECK_PASSED=true/false    # 环境检查是否通过
MISSING_DEPENDENCIES="tool1 tool2"     # 缺失的依赖
DOCKER_AVAILABLE=true/false            # Docker 是否可用
NODE_VERSION="16.14.0"                 # Node.js 版本
JAVA_VERSION="11.0.12"                 # Java 版本
MAVEN_VERSION="3.8.4"                  # Maven 版本
```

#### 返回值
- **0**: 环境检查通过
- **3**: 环境检查失败

---

### api-changes.sh - API 变更检测

#### 调用方式
```bash
./core/api-changes.sh [base_branch]
```

#### 参数说明
- `base_branch`: 基准分支，默认为 `origin/main`

#### 功能描述
检测 OpenAPI 规范文件的变更，识别影响的服务和文件。

#### 环境变量输出
```bash
HAS_API_CHANGES=true/false             # 是否有 API 变更
CHANGED_API_FILES="file1.yaml file2.yaml"  # 变更的 API 文件列表
CHANGED_SERVICES="service1 service2"   # 变更的服务列表
API_CHANGE_COUNT=5                     # 变更文件数量
```

#### 返回值
- **0**: 检测完成
- **1**: 检测失败

---

### validation.sh - OpenAPI 验证

#### 调用方式
```bash
./core/validation.sh [模式] [文件路径]
```

#### 参数说明
- `模式`: 验证模式
  - `all`: 验证所有 OpenAPI 文件
  - `changed`: 只验证变更的文件
  - `file`: 验证指定文件
- `文件路径`: 当模式为 `file` 时指定的文件路径

#### 功能描述
验证 OpenAPI 规范文件的语法和语义正确性。

#### 环境变量输出
```bash
VALIDATION_PASSED=true/false           # 验证是否通过
VALIDATION_ERRORS="error1;error2"     # 验证错误列表
VALIDATED_FILES="file1.yaml file2.yaml"  # 已验证的文件
VALIDATION_COUNT=10                    # 验证文件数量
```

#### 返回值
- **0**: 验证通过
- **1**: 验证失败
- **2**: 参数错误

## 🔨 SDK 模块 API

### generate.sh - SDK 生成

#### 调用方式
```bash
./sdk/generate.sh [语言] [模式]
```

#### 参数说明
- `语言`: 目标语言
  - `all`: 所有支持的语言
  - `java`: Java SDK
  - `typescript`: TypeScript SDK
  - `python`: Python SDK
  - `go`: Go SDK
- `模式`: 生成模式
  - `all`: 生成所有服务的 SDK
  - `changed`: 只生成变更服务的 SDK

#### 功能描述
基于 OpenAPI 规范生成各种语言的 SDK。

#### 环境变量输出
```bash
SDK_GENERATION_SUCCESS=true/false     # SDK 生成是否成功
GENERATED_LANGUAGES="java typescript" # 已生成的语言列表
GENERATED_SERVICES="service1 service2" # 已生成的服务列表
SDK_OUTPUT_DIR="/path/to/output"      # SDK 输出目录
```

#### 返回值
- **0**: 生成成功
- **1**: 生成失败
- **2**: 参数错误

---

### build.sh - SDK 构建

#### 调用方式
```bash
./sdk/build.sh [语言] [模式]
```

#### 参数说明
- `语言`: 目标语言（同 generate.sh）
- `模式`: 构建模式（同 generate.sh）

#### 功能描述
编译和打包生成的 SDK。

#### 环境变量输出
```bash
SDK_BUILD_SUCCESS=true/false          # SDK 构建是否成功
BUILT_LANGUAGES="java typescript"     # 已构建的语言列表
BUILD_ARTIFACTS="path1 path2"         # 构建产物路径
BUILD_DURATION=120                    # 构建耗时（秒）
```

#### 返回值
- **0**: 构建成功
- **1**: 构建失败
- **2**: 参数错误

---

### test.sh - SDK 测试

#### 调用方式
```bash
./sdk/test.sh [语言] [模式]
```

#### 参数说明
- `语言`: 目标语言（同 generate.sh）
- `模式`: 测试模式（同 generate.sh）

#### 功能描述
执行 SDK 的单元测试和集成测试。

#### 环境变量输出
```bash
SDK_TEST_SUCCESS=true/false           # SDK 测试是否成功
TESTED_LANGUAGES="java typescript"    # 已测试的语言列表
TEST_RESULTS="passed:10,failed:0"     # 测试结果统计
TEST_DURATION=60                      # 测试耗时（秒）
```

#### 返回值
- **0**: 测试通过
- **1**: 测试失败
- **2**: 参数错误

---

### deploy.sh - SDK 部署

#### 调用方式
```bash
./sdk/deploy.sh [语言] [部署类型]
```

#### 参数说明
- `语言`: 目标语言（同 generate.sh）
- `部署类型`: 部署类型
  - `snapshots`: 快照版本
  - `releases`: 正式版本

#### 功能描述
将 SDK 发布到相应的包管理仓库。

#### 环境变量输出
```bash
SDK_DEPLOY_SUCCESS=true/false         # SDK 部署是否成功
DEPLOYED_LANGUAGES="java typescript"  # 已部署的语言列表
DEPLOY_URLS="url1 url2"               # 部署地址列表
DEPLOY_VERSIONS="1.0.0 1.0.0"        # 部署版本列表
```

#### 返回值
- **0**: 部署成功
- **1**: 部署失败
- **2**: 参数错误

## 📚 文档模块 API

### build.sh - 文档构建

#### 调用方式
```bash
./docs/build.sh [模式]
```

#### 参数说明
- `模式`: 构建模式
  - `normal`: 普通构建
  - `optimized`: 优化构建（包含 API 文档生成）

#### 功能描述
构建 Docusaurus 文档网站。

#### 环境变量输出
```bash
DOCS_BUILD_SUCCESS=true/false         # 文档构建是否成功
DOCS_BUILD_MODE="optimized"           # 构建模式
DOCS_OUTPUT_DIR="/path/to/build"      # 构建输出目录
BUILD_DURATION=180                    # 构建耗时（秒）
```

#### 返回值
- **0**: 构建成功
- **1**: 构建失败
- **2**: 参数错误

---

### preview.sh - 文档预览

#### 调用方式
```bash
./docs/preview.sh [模式]
```

#### 参数说明
- `模式`: 预览模式（同 build.sh）

#### 功能描述
启动本地文档预览服务器。

#### 环境变量输出
```bash
DOCS_PREVIEW_SUCCESS=true/false       # 文档预览是否成功
PREVIEW_URL="http://localhost:3000"   # 预览地址
PREVIEW_PORT=3000                     # 预览端口
```

#### 返回值
- **0**: 预览启动成功
- **1**: 预览启动失败
- **2**: 参数错误

---

### deploy.sh - 文档部署

#### 调用方式
```bash
./docs/deploy.sh
```

#### 功能描述
将文档部署到群核科技 Manual 网站。

#### 环境变量输入
```bash
MANUAL_ENV=dev                        # 部署环境
MANUAL_VERSION=1.0.0                  # 部署版本
KJL_USERNAME=username                 # kjl 用户名
KJL_PASSWORD=password                 # kjl 密码
```

#### 环境变量输出
```bash
DOCS_DEPLOY_SUCCESS=true/false        # 文档部署是否成功
DEPLOY_URL="https://manual.qunhequnhe.com/..." # 部署地址
DEPLOY_VERSION="1.0.0"                # 部署版本
DEPLOY_ENVIRONMENT="dev"              # 部署环境
```

#### 返回值
- **0**: 部署成功
- **1**: 部署失败
- **4**: 认证失败

## 🔄 工作流模块 API

### mr-pipeline.sh - MR 流水线

#### 调用方式
```bash
./workflows/mr-pipeline.sh
```

#### 功能描述
执行完整的 MR 评审流水线。

#### 环境变量输入
```bash
SKIP_DOCS=false                       # 是否跳过文档生成
SKIP_SDKS=false                       # 是否跳过 SDK 处理
PIPELINE_TIMEOUT=3600                 # 流水线超时时间
```

#### 环境变量输出
```bash
MR_PIPELINE_SUCCESS=true/false        # MR 流水线是否成功
PIPELINE_DURATION=300                 # 流水线耗时（秒）
PIPELINE_STEPS_COMPLETED=6            # 完成的步骤数
```

#### 返回值
- **0**: 流水线成功
- **1**: 流水线失败

---

### local-review.sh - 本地评审

#### 调用方式
```bash
./workflows/local-review.sh
```

#### 功能描述
本地模拟 MR 评审流程。

#### 环境变量输入
```bash
USE_DOCKER=true                       # 是否使用 Docker
CLEANUP_ON_EXIT=true                  # 退出时是否清理
DOCKER_IMAGE="registry-qunhe02..."    # Docker 镜像
```

#### 环境变量输出
```bash
LOCAL_REVIEW_SUCCESS=true/false       # 本地评审是否成功
REVIEW_DURATION=180                   # 评审耗时（秒）
DOCKER_USED=true/false                # 是否使用了 Docker
```

#### 返回值
- **0**: 评审成功
- **1**: 评审失败

---

### main-pipeline.sh - 主分支流水线

#### 调用方式
```bash
./workflows/main-pipeline.sh
```

#### 功能描述
执行主分支的完整 CI/CD 流程。

#### 环境变量输入
```bash
DEPLOY_ENVIRONMENT=staging            # 部署环境
SKIP_TESTS=false                      # 是否跳过测试
SKIP_DEPLOYMENT=false                 # 是否跳过部署
PIPELINE_TIMEOUT=7200                 # 流水线超时时间
```

#### 环境变量输出
```bash
MAIN_PIPELINE_SUCCESS=true/false      # 主流水线是否成功
PIPELINE_DURATION=600                 # 流水线耗时（秒）
DEPLOYED_ENVIRONMENT="staging"        # 部署环境
```

#### 返回值
- **0**: 流水线成功
- **1**: 流水线失败

## 🛠️ 工具模块 API

### common.sh - 通用函数库

#### 主要函数

##### log_info(message)
输出信息级别日志。

##### log_error(message)
输出错误级别日志。

##### log_success(message)
输出成功级别日志。

##### log_warning(message)
输出警告级别日志。

##### increment_counter(name)
增加计数器。

##### get_counter(name)
获取计数器值。

##### get_timestamp()
获取当前时间戳。

##### init_script(name, description)
初始化脚本。

##### finish_script(name, success)
结束脚本。

---

### gitlab-api.sh - GitLab API 工具

#### 主要函数

##### add_ci_status_comment(message)
添加 CI 状态评论到 MR。

##### generate_manual_preview_url()
生成 Manual 预览链接。

##### get_mr_info()
获取 MR 信息。

---

### docker-utils.sh - Docker 工具

#### 主要函数

##### check_docker_available()
检查 Docker 是否可用。

##### pull_docker_image(image)
拉取 Docker 镜像。

##### run_in_docker(image, command)
在 Docker 容器中执行命令。

## 📞 支持

如果您对 API 有疑问或需要帮助，请联系 Backend API Team。
