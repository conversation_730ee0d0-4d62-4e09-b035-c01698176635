{"name": "@qunhe/idp-math", "version": "1.70.0", "description": "> TODO: description", "maintainers": ["da<PERSON>o"], "author": {"name": "da<PERSON>o", "email": "<EMAIL>"}, "homepage": "", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/IDP.Math.git"}, "scripts": {"build": "node ../../scripts/build-package/build-api", "build-package": "npm run build && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "dependencies": {"@qunhe/math-apaas-api": "^3.0.2"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "devDependencies": {"copyfiles": "^2.4.1"}}