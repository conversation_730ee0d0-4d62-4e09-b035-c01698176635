declare namespace IDP {
    // 支付相关接口
    namespace Payment {
        /**
         * 支付状态
         */
        enum PaymentStatus {
            /**
             * 待支付
             */
            UNPAID = 'UNPAID',
            /**
             * 已支付
             */
            PAID = 'PAID',
            /**
             * 支付失败
             */
            FAILED = 'FAILED'
        }
        namespace Types {
            /**
             * 支付结果
             */
            interface PaymentResult {
                /**
                 * 酷家乐订单编号
                 */
                orderNo?: string;
                /**
                 * 支付状态
                 */
                status: PaymentStatus;
                /**
                 * 订单自动关闭时间,（Unix时间戳，单位：毫秒）
                 */
                orderAutoCloseTime?: number;
                /**
                 * 错误信息
                 */
                errorMessage?: string;
            }
            /**
             * 支付弹窗参数
             */
            interface PaymentDialogProps {
                /**
                 * ISV订单号
                 */
                isvOrderNo: string;
                /**
                 * ISV订单信息
                 */
                isvOrderInfo: {
                    /**
                     * SKU编码
                     */
                    skuCode: string;
                    /**
                     * 购买数量
                     */
                    num?: number;
                    /**
                     * 购买时长
                     */
                    duration?: {
                        year?: number;
                        month?: number;
                        day?: number;
                    };
                };
            }
        }

        /**
         * 唤起支付弹窗
         * @param props 支付弹窗参数
         * @description 调用此方法会弹出支付界面，当弹窗打开失败时会触发 `IDP.Payment.PaymentDialogClosed` 事件，并返回错误信息。
         *
         */
        function showPaymentDialog(
            props: IDP.Payment.Types.PaymentDialogProps
        ): void;
    }

    interface EventTypes {
        /**
         * 支付弹窗关闭时触发的事件，会将支付结果作为回调函数参数进行传递
         */
        'IDP.Payment.PaymentDialogClosed': IDP.Payment.Types.PaymentResult;
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
