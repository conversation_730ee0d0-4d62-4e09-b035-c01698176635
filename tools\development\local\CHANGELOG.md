# 本地 SDK 管理工具变更日志

## 版本 2.0.0 (2024-06-27)

### 🚀 重大改进

#### 动态服务发现
- **替换硬编码服务列表**：脚本不再写死 `doorwindow` 和 `furniture` 服务
- **自动扫描服务目录**：自动发现 `openapi/` 目录下的所有有效服务
- **智能过滤**：自动跳过 `common` 和 `shared` 等公共目录
- **验证机制**：只有包含 `restapi.yaml` 和 `config-java.yaml` 的目录才被识别为服务

#### 灵活的配置系统
- **模板化环境变量**：使用 `{SERVICE_NAME}_RESTAPI` 等模式动态生成环境变量
- **向后兼容**：保持对现有 CI/CD 脚本的兼容性
- **动态 Docker 环境**：根据发现的服务自动构建 Docker 环境变量

### 🔧 技术改进

#### 脚本架构重构
- **模块化设计**：将功能拆分为独立的函数
- **智能回退**：优先使用现有 CI/CD 脚本，不存在时自动遍历服务
- **错误处理**：完善的错误检查和友好的错误提示

#### 新增工具脚本
1. **`fix-openapi-issue.sh`** - 修复 OpenAPI 规范文件问题
2. **`debug-openapi.sh`** - OpenAPI 规范验证和调试
3. **`demo-add-service.sh`** - 新服务添加演示

### 🐛 问题修复

#### OpenAPI 规范修复
- **ApiError 类型转换错误**：修复了 `LinkedHashMap` 转换为 `List` 的问题
- **移除有问题的 nullable 字段**：简化 schema 定义避免生成器兼容性问题
- **优化引用结构**：改进内部引用格式，确保生成器正确解析

#### 脚本兼容性
- **干运行模式优化**：在预览模式下跳过 Docker 检查
- **路径处理改进**：更好的跨平台路径处理
- **环境变量构建**：动态构建环境变量，支持任意数量服务

### 📝 文档更新

#### README 完善
- **动态服务说明**：更新服务列表显示方式
- **故障排除指南**：新增 OpenAPI 问题解决方案
- **使用示例**：添加新服务创建示例

#### 配置文档
- **环境变量模板**：更新配置文件说明服务发现机制
- **最佳实践**：添加服务管理的最佳实践指南

### 🎯 使用影响

#### 之前（v1.x）
```bash
# 只支持固定服务
./scripts/local/quick-sdk.sh gen doorwindow
./scripts/local/quick-sdk.sh gen furniture
```

#### 现在（v2.0）
```bash
# 支持任意服务（只要符合规范）
./scripts/local/quick-sdk.sh gen doorwindow
./scripts/local/quick-sdk.sh gen furniture
./scripts/local/quick-sdk.sh gen any-new-service  # 自动支持

# 查看当前支持的服务
./scripts/local/local-sdk-manager.sh --help

# 修复常见问题
./scripts/local/fix-openapi-issue.sh
```

## 兼容性说明

### 保持兼容
- ✅ 现有的 CI/CD 脚本继续正常工作
- ✅ 原有的命令行参数和选项不变
- ✅ 输出目录结构保持一致
- ✅ Docker 镜像和环境变量向后兼容

### 新增功能
- 🆕 自动服务发现
- 🆕 OpenAPI 问题修复工具
- 🆕 调试和验证工具
- 🆕 交互式演示

## 升级指南

### 对于现有用户
1. 无需任何操作，所有现有命令继续工作
2. 如果遇到 OpenAPI 生成错误，运行：
   ```bash
   ./scripts/local/fix-openapi-issue.sh
   ```

### 对于新服务开发
1. 在 `openapi/` 目录下创建服务目录
2. 添加 `restapi.yaml` 和 `config-java.yaml` 文件
3. 脚本会自动识别并支持新服务

## 已知问题

- ❗ 需要 Docker 环境才能运行实际的 SDK 生成
- ❗ OpenAPI Generator 7.12.0 对某些复杂 schema 可能有兼容性问题
- ❗ Maven 部署需要正确的仓库访问权限配置

## 下一步计划

- 🔮 支持更多生成器类型（TypeScript、Python 等）
- 🔮 添加缓存机制，提高生成速度
- 🔮 集成自动化测试和质量检查
- 🔮 提供 Web UI 界面 