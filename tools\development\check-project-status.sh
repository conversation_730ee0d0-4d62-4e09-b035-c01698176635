#!/bin/bash

# API 评审流程项目状态检查脚本

echo "🔍 API 评审流程项目状态检查..."
echo ""

# 检查必要的文件和目录
check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        echo "✅ $description: $file"
        return 0
    else
        echo "❌ $description: $file (缺失)"
        return 1
    fi
}

check_dir() {
    local dir=$1
    local description=$2
    
    if [ -d "$dir" ]; then
        echo "✅ $description: $dir"
        return 0
    else
        echo "❌ $description: $dir (缺失)"
        return 1
    fi
}

# 核心配置文件检查
echo "📋 核心配置文件检查:"
check_file ".gitlab-ci.yml" "GitLab CI/CD 配置"
check_file ".spectral.yml" "Spectral 规范校验配置"

echo ""

# 文档检查
echo "📚 文档检查:"
check_dir "docs" "文档目录"
check_file "docs/api-review-process.md" "详细评审流程文档"
check_file "docs/quick-start-guide.md" "快速开始指南"
check_file "README.md" "项目说明文档"

echo ""

# GitLab 模板检查
echo "🏷️ GitLab 模板检查:"
check_dir ".gitlab" "GitLab 配置目录"
check_file ".gitlab/merge_request_templates/api_review.md" "MR 模板"

echo ""

# 脚本检查
echo "🔧 脚本检查:"
check_dir "scripts" "脚本目录"
check_file "scripts/setup-local-env.sh" "本地环境设置脚本"
check_file "scripts/pre-commit-hook.sh" "Pre-commit Hook 脚本"
check_file "scripts/demo-api-change.sh" "演示脚本"

echo ""

# OpenAPI 文件检查
echo "📄 OpenAPI 规范文件检查:"
check_dir "openapi" "OpenAPI 规范目录"
check_file "openapi/diymodeldw-service/restapi.yaml" "diymodeldw 服务 API 规范"
check_file "openapi/diymodeldw-service/config-java.yaml" "diymodeldw 服务 Java 配置"
check_file "openapi/furniture-design-service/restapi.yaml" "furniture-design 服务 API 规范"
check_file "openapi/furniture-design-service/config-java.yaml" "furniture-design 服务 Java 配置"

echo ""

# CI/CD 流水线结构检查
echo "🔄 CI/CD 流水线结构检查:"
if [ -f ".gitlab-ci.yml" ]; then
    echo "  🔍 检查 GitLab CI 配置..."
    
    # 检查关键 stages
    if grep -q "install_tools_mr" .gitlab-ci.yml; then
        echo "  ✅ MR 工具安装 stage"
    else
        echo "  ❌ MR 工具安装 stage 缺失"
    fi
    
    if grep -q "validate_spec_mr" .gitlab-ci.yml; then
        echo "  ✅ 规范校验 stage"
    else
        echo "  ❌ 规范校验 stage 缺失"
    fi
    
    if grep -q "generate_docs_mr" .gitlab-ci.yml; then
        echo "  ✅ 文档生成 stage"
    else
        echo "  ❌ 文档生成 stage 缺失"
    fi
    
    if grep -q "review_gate_mr" .gitlab-ci.yml; then
        echo "  ✅ 评审检查点 stage"
    else
        echo "  ❌ 评审检查点 stage 缺失"
    fi
    
    if grep -q "generate_sdk" .gitlab-ci.yml; then
        echo "  ✅ SDK 生成 stage"
    else
        echo "  ❌ SDK 生成 stage 缺失"
    fi
else
    echo "  ❌ .gitlab-ci.yml 文件不存在"
fi

echo ""

# 工具依赖检查
echo "🛠️ 工具依赖检查:"
check_tool() {
    local tool=$1
    local description=$2
    
    if command -v "$tool" &> /dev/null; then
        local version=$($tool --version 2>&1 | head -1)
        echo "  ✅ $description: $version"
        return 0
    else
        echo "  ❌ $description: 未安装"
        return 1
    fi
}

if command -v node &> /dev/null; then
    echo "  ✅ Node.js: $(node --version)"
    
    if command -v npm &> /dev/null; then
        echo "  ✅ npm: $(npm --version)"
    else
        echo "  ❌ npm: 未安装"
    fi
else
    echo "  ❌ Node.js: 未安装"
fi

check_tool "spectral" "Spectral CLI"
if command -v redoc-cli &> /dev/null; then
    echo "  ✅ Redoc CLI (默认): $(redoc-cli --version 2>/dev/null || echo 'version unknown')"
elif command -v elements &> /dev/null; then
    echo "  ✅ Elements CLI (社区版): $(elements --version 2>/dev/null || echo 'version unknown')"
else
    echo "  ❌ 文档生成工具: 未安装"
    echo "  💡 建议安装: npm install -g redoc-cli"
fi
check_tool "java" "Java"
check_tool "mvn" "Maven"

echo ""

# Git 配置检查
echo "🔗 Git 配置检查:"
if [ -d ".git" ]; then
    echo "  ✅ Git 仓库已初始化"
    
    if [ -f ".git/hooks/pre-commit" ]; then
        echo "  ✅ Pre-commit hook 已设置"
    else
        echo "  ⚠️  Pre-commit hook 未设置 (可选)"
    fi
else
    echo "  ❌ 不是 Git 仓库"
fi

echo ""

# 建议和下一步
echo "💡 建议和下一步操作:"
echo ""

if ! command -v spectral &> /dev/null || ! command -v redoc-cli &> /dev/null; then
    echo "  📦 运行设置脚本安装必要工具:"
    echo "     bash scripts/setup-local-env-final.sh"
    echo ""
    echo "  📖 或仅安装文档生成工具:"
    echo "     bash scripts/install-docs-tools.sh"
    echo ""
fi

echo "  🧪 测试评审流程:"
echo "     bash scripts/demo-api-change.sh"
echo ""

echo "  📚 查看文档:"
echo "     - docs/quick-start-guide.md (快速开始)"
echo "     - docs/api-review-process.md (详细流程)"
echo ""

echo "  🚀 开始使用:"
echo "     1. 创建特性分支"
echo "     2. 编辑 OpenAPI 规范文件"
echo "     3. 创建 MR 并添加评审标签"
echo "     4. 等待 CI 校验和文档生成"
echo "     5. 等待 API 小组评审"
echo ""

echo "🎉 项目状态检查完成！" 