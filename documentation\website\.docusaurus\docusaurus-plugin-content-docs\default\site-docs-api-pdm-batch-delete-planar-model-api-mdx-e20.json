{"id": "api/pdm/batch-delete-planar-model", "title": "批量删除平面造型数据", "description": "根据平面造型ID批量删除平面造型数据", "source": "@site/docs/api/pdm/batch-delete-planar-model.api.mdx", "sourceDirName": "api/pdm", "slug": "/api/pdm/batch-delete-planar-model", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-delete-planar-model", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "frontMatter": {"id": "batch-delete-planar-model", "title": "批量删除平面造型数据", "description": "根据平面造型ID批量删除平面造型数据", "sidebar_label": "批量删除平面造型数据", "hide_title": true, "hide_table_of_contents": true, "api": {"tags": ["平面造型设计管理接口"], "description": "根据平面造型ID批量删除平面造型数据", "operationId": "batchDeletePlanarModel", "parameters": [{"name": "designId", "in": "path", "description": "设计ID", "required": true, "schema": {"type": "string"}}, {"name": "levelId", "in": "path", "description": "楼层ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "批量删除平面造型数据请求体", "content": {"application/json": {"schema": {"type": "object", "properties": {"requestId": {"type": "string"}, "operationId": {"type": "string"}, "batchRequests": {"type": "array", "items": {"required": ["planarModelId"], "type": "object", "properties": {"planarModelId": {"type": "string", "description": "平面造型id", "example": "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"}}, "description": "平面造型id", "title": "PlanarModelId"}}}, "description": "批量删除平面造型请求体", "title": "BatchDeletePlanarModelRequest"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"elements": {"type": "array", "items": {"required": ["planarModelId"], "type": "object", "properties": {"planarModelId": {"type": "string", "description": "平面造型id", "example": "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"}}, "description": "平面造型id", "title": "PlanarModelId"}}}, "description": "批量删除平面造型数据响应体", "title": "BatchDeletePlanarModelResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "title": "ApiError", "description": "REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范", "required": ["code", "message", "status", "details"], "properties": {"code": {"type": "integer", "description": "HTTP 状态码（数字），例如 403、404、500等", "example": 403, "minimum": 100, "maximum": 599}, "message": {"type": "string", "description": "面向开发者的错误消息，应该使用英文", "example": "Permission denied", "minLength": 1, "maxLength": 1000}, "status": {"description": "RestAPI 对应的状态码枚举值", "type": "string", "title": "Code", "enum": ["OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "PARTIAL_ELEMENT_UPDATE_FAILED"], "x-module": "error", "x-category": "错误处理"}, "details": {"description": "错误的详细信息", "type": "object", "title": "ErrorDetails", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "错误原因，标识错误的直接原因。格式为大写蛇形命名", "example": "INVALID_REQUEST_FORMAT", "pattern": "^[A-Z][A-Z0-9_]*[A-Z0-9]$", "maxLength": 63}, "message": {"type": "string", "description": "针对此错误发生的人类可读的解释说明", "example": "请求参数格式不正确，缺少必需的字段 'name'", "maxLength": 500}, "domain": {"type": "string", "description": "错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称", "example": "deco.manycoreapis.com", "maxLength": 100}, "metaData": {"type": "object", "description": "关于此错误的附加结构化详细信息，键名限制为64个字符", "additionalProperties": {"type": "string", "maxLength": 200}, "example": {"maxAllowedSize": "1000", "actualSize": "1500"}}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "localizedMessage": {"description": "错误的本地化消息，可选字段", "type": "object", "title": "LocalizedMessage", "required": ["locale", "message"], "properties": {"locale": {"type": "string", "description": "消息所使用的语言环境", "example": "zh-CN", "pattern": "^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"}, "message": {"type": "string", "description": "本地化的消息内容", "example": "权限不足，无法访问该资源", "minLength": 1, "maxLength": 1000}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "help": {"description": "错误的帮助信息，可选字段", "type": "object", "title": "Help", "required": ["desc", "url"], "properties": {"desc": {"type": "string", "description": "链接描述，说明该链接提供的帮助内容", "example": "查看 API 使用指南", "minLength": 1, "maxLength": 200}, "url": {"type": "string", "description": "帮助链接的 URL 地址", "example": "https://docs.example.com/api-guide", "format": "uri", "maxLength": 500}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "title": "ApiError", "description": "REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范", "required": ["code", "message", "status", "details"], "properties": {"code": {"type": "integer", "description": "HTTP 状态码（数字），例如 403、404、500等", "example": 403, "minimum": 100, "maximum": 599}, "message": {"type": "string", "description": "面向开发者的错误消息，应该使用英文", "example": "Permission denied", "minLength": 1, "maxLength": 1000}, "status": {"description": "RestAPI 对应的状态码枚举值", "type": "string", "title": "Code", "enum": ["OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "PARTIAL_ELEMENT_UPDATE_FAILED"], "x-module": "error", "x-category": "错误处理"}, "details": {"description": "错误的详细信息", "type": "object", "title": "ErrorDetails", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "错误原因，标识错误的直接原因。格式为大写蛇形命名", "example": "INVALID_REQUEST_FORMAT", "pattern": "^[A-Z][A-Z0-9_]*[A-Z0-9]$", "maxLength": 63}, "message": {"type": "string", "description": "针对此错误发生的人类可读的解释说明", "example": "请求参数格式不正确，缺少必需的字段 'name'", "maxLength": 500}, "domain": {"type": "string", "description": "错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称", "example": "deco.manycoreapis.com", "maxLength": 100}, "metaData": {"type": "object", "description": "关于此错误的附加结构化详细信息，键名限制为64个字符", "additionalProperties": {"type": "string", "maxLength": 200}, "example": {"maxAllowedSize": "1000", "actualSize": "1500"}}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "localizedMessage": {"description": "错误的本地化消息，可选字段", "type": "object", "title": "LocalizedMessage", "required": ["locale", "message"], "properties": {"locale": {"type": "string", "description": "消息所使用的语言环境", "example": "zh-CN", "pattern": "^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"}, "message": {"type": "string", "description": "本地化的消息内容", "example": "权限不足，无法访问该资源", "minLength": 1, "maxLength": 1000}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "help": {"description": "错误的帮助信息，可选字段", "type": "object", "title": "Help", "required": ["desc", "url"], "properties": {"desc": {"type": "string", "description": "链接描述，说明该链接提供的帮助内容", "example": "查看 API 使用指南", "minLength": 1, "maxLength": 200}, "url": {"type": "string", "description": "帮助链接的 URL 地址", "example": "https://docs.example.com/api-guide", "format": "uri", "maxLength": 500}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}}}}, "x-return-extra": {"is-operation": "true"}, "extensions": [{"key": "x-return-extra", "value": {"is-operation": "true"}}], "method": "post", "path": "/deco/api/v2/rest/designs/{designId}/levels/{levelId}/planarmodel:batchdelete", "servers": [{"url": "http://localhost:8083", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "jsonRequestBodyExample": {"requestId": "string", "operationId": "string", "batchRequests": [{"planarModelId": "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"}]}, "info": {"title": "平面造型设计API", "description": "平面造型设计REST API\n\n    ## 功能特性\n    - 平面造型的 CRUD 操作\n    - 批量操作支持（创建、更新、删除、查询）\n    - 分页查询支持\n    \n    ## 数据模型\n    - **平面造型**: 包含平面造型所在的面、平面造型形状信息\n    \n    ## 分页机制\n    使用基于页码的分页，支持自定义页面大小", "contact": {"name": "群核科技开发团队", "url": "https://wiki.manycore.com/furniture-design", "email": "<EMAIL>"}, "license": {"name": "群核科技专有许可证", "url": "https://manycore.com/license"}, "version": "1.0.0"}, "postman": {"name": "批量删除平面造型数据", "description": {"content": "根据平面造型ID批量删除平面造型数据", "type": "text/plain"}, "url": {"path": ["deco", "api", "v2", "rest", "designs", ":designId", "levels", ":levelId", "planarmodel:batchdelete"], "host": ["{{baseUrl}}"], "query": [], "variable": [{"disabled": false, "description": {"content": "(Required) 设计ID", "type": "text/plain"}, "type": "any", "value": "", "key": "designId"}, {"disabled": false, "description": {"content": "(Required) 楼层ID", "type": "text/plain"}, "type": "any", "value": "", "key": "levelId"}]}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}}}, "sidebar_class_name": "post api-method", "info_path": "docs/api/pdm/平面造型设计api", "custom_edit_url": null}, "sidebar": "pdmSidebar", "previous": {"title": "保存单个平面造型数据", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/save-planar-model-v-2"}, "next": {"title": "批量保存平面造型数据", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-save-planar-model-v-2"}}