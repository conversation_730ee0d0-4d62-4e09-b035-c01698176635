import * as path from 'path';
import * as ts from 'typescript';
import * as _ from 'lodash';

export function follow<PERSON>lias(symbol: ts.Symbol, typeChecker: ts.TypeChecker): ts.Symbol {
    while (symbol.flags & ts.SymbolFlags.Alias) {
        symbol = typeChecker.getAliasedSymbol(symbol);
    }
    return symbol;
}

export function cwdRelativePath(toPath: string) {
    return path.relative(process.cwd(), toPath);
}

export function toModuleReference(fromFile: string, toFile: string) {
    return path.relative(path.dirname(fromFile), toFile)
        .replace(/\\/g, '/')
        .replace(/\.d\.ts$/, '')
        .replace(/(\/)?index$/, '')
        .replace(/^([^\.])/, './$1');
}

export function isUnderDir(dir: string, filePath: string) {
    return path.resolve(filePath).startsWith(path.resolve(dir));
}

export function isTypescriptEmbededFile(filePath: string) {
    return /node_modules[\/\\]typescript[\/\\]/.test(filePath);
}

export function ensureGetSymbol(node: ts.Node, typeChecker: ts.TypeChecker) {
    const symbol = typeChecker.getSymbolAtLocation(node);
    if (!symbol) {
        throw new Error(`找不到 symbol 定义: ${node.getText()}`);
    }
    return symbol;
}

export function ensureType<T>(value: T): void {
    //
}

export function getFilePosition(node: ts.Node) {
    const sourceFile = node.getSourceFile();
    const { line, character } = sourceFile.getLineAndCharacterOfPosition(node.getStart());
    return `${sourceFile.fileName}:${line + 1}:${character + 1}`;
}

export function getNodeDesc(node: ts.Node) {
    return `${getFilePosition(node)}.\nDeclaration: ${ts.SyntaxKind[node.kind]}: ${node.getText()}`
}

export function throwIfHasCompilerErrors(program: ts.Program) {
    const allDiagnostics = _.flatten(
        [
            program.getGlobalDiagnostics(),
            program.getConfigFileParsingDiagnostics(),
            // (program as any).getFileProcessingDiagnostics().getDiagnostics() as ts.Diagnostic[],
        ].concat(program.getSourceFiles().map(file => {
            return program.getSemanticDiagnostics(file)
                .concat(program.getSyntacticDiagnostics(file))
                .concat(program.getDeclarationDiagnostics(file));
        }))
    );

    if (allDiagnostics.length) {
        const messages = allDiagnostics.map(diagnostic => {
            const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
            if (diagnostic.file && diagnostic.start) {
                const { line, character } = diagnostic.file.getLineAndCharacterOfPosition(diagnostic.start);
                return `  Error ${diagnostic.file.fileName}:${line + 1}:${character + 1}: ${message}`;
            } else {
                return `  Error: ${message}`;
            }
        });
        throw new Error(`Typing check failed.\n${messages.filter(m => !!m).join('\n')}`);
    }
}
