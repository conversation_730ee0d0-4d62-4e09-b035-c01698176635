{"name": "backend-api-docs", "version": "1.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "build:optimized": "./scripts/build-optimized.sh", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc", "gen-api-docs": "docusaurus gen-api-docs", "clean-api-docs": "docusaurus clean-api-docs"}, "dependencies": {"@docusaurus/core": "^3.8.0", "@docusaurus/module-type-aliases": "^3.8.0", "@docusaurus/preset-classic": "^3.8.0", "@docusaurus/tsconfig": "^3.8.0", "@docusaurus/types": "^3.8.0", "clsx": "^2.1.1", "docusaurus-plugin-openapi-docs": "^4.0.0", "docusaurus-theme-openapi-docs": "^4.0.0", "js-yaml": "^4.1.0", "prism-react-renderer": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "swc-loader": "^0.2.6"}, "devDependencies": {"@docusaurus/tsconfig": "^3.8.0", "@types/js-yaml": "^4.0.9", "cross-env": "^10.0.0", "typescript": "~5.5.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}