#!/bin/bash

# 文档预览流程脚本
# 专门用于生成和预览 API 文档，供评委审查使用
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 设置项目根目录
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
cd "$PROJECT_ROOT" || {
    log_error "无法切换到项目根目录: $PROJECT_ROOT"
    exit 1
}

# 文档预览配置
DOCKER_IMAGE="${DOCKER_IMAGE:-registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest}"
USE_DOCKER="${USE_DOCKER:-true}"
CLEANUP_ON_EXIT="${CLEANUP_ON_EXIT:-true}"

# 设置模拟 CI 环境变量
setup_mock_ci_environment() {
    log_step "1" "设置文档预览环境"
    
    # 基本 CI 环境变量
    export CI=true
    export CI_COMMIT_REF_NAME="docs-preview"
    export CI_COMMIT_SHORT_SHA="$(git rev-parse --short HEAD 2>/dev/null || echo 'localsha')"
    export CI_PROJECT_DIR="$(pwd)"
    
    # 获取当前分支
    local current_branch=$(git branch --show-current 2>/dev/null || echo 'docs-preview')
    export CI_COMMIT_BRANCH="$current_branch"
    
    log_info "文档预览环境变量:"
    echo "  分支: $CI_COMMIT_BRANCH"
    echo "  提交: $CI_COMMIT_SHORT_SHA"
    echo "  项目目录: $CI_PROJECT_DIR"
    
    return 0
}

# 检查 Docker 环境
check_docker_environment() {
    log_step "2" "检查 Docker 环境"
    
    if [ "$USE_DOCKER" != "true" ]; then
        log_info "跳过 Docker 环境检查 (USE_DOCKER=false)"
        return 0
    fi
    
    # 检查 Docker 是否可用
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 不可用，请安装 Docker"
        return 1
    fi
    
    # 检查 Docker 服务是否运行
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker"
        return 1
    fi
    
    # 检查 Docker 镜像
    log_info "检查 Docker 镜像: $DOCKER_IMAGE"
    if ! docker image inspect "$DOCKER_IMAGE" >/dev/null 2>&1; then
        log_info "拉取 Docker 镜像..."
        if ! docker pull "$DOCKER_IMAGE"; then
            log_error "无法拉取 Docker 镜像: $DOCKER_IMAGE"
            return 1
        fi
    fi
    
    log_success "Docker 环境检查通过"
    increment_counter "docs_docker_check_success"
    return 0
}

# 执行 API 变更检测
run_api_detection() {
    log_step "3" "API 变更检测"

    local api_changes_script="$SCRIPT_DIR/../core/api-changes.sh"
    
    if [ ! -f "$api_changes_script" ]; then
        log_error "API 变更检测脚本不存在: $api_changes_script"
        increment_counter "docs_api_detection_failed"
        return 1
    fi
    
    log_info "执行 API 变更检测..."
    
    # 执行 API 变更检测脚本并捕获输出
    local output
    if output=$("$api_changes_script" 2>&1); then
        log_success "API 变更检测完成"
        
        # 从输出中提取环境变量
        local has_changes=$(echo "$output" | grep "有 API 变更:" | sed 's/.*有 API 变更: //')
        local changed_files=$(echo "$output" | grep "变更文件: specifications" | sed 's/.*变更文件: //')
        local changed_services=$(echo "$output" | grep "变更服务:" | sed 's/.*变更服务: //')
        
        # 设置环境变量
        export HAS_API_CHANGES="${has_changes:-false}"
        export CHANGED_API_FILES="${changed_files:-}"
        export CHANGED_SERVICES="${changed_services:-}"
        
        # 显示检测结果
        log_info "API 变更检测结果:"
        echo "  HAS_API_CHANGES: ${HAS_API_CHANGES}"
        echo "  CHANGED_API_FILES: ${CHANGED_API_FILES:-'(无)'}"
        echo "  CHANGED_SERVICES: ${CHANGED_SERVICES:-'(无)'}"
        
        increment_counter "docs_api_detection_success"
        return 0
    else
        log_error "API 变更检测失败"
        log_error "错误信息: $output"
        increment_counter "docs_api_detection_failed"
        return 1
    fi
}

# 执行 OpenAPI 验证
run_openapi_validation() {
    log_step "4" "OpenAPI 验证"

    if [ "$USE_DOCKER" = "true" ]; then
        log_info "在 Docker 容器中执行 OpenAPI 验证..."
        
        # 在 Docker 容器中运行 OpenAPI 验证
        local docker_cmd="docker run --rm"
        docker_cmd="$docker_cmd -v $(pwd):/workspace"
        docker_cmd="$docker_cmd -w /workspace"
        docker_cmd="$docker_cmd -e HAS_API_CHANGES=${HAS_API_CHANGES:-false}"
        docker_cmd="$docker_cmd -e CHANGED_API_FILES='$CHANGED_API_FILES'"
        docker_cmd="$docker_cmd -e CHANGED_SERVICES='$CHANGED_SERVICES'"
        docker_cmd="$docker_cmd $DOCKER_IMAGE"
        docker_cmd="$docker_cmd bash -c 'tools/cicd/core/validation.sh changed'"
        
        if eval "$docker_cmd"; then
            log_success "OpenAPI 验证通过"
            increment_counter "docs_validation_success"
            return 0
        else
            log_error "OpenAPI 验证失败"
            increment_counter "docs_validation_failed"
            return 1
        fi
    else
        # 本地环境验证
        local validation_script="$SCRIPT_DIR/../core/validation.sh"
        
        if [ ! -f "$validation_script" ]; then
            log_error "OpenAPI 验证脚本不存在: $validation_script"
            return 1
        fi
        
        log_info "执行本地 OpenAPI 验证..."
        if "$validation_script" "changed"; then
            log_success "OpenAPI 验证通过"
            increment_counter "docs_validation_success"
            return 0
        else
            log_error "OpenAPI 验证失败"
            increment_counter "docs_validation_failed"
            return 1
        fi
    fi
}

# 生成文档
generate_documentation() {
    log_step "5" "生成文档"

    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过文档生成"
        increment_counter "docs_generation_skipped"
        return 0
    fi

    local docs_build_script="$SCRIPT_DIR/../docs/build.sh"

    if [ ! -f "$docs_build_script" ]; then
        log_error "文档构建脚本不存在: $docs_build_script"
        increment_counter "docs_generation_failed"
        return 1
    fi

    log_info "生成 API 文档..."

    # 设置正确的文档目录
    export DOCS_DIR="documentation/website"

    if "$docs_build_script"; then
        log_success "文档生成完成"
        increment_counter "docs_generation_success"
        return 0
    else
        log_error "文档生成失败"
        increment_counter "docs_generation_failed"
        return 1
    fi
}

# 启动文档预览
start_docs_preview() {
    log_step "6" "启动文档预览"

    local docs_preview_script="$SCRIPT_DIR/../docs/preview.sh"

    if [ ! -f "$docs_preview_script" ]; then
        log_error "文档预览脚本不存在: $docs_preview_script"
        increment_counter "docs_preview_failed"
        return 1
    fi

    log_info "启动文档预览服务..."

    # 设置正确的文档目录
    export DOCS_DIR="documentation/website"

    if "$docs_preview_script"; then
        log_success "文档预览启动成功"
        increment_counter "docs_preview_success"

        # 显示预览信息
        log_info "📖 文档预览信息:"
        echo "  本地预览地址: http://localhost:3000"
        echo "  变更服务: ${CHANGED_SERVICES:-'(无)'}"
        echo "  变更文件数: $(echo "$CHANGED_API_FILES" | wc -w)"

        return 0
    else
        log_error "文档预览启动失败"
        increment_counter "docs_preview_failed"
        return 1
    fi
}

# 显示文档预览统计
show_docs_stats() {
    log_step "7" "显示文档预览统计"
    
    echo ""
    log_info "📊 文档预览流程统计:"
    
    local docker_check=$(get_counter "docs_docker_check_success")
    local api_detection=$(get_counter "docs_api_detection_success")
    local validation=$(get_counter "docs_validation_success")
    local generation=$(get_counter "docs_generation_success")
    local preview=$(get_counter "docs_preview_success")
    
    echo "  Docker 环境检查: $([ "$docker_check" -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  API 变更检测: $([ "$api_detection" -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  OpenAPI 验证: $([ "$validation" -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  文档生成: $([ "$generation" -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    echo "  文档预览: $([ "$preview" -gt 0 ] && echo "✅ 启动" || echo "❌ 失败")"
    
    if [ "${HAS_API_CHANGES:-false}" = "true" ]; then
        echo "  变更服务: ${CHANGED_SERVICES:-'(无)'}"
        echo "  变更文件数: $(echo "$CHANGED_API_FILES" | wc -w)"
    fi
    
    show_stats "文档预览统计"
}

# 清理资源
cleanup_resources() {
    if [ "$CLEANUP_ON_EXIT" = "true" ]; then
        log_info "清理资源..."
        # 这里可以添加清理逻辑，比如停止预览服务等
    fi
}

# 主函数
main() {
    init_script "文档预览流程" "1.0.0"
    
    # 设置清理陷阱
    trap cleanup_resources EXIT
    
    log_info "🚀 开始文档预览流程"
    echo ""
    log_info "流程步骤:"
    log_info "1. 设置文档预览环境"
    log_info "2. 检查 Docker 环境"
    log_info "3. API 变更检测"
    log_info "4. OpenAPI 验证"
    log_info "5. 生成文档"
    log_info "6. 启动文档预览"
    log_info "7. 显示统计信息"
    echo ""
    
    # 执行流程步骤
    setup_mock_ci_environment || exit 1
    check_docker_environment || exit 1
    run_api_detection || exit 1
    run_openapi_validation || exit 1
    generate_documentation || exit 1
    start_docs_preview || exit 1
    show_docs_stats
    
    # 检查整体结果
    local validation_success=$(get_counter "docs_validation_success")
    local preview_success=$(get_counter "docs_preview_success")
    
    if [ "$validation_success" -gt 0 ] && [ "$preview_success" -gt 0 ]; then
        log_success "🎉 文档预览流程执行成功！"
        finish_script 0
    else
        log_error "❌ 文档预览流程执行失败，请检查错误信息"
        finish_script 1
    fi
}

# 执行主函数
main "$@"
