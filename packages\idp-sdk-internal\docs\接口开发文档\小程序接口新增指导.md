## 1. 新增小程序接口 package

> 在 [idp-types 仓库](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/idp-types) 中，已经提供了新增子 package 的模板，集成了相应的流程。
>
> 使用方式：在仓库下执行 `node scripts/create-package {YOUR_PACKAGE_NAME}`。
>
> 以下文档描述了实际的流程内容，运行模板创建脚本后不必再执行以下流程。

#### a. 新建小程序接口和实现 package

用于提供小程序接口相关内容；最终会以 package 粒度集成到 idp-sdk 中提供完整接口集合，分为接口和实现两个 package:

- 接口 package (`@qunhe/idp-foo`): 仅包含接口类型定义
- 实现 package (`@qunhe/idp-foo-impl`): 包含 vm type、变量值定义等具体实现

小程序 package 应包括三部分，三份内容应维持信息一致：

1. **小程序接口的 ts 全局扩展类型声明**。用于集成到 idp-sdk 中，提供对外（小程序开发者）的接口类型声明。示例：
    ```ts
    declare global {
        namespace IDP {
            namespace Foo {
                function func(): void;
            }
        }
    }
    ```
    > 注：此部分当前会由构建工具自动生成。
1. **小程序接口的 ts 类型声明**。用于该接口提供者实现时，以此类型去约束其提供的 value 对象的类型正确。示例：
    ```ts
    export declare namespace IDP {
        namespace Foo {
            function func(): void;
        }
    }
    ```
1. **小程序接口的 vm 绑定类型**。用于该接口提供者实现时，提供 value 对象映射到 vm 的类型描述。编写方式参考 [VM Type 文档](./VM_Type/README.md)。示例：
    ```ts
    import { ObjectType } from '@qunhe/kls-abstraction';
    import { ObjectTypeBuilder } from '@qunhe/kls-runtime';
    export function getVMBindingType(): ObjectType {
        return new ObjectTypeBuilder()
            .addFunction('func', fn => {})
            .build();
    };
    ```
    > 注：此部分当前默认会由构建工具自动生成，但如果语法特殊，需要手动提供。

如果还存在仅二方可见的接口，需要额外提供以上接口内容的针对二方的版本。

#### b. 集成接口类型声明

在 [@qunhe/idk-sdk/include_packages.json](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/idp-types/-/blob/master/packages/idp-sdk/include_packages.json) 及 [@qunhe/idp-sdk-internal/include_packages.json](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/idp-types/-/blob/master/packages/idp-sdk-internal/include_packages.json) 中，新增该小程序 API package 引用，其中后者是针对二方版本接口。之后构建工具会在应用实现中，提供相应的小程序接口实现。

## 2. 新增小程序接口定义

1. 在 `@qunhe/idp-foo` package 中的 `api.d.ts` 文件中，新增小程序接口的 dts 类型定义
2. （必要时）在 `@qunhe/idp-foo-impl` package 中，新增小程序接口的 vm type 定义
   > 大部分情况下可以通过工具自动生成 vm type，少部分需要自行定义。

## 3. 提供接口实现

示例：
```ts
import { getVMBindingType, getVMBindingTypeInternal } from '@qunhe/idp-my-api-pkg-impl';
import { getVMBindingTypeForNamespace } from '@qunhe/idp-vmbinding-utils';

kafAppService.miniappManager.registerApi(info => {
    return {
         namespace: 'MY_NAMESPACE',
         value: MY_API_OBJECT,
         type: getVMBindingTypeForNamespace(info.internal ? getVMBindingTypeInternal() : getVMBindingType(), 'IDP', 'MY_NAMESPACE'),
    }
});
```

> 注：`info.internal` 为 `true`，表示该小程序为二方小程序，应当允许其使用内部接口

### 3.1 异步注册接口

**总是应当使用异步注册**，以支持懒加载。

示例：
```ts
import { getVMBindingType, getVMBindingTypeInternal } from '@qunhe/idp-my-api-pkg-impl';
import { getVMBindingTypeForNamespace } from '@qunhe/idp-vmbinding-utils';

kafAppService.miniappManager.addDynamicLoadedRegister(async () => {
    const MY_API_OBJECT = await import('my-api-module');
    kafAppService.miniappManager.registerApi(info => {
        return {
            namespace: 'MY_NAMESPACE',
            value: MY_API_OBJECT,
            type: getVMBindingTypeForNamespace(info.internal ? getVMBindingTypeInternal() : getVMBindingType(), 'IDP', 'MY_NAMESPACE'),
        };
    });
});
```
