const fs = require('fs-extra');

const package = JSON.parse(fs.readFileSync('./package.json', 'utf-8'));
const newPackage = {};
[
    'name',
    'version',
    'description',
    'homepage',
    'license'
].forEach(key => {
    newPackage[key] = package[key];
})

const cleanFiles = [
    'api.json'
];

newPackage.maintainers = ['kujiale'];

if (process.env['PUBLIC_SCOPE']) {
    newPackage.name = newPackage.name.replace('qunhe', process.env['PUBLIC_SCOPE']);
}


try {
    fs.statSync('./build');
} catch {
    fs.mkdirSync('./build');
}

fs.writeFileSync('./build/package.json', JSON.stringify(newPackage, null, 2));
let changelog = fs.readFileSync('./CHANGELOG.md', 'utf-8');

if (process.env['PUBLIC_SCOPE']) {
    changelog = changelog.replace(/qunhe/g, process.env['PUBLIC_SCOPE']);
}

fs.writeFileSync('./build/CHANGELOG.md', changelog);

let readme = fs.readFileSync('./README.md', 'utf-8');

if (process.env['PUBLIC_SCOPE']) {
    readme = readme.replace(/qunhe/g, process.env['PUBLIC_SCOPE']);
}

fs.writeFileSync('./build/README.md', readme)

for (const file of cleanFiles) {
    fs.removeSync(`./build/${file}`);
}

