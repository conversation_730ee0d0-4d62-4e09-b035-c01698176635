openapi: 3.1.0
info:
  title: Camera Infrastructure API
  version: 0.0.1
  description: |
    相机基础设施 REST API
    
    为群核旗下多个设计工具提供统一的相机查询和管理功能，支持多种相机类型的预设，
    帮助用户在设计过程中快速切换和保存不同的观察角度。
    
    **支持的设计工具：**
    详见 `ToolAppType` 枚举定义
    
    **支持的功能：**
    - 根据不同设计工具的标识和参数查询相机
    - 支持漫游视图、全景图、3D鸟瞰视图
    - 相机参数配置管理
    - 跨工具的相机数据统一格式
    
    **技术规范：**
    - 响应格式：JSON
    - 字符编码：UTF-8
    - 认证方式：待定
  contact:
    name: 设计服务开发团队
    email: <EMAIL>
    url: https://wiki.qunhe.com/design-api
  license:
    name: Proprietary
    url: https://qunhe.com/license
servers:
  - url: 'http://localhost:8083'
    description: 本地开发环境
  - url: 'https://api-dev.qunhe.com'
    description: 开发测试环境
  - url: 'https://api.qunhe.com'
    description: 生产环境
tags:
  - name: Camera Infrastructure
    description: |
      相机基础设施接口
      
      提供统一的相机查询功能，支持多个设计工具的不同参数需求。
      每个相机包含完整的相机参数配置，可用于渲染引擎恢复用户保存的观察状态。
      
      **支持的相机类型：**
      - `normal`: 漫游视图 - 用于室内漫游浏览
      - `panorama`: 全景图 - 360度全景查看
      - `view3d`: 3D鸟瞰视图 - 整体空间俯视
      
      **参数需求：**
      - **酷家乐 (kujiale)**: 需要 designId + levelId
      
        

paths:
  '/camera/openapi/v1/apps/{app}/cameras':
    get:
      tags:
        - Camera Infrastructure
      summary: 获取相机列表
      description: |
        根据指定的设计工具和相应参数获取相机列表。
        
        **酷家乐 (kujiale)**：
        - 路径参数：app=kujiale
        - 必需查询参数：designId, levelId
        - 可选查询参数：type
        

      operationId: getCameras
      parameters:
        - name: app
          in: path
          description: |
            设计工具标识符，用于区分不同的群核设计工具
          required: true
          schema:
            $ref: '#/components/schemas/ToolAppType'
        - name: type
          in: query
          description: |
            相机类型过滤器，如果不指定则返回所有类型的相机
            - `normal`: 漫游视图
            - `panorama`: 全景图
            - `view3d`: 3D鸟瞰视图
          required: false
          schema:
            type: string
            enum:
              - normal
              - panorama
              - view3d
          example: normal
        # 酷家乐和酷家乐商业版参数
        - name: designId
          in: query
          description: |
            设计ID，用于标识具体的设计方案
            **适用工具**: kujiale
            **使用条件**: 当app为kujiale时必需
          required: false
          schema:
            type: string
          example: 3FONSDIEWO34
        - name: levelId
          in: query
          description: |
            楼层ID，用于标识设计中的具体楼层
            **适用工具**: kujiale
            **使用条件**: 当app为kujiale时必需
          required: false
          schema:
            type: string
          example: M5HPLGIKTJME2AABAAAAADQ8


      responses:
        '200':
          description: 成功获取相机列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RestApiCameraListResponse'
              examples:
                kujialeExample:
                  summary: 酷家乐相机列表示例
                  value:
                    elements:
                      - id: "kj_camera_001"
                        type: "NORMAL"
                        imgUrl: "https://example.com/preview/kj_camera_001.jpg"
                        source: "DESIGN_PAGE"
                        app: "kujiale"
                        cameraParameter:
                          position:
                            x: 1.5
                            y: 1.8
                            z: 2.0
                          lookAt:
                            x: 0.0
                            y: 0.0
                            z: 0.0
                          hfov: 60.0
                          near: 0.1
                        createdTime: 1640995200000

        '400':
          description: |
            请求参数错误，可能的原因：
            - 缺少必需的查询参数
            - 设计工具标识符不支持
            - 参数组合无效
          content:
            application/json:
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
              examples:
                unsupportedAppType:
                  summary: 不支持的设计工具类型
                  value:
                    code: 400
                    message: "Unsupported app type"
                    status: "INVALID_ARGUMENT"
                    details:
                      reason: "UNSUPPORTED_APP_TYPE"
                      message: "指定的设计工具类型不支持"
                      domain: "camera.restapi.qunhe.com"
                      metaData:
                        providedApp: "unknown_app"
                        supportedApps: "kujiale"
                    localizedMessage:
                      locale: "zh-CN"
                      message: "不支持的设计工具类型"
                invalidParameterCombination:
                  summary: 参数组合无效
                  value:
                    code: 400
                    message: "Invalid parameter combination for app"
                    status: "INVALID_ARGUMENT"
                    details:
                      reason: "INVALID_PARAMETER_COMBINATION"
                      message: "酷家乐需要designId和levelId参数"
                      domain: "camera.restapi.qunhe.com"
                      metaData:
                        app: "kujiale"
                        requiredParameters: "designId,levelId"
                    localizedMessage:
                      locale: "zh-CN"
                      message: "参数组合与指定的设计工具不匹配"
        '403':
          description: 无权限访问资源，请确认用户具有相应的读取权限
          content:
            application/json:
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
              examples:
                permissionDenied:
                  summary: 权限拒绝示例
                  value:
                    code: 403
                    message: "Permission denied"
                    status: "PERMISSION_DENIED"
                    details:
                      reason: "INSUFFICIENT_PERMISSIONS"
                      message: "用户没有访问该资源的权限"
                      domain: "camera.restapi.qunhe.com"
                      metaData:
                        app: "kujiale"
                        resourceId: "3FONSDIEWO34"
                        requiredPermission: "design:read"
                    localizedMessage:
                      locale: "zh-CN"
                      message: "权限不足，无法访问该资源"
        '404':
          description: 资源不存在，请检查提供的参数是否正确
          content:
            application/json:
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
              examples:
                resourceNotFound:
                  summary: 资源不存在示例
                  value:
                    code: 404
                    message: "Resource not found"
                    status: "NOT_FOUND"
                    details:
                      reason: "RESOURCE_NOT_FOUND"
                      message: "指定的资源不存在"
                      domain: "camera.restapi.qunhe.com"
                      metaData:
                        app: "kujiale"
                        designId: "3FONSDIEWO34"
                        levelId: "M5HPLGIKTJME2AABAAAAADQ8"
                    localizedMessage:
                      locale: "zh-CN"
                      message: "未找到指定的资源"
        '500':
          description: 服务器内部错误，请稍后重试或联系技术支持
          content:
            application/json:
              schema:
                $ref: '../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError'
              examples:
                internalError:
                  summary: 内部服务器错误示例
                  value:
                    code: 500
                    message: "Internal server error"
                    status: "INTERNAL"
                    details:
                      reason: "SERVICE_UNAVAILABLE"
                      message: "相机服务暂时不可用"
                      domain: "camera.restapi.qunhe.com"
                      metaData:
                        errorId: "ERR_CAMERA_001"
                        timestamp: "2024-01-01T12:00:00Z"
                    localizedMessage:
                      locale: "zh-CN"
                      message: "服务器内部错误，请稍后重试"
                    help:
                      desc: "联系技术支持"
                      url: "https://support.qunhe.com"
        
components:
  schemas:
    ToolAppType:
      type: string
      description: |
        设计工具标识符枚举，定义群核旗下支持的设计工具类型
        - `kujiale`: 酷家乐 - 家装设计平台
      enum:
        - kujiale
      example: kujiale
        
    Point3d:
      type: object
      required:
        - x
        - 'y'
        - z
      properties:
        x:
          type: number
          format: double
        'y':
          type: number
          format: double
        z:
          type: number
          format: double
      description: 3D空间点坐标，用于定义相机位置和朝向目标点
    
    RestApiCameraParameter:
      title: CameraParameter
      required:
        - position
        - lookAt
        - hfov
        - near
      type: object
      properties:
        position:
          $ref: '#/components/schemas/Point3d'
          description: 相机在3D空间中的位置坐标
        lookAt:
          $ref: '#/components/schemas/Point3d'
          description: 相机朝向目标点，相机镜头指向的3D空间位置
        hfov:
          maximum: 179
          minimum: 1
          type: number
          description: 相机水平视场角，单位：度，控制相机的视野宽度
          format: double
          example: 60
        near:
          minimum: 0.01
          type: number
          description: 近裁剪面距离，相机能够渲染的最近距离
          format: double
          example: 0.1
      description: 相机参数配置，包含完整的3D相机渲染所需参数
    
    RestApiCamera:
      title: Camera
      required:
        - id
        - type
        - imgUrl
        - source
        - app
        - cameraParameter
      type: object
      properties:
        id:
          type: string
          description: 相机唯一标识符
          example: camera_001
        imgUrl:
          type: string
          description: 预览图片URL地址，用于在列表中显示相机的缩略图
          example: 'https://example.com/preview/camera_001.jpg'
        source:
          title: CameraSource
          type: string
          description: 相机来源，标识相机创建的上下文环境
          enum:
            - CAMERA_SCENE_UNSPECIFIED
            - RENDER_PAGE
            - ALBUM
            - DESIGN_PAGE
        type:
          title: CameraType
          type: string
          description: 相机类型，定义视角的观察模式
          enum:
            - CAMERA_TYPE_UNSPECIFIED
            - NORMAL
            - PANORAMA
            - VIEW_3D
        app:
          $ref: '#/components/schemas/ToolAppType'
          description: 设计工具标识符，标识该相机来源的设计工具
        cameraParameter:
          $ref: '#/components/schemas/RestApiCameraParameter'
        createdTime:
          type: integer
          description: 创建时间戳，Unix时间戳格式，单位为毫秒
          format: int64
          example: 1640995200000
        # 可选的工具特定元数据
        metadata:
          type: object
          description: 工具特定的元数据信息
          additionalProperties: true
          example:
            designId: "3FONSDIEWO34"
            levelId: "M5HPLGIKTJME2AABAAAAADQ8"
            toolVersion: "v2.1.0"
      description: 相机信息，包含完整的相机参数和元数据
      
    RestApiCameraListResponse:
      title: CameraListResponse
      required:
        - elements
      type: object
      properties:
        elements:
          type: array
          description: 相机列表，包含指定条件下的所有相机信息
          items:
            $ref: '#/components/schemas/RestApiCamera'
        pagination:
          type: object
          description: 分页信息（预留字段，当前版本可能为空）
          properties:
            total:
              type: integer
              description: 总记录数
            page:
              type: integer
              description: 当前页码
            pageSize:
              type: integer
              description: 每页大小
      description: 相机列表响应对象，支持跨设计工具的统一格式返回
