import { once } from "lodash";
import { createVMBindingType } from "./dist/vmTypePublic";
import { createVMBindingType as createVMBindingTypeInternal } from "./dist/vmTypeInternal";

export const getVMBindingType = once(() => {
    // TODO: modify your vm type creator params here
    return createVMBindingType();
});
export const getVMBindingTypeInternal = once(() => {
    // TODO: modify your internal vm type creator params here
    return createVMBindingTypeInternal();
});

/**
 * 支付状态
 */
export enum PaymentStatus {
    /**
     * 待支付
     */
    UNPAID = "UNPAID",
    /**
     * 已支付
     */
    PAID = "PAID",
    /**
     * 支付失败
     */
    FAILED = "FAILED"
}
