///////////////////////////////
// 生成项目中的编译输出文件，以及最终的 api-review 文件

const child_process = require('child_process');
const path = require('path');
const process = require('process');
const ts = require('typescript');
const fse = require('fs-extra');

function removeTsComments(dtsFile, outputFile) {
    const sourceFile = ts.createSourceFile(
        dtsFile,
        fse.readFileSync(dtsFile, 'utf-8'),
        ts.ScriptTarget.ES2015
    );


    const printer = ts.createPrinter({
        removeComments: true,
    });
    const newContent = printer.printFile(sourceFile);
    fse.outputFileSync(outputFile, newContent);
}

const echoAndExec = (cmd, { cwd = process.cwd(), env = undefined } = {}) => {
    console.log(`$ ${cmd}`);
    child_process.execSync(cmd, { stdio: 'inherit', cwd, env: env ? { ...process.env, ...env } : undefined, });
};

function buildApiReview(idpSdkDir, isInternal) {
    echoAndExec(`yarn build --output-api-json --no-build-global-decl`, {
        cwd: idpSdkDir,
        env: isInternal ? { API_MODEL_TRIMMING: 'internal' } : {},
    });
    console.log('$ remove ts comments...');
    removeTsComments(path.resolve(idpSdkDir, 'build/index.d.ts'), path.resolve(idpSdkDir, 'etc/api-review.d.ts'));
}

process.chdir(path.resolve(__dirname, '../..'));
console.log(`$ 编译各个 package...`);
echoAndExec(`npx lerna run build-package`);

console.log(`$ 编译 idp-sdk api-review...`);
buildApiReview(path.resolve(__dirname, '../../packages/idp-sdk'), false);
console.log(`$ 编译 idp-sdk-internal api-review...`);
buildApiReview(path.resolve(__dirname, '../../packages/idp-sdk-internal'), true);

const argv = require('yargs').argv;
if (argv['auto-commit']) {
    // 自动提交变更
    console.log(`# 提交变更到仓库...`);
    child_process.execSync('git add packages');
    if (child_process.execSync('git diff --name-only --cached').toString().trim()) {
        const gitUtils = require('@qunhe/tools-script/utils/gitUtils');
        const branchName = gitUtils.getCurrentBranchName();
        if (!branchName) {
            throw new Error('cannot get current branch name');
        }
        echoAndExec(`git checkout ${branchName}`);
        gitUtils.setupWritableRepository();
        echoAndExec('git commit -m "auto-commit: build-project"');
        echoAndExec('git push');
        console.log('# build-project 更新完成.');
    } else {
        console.log(`# build-project 没有变更.`);
    }
}
