import { once } from 'lodash';
import { injections } from '@qunhe/apaas-type-generator-lib';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';

export const getVMBindingType = once(() => {
    return createVMBindingType({
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        },
    });
});
export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal({
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        },
    });
});
