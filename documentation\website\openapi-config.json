{"openApiConfig": {"camera": {"specPath": "../../specifications/services/camera/openapi.yaml", "outputDir": "docs/api/camera", "sidebarOptions": {"groupPathsBy": "tag", "categoryLinkSource": "tag"}, "downloadUrl": "/specifications/services/camera/openapi.yaml", "hideSendButton": false, "showSchemas": false, "disableCompression": false}, "doorwindow": {"specPath": "../../specifications/services/doorwindow/openapi.yaml", "outputDir": "docs/api/doorwindow", "sidebarOptions": {"groupPathsBy": "tag", "categoryLinkSource": "tag"}, "downloadUrl": "/specifications/services/doorwindow/openapi.yaml", "hideSendButton": false, "showSchemas": false, "disableCompression": false}, "furniture": {"specPath": "../../specifications/services/furniture/openapi.yaml", "outputDir": "docs/api/furniture", "sidebarOptions": {"groupPathsBy": "tag", "categoryLinkSource": "tag"}, "downloadUrl": "/specifications/services/furniture/openapi.yaml", "hideSendButton": false, "showSchemas": false, "disableCompression": false}, "koolux": {"specPath": "../../specifications/services/koolux/openapi.yaml", "outputDir": "docs/api/koolux", "sidebarOptions": {"groupPathsBy": "tag", "categoryLinkSource": "tag"}, "downloadUrl": "/specifications/services/koolux/openapi.yaml", "hideSendButton": false, "showSchemas": false, "disableCompression": false}, "layout": {"specPath": "../../specifications/services/layout/openapi.yaml", "outputDir": "docs/api/layout", "sidebarOptions": {"groupPathsBy": "tag", "categoryLinkSource": "tag"}, "downloadUrl": "/specifications/services/layout/openapi.yaml", "hideSendButton": false, "showSchemas": false, "disableCompression": false}, "pdm": {"specPath": "../../specifications/services/pdm/openapi.yaml", "outputDir": "docs/api/pdm", "sidebarOptions": {"groupPathsBy": "tag", "categoryLinkSource": "tag"}, "downloadUrl": "/specifications/services/pdm/openapi.yaml", "hideSendButton": false, "showSchemas": false, "disableCompression": false}}, "sidebarEntries": [{"type": "docSidebar", "sidebarId": "cameraSidebar", "label": "🔧 Camera 服务"}, {"type": "docSidebar", "sidebarId": "doorwindowSidebar", "label": "🔧 Doorwindow 服务"}, {"type": "docSidebar", "sidebarId": "furnitureSidebar", "label": "🪑 Furniture 服务"}, {"type": "docSidebar", "sidebarId": "kooluxSidebar", "label": "🔧 Koolux 服务"}, {"type": "docSidebar", "sidebarId": "layoutSidebar", "label": "🔧 Layout 服务"}, {"type": "docSidebar", "sidebarId": "pdmSidebar", "label": "🔧 Pdm 服务"}], "summary": {"totalServices": 6, "services": [{"id": "camera", "name": "Camera 服务", "path": "../../specifications/services/camera/openapi.yaml"}, {"id": "doorwindow", "name": "Doorwindow 服务", "path": "../../specifications/services/doorwindow/openapi.yaml"}, {"id": "furniture", "name": "Furniture 服务", "path": "../../specifications/services/furniture/openapi.yaml"}, {"id": "k<PERSON><PERSON>", "name": "Koolux 服务", "path": "../../specifications/services/koolux/openapi.yaml"}, {"id": "layout", "name": "Layout 服务", "path": "../../specifications/services/layout/openapi.yaml"}, {"id": "pdm", "name": "Pdm 服务", "path": "../../specifications/services/pdm/openapi.yaml"}]}}