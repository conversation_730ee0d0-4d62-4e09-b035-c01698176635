/**
 * 酷家乐 API 文档自定义样式
 * 基于酷家乐官网设计风格
 */

:root {
  /* 酷家乐品牌主色调 - 蓝色系 */
  --ifm-color-primary: #3b82f6;
  --ifm-color-primary-dark: #2563eb;
  --ifm-color-primary-darker: #1d4ed8;
  --ifm-color-primary-darkest: #1e40af;
  --ifm-color-primary-light: #60a5fa;
  --ifm-color-primary-lighter: #93c5fd;
  --ifm-color-primary-lightest: #dbeafe;
  
  /* 辅助色彩 */
  --kujiale-blue-gradient-start: #3b82f6;
  --kujiale-blue-gradient-end: #8b5cf6;
  --kujiale-purple-gradient-start: #8b5cf6;
  --kujiale-purple-gradient-end: #a855f7;
  
  /* 背景色调 */
  --ifm-background-color: #ffffff;
  --ifm-background-surface-color: #f8fafc;
  
  /* 文字颜色 */
  --ifm-font-color-base: #1e293b;
  --ifm-font-color-secondary: #64748b;
  
  /* 代码相关 */
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(59, 130, 246, 0.1);
  
  /* 导航栏 */
  --ifm-navbar-background-color: rgba(255, 255, 255, 0.95);
  --ifm-navbar-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  /* 按钮样式 */
  --ifm-button-background-color: var(--ifm-color-primary);
  --ifm-button-border-color: var(--ifm-color-primary);
  
  /* 链接颜色 */
  --ifm-link-color: var(--ifm-color-primary);
  --ifm-link-hover-color: var(--ifm-color-primary-dark);
  
  /* 边框和分割线 */
  --ifm-border-color: #e2e8f0;
  --ifm-global-border-width: 1px;
  
  /* 卡片阴影 */
  --ifm-global-shadow-lw: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --ifm-global-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --ifm-global-shadow-tl: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* 深色主题 */
[data-theme='dark'] {
  --ifm-color-primary: #60a5fa;
  --ifm-color-primary-dark: #3b82f6;
  --ifm-color-primary-darker: #2563eb;
  --ifm-color-primary-darkest: #1d4ed8;
  --ifm-color-primary-light: #93c5fd;
  --ifm-color-primary-lighter: #bfdbfe;
  --ifm-color-primary-lightest: #dbeafe;
  
  --ifm-background-color: #0f172a;
  --ifm-background-surface-color: #1e293b;
  --ifm-font-color-base: #f1f5f9;
  --ifm-font-color-secondary: #94a3b8;
  
  --docusaurus-highlighted-code-line-bg: rgba(96, 165, 250, 0.1);
  --ifm-navbar-background-color: rgba(15, 23, 42, 0.95);
  --ifm-border-color: #334155;
}

/* 导航栏样式增强 */
.navbar {
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--ifm-border-color);
}

.navbar__brand {
  font-weight: 700;
  color: var(--ifm-font-color-base);
}

.navbar__item {
  font-weight: 500;
}

.navbar__link--active {
  color: var(--ifm-color-primary);
  font-weight: 600;
}

/* 按钮样式增强 */
.button {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  border: none;
}

.button--primary {
  background: linear-gradient(135deg, var(--kujiale-blue-gradient-start), var(--kujiale-blue-gradient-end));
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, var(--ifm-color-primary-dark), var(--kujiale-purple-gradient-start));
}

.button--secondary {
  background: rgba(59, 130, 246, 0.1);
  color: var(--ifm-color-primary);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.button--secondary:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.button--outline {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.button--outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 侧边栏样式 */
.theme-doc-sidebar-container {
  border-right: 1px solid var(--ifm-border-color);
}

.menu__link {
  border-radius: 6px;
  margin: 2px 0;
}

.menu__link--active {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.05));
  color: var(--ifm-color-primary);
  font-weight: 600;
}

.menu__link--active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, var(--kujiale-blue-gradient-start), var(--kujiale-purple-gradient-start));
  border-radius: 0 2px 2px 0;
}

/* 内容区域 */
.main-wrapper {
  background: var(--ifm-background-color);
}

article {
  max-width: none;
}

/* 表格样式 */
table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--ifm-global-shadow-lw);
}

table thead tr {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
}

/* 代码块样式 */
.theme-code-block {
  border-radius: 12px;
  box-shadow: var(--ifm-global-shadow-md);
}

/* 页脚样式 */
.footer {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  color: #f1f5f9;
}

.footer__title {
  color: #f1f5f9;
  font-weight: 600;
}

.footer__link-item {
  color: #94a3b8;
  transition: color 0.2s ease;
}

.footer__link-item:hover {
  color: var(--ifm-color-primary-lighter);
}

/* HTTP 方法标签样式 */
.http-method {
  display: inline-block;
  padding: 0.2em 0.5em;
  margin-right: 0.5em;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.85em;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.http-method.get {
  background: linear-gradient(90deg, #10b981, #059669);
}

.http-method.post {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.http-method.put {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.http-method.delete {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.http-method.patch {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

/* 搜索框样式 */
.DocSearch-Button {
  border-radius: 8px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* 标签样式 */
.badge {
  border-radius: 6px;
  font-weight: 500;
}

.badge--primary {
  background: linear-gradient(90deg, var(--kujiale-blue-gradient-start), var(--kujiale-blue-gradient-end));
}

/* 警告框样式 */
.alert {
  border-radius: 8px;
  border-left-width: 4px;
}

.alert--info {
  border-left-color: var(--ifm-color-primary);
  background: rgba(59, 130, 246, 0.05);
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .navbar__brand {
    font-size: 1.1rem;
  }
  
  .button {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
  }
}

/* API 服务卡片样式 */
.api-service-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.api-service-card {
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: 8px;
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-0);
  transition: all 0.3s ease;
}

.api-service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.api-service-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.api-service-card p {
  color: var(--ifm-color-emphasis-700);
  margin-bottom: 1rem;
}

.api-service-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.api-service-buttons .button {
  font-size: 0.9rem;
  min-width: auto;
}
