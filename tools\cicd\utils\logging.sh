#!/bin/bash

# 日志工具脚本
# 提供高级日志功能和格式化输出
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# 日志文件配置
export LOG_DIR="${LOG_DIR:-logs}"
export LOG_FILE="${LOG_FILE:-ci-cd.log}"
export LOG_MAX_SIZE="${LOG_MAX_SIZE:-10485760}" # 10MB

# 确保日志目录存在
ensure_log_dir() {
    ensure_dir "$LOG_DIR" "日志目录"
}

# 获取日志文件路径
get_log_file_path() {
    echo "$LOG_DIR/$LOG_FILE"
}

# 轮转日志文件
rotate_log_file() {
    local log_path=$(get_log_file_path)
    
    if [ -f "$log_path" ]; then
        local file_size=$(stat -f%z "$log_path" 2>/dev/null || stat -c%s "$log_path" 2>/dev/null || echo 0)
        
        if [ "$file_size" -gt "$LOG_MAX_SIZE" ]; then
            local backup_path="${log_path}.$(date +%Y%m%d_%H%M%S)"
            mv "$log_path" "$backup_path"
            log_info "日志文件已轮转: $backup_path"
        fi
    fi
}

# 写入日志文件
write_to_log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    ensure_log_dir
    rotate_log_file
    
    local log_path=$(get_log_file_path)
    echo "[$timestamp] [$level] $message" >> "$log_path"
}

# 增强的日志函数（同时输出到控制台和文件）
log_debug_enhanced() {
    local message="$1"
    log_debug "$message"
    write_to_log "DEBUG" "$message"
}

log_info_enhanced() {
    local message="$1"
    log_info "$message"
    write_to_log "INFO" "$message"
}

log_success_enhanced() {
    local message="$1"
    log_success "$message"
    write_to_log "SUCCESS" "$message"
}

log_warning_enhanced() {
    local message="$1"
    log_warning "$message"
    write_to_log "WARNING" "$message"
}

log_error_enhanced() {
    local message="$1"
    log_error "$message"
    write_to_log "ERROR" "$message"
}

# 结构化日志函数
log_structured() {
    local level="$1"
    local component="$2"
    local action="$3"
    local message="$4"
    local extra="${5:-}"
    
    local formatted_message="[$component] $action: $message"
    if [ -n "$extra" ]; then
        formatted_message="$formatted_message ($extra)"
    fi
    
    case "$level" in
        "DEBUG")
            log_debug_enhanced "$formatted_message"
            ;;
        "INFO")
            log_info_enhanced "$formatted_message"
            ;;
        "SUCCESS")
            log_success_enhanced "$formatted_message"
            ;;
        "WARNING")
            log_warning_enhanced "$formatted_message"
            ;;
        "ERROR")
            log_error_enhanced "$formatted_message"
            ;;
        *)
            log_info_enhanced "$formatted_message"
            ;;
    esac
}

# 进度条函数
show_progress() {
    local current="$1"
    local total="$2"
    local description="${3:-进度}"
    local width=50
    
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    printf "\r${BLUE}[%-${width}s] %d%% %s${NC}" \
        "$(printf "%*s" $filled | tr ' ' '=')" \
        "$percentage" \
        "$description"
    
    if [ "$current" -eq "$total" ]; then
        echo ""
    fi
}

# 表格输出函数
print_table_header() {
    local -a headers=("$@")
    local separator=""
    
    printf "${WHITE}"
    for header in "${headers[@]}"; do
        printf "%-20s" "$header"
        separator="$separator--------------------"
    done
    printf "${NC}\n"
    
    printf "${WHITE}%s${NC}\n" "$separator"
}

print_table_row() {
    local -a values=("$@")
    
    for value in "${values[@]}"; do
        printf "%-20s" "$value"
    done
    printf "\n"
}

# 统计信息收集
declare -A STATS_COUNTERS
declare -A STATS_TIMERS

# 增加计数器
increment_counter() {
    local counter_name="$1"
    local increment="${2:-1}"
    
    STATS_COUNTERS["$counter_name"]=$((${STATS_COUNTERS["$counter_name"]:-0} + increment))
}

# 获取计数器值
get_counter() {
    local counter_name="$1"
    echo "${STATS_COUNTERS["$counter_name"]:-0}"
}

# 开始计时
start_timer_named() {
    local timer_name="$1"
    STATS_TIMERS["$timer_name"]=$(date +%s)
}

# 结束计时并返回耗时
end_timer_named() {
    local timer_name="$1"
    local start_time="${STATS_TIMERS["$timer_name"]}"
    
    if [ -z "$start_time" ]; then
        echo "0"
        return 1
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    unset STATS_TIMERS["$timer_name"]
    echo "$duration"
}

# 显示统计信息
show_stats() {
    local title="${1:-统计信息}"
    
    echo ""
    echo -e "${WHITE}=== $title ===${NC}"
    
    # 显示计数器
    if [ ${#STATS_COUNTERS[@]} -gt 0 ]; then
        echo -e "${CYAN}计数器:${NC}"
        for counter in "${!STATS_COUNTERS[@]}"; do
            printf "  %-20s: %s\n" "$counter" "${STATS_COUNTERS[$counter]}"
        done
    fi
    
    echo ""
}

# 清理统计信息
clear_stats() {
    STATS_COUNTERS=()
    STATS_TIMERS=()
}

# 错误处理和堆栈跟踪
print_stack_trace() {
    local frame=0
    echo -e "${RED}堆栈跟踪:${NC}"
    
    while caller $frame; do
        ((frame++))
    done | while read line func file; do
        echo -e "${RED}  在 $file:$line 的 $func()${NC}"
    done
}

# 设置错误陷阱
setup_error_trap() {
    trap 'log_error "脚本在第 $LINENO 行发生错误"; print_stack_trace; exit 1' ERR
}

# 清理陷阱
cleanup_trap() {
    trap - ERR
}

# JSON 格式日志输出
log_json() {
    local level="$1"
    local component="$2"
    local message="$3"
    local extra="$4"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local json_log="{\"timestamp\":\"$timestamp\",\"level\":\"$level\",\"component\":\"$component\",\"message\":\"$message\""
    
    if [ -n "$extra" ]; then
        json_log="$json_log,\"extra\":\"$extra\""
    fi
    
    json_log="$json_log}"
    
    echo "$json_log" >> "$(get_log_file_path).json"
}

# 性能监控
monitor_performance() {
    local command="$1"
    local description="$2"
    
    local start_time=$(date +%s.%N)
    local start_memory=$(ps -o rss= -p $$ 2>/dev/null || echo 0)
    
    log_info "开始执行: $description"
    
    if eval "$command"; then
        local end_time=$(date +%s.%N)
        local end_memory=$(ps -o rss= -p $$ 2>/dev/null || echo 0)
        
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0")
        local memory_diff=$((end_memory - start_memory))
        
        log_success "$description 完成 (耗时: ${duration}s, 内存变化: ${memory_diff}KB)"
        return 0
    else
        local exit_code=$?
        log_error "$description 失败 (退出码: $exit_code)"
        return $exit_code
    fi
}

log_info "日志工具已加载 ✅"
