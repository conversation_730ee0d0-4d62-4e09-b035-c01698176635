import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/koolux/koolux-rest-api",
    },
    {
      type: "category",
      label: "KooLuxLight",
      link: {
        type: "doc",
        id: "api/koolux/koo-lux-light",
      },
      items: [
        {
          type: "doc",
          id: "api/koolux/import-data-source",
          label: "创建KooLux方案",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/koolux/get-scene-document",
          label: "查询照明场景数据（建设中，暂不可用）",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/koolux/batch-update",
          label: "批量更新照明场景数据",
          className: "api-method put",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
