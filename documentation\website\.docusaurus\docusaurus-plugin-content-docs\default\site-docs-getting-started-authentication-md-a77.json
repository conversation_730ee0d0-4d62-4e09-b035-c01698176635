{"id": "getting-started/authentication", "title": "身份认证", "description": "酷家乐 API 使用 API Key 进行身份认证。", "source": "@site/docs/getting-started/authentication.md", "sourceDirName": "getting-started", "slug": "/getting-started/authentication", "permalink": "/manycoreapi-demo/0.0.4/docs/getting-started/authentication", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "authentication", "title": "身份认证", "sidebar_label": "身份认证"}, "sidebar": "tutorialSidebar", "previous": {"title": "开始使用", "permalink": "/manycoreapi-demo/0.0.4/docs/"}, "next": {"title": "发起请求", "permalink": "/manycoreapi-demo/0.0.4/docs/getting-started/making-requests"}}