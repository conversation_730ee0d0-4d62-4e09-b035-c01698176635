#!/bin/bash

# Docker 镜像构建脚本
# 自动尝试不同的镜像源，解决网络问题

set -e

IMAGE_NAME="registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest"

echo "🐳 开始构建 Docker 镜像..."
echo "📦 镜像名称: $IMAGE_NAME"

# 尝试使用网易镜像源构建
echo "🔄 尝试使用网易镜像源构建..."
if docker build -t "$IMAGE_NAME" .; then
    echo "✅ 使用网易镜像源构建成功！"
    
    # 询问是否推送镜像
    read -p "是否推送镜像到仓库？(y/n): " push_choice
    if [[ $push_choice == "y" || $push_choice == "Y" ]]; then
        echo "📤 推送镜像到仓库..."
        docker push "$IMAGE_NAME"
        echo "✅ 镜像推送成功！"
    fi
    
    echo "🎉 Docker 镜像构建完成！"
    exit 0
fi

echo "❌ 网易镜像源构建失败，尝试使用官方镜像..."

# 尝试使用官方镜像构建
echo "🔄 尝试使用官方镜像构建..."
if docker build -f Dockerfile.fallback -t "$IMAGE_NAME" .; then
    echo "✅ 使用官方镜像构建成功！"
    
    # 询问是否推送镜像
    read -p "是否推送镜像到仓库？(y/n): " push_choice
    if [[ $push_choice == "y" || $push_choice == "Y" ]]; then
        echo "📤 推送镜像到仓库..."
        docker push "$IMAGE_NAME"
        echo "✅ 镜像推送成功！"
    fi
    
    echo "🎉 Docker 镜像构建完成！"
    exit 0
fi

echo "❌ 官方镜像构建也失败了。"
echo "💡 可能的解决方案："
echo "   1. 检查网络连接"
echo "   2. 使用代理: docker build --build-arg https_proxy=http://your-proxy:port ."
echo "   3. 尝试手动拉取镜像: docker pull maven:3-jdk-11"
echo "   4. 使用其他平台: docker pull --platform linux/amd64 maven:3-jdk-11"

exit 1 