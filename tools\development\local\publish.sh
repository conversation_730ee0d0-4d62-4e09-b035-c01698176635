#!/bin/bash

# 一键发布脚本 - 自动检测配置并选择最佳发布方式
# 支持多种配置方式，优先使用最简单的方式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 检查必要的工具
check_requirements() {
    local missing=0
    
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker 未安装或不在 PATH 中"
        missing=1
    fi
    
    if [ ! -f "$SCRIPT_DIR/local-sdk-manager.sh" ]; then
        print_error "local-sdk-manager.sh 脚本不存在"
        missing=1
    fi
    
    if [ $missing -eq 1 ]; then
        exit 1
    fi
}

# 检测配置方式
detect_config() {
    local config_type=""
    local config_file=""
    
    # 检查 ~/.manycore-sdk-config
    if [ -f "$HOME/.manycore-sdk-config" ]; then
        config_type="global"
        config_file="$HOME/.manycore-sdk-config"
        print_status "发现全局配置: $config_file"
        return 0
    fi
    
    # 检查本地 config.env
    if [ -f "$SCRIPT_DIR/config.env" ]; then
        config_type="local"
        config_file="$SCRIPT_DIR/config.env"
        print_status "发现本地配置: $config_file"
        return 0
    fi
    
    # 检查环境变量
    if [ -n "$GPG_KEY_NAME" ] && [ -n "$CENTRAL_USERNAME" ]; then
        config_type="env"
        print_status "发现环境变量配置"
        return 0
    fi
    
    return 1
}

# 加载配置
load_config() {
    local config_type=""
    local config_file=""
    
    # 检查 ~/.manycore-sdk-config
    if [ -f "$HOME/.manycore-sdk-config" ]; then
        config_file="$HOME/.manycore-sdk-config"
        print_status "加载全局配置: $config_file"
        source "$config_file"
        return 0
    fi
    
    # 检查本地 config.env
    if [ -f "$SCRIPT_DIR/config.env" ]; then
        config_file="$SCRIPT_DIR/config.env"
        print_status "加载本地配置: $config_file"
        source "$config_file"
        return 0
    fi
    
    # 环境变量已经存在，无需加载
    if [ -n "$GPG_KEY_NAME" ] && [ -n "$CENTRAL_USERNAME" ]; then
        print_status "使用现有环境变量"
        return 0
    fi
    
    return 1
}

# 验证配置
validate_config() {
    local errors=0
    
    if [ -z "$GPG_KEY_NAME" ]; then
        print_error "GPG_KEY_NAME 未设置"
        errors=1
    fi
    
    if [ -z "$GPG_PASSPHRASE" ]; then
        print_error "GPG_PASSPHRASE 未设置"
        errors=1
    fi
    
    if [ -z "$CENTRAL_USERNAME" ]; then
        print_error "CENTRAL_USERNAME 未设置"
        errors=1
    fi
    
    if [ -z "$CENTRAL_PASSWORD" ]; then
        print_error "CENTRAL_PASSWORD 未设置"
        errors=1
    fi
    
    if [ $errors -eq 1 ]; then
        echo ""
        print_warning "配置不完整，请运行以下命令之一进行设置："
        echo "  1. 全局配置: $SCRIPT_DIR/setup-local-gpg.sh"
        echo "  2. 本地配置: cp $SCRIPT_DIR/config.env.template $SCRIPT_DIR/config.env && nano $SCRIPT_DIR/config.env"
        echo "  3. 环境变量: export GPG_KEY_NAME=... 等"
        return 1
    fi
    
    return 0
}

# 显示配置摘要
show_config_summary() {
    echo ""
    print_status "当前配置摘要:"
    echo "  GPG Key: ${GPG_KEY_NAME:0:8}..."
    echo "  GPG Passphrase: $([ -n "$GPG_PASSPHRASE" ] && echo "已设置" || echo "未设置")"
    echo "  Central User: $CENTRAL_USERNAME"
    echo "  Central Password: $([ -n "$CENTRAL_PASSWORD" ] && echo "${CENTRAL_PASSWORD:0:8}..." || echo "未设置")"
    echo ""
}

# 获取支持的服务列表
get_supported_services() {
    local services=""
    for dir in "$PROJECT_ROOT/openapi"/*; do
        if [ -d "$dir" ] && [ "$(basename "$dir")" != "common" ] && [ "$(basename "$dir")" != "shared" ]; then
            local service_name=$(basename "$dir")
            if [ -f "$dir/restapi.yaml" ] && [ -f "$dir/config-java.yaml" ]; then
                services="$services $service_name"
            fi
        fi
    done
    echo "$services"
}

# 显示帮助信息
show_help() {
    cat << EOF
🚀 ManyCore SDK 一键发布工具

用法: $0 [选项] <服务名>

服务名:
$(get_supported_services | tr ' ' '\n' | sed 's/^/  - /')
  - all (所有服务)

选项:
  --dry-run    预览操作，不实际执行
  --help       显示此帮助信息

示例:
  $0 doorwindow              # 发布 doorwindow 服务
  $0 all                     # 发布所有服务
  $0 --dry-run furniture     # 预览发布 furniture 服务

配置方式（按优先级）:
  1. ~/.manycore-sdk-config  (运行 setup-local-gpg.sh 生成)
  2. scripts/local/config.env (复制 config.env.template)
  3. 环境变量 (GPG_KEY_NAME, CENTRAL_USERNAME 等)

EOF
}

# 主函数
main() {
    local service=""
    local dry_run=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run="--dry-run"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$service" ]; then
                    service="$1"
                else
                    print_error "只能指定一个服务名"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查服务名参数
    if [ -z "$service" ]; then
        print_error "请指定要发布的服务名"
        show_help
        exit 1
    fi
    
    # 检查前置要求
    check_requirements
    
    echo "🚀 ManyCore SDK 一键发布工具"
    echo "=============================="
    echo ""
    
    # 检测并加载配置
    if ! detect_config; then
        print_error "未找到任何配置，请先进行配置设置"
        echo ""
        print_status "推荐配置方式："
        echo "  1. 快速设置: $SCRIPT_DIR/setup-local-gpg.sh"
        echo "  2. 手动配置: cp $SCRIPT_DIR/config.env.template $SCRIPT_DIR/config.env"
        exit 1
    fi
    
    if ! load_config; then
        print_error "配置加载失败"
        exit 1
    fi
    
    # 验证配置
    if ! validate_config; then
        exit 1
    fi
    
    # 显示配置摘要
    show_config_summary
    
    # 确认发布
    if [ -z "$dry_run" ]; then
        echo "🎯 准备发布服务: $service"
        read -p "确认继续？(y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_warning "发布已取消"
            exit 0
        fi
        echo ""
    fi
    
    # 执行发布
    print_status "开始发布流程..."
    cd "$PROJECT_ROOT"
    
    if ! "$SCRIPT_DIR/local-sdk-manager.sh" $dry_run deploy-rel "$service"; then
        print_error "发布失败"
        exit 1
    fi
    
    if [ -z "$dry_run" ]; then
        print_success "🎉 发布完成！"
        echo ""
        print_status "后续操作："
        echo "  - 检查 Maven Central Portal: https://central.sonatype.com/"
        echo "  - 等待同步到 Maven Central Repository (通常需要10-30分钟)"
        echo "  - 验证发布: mvn dependency:get -Dartifact=com.manycoreapis:$service-sdk:版本号"
    else
        print_success "预览完成"
    fi
}

main "$@" 