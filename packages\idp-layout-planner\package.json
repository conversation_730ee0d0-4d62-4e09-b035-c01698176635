{"name": "@qunhe/idp-layout-planner", "version": "1.70.0", "description": "api typings for idp-layout-planner", "keywords": [], "author": "misen <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/idp-types.git"}, "scripts": {"build": "node ../../scripts/build-package/build-api", "build-package": "npm run build && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "devDependencies": {"copyfiles": "^2.4.1"}}