declare enum RenderMode  {
    /**
     * 普通图
     */
    Normal = 'Normal',
    /**
     * 俯视图
     */
    Orbit = 'Orbit',
    /**
     * 全景图
     */
    Panorama = 'Panorama',
    /**
     * 视频
     */
    Video = 'Video'
}

declare namespace IDP {
        namespace Platform {
            /**
             * 进入渲染模式
             * @default 默认首次进入普通图
             * @param renderMode 渲染模式
             * @internal
             */
            function enterRenderModeAsync(renderMode?: RenderMode): Promise<void>;
            /**
             * 进入图册
             * @default 默认进入列表模式，不加载教程图片
             * @param viewList 是否列表模式,默认为列表模式
             * @param needTutorialPic 是否加载教程图片,仅首次进入图册可用,需在进入前设定方案为教学方案
             * @internal
             */
            function enterGalleryAsync(viewList? : boolean, needTutorialPic?: boolean): Promise<void>;
        }
        namespace UI {
            /**
             * 左侧栏空出 padding
             * @param padding 左侧需要空出的距离padding
             * @internal
             */
            function setYunrenderLeftSidePanelPaddingLeft(padding?: number): void;
        }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
