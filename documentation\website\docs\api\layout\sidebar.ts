import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/layout/户型图例管理api",
    },
    {
      type: "category",
      label: "图例管理接口",
      link: {
        type: "doc",
        id: "api/layout/图例管理接口",
      },
      items: [
        {
          type: "doc",
          id: "api/layout/batch-create-legend-group",
          label: "批量创建图例组合",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/list-legend",
          label: "获取图例列表",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/layout/create-legend",
          label: "创建单个图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-update-legend",
          label: "批量更新图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-get-legend",
          label: "批量获取图例信息",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-delete-legend",
          label: "批量删除图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-create-legend",
          label: "批量创建图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/get-single-legend",
          label: "获取单个图例信息",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/layout/update-legend",
          label: "更新单个图例",
          className: "api-method put",
        },
        {
          type: "doc",
          id: "api/layout/delete-legend",
          label: "删除单个图例",
          className: "api-method delete",
        },
      ],
    },
    {
      type: "category",
      label: "colored-floor-plan-rest-api-controller",
      items: [
        {
          type: "doc",
          id: "api/layout/get-colored-floor-plan",
          label: "getColoredFloorPlan",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "高级功能",
      items: [
        {
          type: "doc",
          id: "api/layout/batch-create-legend-group",
          label: "批量创建图例组合",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "批量操作",
      items: [
        {
          type: "doc",
          id: "api/layout/batch-create-legend-group",
          label: "批量创建图例组合",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-update-legend",
          label: "批量更新图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-get-legend",
          label: "批量获取图例信息",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-delete-legend",
          label: "批量删除图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/batch-create-legend",
          label: "批量创建图例",
          className: "api-method post",
        },
      ],
    },
    {
      type: "category",
      label: "图例查询",
      items: [
        {
          type: "doc",
          id: "api/layout/list-legend",
          label: "获取图例列表",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/layout/get-single-legend",
          label: "获取单个图例信息",
          className: "api-method get",
        },
      ],
    },
    {
      type: "category",
      label: "图例管理",
      items: [
        {
          type: "doc",
          id: "api/layout/create-legend",
          label: "创建单个图例",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/layout/update-legend",
          label: "更新单个图例",
          className: "api-method put",
        },
        {
          type: "doc",
          id: "api/layout/delete-legend",
          label: "删除单个图例",
          className: "api-method delete",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
