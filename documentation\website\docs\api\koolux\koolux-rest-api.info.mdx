---
id: koolux-rest-api
title: "KooLux REST API"
description: "KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import Api<PERSON>ogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: 1.0.0"}
>
</span>

<Export
  url={"/specifications/services/koolux/openapi.yaml"}
  proxy={undefined}
>
  
</Export>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"KooLux REST API"}
>
</Heading>



KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。  

## 功能特性  
- 支持IES文件的管理  
- 支持照明模型管理  
- 批量操作支持（创建、更新、删除、查询）  

## 数据模型  
- **照明模型**: 支持材质编辑和灯光模型排除  
- **IES光源**: 包含IES文件全量信息


<div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    照明设计团队: [<EMAIL>](mailto:<EMAIL>)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://www.manycore.com"}
  >
    内部使用
  </a>
</div>
      