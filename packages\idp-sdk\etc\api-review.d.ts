declare type AugmentedOptional<T extends object, K extends keyof T = keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
declare type AugmentedRequired<T extends object, K extends keyof T = keyof T> = Omit<T, K> & Required<Pick<T, K>>;
declare interface BomDetectionOption {
    orderId: string;
    productIds?: string[];
}
declare interface BomDetectionOptionInternal {
    orderId: string;
    productIds?: string[];
    eventKey?: string;
}
declare interface BomDetectionProblem {
    ruleId: number;
    type: BomDetectionType;
    material?: BomMaterialBase;
    product?: BomProductBase;
    message?: string;
}
declare interface BomDetectionRule {
    ruleId: number;
    ruleName: string;
    alertLevel: BomDetectionRuleAlertLevel;
}
declare enum BomDetectionRuleAlertLevel {
    ERR = 1,
    WARN = 2
}
declare enum BomDetectionType {
    MATERIAL = 1,
    PRODUCT = 2,
    ORDER = 3
}
declare interface BomMaterialBase {
    id: string;
    type: BomMaterialType;
    name: string;
    productId: string;
    productName: string;
}
declare enum BomMaterialType {
    PLANK = 1,
    MOLDING = 2,
    FINISHED_PRODUCT = 3
}
declare interface BomProductBase {
    productId: string;
    productName: string;
}
declare enum BoolEffectCode {
    ModelNotFound = 1,
    BoolMountNotFound = 2,
    CustomBoolServiceNotFound = 3,
    NoBoolModels = 4,
    InternalError = 5
}
declare interface CheckModelSplitOrderResult {
    modelId: string;
    isSplitOrder: boolean;
}
declare interface Connector {
    id: string;
    name: string;
    type: EConnectorType;
}
declare type CustomModelSimple = ICustomModelPureSimple | ICustomModelAssemblySimple;
declare interface CustomModelSimpleData {
    id: string;
    name: string;
    type: ModelDataType;
    category: number;
    toolType: EToolType;
    children: CustomModelSimpleData[];
    accessories: CustomModelSimpleData[];
}
declare type DeleteFinishedProductOption = AugmentedRequired<IFindOrDeleteFinishedProductOption, "finishedIds"> | AugmentedRequired<IFindOrDeleteFinishedProductOption, "orderIds"> | AugmentedRequired<IFindOrDeleteFinishedProductOption, "productIds" | "orderId">;
declare type DeleteMoldingsOption = Pick<IDeleteMoldingsOption, "orderId" | "productIds"> | Pick<IDeleteMoldingsOption, "moldingIds"> | Pick<IDeleteMoldingsOption, "orderIds">;
declare interface DesignUnlockModelResult {
    modelId: string;
    eventKey: string;
}
declare interface DetectionResult {
    pass: IDP.DB.Types.ElementId[];
    fail: IDP.DB.Types.ElementId[];
    untouched: IDP.DB.Types.ElementId[];
}
declare enum DetectionRuleType {
    VALID = 1,
    RULE,
    INTERSECT
}
declare type DragCustomProductPromiseResult = Result__idp_custom_api<IDP.DB.Types.ElementId[]>;
declare enum EAlertLevel {
    ERROR = 0,
    WARNING = 1
}
declare enum EAppliedGrooveType {
    PLANK = "PLANK",
    FITTING = "FITTING",
    AUXILIARY = "AUXILIARY",
    LAMELLO = "LAMELLO",
    LOCK = "LOCK",
    EDGE = "EDGE",
    INVISIBLE = "INVISIBLE",
    ARRAY = "ARRAY"
}
declare enum EAppliedHoleType {
    PLANK = "PLANK",
    FITTING = "FITTING",
    AUXILIARY = "AUXILIARY"
}
declare enum EAttachmentType {
    DESIGN_ATTACHMENT = 1,
    AUDIT = 2,
    SPLIT_ORDER_ATTACHMENT = 3,
    ORDER_ATTACHMENT = 4
}
declare enum EBomCurveType {
    LineSeg = "LineSeg",
    Arc = "Arc"
}
declare enum EBomDetectionInterceptType {
    ERR = 1,
    WARN = 2
}
declare enum EBomMoldingCornerType {
    FLAT = "FLAT",
    OUTSIDE = "OUTSIDE",
    INSIDE = "INSIDE"
}
declare enum EBomMoldingType {
    CROWN = "CROWN",
    SKIRTING = "SKIRTING",
    LIGHT = "LIGHT",
    OTHER = "OTHER"
}
declare enum EBomPlankGrooveType {
    PLANK = "PLANK",
    FITTING = "FITTING",
    AUXILIARY = "AUXILIARY",
    LAMELLO = "LAMELLO",
    LOCK = "LOCK",
    EDGE = "EDGE",
    INVISIBLE = "INVISIBLE",
    ARRAY = "ARRAY",
    BEVEL = "BEVEL"
}
declare enum EBomPlankHoleType {
    PLANK = "PLANK",
    FITTING = "FITTING",
    AUXILIARY = "AUXILIARY"
}
declare enum EBomPlankType {
    BODY = "BODY",
    BACK = "BACK",
    DOOR = "DOOR",
    SLIDINGDOOR = "SLIDINGDOOR"
}
declare enum EBomRawPlankType {
    RAW = "RAW",
    SURPLUS = "SURPLUS"
}
declare enum EBzPropertyType {
    number = "number",
    string = "string",
    boolean = "boolean"
}
declare enum ECheckModelsStatus {
    SUCCESS = "success",
    FAIL = "fail"
}
declare enum EClockWise {
    TRUE = "true",
    FALSE = "false"
}
declare enum EConnectorType {
    point,
    lineSeg,
    arc,
    face
}
declare enum ECustomAppMode {
    INNER_SPACE = "INNER_SPACE",
    ORDER_AUDIT = "ORDER_AUDIT",
    FOP_ORDER = "FOP_ORDER",
    DOOR_COVERING_HARDWARE = "DOOR_COVERING_HARDWARE"
}
declare enum ECustomAppModeAsync {
    SUBMIT_AUDIT = "SUBMIT_AUDIT"
}
declare enum ECustomerQueryRange {
    MYSELF = 1,
    ALL = 2
}
declare enum ECustomerQueryType {
    CUSTOMER_NAME = 1,
    CUSTOMER_PHONE = 2,
    CUSTOMER_ADDR = 3
}
declare enum ECustomerSource {
    OTHER = 0,
    NATURAL = 1,
    MODEL_ROOM = 2,
    NEW_MEDIA = 3,
    LIVE = 4,
    CROSS_COOP = 5,
    TELEMARKETING = 6,
    OLD_INTRODUCE = 7,
    REPURCHASE = 8
}
declare enum ECustomLibraryPosition {
    PRODUCT_LIBRARY = 0,
    ASSEMBLY_LIBRARY = 1,
    COMPONENT_LIBRARY = 2,
    TEXTURE_LIBRARY = 3,
    ALUMINIFEROUS_LIBRARY = 4,
    RELATIONAL_COMPONENT_LIBRARY = 5,
    FITTING_PRODUCT_LIBRARY = 6,
    MIX_LIBRARY = 7,
    BOOL_MODELING_LIBRARY = 8
}
declare enum ECustomLibraryType {
    Enterprise = "enterprise",
    Public = "public",
    Brand = "brand",
    SupplyChain = "supplyChain",
    My = "my"
}
declare enum ECustomModelType {
    PARAM_ENTITY = 1,
    PARAM_FURNITURE = 2,
    PARAM_PRIMITIVE = 3,
    PARAM_VIRTUAL = 4,
    PARAM_ASSEMBLY = 5,
    TEMPLATE_ASSEMBLY = 6
}
declare enum EDesignExportCode {
    SUCCESS = 1,
    FAIL = -1
}
declare enum EDesignType {
    Design = "Design",
    Audit = "Audit",
    Production = "Production"
}
declare enum EDetectType {
    Intersect = "intersect",
    Validity = "validity",
    Rule = "rule"
}
declare enum EDivisionType {
    Space = "spaceDivision",
    Relation = "relationDivision"
}
declare enum EDrawingFileType {
    CAD = 7,
    JPG = 2,
    PDF = 3
}
declare enum EEngravingMachineCuttingStatus {
    waiting = "waiting",
    assigned = "assigned",
    running = "running",
    finished = "finished",
    abnormal = "abnormal",
    failed = "failed"
}
declare enum EFace {
    LEFT = "left",
    RIGHT = "right",
    BOTTOM = "bottom",
    TOP = "top",
    BACK = "back",
    FRONT = "front"
}
declare enum EFieldEnum {
    emergencyLevel = 6,
    receiptType = 7
}
declare enum EFinishedProductUnit {
    NONE = "NONE",
    KG = "KG",
    METRE = "METRE",
    SQUARE_METRE = "SQUARE_METRE",
    LITRE = "LITRE"
}
declare enum EFittingType {
    GROOVE = 1,
    HOLE = 2,
    HARDWARE = 3,
    HARDWARE_GROOVE = 4,
    INTERSECTED = "intersected",
    HOLE_V2 = "holeV2",
    SQUARE_GROOVE = "squareGroove",
    ROUND_CORNER_GROOVE = "roundCornerGroove",
    SPECIAL_SHAPED_GROOVE = "specialShapedGroove",
    PATH_GROOVE = "pathGroove"
}
declare enum EGrooveGeometryType {
    SQUARE = "SQUARE",
    ROUND_CORNER = "ROUND_CORNER",
    PATH = "PATH",
    SPECIAL_SHAPED = "SPECIAL_SHAPED"
}
declare enum EIntersectedCreatedType {
    BOTH_PLANK = 0,
    PLANK_FURNITURE = 1
}
declare enum EIntersectedInfoType {
    SHELL = "1",
    THROUGH_SHELL = "3"
}
declare enum EIntersectedType {
    SHELL = "1",
    FACE = "2",
    THROUGH_SHELL = "3"
}
declare enum EIntersectModelType {
    PLANK = 0,
    HARDWARE = 1,
    PRODUCT = 2
}
declare interface ElementId_2 {
    id: string;
    type: ElementType_2;
}
declare interface ElementLabelConfig {
    id: number;
    key: string;
    name: string;
    type: IDP.DB.Types.ElementLabelType;
    options?: string[];
}
declare interface ElementLabelData {
    id: number;
    value: string;
}
declare enum ElementType_2 {
    Wardrobe = "Wardrobe",
    WardrobeCopy = "WardrobeCopy",
    Cabinet = "Cabinet",
    CabinetCopy = "CabinetCopy",
    DoorWindow = "DoorWindow",
    DoorWindowCopy = "DoorWindowCopy",
    CustomGroup = "CustomGroup",
    CabinetRef = "CabinetRef",
    CabinetCopyRef = "CabinetCopyRef",
    WardrobeRef = "WardrobeRef",
    WardrobeCopyRef = "WardrobeCopyRef"
}
declare enum ELineType {
    SEGMENT = 0,
    CIRCLE_ARC = 1,
    ELLIPSE_ARC = 3,
    CIRCLE_CENTER = 4
}
declare enum ENavigatorKey {
    publicLibrary = "publicLibrary",
    privateLibrary = "privateLibrary",
    myLibrary = "myLibrary",
    coohomBrandHall = "coohomBrandHall"
}
declare enum EOpenDirection {
    LEFT = "LEFT",
    RIGHT = "RIGHT",
    TOP = "TOP",
    BOTTOM = "BOTTOM",
    NONE = "NONE"
}
declare enum EOrderEventType {
    sys = 0,
    custom = 1
}
declare enum EOrderFieldDataAuth {
    Visible = "visible",
    Editable = "editable"
}
declare enum EOrderFieldType {
    STR = 0,
    INT = 1,
    FLOAT = 2,
    BOOL = 3
}
declare enum EOrderModeConfigType {
    MODEL_EDITABLE = 0
}
declare enum EOrderOperationType {
    DYNAMIC = 0,
    STATIC = 1
}
declare enum EOrderRemarkType {
    AUDIT = 0,
    ORDER_REMARK = 1,
    DESIGN_REMARK = 2
}
declare enum EParamForm {
    SINGLE = 0,
    RANGE = 1,
    OPTION = 2,
    STEP = 3,
    COMPOUND_FORMULA = 4,
    FIXED_FORMULA = 5,
    FIXED_VALUE = 6,
    COMPOUND_FORMULA_OPTION = 7
}
declare enum EParamModelType {
    COMMON = 0,
    ASSEMBLY = 1,
    TEMPLATE_ASSEMBLY = 2
}
declare enum EParamType {
    FLOAT = "float",
    FLOAT2 = "float2",
    FLOAT3 = "float3",
    FLOAT_BUFFER = "floatbuf",
    FLOAT2_BUFFER = "float2buf",
    FLOAT3_BUFFER = "float3buf",
    REFERENCE = "reference",
    INT = "int",
    INT_BUFFER = "intbuf",
    STRING = "string",
    SHAPE = "shape",
    MATERIAL = "material",
    BOOLEAN = "boolean",
    BOOLEAN_BUFFER = "booleanbuf",
    FIT = "fit",
    PATH = "paramcornerpath",
    STYLE = "style",
    FORMULA_STRING = "formulaString",
    BOOLEAN_LIST = "booleanlist"
}
declare enum EParamUnitType {
    DEFAULT = 0,
    LENGTH = 1,
    AREA = 2,
    NUMBER = 3
}
declare enum EPlankPathLineType {
    SEGMENT = 0,
    CIRCLE_ARC = 1,
    CIRCLE_CENTER = 4
}
declare enum EPlankPathPointType {
    NONE = 0,
    CIRCLE = 1,
    LINE = 2,
    CUT_CIRCLE = 3,
    CIRCLE_CENTER = 5
}
declare enum EPlankRelationType {
    SPLIT = "split",
    ENLARGE = "enlarge",
    MERGE = "merge",
    DEMERGE = "demerge"
}
declare enum EPointType {
    NONE = 0,
    CIRCLE = 1,
    LINE = 2,
    CUT_CIRCLE = 3,
    ELLIPSE = 4,
    CIRCLE_CENTER = 5
}
declare enum EProcessDirect {
    FORWARD = 0,
    REVERSE = 1
}
declare enum EProductDirection {
    XY = 0,
    YZ = 1,
    XZ = 2
}
declare enum EQuotationLibraryType {
    PRIVATE = 0,
    PUBLIC = 1
}
declare enum EReferencePlane {
    XY = "XY",
    YZ = "YZ",
    XZ = "XZ"
}
declare enum EReportFileType {
    PDF = "pdf",
    EXCEL = "excel"
}
declare enum ERoutingFaceDirection {
    horizontal = "horizontal",
    vertical = "vertical"
}
declare enum ETextureDirection {
    HORIZONTAL = "HORIZONTAL",
    VERTICAL = "VERTICAL",
    NONE = "NONE"
}
declare enum EToolType {
    Cabinet = "Cabinet",
    Wardrobe = "Wardrobe",
    DoorWindow = "DW",
    CabinetCopy = "Cabinet_Copy",
    WardrobeCopy = "Wardrobe_Copy",
    DoorWindowCopy = "DW_Copy"
}
declare enum EUploadFileBucket {
    ORDER = "order-mini-app",
    QUOTATION = "report-miniapp"
}
declare type FindFinishedProductOption = DeleteFinishedProductOption | AugmentedRequired<IFindOrDeleteFinishedProductOption, "codes">;
declare type FindMoldingsOption = Pick<IFindMoldingsOption, "orderId" | "productIds"> | Pick<IFindMoldingsOption, "moldingIds"> | Pick<IFindMoldingsOption, "orderIds"> | Pick<IFindMoldingsOption, "codes">;
declare interface FindTopModelsSimpleOption {
    ids?: string[];
    categories?: number[];
    toolType?: EToolType[];
    locked?: boolean;
    hidden?: boolean;
    submitted?: boolean;
    readonly?: boolean;
    exclusive?: boolean;
    child?: boolean;
    accessory?: boolean;
}
declare enum FitBehavior {
    MATCH = "match",
    START = "start",
    END = "end",
    NONE = "none",
    CENTER = "center"
}
declare interface FitBorder {
    model: IParamModelLite;
    face: EFace;
}
declare interface FitRule {
    fitBehavior?: {
        x?: FitBehavior;
        y?: FitBehavior;
        z?: FitBehavior;
    };
    offset?: {
        x?: string;
        y?: string;
        z?: string;
    };
    customSize?: {
        x?: string;
        y?: string;
        z?: string;
    };
}
declare interface FrameHost {
    readonly id: string;
    readonly mounted: boolean;
    mount(mountPointIndex: number): boolean;
    unmount(): void;
    resize(width: number, height: number): void;
    onReady(fn: () => void): void;
    onMessageReceive(fn: (data: any, origin: string) => void): void;
    onResize(fn: (width: number, height: number) => void): void;
    postMessage(data: any, origin?: string): void;
}
declare interface Furniture {
    elementId: IDP.DB.Types.ElementId;
    productId: string;
    position: KPoint3d;
    rotation: KPoint3d;
    size: KPoint3d;
    scale: KPoint3d;
}
declare interface FurnitureCreateInfo {
    productId: string;
    position?: KPoint3d;
    rotation?: KPoint3d;
    scale?: KVector3d;
}
declare interface GenerateReportPageOption {
    data: JsonData;
    templateId: string;
}
declare interface GetModelJsonOptionV2 {
    modelIds: string | string[];
    levelId?: string;
    timeout?: number;
    templateId?: string;
}
declare interface GetModelJsonResultV2 {
    modelId: string;
    url: string;
}
declare interface IAddOrderStateAttachmentOption {
    orderId: string;
    attachments: IOrderAttachment[];
    orderState: number;
}
declare interface IAddOrderStateRemarkOption {
    orderId: string;
    content: string;
    orderState: number;
    images?: IOrderAttachment[];
}
declare interface IAddRefModelOption {
    templateId: string;
    position?: Number3___qunhe_custom_apass_api;
    rotation?: Number3___qunhe_custom_apass_api;
}
declare interface IAssemblyModels {
    id: string;
    name: string;
    obsBrandGoodId: string;
    parameters: IParamModelParameter[];
    paramModelIds: Array<string>;
    size: Number3___qunhe_custom_apass_api;
    position: Number3___qunhe_custom_apass_api;
    rotate: Number3___qunhe_custom_apass_api;
    rotateDegree: Number3___qunhe_custom_apass_api;
    obsCollectBrandGoodId: string;
    price: number;
    assemblyModelType: string;
    remark: string;
    installationCode: string;
    standard: boolean;
    [key: string]: any;
}
declare interface IAssociatedSchema {
    planId: string;
    designId: string;
    [key: string]: any;
}
declare interface IAuditDesignOrders {
    productOrderCode: string;
    productCode: string;
    orderCode: string;
    auditName: string;
    auditCode: string;
    customerName: string;
    obsDesignerUserId: string;
    designerName: string;
    obsAuditorUserId: string;
    auditorName: string;
    typeName: string;
    type: number;
    statusName: string;
    status: number;
    state: string;
    stateName: string;
    productOrderOperations: IProductOrderOperation[];
    bomOperatorName: string;
    obsBomOperatorId: string;
    [key: string]: any;
}
declare interface IAuditOrder {
    auditName: string;
    auditCode: string;
    auditorName: string;
    associatedNumber: string;
    auditOrderOperations: IAuditOrderOperation[];
    userDefinedInfos: IUserDefinedInfo[];
    [key: string]: any;
}
declare interface IAuditOrderOperation extends IOrderOperation {
    [key: string]: any;
}
declare interface IAuditOrderRemarks extends IOrderRemarks {
    images: IOrderAttachment[];
}
declare interface IAuditOrderResult {
    orderReadableId: string;
    auditName?: string;
    auditorName?: string;
    foreignOrderCode?: string;
    remark?: string;
}
declare interface IAuxiliaryBizProperties {
    bizProperties: Record<string, IBizPropertyData>;
}
declare interface IBaseFittingData {
    id: string;
    fittingType: EFittingType;
    params?: IBaseFittingParameters[];
}
declare interface IBaseFittingParameters {
    key: string;
    value: string;
    type: string;
}
declare interface IBaseHoleGrooveData extends IBaseFittingData {
    depth: number;
    plankFaceId: number;
    start: Number3___qunhe_custom_apass_api;
    end: Number3___qunhe_custom_apass_api;
}
declare type IBaseIntersected = (ICommonIntersected<EIntersectedType.FACE> & IPlankFaceId) | (ICommonIntersected<EIntersectedType.SHELL> & IPlankFaceId) | ICommonIntersected<EIntersectedType.THROUGH_SHELL>;
declare interface IBatchGetFittingDataByAuxiliaryOption extends IGetFittingDataByAuxiliaryBaseOption {
    modelIds: string[];
}
declare interface IBizPropertyData {
    key: string;
    name: string;
    namespace: string;
    value: string;
    valueType: string;
}
declare interface IBomAddition extends Required<IBomRoomMetaInfo>, Required<IBomProductMetaInfo>, Required<IBomOrderMetaInfo>, Required<IBomProductionLineInfo> {
}
declare interface IBomArcCurveByBulge {
    type: EBomCurveType.Arc;
    bulge: number;
}
declare interface IBomArcCurveByRadius {
    type: EBomCurveType.Arc;
    isCCW: boolean;
    minorArc: boolean;
    radius: number;
}
declare interface IBomCategoryAttr<T = any> {
    category?: string;
    attributes?: {
        [key: string]: T;
    };
}
declare type IBomCurve = IBomLineSegCurve | IBomArcCurveByBulge | IBomArcCurveByRadius;
declare interface IBomCuttingInfo {
    cuttingWidth: number;
    cuttingHeight: number;
    cuttingProfile: IBomProfile;
}
declare interface IBomDetectionRule {
    ruleType: number;
    ruleName: string;
    ruleDesc: string;
}
declare type IBomFinishedProduct = IBomFinishedProductWithId & AugmentedRequired<IBomFinishedProductEditableByProductId<any>, "attributes" | "category"> & IProductCodeInOrder;
declare interface IBomFinishedProductBaseDataEditable<AttrValueType = string> extends AugmentedRequired<IBomMetaInfo, "name" | "code">, IBomCategoryAttr<AttrValueType> {
    quantity: number;
    packageId?: string;
    unit: EFinishedProductUnit;
    material: string;
    model: string;
    productType: string;
    displayUnit?: string;
    surfaceTechnology?: string;
}
declare type IBomFinishedProductEditable = IBomFinishedProductEditableByModelId<string> | IBomFinishedProductEditableByProductId<string>;
declare interface IBomFinishedProductEditableByModelId<AttrValueType = string> extends IBomFinishedProductBaseDataEditable<AttrValueType>, IBomRoomMetaInfo {
    modelId: string;
}
declare interface IBomFinishedProductEditableByProductId<AttrValueType = string> extends IBomFinishedProductBaseDataEditable<AttrValueType>, Required<IBomRoomMetaInfo> {
    productId: string;
    productName: string;
    orderId: string;
    orderName: string;
}
declare type IBomFinishedProductUpdate = IBomFinishedProductWithId & Partial<IBomFinishedProductEditable>;
declare interface IBomFinishedProductWithId {
    finishedId: string;
}
declare interface IBomGroup<AttrValueType = string> extends IBomGroupBase<AttrValueType> {
    orderId: string;
}
declare interface IBomGroupBase<T = string> extends IBomCategoryAttr<T>, Pick<IBomMetaInfo, "sortingCode" | "codeInERP">, IBomRoomMetaInfo {
    bomId?: string;
    name?: string;
    children?: IBomGroupBase<T>[];
    comment?: string;
    productId?: string;
}
declare interface IBomGroupEditable<T = string> extends IBomCategoryAttr<T>, IBomGroupId, Pick<IBomGroupBase, "sortingCode" | "codeInERP"> {
}
declare type IBomGroupFull<T = any> = AugmentedRequired<Omit<IBomGroup<T>, "children">, "category" | "attributes"> & IBomGroupId & Required<IBomRoomMetaInfo> & IProductCodeInOrder & {
    productName: string;
    orderName: string;
    packageId?: string;
    children?: Array<IBomGroupFull<T>>;
};
declare interface IBomGroupId {
    id: string;
}
declare interface IBomGroupPropertiesClearOption<T extends keyof IBomGroupBase = "sortingCode" | "comment" | "name" | "codeInERP"> {
    ids: string[];
    properties: T[];
}
declare interface IBomHoleOrGrooveProcessingTechnology {
    toolNo?: string;
}
declare interface IBomLineSegCurve {
    type: EBomCurveType.LineSeg;
}
declare interface IBomMetaInfo {
    name?: string;
    code?: string;
    comment?: string;
    packageId?: string;
    sortingCode?: string;
    codeInERP?: string;
    labels?: Record<string, any>;
}
declare type IBomMolding<T = any> = IBomMoldingWithId & AugmentedRequired<IBomMoldingEditable<T>, "attributes" | "category"> & IBomAddition & IProductCodeInOrder;
declare interface IBomMoldingEditable<AttrValueType = string> extends AugmentedRequired<IBomMetaInfo, "name" | "code">, IBomPlankModelId, IBomCategoryAttr<AttrValueType>, IBomRoomMetaInfo {
    packageId?: string;
    moldingType: EBomMoldingType | string;
    cornerTypeLeft: EBomMoldingCornerType;
    cornerTypeRight: EBomMoldingCornerType;
    cornerAngleLeft: number;
    cornerAngleRight: number;
    width: number;
    length: number;
    thickness: number;
    productId?: string;
    productName: string;
    surfaceTechnology?: string;
}
declare interface IBomMoldingWithId {
    moldingId: string;
}
declare interface IBomOrderMetaInfo {
    orderId?: string;
    orderName?: string;
}
declare interface IBomPath<T = IBomCurve> {
    points: number[];
    curves: T[];
}
declare type IBomPlank = IBomPlankWithId & IBomPlankEditable<IBomPlankHole, IBomPlankGroove, any> & IProductCodeInOrder & IBomCuttingInfo & IBomPlankExtraProfiles;
declare interface IBomPlankEdge {
    bandName?: string;
    bandCode?: string;
    bandThickness?: number;
    preMillingThickness?: number;
    universalEnlargeThickness?: number;
    bananaBendEnlargeThickness?: number;
    skeletalDoorEnlargeThickness?: number;
    designEdgeType?: number;
}
declare interface IBomPlankEditable<H = IBomPlankHoleEditable, G = IBomPlankGrooveEditable, AttrValueType = string> extends IBomPlankModelId, AugmentedRequired<IBomMetaInfo, "name" | "code">, IBomCategoryAttr<AttrValueType>, IBomPlankRelation, IBomRoomMetaInfo {
    productId?: string;
    plankType: EBomPlankType;
    doorType?: string;
    material: string;
    backMaterial?: string;
    frontMaterial?: string;
    baseMaterial: string;
    texture?: string;
    textureDirection?: ETextureDirection;
    finishedWidth?: number;
    finishedHeight?: number;
    thickness: number;
    finishedProfile?: IBomProfile;
    edgeBanding?: number[];
    holes?: H[];
    grooves?: G[];
    openDirection?: EOpenDirection;
    hinges?: number[];
    inners?: IBomProfile[];
    backCode?: string;
    fourEdgesBanding?: [
        number,
        number,
        number,
        number
    ];
    edges?: IBomPlankEdge[];
    fourEdges?: IBomPlankFourEdge[];
    stallEdgeIndex?: number;
    surfaceTechnology?: string;
}
declare interface IBomPlankExtraProfile {
    width: number;
    height: number;
    profile: IBomProfile;
}
declare interface IBomPlankExtraProfiles {
    extraProfiles: {
        edgeBanding: IBomPlankExtraProfile;
        edgeBanding_preMilling_universalEnlarge?: IBomPlankExtraProfile;
        edgeBanding_universalEnlarge?: IBomPlankExtraProfile;
        edgeBanding_preMilling_bananaBendEnlarge?: IBomPlankExtraProfile;
        edgeBanding_bananaBendEnlarge?: IBomPlankExtraProfile;
        edgeBanding_preMilling_skeletalDoorEnlarge?: IBomPlankExtraProfile;
        edgeBanding_skeletalDoorEnlarge?: IBomPlankExtraProfile;
    };
}
declare interface IBomPlankFourEdge {
    bandThickness?: number;
    preMillingThickness?: number;
    designEdgeType?: number;
}
declare type IBomPlankFull = Required<IBomPlank> & IBomAddition;
declare type IBomPlankGroove = IBomPlankGrooveWithId & IBomPlankGrooveEditable;
declare interface IBomPlankGrooveEditable extends IBomPlankHoleOrGrooveShare {
    start?: Number3___qunhe_custom_apass_api;
    end?: Number3___qunhe_custom_apass_api;
    plankFaceId: string;
    width?: number;
    type: EBomPlankGrooveType;
    geometryType?: EGrooveGeometryType;
    profile?: IBomProfile;
    radius?: number;
    path?: IBomPath;
}
declare interface IBomPlankGrooveWithId extends IBomPlankWithId {
    grooveId: string;
}
declare type IBomPlankHole = IBomPlankHoleWithId & IBomPlankHoleEditable;
declare interface IBomPlankHoleEditable extends IBomPlankHoleOrGrooveShare {
    start: Number3___qunhe_custom_apass_api;
    end: Number3___qunhe_custom_apass_api;
    plankFaceId: string;
    type: EBomPlankHoleType;
    diameter: number;
}
declare interface IBomPlankHoleOrGrooveShare {
    depth: number;
    name: string;
    processingTechnology?: IBomHoleOrGrooveProcessingTechnology;
    extra?: Record<string, any>;
}
declare interface IBomPlankHoleWithId extends IBomPlankWithId {
    holeId: string;
}
declare interface IBomPlankLayoutArrange extends IBomPlankWithId, IBomRawPlankMeta, IBomPlankLayoutArrangeMeta {
}
declare interface IBomPlankLayoutArrangeMeta {
    rotation: number;
    flipX: boolean;
    flipY: boolean;
    translation: IBomPlankLayoutTranslation;
}
declare interface IBomPlankLayoutProcessAttribute extends IBomRawPlankMeta {
    plankSpacing: number;
}
declare interface IBomPlankLayoutTranslation {
    x: number;
    y: number;
}
declare interface IBomPlankMetaInfo {
    material: string;
    baseMaterial: string;
    thickness: number | string;
    width: number | string;
    height: number | string;
}
declare interface IBomPlankModelId {
    modelId: string;
}
declare interface IBomPlankRelation {
    relation?: {
        relationType: EPlankRelationType;
        relationData: IBomPlankEditable[];
        technology?: string;
    };
}
declare type IBomPlankUpdate = IBomPlankWithId & Partial<Omit<IBomPlankEditable<IBomPlankHole, IBomPlankGroove, string>, "relation">>;
declare interface IBomPlankWithId {
    plankId: string;
}
declare interface IBomProductionLineInfo {
    productionLineOrderId?: string;
    productionLineName?: string;
    productionLineOrderNo?: string;
}
declare interface IBomProductMetaInfo {
    productId?: string;
    productName?: string;
}
declare interface IBomProfile<T = IBomCurve> {
    points: number[];
    curves: T[];
    name?: string;
}
declare interface IBomPropertiesClearOption<T extends keyof IBomMetaInfo = keyof Omit<IBomMetaInfo, "code">> {
    ids: string[];
    properties: T[];
}
declare type IBomRawPlank = IBomRawPlankEditable & IBomRawPlankWithId;
declare interface IBomRawPlankEditable extends Omit<IBomMetaInfo, "code" | "sortingCode" | "codeInERP" | "labels">, IBomPlankMetaInfo {
    quantity: number;
    mergeId: string;
    model: string;
}
declare interface IBomRawPlankLayoutResult {
    averageRate: number;
    layouts: Array<AugmentedOptional<IBomPlankLayoutProcessAttribute, "plankSpacing"> & {
        rate: number;
        width: number;
        height: number;
        thickness?: number;
        rawPlankArea: number;
        sumOfPlankArea: number;
        planks: Array<IBomPlankWithId & IBomPlankLayoutArrangeMeta & {
            profile: IBomProfile<IBomLineSegCurve | IBomArcCurveByBulge>;
            layoutId: string;
        }>;
    }>;
}
declare interface IBomRawPlankMeta {
    rawPlankIndex: number;
    rawPlankType: EBomRawPlankType;
    rawPlankId: string;
}
declare interface IBomRawPlankWithId {
    id: string;
}
declare interface IBomRoomMetaInfo {
    roomId?: string;
    roomName?: string;
}
declare type IBomStructure = IBomStructureWithId & Required<IBomStructureEditable<IBomPlankHole, IBomPlankGroove, any>> & Required<IProductCodeInOrder> & IBomAddition;
declare interface IBomStructureEditable<H = IBomPlankHoleEditable, G = IBomPlankGrooveEditable, AttrValueType = string> extends AugmentedRequired<IBomMetaInfo, "name" | "code">, IBomCategoryAttr<AttrValueType>, IBomRoomMetaInfo {
    modelId: string;
    productId?: string;
    material: string;
    baseMaterial: string;
    textureDirection?: ETextureDirection;
    length: number;
    width: number;
    thickness: number;
    holes?: H[];
    grooves?: G[];
    surfaceTechnology?: string;
    referencePlane: EReferencePlane;
}
declare type IBomStructureUpdate = IBomStructureWithId & Partial<Omit<IBomStructureEditable<IBomPlankHole, IBomPlankGroove, string>, keyof IBomCategoryAttr | keyof IBomRoomMetaInfo | "modelId" | "productId">>;
declare interface IBomStructureWithId {
    structureId: string;
}
declare type IBomSurplusPlank = IBomSurplusPlankEditable & IBomSurplusPlankWithId;
declare interface IBomSurplusPlankEditable extends Omit<IBomMetaInfo, "code" | "sortingCode" | "codeInERP" | "labels">, IBomPlankMetaInfo {
    fromMergeId: string;
    usedMergeId: string;
}
declare interface IBomSurplusPlankWithId {
    id: string;
}
declare interface IBzParamModelLiteProperty<T extends number | string | boolean = string> {
    namespace: string;
    name: string;
    value: string | undefined;
    type: EBzPropertyType | string;
    readonly: boolean;
    getName(): string;
    getNamespace(): string;
    getType(): string;
    getValue(): T | undefined;
    setValue(value: T): void;
}
declare interface ICategory {
    code: string;
    name: string;
    description?: string;
}
declare interface ICategoryAttribute {
    name: string;
    description?: string;
    type: "STRING" | "INTEGER" | "FLOAT" | "TIME";
    rules: ICategoryAttributeRule[];
}
declare interface ICategoryAttributeRule {
    notNull?: boolean;
    enumValues?: string[];
}
declare interface ICheckBomResult {
    productId: string;
    productName?: string;
    planks?: {
        plankId: number;
        plankName: string;
    }[];
    ruleType: number;
    interceptType: EBomDetectionInterceptType;
    hit: boolean;
    checkTime: number;
}
declare type ICheckBomsOptionV1 = {
    orderId: string;
    eventKey: string;
} | {
    orderId: string;
    productIds?: string[];
};
declare interface ICheckOperatePermissionOption {
    eventKey?: string;
    operationKey: string;
    orderId?: string;
}
declare interface ICheckOrderModePermissionOption {
    orderId: string;
    mode: EOrderModeConfigType;
    action: string;
}
declare interface ICombinationData {
    name: string;
    brandGoodId: number;
    paramModelIds: Array<string>;
    size: Number3___qunhe_custom_apass_api;
    rotate: Number3___qunhe_custom_apass_api;
    position: Number3___qunhe_custom_apass_api;
    collectBrandGoodId: number;
    assemblyModelType: string;
    id: string;
    [key: string]: any;
}
declare interface ICommonIntersected<T extends EIntersectedType> {
    points: Number3___qunhe_custom_apass_api[];
    linkedModelId: string;
    linkedParentModelId: string;
    "@type": T;
    shell?: string;
    needGroove?: boolean;
    referencePlane?: EReferencePlane;
}
declare interface ICreateAfterSaleOrderFromBomOption {
    orderName: string;
    receiptType: number;
    parentOrderId: string;
    bomMaterialIds: string[];
    bomGroupRootIds: string[];
}
declare type ICreateAfterSaleOrderFromDesignOption = ICreateOrderOption;
declare interface ICreateCustomerOption {
    customerName: string;
    customerPhone: string;
    customerAddr?: string;
    storeId?: string;
}
declare interface ICreateCustomerResult {
    customerId: string;
}
declare interface ICreateCustomModelSimpleOption extends Partial<ICustomModelSimpleChangeable> {
    productId: string;
}
declare interface ICreateOrderOption {
    orderName: string;
    externalCode?: string;
    orderType: EToolType[];
    customerId: string;
    customerName: string;
    customerPhone: string;
    customerAddr?: string;
    storeId?: string;
    storeName?: string;
    contactUser?: string;
    contactPhone?: string;
    shippingAddr?: string;
    remark?: string;
    modelIds: string[];
    customFields?: Record<string, string | boolean | number>;
    designAttachments?: IDesignAttachments[];
    emergencyLevel?: number;
    receiptType?: number;
    parentOrderId?: string;
    subModels?: ISubModel[];
    designRemark?: string;
    attachments?: IDesignAttachments[];
    repetitions?: number;
}
declare interface ICreateOrderResult {
    orderId: string;
    orderNo: string;
}
declare interface ICreateOrdersResult {
    successfulOrders: ISuccessfulOrder[];
    failedOrders: IFailedOrder[];
}
declare type ICreateTemplateOption = {
    modelId: string;
} | {
    model: IParamModelLite;
    toolType?: EToolType;
};
declare interface ICurve {
    type: string;
    bulge?: number;
    ellipse?: IEllipse;
}
declare interface ICustomEnterAppMode {
    mode: ECustomAppMode;
    [s: string]: any;
}
declare interface ICustomEnterAppModeAsync {
    mode: ECustomAppMode | ECustomAppModeAsync;
    [s: string]: any;
}
declare interface ICustomerBaseInfo {
    customerName: string;
    customerPhone: string;
    customerAddr: string;
}
declare interface ICustomerInfo extends ICustomerBaseInfo {
    customerId: string;
    storeName: string;
    creatorName: string;
}
declare interface ICustomerOrderDetail {
    templateKey: string;
    orderId: string;
    orderReadableId: string;
    orderName: string;
    customerName: string;
    customerPhoneNumber: string;
    customerArea?: string;
    customerAddress?: string;
    storeName?: string;
    buildingArea?: string;
    buildingAddress?: string;
}
declare interface ICustomField {
    fieldId: string;
    fieldName: string;
    customFieldValue: string;
    obsBrandGoodId: string;
    [key: string]: any;
}
declare interface ICustomModelAssemblySimple extends Omit<ICustomModelPureSimple, "productId" | "productName" | "category"> {
    productId?: string;
    productName?: string;
    children: ICustomModelPureSimple[];
}
declare interface ICustomModelFilter {
    locked?: boolean;
    hidden?: boolean;
    submitted?: boolean;
    readonly?: boolean;
    exclusive?: boolean;
}
declare interface ICustomModelLiteInfo {
    id: string;
    toolType: EToolType;
    name: string;
    roomInfo: IRoomInfo;
    isSplit: boolean;
    isLocked: boolean;
    isHidden: boolean;
    isAudited: boolean;
}
declare interface ICustomModelPureSimple extends ICustomModelSimpleChangeable {
    elementId: ElementId_2;
    toolType: EToolType;
    productId: string;
    productName: string;
    category: number;
}
declare interface ICustomModelSimpleChangeable {
    name: string;
    position: Number3___qunhe_custom_apass_api;
    rotation: Number3___qunhe_custom_apass_api;
    size: Number3___qunhe_custom_apass_api;
}
declare interface IDeleteAttachmentOption {
    uploadKey: string;
    orderId: string;
}
declare interface IDeleteMoldingsOption {
    orderId: string;
    productIds: string[];
    moldingIds: string[];
    orderIds: string[];
}
declare interface IDeleteOrderRemarkOption {
    remarkId: string;
    orderId?: string;
}
declare interface IDeleteOrderStateAttachmentOption {
    uploadKey: string;
    orderId: string;
    orderState: number;
}
declare interface IDesignAttachments {
    uploadKey: string;
    name: string;
    url: string;
}
declare interface IDesignData {
    roomDataList: IRoomData[];
    paramModelIds: Array<string>;
    levelIndex: string;
    combinationDataList: ICombinationData[];
    [key: string]: any;
}
declare interface IDesignExportData {
    paramModel: IExportParamModelData[];
    resource: IResource;
    partnerOrder: IPartnerOrder;
    designData: IDesignData;
    assemblyModels: IAssemblyModels[];
    auditDesignOrders: IAuditDesignOrders[];
    auditOrder: IAuditOrder;
    jsonInfo: IJsonInfo;
    toolTypes: Array<number>;
    [key: string]: any;
}
declare interface IDesignExportDataParams {
    levelId?: string;
    toolType?: EToolType;
    roomId?: string;
    timeout?: number;
    templateId?: string;
    modelIds?: string | string[];
}
declare interface IDesignExportDataResponse<T = IDesignExportData | null> {
    code: EDesignExportCode;
    message: string;
    data: T;
}
declare type IDesignExportUrlDataResponse = IDesignExportDataResponse<{
    url: string;
} | null>;
declare interface IDesignExportXmlDataParams {
    elements?: Array<ElementId_2>;
    levelId?: string;
    toolType?: EToolType;
    timeout?: number;
}
declare interface IDesignOrderListOption {
    orderState?: number;
}
declare type IDesignOrderListResult = IOrderBaseInfo & {
    processDirect: EProcessDirect;
};
declare type IDesignOwnerDeatils = IUserDetails;
declare interface IDetectBaseResult<T> {
    status: ECheckModelsStatus;
    hasPermission: boolean;
    error?: any;
    result?: T;
}
declare type IDetectConfig = Partial<Record<EToolType, EDetectType[]>>;
declare type IDetectItem<T> = Partial<Record<EDetectType, T>>;
declare interface IDetectOption {
    toolType?: EToolType;
}
declare interface IDetectResult {
    result?: boolean;
    errMsg?: string;
}
declare interface IDoorCoveringHardwareMode {
    hardwareModel: "edit" | "add";
    selectTarget: "door" | "cabinet";
}
export declare namespace IDP {
    export namespace Platform {
    }
}
export declare namespace IDP {
    export namespace Host {
        export function postMessage(data: any): void;
        export function onMessageReceive(callback: (data: any) => void): void;
    }
}
export declare namespace IDP {
    export namespace Miniapp {
        const view: View;
        export function exit(): void;
    }
}
export declare namespace IDP {
    export namespace UI {
        export interface ClientRect {
            top: number;
            left: number;
            bottom: number;
            right: number;
            width: number;
            height: number;
        }
        export interface Layout {
            windowWidth: number;
            windowHeight: number;
            topBar: ClientRect;
            bottomBar: ClientRect;
            leftPanel: ClientRect;
        }
        export function onThemeChange(fn: (theme: Theme) => void): void;
        export function hideAll(): void;
        export function computeLayout(): Layout;
        const toast: Toast;
        const theme: Theme;
    }
}
export declare namespace IDP {
    export namespace Design {
        export function save(): Promise<void>;
    }
}
export declare namespace IDP {
    export namespace Scene3D {
    }
}
export declare namespace IDP {
    export namespace Platform {
    }
    export namespace UI {
    }
    export namespace Design {
    }
    export namespace User {
    }
    export namespace DB {
        export namespace Types {
        }
    }
    export namespace Interaction {
        export function getSelectedElements(): IDP.DB.Types.ElementId[];
        export function setSelectedElements(option: IDP.DB.Types.ElementId[]): IDP.DB.Types.ElementId[];
    }
    export interface EventTypes {
    }
    export namespace Events {
    }
}
export declare namespace IDP {
    export namespace Platform {
        export enum AppMode {
            DefaultMode = "DefaultMode",
            SuperFloorplanMode = "SuperFloorplanMode",
            CustomMode = "CustomMode",
            UnknownMode = "UnknownMode"
        }
        export function getAppMode(): AppMode;
        export function getLocale(): Locale;
        export enum LengthUnitType {
            mm,
            m,
            ft,
            in
        }
        export function getGlobalLengthUnit(): LengthUnitType;
        export function getGlobalLengthPrecision(): string;
    }
    export namespace Events {
        export function on<T extends keyof IDP.EventTypes>(eventType: T, callback: (data: IDP.EventTypes[T]) => void): void;
        export function once<T extends keyof IDP.EventTypes>(eventType: T, callback: (data: IDP.EventTypes[T]) => void): void;
        export function off<T extends keyof IDP.EventTypes>(eventType: T, callback: (data: IDP.EventTypes[T]) => void): void;
    }
    export namespace Design {
        export interface Level {
            id: string;
            name: string;
            index: number;
        }
        export function getDesignId(): string | undefined;
        export function getName(): string | undefined;
        export function getAllLevels(): Level[];
        export function getCurrentLevel(): Level | undefined;
        export function changeCurrentLevelAsync(levelId: string): Promise<void>;
    }
    export namespace User {
        export function getUserId(): string;
    }
    export namespace DB {
        export namespace Types {
            export enum ElementType {
                Furniture = "Furniture",
                Wardrobe = "Wardrobe",
                WardrobeCopy = "WardrobeCopy",
                Cabinet = "Cabinet",
                CabinetCopy = "CabinetCopy",
                DoorWindow = "DoorWindow",
                DoorWindowCopy = "DoorWindowCopy",
                Skirting = "Skirting",
                Paving = "Paving",
                CustomGroup = "CustomGroup",
                ModelDoorWindow = "ModelDoorWindow",
                ModelMolding = "ModelMolding",
                MixGroup = "MixGroup",
                Wall = "Wall",
                Room = "Room",
                FurnitureLegend = "FurnitureLegend",
                ParamLegend = "ParamLegend",
                LegendGroup = "LegendGroup",
                CabinetRef = "CabinetRef",
                CabinetCopyRef = "CabinetCopyRef",
                WardrobeRef = "WardrobeRef",
                WardrobeCopyRef = "WardrobeCopyRef",
                CabinetSnapper = "CabinetSnapper",
                CabinetCopySnapper = "CabinetCopySnapper",
                WardrobeSnapper = "WardrobeSnapper",
                WardrobeCopySnapper = "WardrobeCopySnapper",
                CabinetSnapperGroup = "CabinetSnapperGroup",
                CabinetCopySnapperGroup = "CabinetCopySnapperGroup",
                WardrobeSnapperGroup = "WardrobeSnapperGroup",
                WardrobeCopySnapperGroup = "WardrobeCopySnapperGroup",
                FrozenModel = "FrozenModel",
                RoutingFace = "RoutingFace",
                RoutingFaceGroup = "RoutingFaceGroup",
                RoutingCube = "RoutingCube",
                RoutingSocket = "RoutingSocket"
            }
            export interface ElementId {
                id: string;
                type: ElementType;
            }
        }
    }
    export interface EventTypes {
        "IDP.Interaction.SelectedElementsChange": IDP.DB.Types.ElementId[];
        "IDP.Custom.Design.CustomModel.Add": IDP.DB.Types.ElementId[];
        "IDP.Custom.Design.CustomModel.Delete": IDP.DB.Types.ElementId[];
        "IDP.DB.Element.Add": IDP.DB.Types.ElementId[];
        "IDP.DB.Element.Delete": IDP.DB.Types.ElementId[];
        "IDP.Design.Save": void;
        "IDP.Custom.Design.Loaded": void;
        "IDP.UI.Layout.WindowResize": void;
        "IDP.Miniapp.View.ContainerMinimizedChange": {
            frameId: string;
            isMinimized: boolean;
        };
        "IDP.Design.Saved": void;
    }
    export namespace Miniapp {
        export function getUploadedDataAsync(): Promise<any>;
        export function uploadDataAsync(option: MiniappUploadDataOption): Promise<MiniappUploadDataResult | undefined>;
    }
}
export declare namespace IDP {
    export namespace Math {
        const KGeomLib: KGeomLib_2;
        const KCurve2dType: typeof KCurve2dType_2;
        const KCurve3dType: typeof KCurve3dType_2;
        const KPtInLoopType: typeof KPtInLoopType_2;
        const KCurveSurfaceIntersectType: typeof KCurveSurfaceIntersectType_2;
        const KSurfaceType: typeof KSurfaceType_2;
        const KCurveInLoopType: typeof KCurveInLoopType_2;
        const KFaceBooleanType: typeof KFaceBooleanType_2;
        export type KBoundedCurve2d = KBoundedCurve2d_2;
        export type KBoundedCurve3d = KBoundedCurve3d_2;
        export type KLineSegment3d = KLineSegment3d_2;
        export type KGeomFace2d = KGeomFace2d_2;
        export type KLine3d = KLine3d_2;
        export type KArc3d = KArc3d_2;
        export type KFace3d = KFace3d_2;
    }
}
export declare namespace IDP {
    export namespace DB {
        export namespace Types {
        }
        export namespace Methods {
        }
    }
}
export declare namespace IDP {
    export namespace Interaction {
        export function startDragProductAsync(productId: string): Promise<StartDragProductPromiseResult>;
    }
    export namespace DB {
        export namespace Types {
            export enum ModelDoorWindowType {
                Window = 1,
                FrenchWindow,
                BayWindow,
                Door,
                DoubleDoor,
                SlidingDoor
            }
        }
        export namespace Methods {
            export function createFurniture(furnitureInfo: FurnitureCreateInfo): Promise<PromiseResultWithUuid>;
            export function deleteFurniture(elementId: IDP.DB.Types.ElementId): boolean;
            export function getFurniture(elementId: IDP.DB.Types.ElementId): Furniture | undefined;
            export function getAllModelDoorWindowList(): ModelDoorWindow[];
            export function getModelDoorWindow(elementId: IDP.DB.Types.ElementId): ModelDoorWindow | undefined;
            export function deleteModelDoorWindow(elementId: IDP.DB.Types.ElementId): boolean;
            export function createModelDoorWindowAsync(modelDoorWindowInfo: ModelDoorWindowCreateInfo): Promise<PromiseResult>;
        }
    }
    export namespace UI {
    }
    export namespace Platform {
    }
}
export declare namespace IDP {
    export namespace DB {
        export namespace Methods {
        }
    }
}
export declare namespace IDP {
    export namespace DB {
        export namespace Types {
            export interface GrepFace {
                readonly id: string;
                readonly geometry: KFace3d_2;
                readonly roomIds: IDP.DB.Types.ElementId[];
            }
            export interface Wall {
                readonly id: IDP.DB.Types.ElementId;
                readonly profile2d: KGeomFace2d_2[];
                readonly geometry3d: GrepFace[];
            }
            export interface Opening {
                readonly id: IDP.DB.Types.ElementId;
                readonly hostId: IDP.DB.Types.ElementId;
                readonly profile2d: KGeomFace2d_2[];
            }
            export interface Room {
                readonly id: IDP.DB.Types.ElementId;
                readonly type: number;
                readonly name: string;
                readonly profile2d: KGeomFace2d_2[];
            }
        }
        export namespace Methods {
            export function getAllWallList(): IDP.DB.Types.Wall[];
            export function getAllRoomList(): IDP.DB.Types.Room[];
            export function getAllOpeningList(): IDP.DB.Types.Opening[];
        }
    }
    export namespace Design {
    }
    export namespace Interaction {
    }
}
export declare namespace IDP {
    export namespace Custom {
        export namespace DoorWindow {
        }
    }
}
export declare namespace IDP {
    export namespace Design {
        export function copyAsync(): Promise<{
            designId: string;
        }>;
    }
    export interface EventTypes {
        "IDP.Custom.Mode.Enter": ECustomAppMode;
        "IDP.Custom.Mode.Exit": ECustomAppMode;
        "IDP.Custom.DoorCoveringHardware.Mode": IDoorCoveringHardwareMode;
        "IDP.Custom.DoorCoveringHardware.SelectedElementsChange": IDP.DB.Types.ElementId[];
        "IDP.Custom.Design.CustomModel.Update": IDP.DB.Types.ElementId[];
        "IDP.Integration.FOP.OrderMode.OrderModel.Update": Pick<IFindAuditedModelResult, "modelIds" | "subModels">;
        "IDP.Custom.Design.CustomModel.UpdateV2": IDP.DB.Types.ElementId[];
        "IDP.Custom.Design.CustomModel.AddV2": IDP.DB.Types.ElementId[];
        "IDP.Custom.Design.CustomModel.DeleteV2": IDP.DB.Types.ElementId[];
        "IDP.Custom.LeftPanel.Style.Selected": IProduct;
    }
    export namespace Custom {
        export namespace Common {
            export interface OpenOrCloseOption {
                clientId?: string;
                timeout?: number;
            }
            export type CustomElementId = ElementId_2;
            const ToolType: typeof EToolType;
            const DesignType: typeof EDesignType;
            export function openTerminalAsync(option?: OpenOrCloseOption): Promise<void>;
            export function closeTerminalAsync(option?: OpenOrCloseOption): Promise<void>;
            export function getCurrentToolType(): EToolType | undefined;
            export function getDesignType(): EDesignType | undefined;
            export function getOrderDesignId(): string | undefined;
            export function isDesignLoaded(): boolean;
        }
        export namespace InnerSpace {
            const Face: typeof EFace;
            const selection: IEvent<IInnerSpaceData | undefined>;
            export function getSelected(): IInnerSpaceData | undefined;
        }
        export namespace DoorCoveringHardware {
            export interface SetOption {
                productId: string;
            }
            export function getOption(): IDoorCoveringHardwareMode;
            export function setOption(option: IDoorCoveringHardwareMode): void;
            export function setProductId(option: SetOption): void;
            export function getSelectedElements(): IDP.DB.Types.ElementId[];
        }
        export namespace Mode {
            const AppMode: typeof ECustomAppMode;
            export function current(): ECustomAppMode | null;
            export function enter(mode: ICustomEnterAppMode): void;
            export function enterAsync(mode: ICustomEnterAppModeAsync): Promise<void>;
            export function exit(): void;
        }
        export namespace Design {
            export namespace Export {
                const DesignExportCode: typeof EDesignExportCode;
                const PointType: typeof EPointType;
                const ClockWise: typeof EClockWise;
                const LineType: typeof ELineType;
                const IntersectModel: typeof EIntersectModelType;
                const ProductDirection: typeof EProductDirection;
                const IntersectedInfoType: typeof EIntersectedInfoType;
                export interface ModelJson {
                    paramModel: IExportParamModelData[];
                }
                export function getDesignJsonAsync(option?: IDesignExportDataParams): Promise<IDesignExportDataResponse>;
                export function getDesignFullJsonAsync(option?: IDesignExportDataParams): Promise<IDesignExportDataResponse>;
                export function getModelJsonAsync(option: IGetModelJsonOption): Promise<IDP.Custom.Design.Export.ModelJson | null>;
                export function getDesignJsonUrlAsync(option?: IDesignExportDataParams): Promise<IDesignExportDataResponse<{
                    url: string;
                }>>;
                export function getDesignFullJsonUrlAsync(option?: IDesignExportDataParams): Promise<IDesignExportDataResponse<{
                    url: string;
                }>>;
                export function getDesignXmlUrlAsync(option?: IDesignExportXmlDataParams): Promise<IDesignExportUrlDataResponse>;
                export function getQuotation(option: IQuotationOption): IQuotationResult;
                export function getQuotationTemplateAsync(option?: IQuotationReportTemplateParam): Promise<{
                    result: IQuotationReportTemplateItem[];
                }>;
                export function getModelJsonAsyncV2(option: GetModelJsonOptionV2): Promise<GetModelJsonResultV2 | GetModelJsonResultV2[]>;
                export function getFittingDataFromAuxiliaryAsync(option: IGetFittingDataByAuxiliaryOption): Promise<IGetFittingDataByAuxiliaryResult[]>;
                export function getFittingDatasFromAuxiliaryAsync(option: IBatchGetFittingDataByAuxiliaryOption): Promise<IGetFittingDataByAuxiliaryResult[][]>;
                export function getIntersectedDataAsync(option: IGetIntersectedOption): Promise<IIntersectedData>;
            }
            export namespace CustomModel {
                const ParamType: typeof EParamType;
                const ParamModelType: typeof EParamModelType;
                const BzPropertyType: typeof EBzPropertyType;
                export type CustomModel = IParamModelLite;
                export type CustomModelParam<T = any> = IParamModelLiteParam<T>;
                export type CustomModelProperty<T extends number | string | boolean = any> = IBzParamModelLiteProperty<T>;
                export function newCustomModelByCategoryAsync(category: number): Promise<CustomModel | undefined>;
                export function newCustomModelByProductIdAsync(productId: string): Promise<CustomModel | undefined>;
                export function getCustomModelByModelIdAsync(id: string): Promise<CustomModel | undefined>;
                export function findTopModelsAsync(options?: IListTopModelsOptions): Promise<CustomModel[]>;
                export function insertAsync(options: {
                    models: CustomModel[];
                }): Promise<void>;
                export function updateAsync(options: {
                    models: CustomModel[];
                }): Promise<void>;
                export function deleteTopModelsAsync(options: {
                    models: CustomModel[];
                }): Promise<void>;
                export function getPreviewImgAsync(option: IGetPreviewImgOption): Promise<IPreviewImgInfo>;
                export function getTopModelsLiteInfoAsync(option: IGetTopModelsLiteInfoOption): Promise<{
                    count: number;
                    page: number;
                    size: number;
                    data: ICustomModelLiteInfo[];
                }>;
                export function highlightModels(option: {
                    modelIds: string[];
                }): void;
                export function findTopModelsSimpleAsync(option?: FindTopModelsSimpleOption): Promise<CustomModelSimpleData[]>;
                export function lockModel(option: IModelLockOption): void;
                export function unlockModel(option: IModelLockOption): void;
                export function computeInnerSpacesAsync(id: string): Promise<IInnerSpaceWithBorders[]>;
            }
            export function splitDesignByElementsAsync(option: ISplitDesignParams): Promise<ISplitDesignResponse>;
        }
        export namespace Product {
            export function startDragProductAsync(productId: string, option?: {
                isAccessory?: boolean;
            }): Promise<DragCustomProductPromiseResult>;
            export function isEnterpriseLibraryModel(id: string): boolean;
            export function importProductAsync(option: IImportModel): Promise<IImportModelResult>;
        }
        export namespace Drawing {
            const DivisionType: typeof EDivisionType;
            const FileType: typeof EDrawingFileType;
            export function getConstructionDrawingByElementsAsync(option: IDrawingExportParams): Promise<IDrawingExportResponse[]>;
            export function enterCustomDrawingAsync(configId: string): Promise<void>;
        }
        export namespace FittingDesign {
            const IntersectModel: typeof EIntersectModelType;
            const ProductDirection: typeof EProductDirection;
            const IntersectedInfoType: typeof EIntersectedInfoType;
            export function getIntersectedDataAsync(option: IGetIntersectedOption): Promise<IIntersectedData>;
            export function getDesignDataAsync(option: IFittingDesignOption): Promise<IFittingDesignData | null>;
            export function saveDesignDataAsync(option: ISaveFittingDesignOption): Promise<void>;
            export function deleteDesignDataAsync(option: IFittingDesignOption): Promise<void>;
            export function findDesignDataAsync(ids: string[]): Promise<IFittingDesignData[]>;
            export function saveDesignDatasAsync(option: ISaveFittingDesignOption[]): Promise<void>;
            export function deleteDesignDatasAsync(ids: string[]): Promise<void>;
        }
        export namespace FittingDesignV2 {
            export function putDesignDataAsync(option: IFittingDesignDataV2<false>[]): Promise<IFittingDesignDataV2<true>[]>;
            export function findDesignDataAsync(ids: string[]): Promise<IFittingDesignDataV2<true>[]>;
            export function deleteDesignDataAsync(ids: string[]): Promise<void>;
        }
        export namespace Detection {
            const DetectType: typeof EDetectType;
            export function checkIntersectionAsync(option?: IIntersectCheckOption): Promise<IIntersectCheckResult[]>;
            export function detectAsync(config: IDetectItem<boolean>, option?: IDetectOption): Promise<IDetectItem<IDetectResult>>;
            export function detectByModelIdAsync(config: IDetectItem<boolean>, modelIds: string[]): Promise<IDetectItem<IDetectResult>>;
            export function getDetectConfig(): IDetectConfig;
        }
        export namespace Order {
            export type CustomerOrderDetail = ICustomerOrderDetail;
            export function getRelatedOrderAsync(orderDesignId: string): Promise<IRelatedOrderResult>;
            export function getCustomerOrderAsync(option: IGetCustomerOrderOption): Promise<CustomerOrderDetail | undefined>;
            export function getAuditOrderAsync(orderId: string): Promise<IAuditOrderResult>;
            export function openOrderDetailPanel(orderReadableId: string): void;
        }
        export namespace Group {
            export function getGroupDataAsync(): Promise<IGroupData | undefined>;
            export function updateGroupDataAsync(updateData: IGroupData): Promise<undefined>;
        }
        export namespace InstallCode {
            export function attachInstallDataAsync(installData: IInstallData): Promise<undefined>;
            export function getInstallCodeAsync(option: {
                toolType?: EToolType;
                timeout?: number;
            }): Promise<Record<string, string>>;
        }
        export namespace LeftPanel {
        }
        export namespace BoolModeling {
            export function triggerBoolEffectAsync(modelId: string): Promise<ITriggerBoolEffectResult>;
        }
        export namespace UI {
            export function openStyleExtensionAsync(option: ILeftPanelStyleExtensionOptions): Promise<void>;
        }
    }
    export namespace Product {
        export function getProductAsync(productId: string): Promise<IProduct>;
        export function getProductsAsync(productIds: string[]): Promise<Record<string, IProduct>>;
        export function findCustomFoldersAsync(options: IFindCustomFoldersOptions): Promise<IFolder[]>;
        export function findCustomProductsAsync(options: IFindCustomProductsOptions): Promise<{
            list: IProduct[];
            totalCount: number;
        }>;
        export function getProductDefaultBuildAsync(options: IGetProductDefaultBuildOption): Promise<string>;
    }
    export namespace DB {
        export namespace Types {
            export interface CustomGroup {
                elementId: IDP.DB.Types.ElementId;
                subElements: IDP.DB.Types.ElementId[];
            }
        }
        export namespace Methods {
            export function getCustomGroup(eid: IDP.DB.Types.ElementId): IDP.DB.Types.CustomGroup | undefined;
            export function createCabinetAsync(option: ICreateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId>;
            export function updateCabinetAsync(option: IUpdateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId | undefined>;
            export function deleteCabinetAsync(elementId: IDP.DB.Types.ElementId): Promise<boolean>;
            export function getCabinetAsync(elementId: IDP.DB.Types.ElementId): Promise<CustomModelSimple | undefined>;
            export function findCabinetListAsync(): Promise<CustomModelSimple[]>;
            export function findCabinetRefListAsync(): Promise<IRefModelLite[]>;
            export function createCabinetCopyAsync(option: ICreateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId>;
            export function updateCabinetCopyAsync(option: IUpdateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId | undefined>;
            export function deleteCabinetCopyAsync(elementId: IDP.DB.Types.ElementId): Promise<boolean>;
            export function getCabinetCopyAsync(elementId: IDP.DB.Types.ElementId): Promise<CustomModelSimple | void>;
            export function findCabinetCopyListAsync(): Promise<CustomModelSimple[]>;
            export function findCabinetCopyRefListAsync(): Promise<IRefModelLite[]>;
            export function createWardrobeAsync(option: ICreateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId>;
            export function updateWardrobeAsync(option: IUpdateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId | void>;
            export function deleteWardrobeAsync(elementId: IDP.DB.Types.ElementId): Promise<boolean>;
            export function getWardrobeAsync(elementId: IDP.DB.Types.ElementId): Promise<CustomModelSimple | void>;
            export function findWardrobeListAsync(): Promise<CustomModelSimple[]>;
            export function createWardrobeCopyAsync(option: ICreateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId>;
            export function updateWardrobeCopyAsync(option: IUpdateCustomModelSimpleOption): Promise<IDP.DB.Types.ElementId | void>;
            export function deleteWardrobeCopyAsync(elementId: IDP.DB.Types.ElementId): Promise<boolean>;
            export function getWardrobeCopyAsync(elementId: IDP.DB.Types.ElementId): Promise<CustomModelSimple | void>;
            export function findWardrobeCopyListAsync(): Promise<CustomModelSimple[]>;
            export function findWardrobeRefListAsync(): Promise<IRefModelLite[]>;
            export function findWardrobeCopyRefListAsync(): Promise<IRefModelLite[]>;
        }
    }
    export namespace Integration {
        export namespace FOP {
            export type GetOrderFieldsResult = IGetOrderFieldsResult;
            const CustomerSource: typeof ECustomerSource;
            const CustomerQueryType: typeof ECustomerQueryType;
            export type AddOrderStateAttachmentOption = IAddOrderStateAttachmentOption;
            export type DeleteOrderStateAttachmentOption = IDeleteOrderStateAttachmentOption;
            export type UpdateOrderStateRemarkOption = IUpdateOrderStateRemarkOption;
            export type AddOrderStateRemarkOption = IAddOrderStateRemarkOption;
            export type GetOrderAttachmentsOption = IGetOrderAttachmentsOption;
            export type GetOrderAttachmentsResult = IGetOrderAttachmentsResult;
            export type GetOrderRemarksOption = IGetOrderRemarksOption;
            export type GetOrderRemarksResult = IGetOrderRemarksResult;
            export type OrderStateData = IOrderStateData;
            export type OrderEventData = IOrderEventData;
            export type OrderOperationData = IOrderOperationData;
            export type ExecuteOrderOperationOption = IExecuteOrderOperationOption;
            export type CheckOperatePermissionOption = ICheckOperatePermissionOption;
            export function createOrderAsync(option: ICreateOrderOption): Promise<ICreateOrderResult>;
            export function getOrderAsync(orderId: string): Promise<IOrderData>;
            export function addAttachmentAsync(option: AddOrderStateAttachmentOption): Promise<void>;
            export function deleteAttachmentAsync(option: IDeleteAttachmentOption): Promise<void>;
            export function updateRemarkAsync(option: UpdateOrderStateRemarkOption): Promise<void>;
            export function addRemarkAsync(option: AddOrderStateRemarkOption): Promise<void>;
            export function deleteRemarkAsync(option: IDeleteOrderRemarkOption): Promise<void>;
            export function getDesignOrderListAsync(option: IDesignOrderListOption): Promise<IDesignOrderListResult[]>;
            export function getModelAssociatedOrderAsync(option: IModelIsAssociatedOrderOption): Promise<IModelIsAssociatedOrderResult[]>;
            export function executeOrderOperationAsync(option: ExecuteOrderOperationOption): Promise<boolean>;
            export function checkOperatePermissionAsync(option: CheckOperatePermissionOption): Promise<boolean>;
            export function createCustomerAsync(option: ICreateCustomerOption): Promise<ICreateCustomerResult>;
            export function findCustomerListAsync(option: IGetCustomerListOption): Promise<IGetCustomerListResult>;
            export function getStoreAsync(): Promise<IGetStoreInfoResult | null>;
            export function getOrderFieldsAsync(option?: {
                orderId?: string;
            }): Promise<GetOrderFieldsResult>;
            export function findOrdersAsync(option?: IFindOrdersOption): Promise<IFindOrdersResult>;
            export function getAttachmentsAsync(option: GetOrderAttachmentsOption): Promise<GetOrderAttachmentsResult>;
            export function getRemarksAsync(option: GetOrderRemarksOption): Promise<GetOrderRemarksResult[]>;
            export function createAfterSaleOrderFromDesignAsync(option: ICreateAfterSaleOrderFromDesignOption): Promise<ICreateOrderResult>;
            export function findAuditedModelAsync(option: {
                orderId: string;
            }): Promise<IFindAuditedModelResult>;
            export function getStoresAsync(): Promise<IStoreInfo[]>;
            export function getStatesAsync(): Promise<{
                sysStates: OrderStateData[];
                customStates: OrderStateData[];
            }>;
            export function getOperationsAsync(): Promise<{
                sysOperations: OrderOperationData[];
                customOperations: OrderOperationData[];
            }>;
            export function updateOrderModelsAsync(option: IUpdateOrderModel): Promise<void>;
            export function findOrderModelsAsync(option: {
                orderId: string;
            }): Promise<IOrderModel>;
            export function getReportFileAsync(option: IGetOrderReportFileOption | IGetMergeTaskReportFileOption): Promise<{
                url: string;
            }>;
            export function getAllPlankDrawingAsync(orderId: string): Promise<{
                url: string;
            }>;
            export function getPlankDrawingAsync(option: {
                orderId: string;
                code: string;
            }): Promise<{
                url: string;
            }>;
            export function getPlankDrawingsAsync(option: {
                orderId: string;
                codes: string[];
            }): Promise<Array<{
                code: string;
                url: string;
            }>>;
            export function updateOrderAsync(option: IUpdateOrderOption): Promise<void>;
            export function updateCustomerAsync(option: IUpdateCustomerOption): Promise<void>;
            export function findAfterSaleOrderListAsync(option: {
                parentOrderId: string;
            }): Promise<IDesignOrderListResult[]>;
            export function createAfterSaleOrderFromBomAsync(option: ICreateAfterSaleOrderFromBomOption): Promise<ICreateOrderResult>;
            export function deleteOrderAsync(option: {
                orderId: string | string[];
            }): Promise<void>;
            export namespace OrderMode {
                export function getEditable(): boolean;
                export function setEditable(editable: boolean): void;
                export function getOrderId(): string | undefined;
                export function pauseAutoUpdateOrderModel(): void;
                export function resumeAutoUpdateOrderModel(): void;
            }
            export namespace Config {
                const FieldEnum: typeof EFieldEnum;
                export type FieldEnumResult = IFieldEnumResult;
                export interface GetDetectionConfigRes {
                    rules: DetectionRuleType[];
                    operations: string[];
                }
                export function getEnumAsync(option: {
                    field: EFieldEnum;
                }): Promise<FieldEnumResult[]>;
            }
            export namespace InstallationSharing {
                export function createShareDataAsync(option: {
                    orderId: string;
                }): Promise<{
                    expirationTime: number;
                }>;
            }
        }
        export namespace Upload {
        }
        export namespace EngravingMachineCutting {
            export type EngravingMachineCuttingResponse = IEngravingMachineCuttingResponse;
            export type EngravingMachineCuttingCreateTask = IEngravingMachineCuttingTaskCreate;
            export type EngravingMachineCuttingTaskCreateResponse = IEngravingMachineCuttingTaskCreateResponse;
            export type EngravingMachineCuttingTaskCreateErrResponse = IEngravingMachineCuttingTaskCreateErrResponse;
            export type FindOrDeleteTaskOption = Record<"task_id", string>;
            export function createTaskAsync(option: EngravingMachineCuttingCreateTask): Promise<EngravingMachineCuttingTaskCreateResponse | EngravingMachineCuttingTaskCreateErrResponse>;
            export function findTaskAsync(option: FindOrDeleteTaskOption): Promise<EngravingMachineCuttingResponse>;
            export function deleteTaskAsync(option: FindOrDeleteTaskOption): Promise<void>;
        }
        export namespace Bom {
            export type BomPlankEditable = IBomPlankEditable;
            export type BoomPlank = IBomPlankUpdate;
            export type BomPlankFull = IBomPlankFull;
            export type BoomPlankWithId = IBomPlankWithId;
            const BomPlankType: typeof EBomPlankType;
            const BomCurveType: typeof EBomCurveType;
            const BomPlankHoleType: typeof EBomPlankHoleType;
            export type FindPlankListOption = IFindPlankListOption;
            export interface CreateMaterialsOption {
                moldings?: BomMoldingEditable[];
                planks?: BomPlankEditable[];
                finishedProducts?: BomFinishedProductEditable[];
                groups?: BomGroup[];
                structures?: BomStructureEditable[];
                productId: string;
            }
            export interface FindMaterialsResult {
                moldings?: BomMolding<any>[];
                planks?: BomPlankFull[];
                finishedProducts?: BomFinishedProduct[];
                groups?: IBomGroupFull[];
                structures?: BomStructure[];
            }
            export interface FindMaterialsOption {
                orderId: string;
                productIds?: string[];
            }
            export interface CreateMaterialsResult {
                moldingIds?: string[];
                plankIds?: string[];
                finishedProductIds?: string[];
                rootIds?: string[];
                structureIds?: string[];
            }
            export interface DeleteMaterialsOption {
                orderId: string;
                productIds: string[];
            }
            export interface DeleteMaterialsResult {
                productMaterials: Array<{
                    bomIds: string[];
                    productId: string;
                    groupIds: string[];
                }>;
            }
            export interface DeleteMaterialsByOrderIdResult {
                orderMaterial: {
                    bomIds: string[];
                    groupIds: string[];
                };
            }
            export function createMaterialsAsync(option: CreateMaterialsOption): Promise<CreateMaterialsResult>;
            export function deleteMaterialsAsync(option: DeleteMaterialsOption): Promise<DeleteMaterialsResult>;
            export function deleteMaterialsByOrderIdAsync(option: Omit<DeleteMaterialsOption, "productIds">): Promise<DeleteMaterialsByOrderIdResult>;
            export function unpackMaterialsByBomIdAsync(option: {
                bomIds: string[];
            }): Promise<{
                bomIds: string[];
                groupIds: string[];
            }>;
            export function unpackMaterialsByPackageIdAsync(option: {
                packageIds: string[];
            }): Promise<{
                bomIds: string[];
                groupIds: string[];
            }>;
            export function unpackMaterialsByOrderIdAsync(option: {
                orderIds: string[];
            }): Promise<{
                bomIds: string[];
                groupIds: string[];
            }>;
            export function findPlankListAsync(option: FindPlankListOption): Promise<{
                result: BomPlankFull[];
            }>;
            export function createPlankAsync(plank: BomPlankEditable): Promise<BoomPlankWithId>;
            export function updatePlankAsync(plank: BoomPlank): Promise<void>;
            export function deletePlankAsync(plankId: string): Promise<void>;
            export function createPlanksAsync(option: {
                planks: BomPlankEditable[];
            }): Promise<{
                plankIds: string[];
            }>;
            export function updatePlanksAsync(option: {
                planks: BoomPlank[];
            }): Promise<void>;
            export function deletePlanksAsync(option: {
                plankIds: string[];
            }): Promise<{
                plankIds: string[];
            }>;
            export function deletePlanksByProductIdsAsync(option: {
                orderId: string;
                productIds: string[];
            }): Promise<{
                plankIds: string[];
            }>;
            export function deletePlanksByOrderIdsAsync(option: {
                orderIds: string[];
            }): Promise<{
                plankIds: string[];
            }>;
            export namespace Plank {
                export type BoomPlankHoleWithId = IBomPlankHoleWithId;
                export type BomPlankHoleEditable = IBomPlankHoleEditable;
                export type BomPlankHole = IBomPlankHole;
                export function createHolesAsync(option: {
                    holes: Array<BomPlankHoleEditable & BoomPlankWithId>;
                }): Promise<void>;
                export function updateHoleAsync(hole: BomPlankHole & BoomPlankWithId): Promise<void>;
                export function deleteHoleAsync(holeId: string): Promise<void>;
                export type BoomPlankGrooveWithId = IBomPlankGrooveWithId;
                export type BomPlankGrooveEditable = IBomPlankGrooveEditable;
                export type BomPlankGroove = IBomPlankGroove;
                export function createGroovesAsync(option: {
                    grooves: Array<BomPlankGrooveEditable & BoomPlankWithId>;
                }): Promise<void>;
                export function updateGrooveAsync(groove: BomPlankGroove & BoomPlankWithId): Promise<void>;
                export function deleteGrooveAsync(grooveId: string): Promise<void>;
            }
            export type BomMoldingEditable = IBomMoldingEditable;
            export type BomMolding<T = any> = IBomMolding<T>;
            export function createMoldingsAsync(option: {
                moldings: BomMoldingEditable[];
            }): Promise<{
                moldingIds: string[];
            }>;
            export function updateMoldingsAsync(option: {
                moldings: BomMoldingEditable[];
            }): Promise<void>;
            export function deleteMoldingsAsync(option: DeleteMoldingsOption): Promise<void>;
            export function findMoldingsAsync(option: FindMoldingsOption): Promise<{
                moldings: BomMolding<any>[];
            }>;
            export type BomFinishedProductEditableByProductId = IBomFinishedProductEditableByProductId;
            export type BomFinishedProductEditableByModelId = IBomFinishedProductEditableByModelId;
            export type BomFinishedProductEditable = IBomFinishedProductEditable;
            export type BomStructureEditable = IBomStructureEditable;
            export type BomStructure = IBomStructure;
            export type BomFinishedProduct = IBomFinishedProduct;
            export type BomFinishedProductUpdate = IBomFinishedProductUpdate;
            export function createFinishedProductsAsync(option: {
                finishedProducts: BomFinishedProductEditable[];
            }): Promise<{
                finishedProductIds: string[];
            }>;
            export function updateFinishedProductsAsync(option: {
                finishedProducts: BomFinishedProductUpdate[];
            }): Promise<void>;
            export function deleteFinishedProductsAsync(option: DeleteFinishedProductOption): Promise<{
                finishedProductIds: string[];
            }>;
            export function findFinishedProductsAsync(option: FindFinishedProductOption): Promise<{
                finishedProducts: BomFinishedProduct[];
            }>;
            export type BomGroup<T = any> = IBomGroup<T>;
            export interface BomGroupDeleteOption {
                rootIds: string[];
            }
            export interface BomGroupDeleteByProductIdsOption {
                orderId: string;
                productIds: string[];
            }
            export interface BomGroupDeleteByProductIdsResult {
                groups: Array<{
                    productId: string;
                    groupIds: string[];
                }>;
            }
            export function createGroupsAsync(option: {
                groups: BomGroup<string>[];
            }): Promise<{
                rootIds: string[];
            }>;
            export function updateGroupsAsync(option: {
                groups: IBomGroupEditable[];
            }): Promise<{
                groupIds: string[];
            }>;
            export function deleteGroupsAsync(option: BomGroupDeleteOption): Promise<BomGroupDeleteOption>;
            export function deleteGroupsByProductIdAsync(option: BomGroupDeleteByProductIdsOption): Promise<BomGroupDeleteByProductIdsResult>;
            export function generateGroupRelationsAsync(option: {
                regenerate?: boolean;
                orderId: string;
            }): Promise<void>;
            export function findGroupsByRootIdAsync(option: {
                rootIds: string[];
            }): Promise<{
                groups: IBomGroupFull[];
            }>;
            export function findGroupsByBomIdAsync(option: {
                bomIds: string[];
            }): Promise<{
                groups: IBomGroupFull[];
            }>;
            export function findGroupsByOrderIdAsync(option: {
                orderIds: string[];
            }): Promise<{
                groups: IBomGroupFull[];
            }>;
            export function findGroupsByProductIdAsync(option: {
                orderId: string;
                productIds: string[];
            }): Promise<{
                groups: IBomGroupFull[];
            }>;
            export type BomRawPlankEditable = IBomRawPlankEditable;
            export type BomRawPlank = IBomRawPlank;
            export interface FindBomRawPlankOption {
                mergeIds?: string[];
                rawPlankIds?: string[];
            }
            export function createRawPlanksAsync(option: {
                rawPlanks: BomRawPlankEditable[];
            }): Promise<{
                rawPlankIds: string[];
            }>;
            export function deleteRawPlanksAsync(option: {
                rawPlankIds: string[];
            }): Promise<{
                rawPlankIds: string[];
            }>;
            export function updateRawPlanksAsync(option: {
                rawPlanks: Array<Omit<BomRawPlank, "mergeId">>;
            }): Promise<void>;
            export function findRawPlanksAsync(option: FindBomRawPlankOption): Promise<{
                rawPlanks: BomRawPlank[];
            }>;
            export type BomPlankLayoutArrange = IBomPlankLayoutArrange;
            export type BomRawPlankLayoutResult = IBomRawPlankLayoutResult;
            export type BomPlankLayoutProcessAttribute = IBomPlankLayoutProcessAttribute;
            export function arrangePlanksAsync(option: {
                layouts: BomPlankLayoutArrange[];
            }): Promise<{
                layoutIds: string[];
            }>;
            export function updateRawPlankProcessingAttributesAsync(option: {
                attributes: BomPlankLayoutProcessAttribute[];
            }): Promise<undefined>;
            export function findLayoutAsync(option: {
                mergeId: string;
            }): Promise<BomRawPlankLayoutResult>;
            export function deleteLayoutsAsync(option: {
                layoutIds: string[];
            }): Promise<{
                layoutIds: string[];
            }>;
            export function deleteLayoutsByPlankIdAsync(option: {
                plankIds: string[];
            }): Promise<{
                layoutIds: string[];
            }>;
            export type BomSurplusPlankEditable = IBomSurplusPlankEditable;
            export type BomSurplusPlank = IBomSurplusPlank;
            export interface FindSurplusPlanksOption {
                fromMergeIds?: string[];
                usedMergeIds?: string[];
                surplusPlankIds?: string[];
            }
            export function createSurplusPlanksAsync(option: {
                surplusPlanks: BomSurplusPlankEditable[];
            }): Promise<{
                surplusPlankIds: string[];
            }>;
            export function deleteSurplusPlanksAsync(option: {
                surplusPlankIds: string[];
            }): Promise<{
                surplusPlankIds: string[];
            }>;
            export function updateSurplusPlanksAsync(option: {
                surplusPlanks: BomSurplusPlank[];
            }): Promise<void>;
            export function findSurplusPlanksAsync(option: FindSurplusPlanksOption): Promise<{
                surplusPlanks: BomSurplusPlank[];
            }>;
            export interface FindCategoryAttrOption {
                categoryCodes: string[];
            }
            export type CategoryAttribute = ICategoryAttribute;
            export type Category = ICategory;
            export interface FindCategoryAttrResult {
                catAttrs: Array<{
                    attributes: CategoryAttribute[];
                    category: Category;
                }>;
            }
            export function findCategoryAttrsAsync(option: FindCategoryAttrOption): Promise<FindCategoryAttrResult>;
            export function clearMaterialPropertiesAsync(option: IBomPropertiesClearOption): Promise<void>;
            export function clearGroupPropertiesAsync(option: IBomGroupPropertiesClearOption): Promise<void>;
            export interface ProductCodeInOrder {
                products?: IProductCodeInOrderInfo[];
            }
            export interface UpdateProductsOption extends ProductCodeInOrder {
                orderId: string;
            }
            export function refreshProductCodeInOrderAsync(option: {
                orderId: string;
            }): Promise<ProductCodeInOrder>;
            export function updateProductsAsync(option: UpdateProductsOption): Promise<void>;
            export function refreshCodeAsync(option: {
                orderId: string;
            }): Promise<void>;
            export function findMaterialsAsync(option: FindMaterialsOption): Promise<FindMaterialsResult>;
            export function previewPlankDrawingAsync(option: IPreviewPlankDrawingOption): Promise<IPlankDrawingResult>;
            export function findProductsAsync(orderId: string): Promise<Array<{
                productId: string;
            }>>;
            export function previewStructureDrawingAsync(option: IPreviewStructureDrawingOption): Promise<IStructureDrawingResult>;
            export function updateStructureAsync(structure: IBomStructureUpdate): Promise<void>;
            export function previewMaterialDrawingsAsync(option: IPreviewMaterialDrawingsOption): Promise<IPreviewMaterialDrawingsResult>;
            export namespace Detection {
                export function detectAsync(option: BomDetectionOption): Promise<{
                    bomDetectionProblem: BomDetectionProblem[];
                }>;
                export function findRuleListAsync(): Promise<BomDetectionRule[]>;
            }
        }
        export namespace Report {
            export function generateReportPageAsync(option: GenerateReportPageOption): Promise<string>;
        }
    }
    export namespace User {
        export type UserDetails = IUserDetails;
        export function getAppUidAsync(): Promise<IGetAppUidResult>;
        export function getUserDetailsAsync(): Promise<UserDetails>;
    }
    export namespace Storage {
        export namespace Common {
            export function putItemAsync(option: IStorageItem): Promise<void>;
            export function getItemAsync(key: string): Promise<Record<string, any> | undefined>;
            export function getItemListAsync(keys: string[]): Promise<IStorageItem[]>;
            export function deleteItemAsync(key: string): Promise<void>;
            export function getKeysAsync(option?: IStorageGetKeysOption): Promise<IStorageGetKeysResult>;
        }
        export namespace Design {
            export function putItemAsync(option: IStorageItem): Promise<void>;
            export function getItemAsync(key: string): Promise<Record<string, any> | undefined>;
            export function getItemListAsync(keys: string[]): Promise<IStorageItem[]>;
            export function deleteItemAsync(key: string): Promise<void>;
            export function getKeysAsync(option?: IStorageGetKeysOption): Promise<IStorageGetKeysResult>;
        }
        export namespace DesignV2 {
            export function putItemAsync(option: IStorageItem): Promise<void>;
            export function getItemAsync(key: string): Promise<Record<string, any> | undefined>;
            export function deleteItemAsync(key: string): Promise<void>;
        }
        export namespace Enterprise {
            export function putItemAsync(option: IStorageItem): Promise<void>;
            export function getItemAsync(key: string): Promise<Record<string, any> | undefined>;
            export function getItemListAsync(keys: string[]): Promise<IStorageItem[]>;
            export function deleteItemAsync(key: string): Promise<void>;
            export function getKeysAsync(option?: IStorageGetKeysOption): Promise<IStorageGetKeysResult>;
        }
    }
    export namespace Interaction {
        export function getSelectedElements(): IDP.DB.Types.ElementId[];
        export function setSelectedElements(option: IDP.DB.Types.ElementId[]): IDP.DB.Types.ElementId[];
        export function getSelectedRooms(): {
            roomId: string;
            roomName: string;
        }[];
    }
    export namespace Platform {
        export function enterCustomModeAsync(toolType?: EToolType): Promise<void>;
    }
}
export declare namespace IDP {
    export namespace DB {
        export namespace Types {
            export interface FurnitureLegend {
                readonly id: IDP.DB.Types.ElementId;
                readonly area: number;
            }
            export interface ParamLegend {
                readonly id: IDP.DB.Types.ElementId;
                readonly area: number;
            }
            export interface LegendGroup {
                readonly id: IDP.DB.Types.ElementId;
                readonly subElements: IDP.DB.Types.ElementId[];
            }
        }
        export namespace Methods {
            export function getAllFurnitureLegendList(): IDP.DB.Types.FurnitureLegend[];
            export function getAllParamLegendList(): IDP.DB.Types.ParamLegend[];
            export function getAllLegendGroupList(): IDP.DB.Types.LegendGroup[];
        }
    }
}
export declare namespace IDP {
    export namespace Interaction {
    }
}
export declare namespace IDP {
    export namespace DB {
        export namespace Types {
        }
        export namespace Methods {
        }
    }
    export namespace Interaction {
    }
    export interface EventTypes {
    }
}
export declare namespace IDP {
    export namespace Platform {
    }
    export namespace UI {
    }
}
export declare namespace IDP {
    export namespace Platform {
    }
}
export declare namespace IDP {
    export namespace DB {
        export namespace Methods {
        }
    }
}
export declare namespace IDP {
    export namespace Payment {
        export enum PaymentStatus {
            UNPAID = "UNPAID",
            PAID = "PAID",
            FAILED = "FAILED"
        }
        export namespace Types {
            export interface PaymentResult {
                orderNo?: string;
                status: PaymentStatus;
                orderAutoCloseTime?: number;
                errorMessage?: string;
            }
            export interface PaymentDialogProps {
                isvOrderNo: string;
                isvOrderInfo: {
                    skuCode: string;
                    num?: number;
                    duration?: {
                        year?: number;
                        month?: number;
                        day?: number;
                    };
                };
            }
        }
        export function showPaymentDialog(props: IDP.Payment.Types.PaymentDialogProps): void;
    }
    export interface EventTypes {
        "IDP.Payment.PaymentDialogClosed": IDP.Payment.Types.PaymentResult;
    }
}
export declare namespace IDP {
    export namespace Integration {
        export namespace Catalog {
            export function getElementLabelConfigsAsync(): Promise<ElementLabelConfig[]>;
        }
    }
    export namespace DB {
        export namespace Types {
            export enum ElementLabelType {
                number = 0,
                string = 1,
                option = 2
            }
            export enum ElementLabelWriteErrorType {
                config = "ElementLabel.ConfigError",
                value = "ElementLabel.ValueError",
                element = "ElementLabel.ElementError"
            }
        }
        export namespace Methods {
            export function putElementLabelsAsync(elementId: IDP.DB.Types.ElementId, updateData: ElementLabelData[]): Promise<void>;
        }
    }
}
export declare namespace IDP {
    export interface EventTypes {
        "IDP.Kada.RoutingFace.enter": undefined | Error;
        "IDP.Kada.RoutingFace.exit": void | Error;
        "IDP.Kada.RoutingFace.BeforeConnectivityDetection": IDP.DB.Types.ElementId[];
        "IDP.Kada.RoutingFace.AfterConnectivityDetection": RoutingFaceConnectivityDetectionResult;
        "IDP.Kada.RoutingSocket.Delete": IDP.DB.Types.ElementId[];
        "IDP.Kada.RoutingSocket.Add": IDP.DB.Types.ElementId[];
    }
    export namespace Custom {
    }
    export namespace Kada {
        export interface SnapperUniquenessResult {
            result: Array<{
                elementId: IDP.DB.Types.ElementId;
                uniqueValue: string;
            }>;
        }
        export function getSnapperUniquenessAsync(option: {
            elements?: IDP.DB.Types.ElementId[];
            toolType?: EToolType;
        }): Promise<SnapperUniquenessResult>;
        export function openBudgetAsync(options: {
            elements?: IDP.DB.Types.ElementId[];
            toolType?: EToolType;
        }): Promise<void>;
        export namespace RoutingFace {
            export interface RoutingSocket {
                productId: string;
                elementId: IDP.DB.Types.ElementId;
                name: string;
                position: Number3__idp_custom_kada_api;
                rotation: Number3__idp_custom_kada_api;
                size: Number3__idp_custom_kada_api;
                isCreated: boolean;
            }
            export interface RoutingFace {
                linkedProductId: string;
                elementId: IDP.DB.Types.ElementId;
                type: ERoutingFaceDirection;
                name: string;
                outLoop: Number3__idp_custom_kada_api[][];
                active: boolean;
                linkedSockets: RoutingSocket[];
            }
            export interface RoutingCube {
                linkedProductId: string;
                elementId: IDP.DB.Types.ElementId;
                name: string;
                active: boolean;
            }
            export interface RoutingFaceGroup {
                elementId: IDP.DB.Types.ElementId;
                active: boolean;
                faces: RoutingFace[];
            }
            export interface RoutingFaceForCreateAdsorption {
                belongsToSnapperElementId: IDP.DB.Types.ElementId;
                dragRoutingSocket: {
                    productId: string;
                    elementId: IDP.DB.Types.ElementId;
                    size: Number2__idp_custom_kada_api;
                };
                routingFace2d: RoutingFace2d | RoutingFaceGroup2d;
            }
            export interface RoutingFace2d {
                linkedProductId: string;
                elementId: IDP.DB.Types.ElementId;
                name: string;
                active: boolean;
                type: ERoutingFaceDirection;
                outLoop: Number2__idp_custom_kada_api[][];
                linkedSockets: RoutingSocket2d[];
            }
            export interface RoutingFaceGroup2d {
                elementId: IDP.DB.Types.ElementId;
                faces: RoutingFace2d[];
            }
            export interface RoutingSocket2d {
                productId: string;
                elementId: IDP.DB.Types.ElementId;
                name: string;
                position: Number2__idp_custom_kada_api;
                rotation: Number2__idp_custom_kada_api;
                size: Number2__idp_custom_kada_api;
                isCreated: boolean;
            }
            export interface AdsorptionPointData {
                faceId: IDP.DB.Types.ElementId;
                point: Number2__idp_custom_kada_api;
                positionFormula?: {
                    x?: string;
                    y?: string;
                    z?: string;
                };
            }
            export interface AdsorptionLineData {
                faceId: IDP.DB.Types.ElementId;
                startPoint: Number2__idp_custom_kada_api;
                endPoint: Number2__idp_custom_kada_api;
            }
            export type AdsorptionData = AdsorptionPointData | AdsorptionLineData;
            export interface EnterRoutingFaceOption {
                socketProductIds: string[];
                createAdsorptionData(face: RoutingFaceForCreateAdsorption): AdsorptionData[];
            }
            export interface RoutingSnapperGroup {
                elementId: IDP.DB.Types.ElementId;
                position: Number3__idp_custom_kada_api;
                rotation: Number3__idp_custom_kada_api;
                size: Number3__idp_custom_kada_api;
                children: RoutingSnapper[];
                connection: RoutingSnapperConnection[];
            }
            export interface RoutingSnapperConnection {
                firstId: string;
                firstSnapperId: string;
                firstConnectorId: string;
                secondId: string;
                secondSnapperId: string;
                secondConnectorId: string;
            }
            export interface RoutingSnapper {
                productId: string;
                elementId: IDP.DB.Types.ElementId;
                position: Number3__idp_custom_kada_api;
                rotation: Number3__idp_custom_kada_api;
                size: Number3__idp_custom_kada_api;
                connectors: Connector[];
                routingFaces: RoutingFace[];
                routingCubes: RoutingCube[];
            }
            export function enter(option: EnterRoutingFaceOption): void;
            export function exit(ignoreErrors?: boolean): void;
            export function startDragSocketAsync(socketProductId: string): Promise<IDP.DB.Types.ElementId>;
            export function attachSocketToModel(elementId?: IDP.DB.Types.ElementId[]): Promise<void>;
            export type RoutingSnapperGroupListResult = RoutingSnapperGroup | RoutingSnapper;
            export function getRoutingSnapperGroupList(): RoutingSnapperGroupListResult[];
            export function findRoutingSnapperGroupById(faceId: IDP.DB.Types.ElementId): RoutingSnapperGroup | null;
            export function getRoutingSocketList(): RoutingSocket[];
            export function deleteRoutingSocket(elementId: IDP.DB.Types.ElementId): void;
        }
        export namespace Detection {
            export function detectRoutingFaceConnectivityAsync(elementIds?: IDP.DB.Types.ElementId[]): Promise<RoutingFaceConnectivityDetectionResult>;
            export function forceConnectionDetectionAsync(elementIds?: IDP.DB.Types.ElementId[]): Promise<DetectionResult>;
            export function spatialProximityDetectionAsync(elementIds?: IDP.DB.Types.ElementId[]): Promise<DetectionResult>;
        }
    }
}
declare interface IDrawingExportBase<T extends EDivisionType> {
    elements: Array<ElementId_2>;
    macroData?: Record<string, {
        value: string[];
        separator?: string;
    }>;
    divisionType: T;
    fileType: EDrawingFileType | Array<EDrawingFileType>;
}
declare type IDrawingExportParams = IDrawingExportBase<EDivisionType.Space> | (IDrawingExportBase<EDivisionType.Relation> & {
    name?: string;
});
declare interface IDrawingExportResponse {
    status: boolean;
    url?: string;
    fileType: EDrawingFileType;
}
declare interface IEllipse {
    ccs: {
        o: number[];
        dx: number[];
    };
    majorRadius: number;
    minorRadius: number;
    clock: number;
}
declare interface IEngravingMachineCuttingPlate {
    is_residual: boolean;
    plate_cost: number;
    plate_count: number;
    plate_id: number;
    plate_length: number;
    plate_name: string;
    plate_width: number;
}
declare interface IEngravingMachineCuttingResponse {
    result?: IEngravingMachineCuttingResultResponse;
    status: EEngravingMachineCuttingStatus;
    task_id: string;
}
declare interface IEngravingMachineCuttingResultResponse {
    objects: IEngravingMachineCuttingResultResponseObjects[];
    origin: number;
    plate_type_num: number;
    project_id: string;
    task_id: string;
}
declare interface IEngravingMachineCuttingResultResponseObjects {
    average_ratio: number;
    nesting: IIEngravingMachineCuttingResultResponseNesting[];
    plate_count: number;
    plate_type: string;
    total_item_count: number;
}
declare interface IEngravingMachineCuttingSearchResult {
    total: number;
    count: number;
    tasks: IEngravingMachineCuttingResponse[];
}
declare interface IEngravingMachineCuttingTaskCreate {
    item_spacing: number;
    items: IEngravingMachineCuttingTaskItem[];
    origin: number;
    plate_spacing: number;
    plates: IEngravingMachineCuttingPlate[];
    project_id: string;
    task_id: string;
    time: number;
}
declare interface IEngravingMachineCuttingTaskCreateErrResponse {
    error_code?: string;
    error_msg?: string;
}
declare interface IEngravingMachineCuttingTaskCreateResponse {
    etr?: string;
    task_id?: string;
}
declare interface IEngravingMachineCuttingTaskItem {
    item_type: string;
    objects: IEngravingMachineCuttingTaskItemObject[];
}
declare interface IEngravingMachineCuttingTaskItemObject {
    count: number;
    flip: number;
    is_irshape: boolean;
    item_id: string;
    item_name: string;
    orientations: number[];
    vertices: Array<[
        number,
        number
    ]>;
}
declare interface IEvent<A = void> {
    on(listener: IEventListener<A>): IEventUnListener;
    once(listener: IEventListener<A>): IEventUnListener;
    emit(args: A): void;
}
declare interface IEventListener<A = void> {
    (args: A): void;
}
declare interface IEventUnListener {
    (): void;
}
declare interface IExecuteOrderOperationOption {
    key: string;
    orderId: string;
    extraParam?: Record<string, any>;
}
declare interface IExportParamModelData {
    id: string;
    "@type": string;
    modelTypeId: number;
    modelName: string;
    originalModelName: string;
    subModels: IExportParamModelData[];
    obsBrandGoodId: string;
    prodCatId: number;
    modelBrandGoodName: string;
    modelNumber: string;
    modelBrandGoodCode: string;
    modelProductNumber: string;
    modelAvailable: boolean;
    customCode: string;
    obsModelAccountId: string;
    textureObsBrandGoodId: string;
    textureName: string;
    textureNumber: string;
    textureBrandGoodCode: string;
    textureProductNumber: string;
    textureAvailable: boolean;
    textureCustomCode: string;
    obsTextureAccountId: string;
    baseTexture: string;
    obsCollectBrandGoodId: string;
    needQuotation: boolean;
    displayInCostList: boolean;
    billOutput: boolean;
    customFields: ICustomField[];
    modelCostInfo: IModelCostInfo;
    parameters: IParamModelParameter[];
    ignoreParameters: IParamModelParameter[];
    constParameters: IParamModelParameter[];
    roomId: string;
    accessoryModel: boolean;
    modelInstanceId: string;
    deleted: boolean;
    ignore: boolean;
    unitParamModelIds: Array<string>;
    globalInvokedPositionTypeId?: number;
    installationCode: string;
    parentId: string;
    remark: string;
    standard: boolean;
    position?: Number3___qunhe_custom_apass_api;
    center?: Number3___qunhe_custom_apass_api;
    rotate?: Number3___qunhe_custom_apass_api;
    rotateDegree?: Number3___qunhe_custom_apass_api;
    absBoundingBox?: {
        min: Number3___qunhe_custom_apass_api;
        max: Number3___qunhe_custom_apass_api;
    };
    boundingBox?: {
        min: Number3___qunhe_custom_apass_api;
        max: Number3___qunhe_custom_apass_api;
    };
    scale: Number3___qunhe_custom_apass_api;
    size: Number3___qunhe_custom_apass_api;
    boxSize: Number3___qunhe_custom_apass_api;
    absPosition: Number3___qunhe_custom_apass_api;
    absRotation: Number3___qunhe_custom_apass_api;
    absRotationDegree: Number3___qunhe_custom_apass_api;
    customSize: Number3___qunhe_custom_apass_api;
    direction?: IParamModelDirection;
    length: number;
    arrayStyle: {
        step: number;
        number: number;
    };
    type: string;
    element: IExportParamModelData;
    scaleHeight: number;
    paramLoftPath: any;
    profile: IResourceProfiles[];
    profileBrandGoodIds: Array<string>;
    edges: IParamModelEdges[];
    profiles: IResourceProfiles[];
    profileSegments: any;
    thickness: number;
    paramPlankPath: IPlankPathData;
    productionOldPath: IPlankPathData;
    textureAngle: number;
    brushMaterialBgId?: number;
    bottomBrushMaterialBgId?: number;
    points?: Number2___qunhe_custom_apass_api;
    arcIndex?: number;
    radius?: number;
    clockwise?: boolean;
    minorArc?: boolean;
    [key: string]: any;
}
declare interface IExtrusionModel extends IModelBaseInfo {
    parameters: IParameter[];
    texture?: ITexture;
    profile: IImportPath;
    holes?: IImportPath[];
}
declare interface IFailedOrder {
    orderInfo: ICreateOrderOption;
    errorMsg?: string;
}
declare interface IFieldEnumResult {
    name: string;
    value: string | number | boolean;
}
declare interface IFieldOptionItem {
    name?: string;
    value: any;
}
declare interface IFindAuditedModelResult {
    modelIds: string[];
    subModels: ISubModel[];
}
declare interface IFindCustomFoldersOptions {
    lib?: ECustomLibraryType;
    tool: EToolType;
    packageId: string;
    categoryIds?: number[];
}
declare interface IFindCustomProductsOptions {
    lib?: ECustomLibraryType;
    tool: EToolType;
    packageId: string;
    categoryIds?: number[];
    start: number;
    num: number;
    folderId?: string;
    deep?: boolean;
}
declare interface IFindMoldingsOption {
    orderId: string;
    productIds: string[];
    codes: string[];
    moldingIds: string[];
    orderIds: string[];
}
declare interface IFindOrDeleteFinishedProductOption {
    finishedIds?: string[];
    orderIds?: string[];
    codes?: string[];
    productIds?: string[];
    orderId?: string;
}
declare interface IFindOrderItem extends ICustomerBaseInfo, IOrderBaseInfo {
    externalCode?: string;
    orderType: EToolType[];
    placeTime: number;
    storeName: string;
    designId: string;
    designName: string;
    events: IOrderEvent[];
    processDirect: EProcessDirect;
    emergencyLevel: number;
    emergencyLevelName: string;
    receiptType: number;
    receiptTypeName: string;
    actions: IOrderAction[];
    deleteTime: number;
    deleted: boolean;
}
declare interface IFindOrdersOption extends IPaginationQuery, Partial<Omit<IOrderBaseInfo, "orderId" | "orderStateName" | "orderState">> {
    orderType?: EToolType;
    customerName?: string;
    designerName?: string;
    placeTimeStart?: number;
    placeTimeEnd?: number;
    sortRule?: "asc" | "desc";
    sortField?: string;
    orderStateSortPriority?: number[];
    searchType?: number;
    emergencyLevel?: number;
    receiptType?: number;
    includeDeleted?: boolean;
    deleteTimeStart?: number;
    deleteTimeEnd?: number;
    orderIds?: string[];
    orderState?: number | number[];
    externalCode?: string;
}
declare type IFindOrdersResult = IPaginationResult<IFindOrderItem>;
declare type IFindPlankListOption = AugmentedRequired<IFindPlankListPortionOption, "orderIds"> | AugmentedRequired<IFindPlankListPortionOption, "orderId"> | AugmentedRequired<IFindPlankListPortionOption, "codes"> | AugmentedRequired<IFindPlankListPortionOption, "mergeIds"> | AugmentedRequired<IFindPlankListPortionOption, "plankIds"> | AugmentedRequired<IFindPlankListPortionOption, "backCodes"> | AugmentedRequired<IFindPlankListPortionOption, "orderId" | "productIds">;
declare interface IFindPlankListPortionOption {
    orderId?: string;
    orderIds?: string[];
    codes?: string[];
    mergeIds?: string[];
    plankIds?: string[];
    backCodes?: string[];
    productIds?: string[];
}
declare interface IFindRefModelsOption {
    toolType: EToolType;
    templateId?: string;
}
declare interface IFindTemplatesOption {
    toolType: EToolType;
}
declare interface IFittingDataV2<WithRequiredId extends boolean = false> {
    modelId: string;
    holes?: Array<WithRequiredId extends true ? IHoleDataV2WithId : IHoleDataV2WithOptionalId>;
    grooves?: Array<WithRequiredId extends true ? IGrooveDataV2WithId : IGrooveDataV2WithOptionalId>;
}
declare interface IFittingDesignData {
    id?: string;
    holes: IFittingHoleCollect;
    grooves: IFittingGrooveCollect;
    hardwares: IFittingHardwareCollect;
    hardwareGrooves: IFittingHardwareGrooves;
}
declare interface IFittingDesignDataV2<WithRequiredId extends boolean = false> {
    id: string;
    fittingDesign: Array<IFittingDataV2<WithRequiredId>>;
}
declare interface IFittingDesignOption {
    modelId: string;
}
declare type IFittingGrooveCollect = Record<string, IGrooveData[]>;
declare type IFittingHardwareCollect = Record<string, IHardwareData[]>;
declare type IFittingHardwareGrooves = Record<string, IHardwareGrooveData[]>;
declare type IFittingHoleCollect = Record<string, IHoleData[]>;
declare interface IFittingProcessingTechnology {
    toolNo?: string;
}
declare interface IFloorplanRelation {
    hostIds: string[];
    openingIds: string[];
    reverse?: boolean;
}
declare interface IFolder {
    id: string;
    name: string;
    imageUrl?: string;
    children: IFolder[];
}
declare interface IGetAppUidResult {
    appUid: string | number | null;
}
declare interface IGetCustomerListOption extends IPaginationQuery {
    keyWord?: string;
    queryType?: ECustomerQueryType;
    queryRange: ECustomerQueryRange;
}
declare type IGetCustomerListResult = IPaginationResult<ICustomerInfo>;
declare type IGetCustomerOrderOption = {
    designId: string;
} | {
    orderId: string;
};
declare interface IGetFittingDataByAuxiliaryBaseOption {
    distanceTol?: number;
}
declare interface IGetFittingDataByAuxiliaryOption extends IGetFittingDataByAuxiliaryBaseOption {
    modelId: string;
}
declare interface IGetFittingDataByAuxiliaryResult {
    id: string;
    holes: Array<IHoleDataV2 & IAuxiliaryBizProperties>;
    grooves: Array<IGrooveDataV2 & IAuxiliaryBizProperties>;
}
declare interface IGetIntersectedOption {
    modelId: string;
    tolerance?: number;
    timeout?: number;
    bodyDistTol?: number;
    faceDistTol?: number;
    thicknessFilter?: boolean;
    computeModelTypes?: Array<EIntersectModelType>;
    products?: Array<{
        category: number;
    }>;
    direction?: EProductDirection;
    intersectedInfoType?: EIntersectedInfoType[];
    thicknessFilterBody?: boolean;
    thicknessFilterFaceDistTol?: number;
    primitiveMatch?: {
        extendedCode: string[];
    };
    productDirectionConfig?: {
        extendedCode: string;
        referencePlane: EReferencePlane;
    }[];
}
declare interface IGetMergeTaskReportFileOption {
    mergeId: string;
    templateId: string;
    type?: EReportFileType;
}
declare interface IGetModelJsonOption {
    modelId: string;
    templateId?: string;
    force?: boolean;
}
declare interface IGetOrderAttachmentsOption {
    start?: number;
    num?: number;
    sortRule?: "asc" | "desc";
    sortField?: string;
    orderId: string;
    name?: string;
    orderState?: number;
    userName?: string;
}
declare type IGetOrderAttachmentsResult = IPaginationResult<IOrderAttachmentItem>;
declare interface IGetOrderFieldsResult {
    systemFields: Omit<IOrderField, "type">[];
    customFields: IOrderField[];
}
declare interface IGetOrderRemarksOption {
    orderId: string;
}
declare interface IGetOrderRemarksResult {
    userName: string;
    userAvatar: string;
    orderState: number;
    orderStateName: string;
    remarkId: string;
    content: string;
    createTime: number;
    type: EOrderRemarkType | null;
    auths: EOrderFieldDataAuth[];
    images?: IOrderAttachment[];
}
declare interface IGetOrderReportFileOption {
    orderId: string;
    templateId: string;
    packageId?: string;
    type?: EReportFileType;
}
declare interface IGetPreviewImgOption {
    modelId: string;
    ignoreCategory?: number[];
}
declare interface IGetProductDefaultBuildOption {
    productId: string;
    version?: number;
}
declare interface IGetStoreInfoResult {
    storeId: string;
    storeName: string;
    contactUser: string;
    contactPhone: string;
    shippingAddr: string;
}
declare interface IGetTopModelsLiteInfoOption {
    page?: number;
    size?: number;
    ignoreCategory?: number[];
}
declare interface IGrooveData extends IBaseHoleGrooveData {
    width: number;
}
declare interface IGrooveDataV2 {
    start?: Number3___qunhe_custom_apass_api;
    end?: Number3___qunhe_custom_apass_api;
    plankFaceId: string;
    width?: number;
    type: EAppliedGrooveType;
    geometryType?: EGrooveGeometryType;
    profile?: IProfileOrPath;
    radius?: number;
    path?: IProfileOrPath;
    depth: number;
    name: string;
    processingTechnology?: IFittingProcessingTechnology;
}
declare type IGrooveDataV2WithId = IGrooveDataV2 & IGrooveId;
declare type IGrooveDataV2WithOptionalId = AugmentedOptional<IGrooveDataV2WithId, "grooveId">;
declare interface IGrooveId {
    grooveId: string;
}
declare interface IGroupData {
    rooms: {
        roomId: string;
        order: number;
        groups: {
            name: string;
            order: number;
            id: string;
            wallFaces?: {
                wallFaceId: string;
            }[];
            models?: {
                id: string;
            }[];
        }[];
    }[];
}
declare interface IHardwareData extends IBaseFittingData {
    position: Number3___qunhe_custom_apass_api;
    rotate: Number3___qunhe_custom_apass_api;
    scale: Number3___qunhe_custom_apass_api;
    brandGoodId: string;
    linkedIds: string[];
}
declare interface IHardwareGrooveData extends IBaseFittingData {
    depth: number;
    plankFaceId: number;
    points: IPointData[];
    lines: ILineData[];
}
declare interface IHoleData extends IBaseHoleGrooveData {
    diameter: number;
}
declare interface IHoleDataV2 {
    start: Number3___qunhe_custom_apass_api;
    end: Number3___qunhe_custom_apass_api;
    plankFaceId: string;
    type: EAppliedHoleType;
    diameter: number;
    depth: number;
    name: string;
    processingTechnology?: IFittingProcessingTechnology;
}
declare interface IHoleDataV2WithId extends IHoleDataV2 {
    holeId: string;
}
declare type IHoleDataV2WithOptionalId = AugmentedOptional<IHoleDataV2WithId, "holeId">;
declare interface IIEngravingMachineCuttingResultResponseNesting {
    item_count: number;
    items: IIEngravingMachineCuttingResultResponseNestingItem[];
    plate_id: number;
    plate_length: number;
    plate_name: string;
    plate_ratio: number;
    plate_width: number;
}
declare interface IIEngravingMachineCuttingResultResponseNestingItem {
    flip_x: number;
    flip_y: number;
    item_angle: number;
    item_name: string;
    vertices: Array<[
        number,
        number
    ]>;
}
declare interface IImportModel {
    id?: string;
    type: string;
    name: string;
    category: string;
    previewImgUrl?: string;
    previewImgData?: string;
    parameters?: IParameter[];
    subModels: Array<IReferenceModel | IExtrusionModel | ISweepModel | IImportModel>;
}
declare interface IImportModelResult {
    productId: string;
}
declare interface IImportPath {
    points: number[];
    curves: ICurve[];
}
declare interface IInnerSpaceData {
    size: Number3___qunhe_custom_apass_api;
    position: Number3___qunhe_custom_apass_api;
    rotate: Number3___qunhe_custom_apass_api;
    rotation: Number3___qunhe_custom_apass_api;
    face: EFace;
    parent: IParamModelLite;
}
declare interface IInnerSpaceWithBorders {
    rotate: Number3___qunhe_custom_apass_api;
    position: Number3___qunhe_custom_apass_api;
    size: Number3___qunhe_custom_apass_api;
    borderMap: Record<EFace, string[]>;
}
declare interface IInstallData {
    rooms: {
        roomId: string;
        roomCode: string;
    }[];
    models: {
        id: string;
        installCode: string;
    }[];
}
declare interface IIntersectCheckOption {
    modelIds?: string | string[];
    toolType?: EToolType | EToolType[];
}
declare interface IIntersectCheckResult {
    id: string;
    name: string;
    level: number;
    toolType?: EToolType;
    children: IIntersectCheckResult[];
}
declare interface IIntersectedData {
    intersectedGroups: IIntersectedGroup[];
}
declare interface IIntersectedGroup {
    id: string;
    intersectType: EIntersectedCreatedType;
    intersecteds: IBaseIntersected[];
}
declare interface IIntersectResult {
    id: string;
    name: string;
    children: IIntersectResult[];
    [s: string]: any;
}
declare interface IIsolateRefModelResult {
    id: string;
}
declare interface IJsonInfo {
    generatedTime: number;
    [key: string]: any;
}
declare interface ILeftPanelStyleExtensionOptions {
    title: string;
    prodCatId?: number;
    paramPackageId?: string;
    toolType: EToolType;
    mode: ProductSearchMode;
    pos: number;
    defaultLeft?: number;
}
declare interface ILineBase {
    type: ELineType;
    clockwise?: boolean;
    minorArc?: boolean;
    radius?: number;
    majorRadius?: number;
    minorRadius?: number;
    ellipseCenter?: Number2___qunhe_custom_apass_api;
}
declare type ILineData = ILineDataBase | ILineDataCircle;
declare interface ILineDataBase extends Omit<ILineBase, "ellipseCenter" | "clockwise" | "minorArc" | "radius" | "majorRadius" | "minorRadius"> {
    type: ELineType.SEGMENT;
}
declare interface ILineDataCircle extends Omit<ILineBase, "ellipseCenter" | "majorRadius" | "minorRadius"> {
    type: ELineType.CIRCLE_ARC;
    clockwise: boolean;
    minorArc: boolean;
    radius: number;
}
declare interface IListTopModelsOptions extends ICustomModelFilter {
    categories?: number[];
    toolType?: EToolType | EToolType[];
}
declare interface IModelBaseInfo {
    type: string;
    translate: [
        number,
        number,
        number
    ];
    rotate: [
        number,
        number,
        number
    ];
    name: string;
    instanceId: string;
}
declare interface IModelCostInfo {
    unitPrice: string;
    quantity: string;
    unitCost: string;
    aditionalFee: string;
    nonStandardCoef: string;
    price: number;
    quotationUnit: string;
    unitNumber: number;
    description: number;
    [key: string]: any;
}
declare interface IModelIsAssociatedOrderOption {
    modelIds: string | string[];
    excludeOrderId?: string;
}
declare interface IModelIsAssociatedOrderResult {
    modelId: string;
    orderId: string | null;
    orderNo: string | null;
}
declare interface IModelLockOption {
    modelId: string;
    businessId: string;
}
declare interface IOrderAction {
    name: string;
    key: string;
    extra?: IOrderJumpActionExtra;
}
declare interface IOrderAttachment {
    uploadKey: string;
    name: string;
    url: string;
}
declare interface IOrderAttachmentItem extends IOrderAttachment {
    attachmentId: number;
    orderState: number;
    orderStateName: string;
    userId: string;
    userName: string;
    userAvatar: string;
    createTime: number;
    attachmentType: EAttachmentType | null;
    auths: EOrderFieldDataAuth[];
}
declare interface IOrderAttachments extends IOrderAttachment {
    attachmentType: EAttachmentType | null;
    auths: EOrderFieldDataAuth[];
}
declare interface IOrderBaseInfo {
    orderId: string;
    orderNo: string;
    orderName: string;
    orderState: number;
    orderStateName: string;
}
declare interface IOrderData extends IOrderBaseInfo, ICustomerBaseInfo {
    externalCode?: string;
    orderType: EToolType[];
    processDirect: EProcessDirect;
    placeTime: number;
    customerId: string;
    customerSource: ECustomerSource;
    storeId: string;
    storeName: string;
    contactUser: string;
    contactPhone: string;
    shippingAddr: string;
    remark: string;
    orderRemarks: IOrderRemarks[];
    attachments: IOrderAttachments[];
    designId: string;
    designName: string;
    designerName: string;
    designerId: string;
    customFields: Record<string, string | boolean | number>;
    designRemark: string;
    designRemarks: IOrderRemarks[];
    designAttachments: IOrderAttachments[];
    auditOrderNo: string;
    auditorName: string;
    auditRemarks: IAuditOrderRemarks[];
    auditAttachments: IOrderAttachments[];
    payAmt: string;
    actualPayAmt: string;
    payRecordCreator: string;
    payCertificateSendBack: boolean;
    payCertificates: IOrderAttachment[];
    events: IOrderEvent[];
    emergencyLevel: number;
    emergencyLevelName: string;
    receiptType: number;
    receiptTypeName: string;
    parentOrderId: string;
    parentOrderName: string;
    creatorName: string;
    actions: IOrderAction[];
    deleted: boolean;
    processSetPlanCompleteTime?: number;
    repetitions: number;
    levelId?: string;
    levelIndex?: number;
}
declare interface IOrderEvent {
    name: string;
    key: string;
    nextState: number;
    nextStateName: string;
    eventType: EOrderEventType;
}
declare interface IOrderEventData {
    key: string;
    name: string;
}
declare interface IOrderField {
    key: string;
    type: EOrderFieldType;
    name: string;
    desc?: string;
    isVisible: boolean;
    isEditable: boolean;
    required?: boolean;
    optionalValue?: string;
    defaultValue?: any;
    optionalItems?: IFieldOptionItem[];
}
declare interface IOrderJumpActionExtra {
    targetMiniappId: string;
    targetMiniappName: string;
    info?: {
        needWriteOrderId?: boolean;
        needWriteMergeId?: boolean;
        customContent?: string;
    };
}
declare interface IOrderModel {
    modelIds: string[];
    subModels: ISubModel[];
}
declare interface IOrderOperation {
    operator: string;
    operationName: string;
    roleName: string;
    role: number;
    operation: number;
    createTime: string;
}
declare interface IOrderOperationData {
    key: string;
    name: string;
    type: EOrderOperationType;
}
declare interface IOrderRemarks extends IGetOrderRemarksResult {
    friendlyTime: string;
}
declare interface IOrderStateData {
    name: string;
    key: number;
}
declare interface IPaginationQuery {
    start?: number;
    pageSize?: number;
}
declare interface IPaginationResult<T> {
    totalCount: number;
    count: number;
    hasMore: boolean;
    result: T[];
}
declare interface IParameter {
    category?: string;
    name: string;
    displayName?: string;
    value: string;
    valueType?: string;
    setType?: string;
    min?: string;
    max?: string;
    options?: IParameterOption[];
}
declare interface IParameterOption {
    name?: string;
    value: string;
    ignore?: string;
    priority?: string;
}
declare interface IParamModelDirection {
    x: number;
    y: number;
    z: number;
    isCustom: boolean;
}
declare interface IParamModelEdges {
    mStart: Number2___qunhe_custom_apass_api;
    mEnd: Number2___qunhe_custom_apass_api;
}
declare interface IParamModelLite {
    readonly id: string;
    type: ECustomModelType;
    readonly productId?: string;
    category: number;
    isHidden?: boolean;
    modelType?: EParamModelType;
    toolType?: EToolType;
    isRoot?: boolean;
    readonly legal?: boolean;
    readonly size: Number3___qunhe_custom_apass_api;
    readonly version: number;
    getProductCode(): string | undefined;
    getName(): string;
    setName(name: string): void;
    getRemark(): string | undefined;
    setRemark(remark: string): void;
    setPosition(position: Number3___qunhe_custom_apass_api): void;
    getPosition(): Number3___qunhe_custom_apass_api;
    setRotation(rotation: Number3___qunhe_custom_apass_api): void;
    getRotation(): Number3___qunhe_custom_apass_api;
    getToolType(): EToolType | undefined;
    setToolType(toolType: EToolType): void;
    clone(): this;
    dispose(): void;
    getParent(): this | undefined;
    getRoot(recursion?: boolean): this;
    getChild(): this[];
    getAccessory(): this[];
    appendChild(child: this): void;
    appendAccessory(accessory: this): void;
    getParam(name: string): IParamModelLiteParam | undefined;
    getBzProperty(name: string, namespace?: string): IBzParamModelLiteProperty | undefined;
    hasBzProperty(name: string, namespace?: string): boolean;
    removeBzProperty(name: string, namespace?: string): boolean;
    hasParam(name: string): boolean;
    toJSON(): Record<string, any>;
    serialize(): string;
    deserialize(model: string): this;
    forceUpdate(update?: boolean): void;
    getElementId(): ElementId_2 | undefined;
    getPlankPathAsync(): Promise<IParamModelLitePlankPath | undefined>;
    getFloorplanRelation(): Readonly<IFloorplanRelation> | undefined;
    setFloorplanRelation(floorplanRelation: Omit<IFloorplanRelation, "hostIds">): void;
}
declare interface IParamModelLiteParam<T = any> {
    getName(): string;
    getDescription(): string | undefined;
    getSimpleName(): string | undefined;
    getDisplayName(): string;
    getVisible(): boolean | undefined;
    isOverridden(): boolean | undefined;
    cancelOverride(): any;
    getUnitType(): EParamUnitType | undefined;
    getParamForm(): EParamForm | undefined;
    isUsingMixFormula(): boolean;
    useMixFormula(): void;
    getValue(): T;
    getParsedValue(): T;
    setValue(value: T): any;
    setParsedValue(value: T): any;
    getType(): EParamType;
    getMax(): number | undefined;
    getMin(): number | undefined;
    getStep(): number | undefined;
    getOptionValues(): NamedValue[];
    getEditable(): boolean;
    getRecommendValues(): NamedValue[];
    getValueDisplayNames(): string[];
    getPrimitiveOverride(): boolean;
    setPrimitiveOverride(value: boolean): void;
    getPackageId(): string | undefined;
}
declare interface IParamModelLitePlankPath {
    getOriginOuterPath(): IPathData;
    getOriginInnerHoles(): IPathData[];
    getOriginSlots(): ISlotData[];
    getOuterPath(): IPathData;
    getInnerHoles(): IPathData[];
    getSlots(): ISlotData[];
    setOuterPath(path: IPathData): void;
    setInnerHoles(holes: IPathData[]): void;
    setSlots(slots: ISlotData[]): void;
}
declare interface IParamModelOptionValues {
    name: string;
    value: string;
    ignore: string;
    parsedIgnore: boolean;
    [key: string]: any;
}
declare interface IParamModelParameter {
    globalId: string;
    name: string;
    simpleName: string;
    type: string;
    paramTypeId: number;
    value: string;
    formula: string;
    editable: boolean;
    valueDisplayNames: Array<string>;
    optionValues: IParamModelOptionValues[];
    options: Array<string>;
    recommendValues: IParamModelOptionValues[];
    recommends: Array<string>;
    min: string;
    max: string;
    step: string;
    link: string;
    displayName: string;
    formulaDisplayName: string;
    ignored: boolean;
    override: boolean;
    overridable: boolean;
    primitiveOverride: boolean;
    status: number;
    instanceGroupTypeId: number;
    description: string;
    visible: boolean;
    required: boolean;
    originalValue: string;
    quotationValue: string;
    [key: string]: any;
}
declare interface IPartnerOrder {
    orderCode: string;
    customerName: string;
    telephone: string;
    sex: string;
    detailAddress: string;
    source: string;
    sourceRemark: string;
    remark: string;
    buildingVillage: string;
    address: string;
    size: number;
    houseType: string;
    houseStatus: string;
    houseModel: string;
    orderTime: string;
    needDeposit: boolean;
    depositCost: string;
    decoStyle: string;
    providedPlan: boolean;
    decoBudget: string;
    designCost: string;
    constructCost: string;
    beginDate: string;
    endDate: string;
    manager: string;
    supervisor: string;
    currentNodeKey: string;
    discard: number;
    discardReason: string;
    associatedSchema: IAssociatedSchema[];
    location: string;
    createTime: string;
    statusId: number;
    obsDesignerUserId: string;
    designerName: string;
    salesman: string;
    storeName: string;
    storeCode: string;
    storeAddress: string;
    storeSpecificAddress: string;
    storeTelephone: string;
    signDate: string;
    deliveryDate: string;
    associatedNumber: string;
    [key: string]: any;
}
declare interface IPathData {
    paramPoints: IPlankPathPoint[];
    paramPathLines: IPlankPathLine[];
    [key: string]: any;
}
declare interface IPlankDrawingResult {
    url: string;
}
declare interface IPlankFaceId {
    plankFaceId: number;
}
declare interface IPlankPathData {
    path?: IPlankPathPathData;
    holes: IPlankPathPathData[];
    [key: string]: any;
}
declare interface IPlankPathLine {
    type: EPlankPathLineType;
    clockwise?: boolean;
    minorArc?: boolean;
    radius?: string;
}
declare type IPlankPathLineData = ILineBase;
declare interface IPlankPathPathData {
    offset?: number;
    paramPoints: IPlankPathPointData[];
    paramPathLines: IPlankPathLineData[];
}
declare interface IPlankPathPoint {
    position: string;
    type: EPlankPathPointType;
    cornerCutDistance?: [
        string,
        string
    ];
    clockwise?: boolean;
    radius?: string;
    offset?: string;
    majorRadius?: string;
    minorRadius?: string;
}
declare type IPlankPathPointData = IPointBase;
declare interface IPointBase {
    type: EPointType;
    position: Number3___qunhe_custom_apass_api | Number2___qunhe_custom_apass_api;
    cornerCutDistance?: [
        number,
        number
    ];
    majorRadius?: number;
    minorRadius?: number;
    radius?: number;
    clockwise?: boolean;
    offset?: Number2___qunhe_custom_apass_api;
}
declare type IPointData = IPointWithNone | IPointWithLine | IPointWithCircle | IPointWithCutCircle;
declare interface IPointWithCircle extends Omit<IPointBase, "clockwise" | "majorRadius" | "minorRadius" | "cornerCutDistance" | "offset"> {
    type: EPointType.CIRCLE;
    radius: number;
}
declare interface IPointWithCutCircle extends Omit<IPointWithCircle, "type"> {
    type: EPointType.CUT_CIRCLE;
    clockwise: boolean;
}
declare interface IPointWithLine extends Omit<IPointBase, "clockwise" | "majorRadius" | "minorRadius" | "radius" | "offset"> {
    type: EPointType.LINE;
    cornerCutDistance: [
        number,
        number
    ];
}
declare interface IPointWithNone extends Omit<IPointBase, "cornerCutDistance" | "majorRadius" | "minorRadius" | "radius" | "offset"> {
    type: EPointType.NONE;
    clockwise?: boolean;
}
declare interface IPreviewImgInfo {
    modelId: string;
    imgData: string;
}
declare interface IPreviewMaterialDrawing {
    url: string;
}
declare interface IPreviewMaterialDrawingsOption {
    planks?: IPreviewPlankDrawingOption[];
    structures?: IPreviewStructureDrawingOption[];
}
declare interface IPreviewMaterialDrawingsResult {
    planks?: IPreviewMaterialDrawing[];
    structures?: IPreviewMaterialDrawing[];
}
declare type IPreviewPlankDrawingOption = Pick<IBomPlankEditable<Partial<IBomPlankHoleWithId> & IBomPlankHoleEditable, Partial<IBomPlankGrooveWithId> & IBomPlankGrooveEditable, any>, "thickness" | "textureDirection" | "finishedWidth" | "finishedHeight" | "finishedProfile" | "holes" | "grooves" | "inners"> & {
    modelId?: string;
    orderId?: string;
};
declare type IPreviewStructureDrawingOption = Pick<IBomStructureEditable<Partial<IBomPlankHoleWithId> & IBomPlankHoleEditable, Partial<IBomPlankGrooveWithId> & IBomPlankGrooveEditable>, "length" | "width" | "thickness" | "holes" | "grooves" | "textureDirection"> & {
    modelId?: string;
    orderId?: string;
};
declare interface IProduct {
    productId: string;
    category: number;
    name: string;
    coverImgUrl?: string;
    sketchImgUrl?: string;
    customFields?: IProductCustomField[];
    ownerCompanyId: string;
}
declare interface IProductCodeInOrder {
    productCodeInOrder?: string;
}
declare interface IProductCodeInOrderInfo {
    productId: string;
    codeInOrder?: string;
}
declare interface IProductCustomField {
    id: string;
    name: string;
    code: string;
    value: string;
}
declare interface IProductOrderOperation extends IOrderOperation {
    productOrderCode: string;
    [key: string]: any;
}
declare interface IProfileOrPath {
    points: number[];
    curves: Array<{
        type: "LineSeg" | "Arc";
        isCCW?: boolean;
        minorArc?: boolean;
        radius?: number;
        bulge?: number;
    }>;
}
declare interface IQuotationOption {
    uploadFileKey: string;
    hlkTemplateId?: string;
}
declare interface IQuotationReportTemplateItem {
    templateId: string;
    name: string;
}
declare interface IQuotationReportTemplateParam {
    activeToolType?: EToolType;
    libraryType?: Array<EQuotationLibraryType>;
}
declare interface IQuotationResult {
    url: string;
}
declare interface IReferenceModel extends IModelBaseInfo {
    refId: string;
    parameters: IParameter[];
}
declare interface IRefModel {
    id: string;
    templateName?: string;
    position: Number3___qunhe_custom_apass_api;
    rotation: Number3___qunhe_custom_apass_api;
    templateId: string;
}
declare interface IRefModelLite {
    elementId: ElementId_2;
    position: Number3___qunhe_custom_apass_api;
    rotation: Number3___qunhe_custom_apass_api;
    size: Number3___qunhe_custom_apass_api;
    templateInfo: {
        isAssembly: boolean;
    } & CustomModelSimple;
    toolType: EToolType;
}
declare interface IRefModelTemplate {
    id: string;
    name?: string;
    refModels: IRefModel[];
    customModel: ITemplateSource;
}
declare interface IRelatedOrderResult {
    customerOrderId?: string;
    auditOrderId?: string;
}
declare interface IReplaceParamModelInfoOption {
    inherit?: boolean;
}
declare interface IResource {
    profiles: IResourceProfiles[];
    texture: IResourceTexture[];
    models: IResourceModels[];
    patterns: IResourcePatterns[];
    [key: string]: any;
}
declare interface IResourceModels {
    obsBrandGoodId: string;
    brandGoodName: string;
    prodCatId: number;
    available: boolean;
    obsAccountId: string;
    brandName: string;
    seriesTagName: string;
    onSale: boolean;
    billOutput: boolean;
    price: number;
    unitCost: number;
    unitPrice: number;
    [key: string]: any;
}
declare interface IResourcePatterns {
    obsrootAccountId: string;
    [key: string]: any;
}
declare interface IResourceProfiles {
    obsBrandGoodId: string;
    brandGoodCode: string;
    productNumber: string;
    brandGoodName: string;
    prodCatId: number;
    available: boolean;
    customCode: string;
    obsAccountId: string;
    brandName: string;
    seriesTagName: string;
    dimensions: string;
    customTexture: string;
    description: string;
    onSale: boolean;
    customFields: ICustomField[];
    obsProfileBrandGoodId: string;
    obsProfileAccountId: string;
    profileCustomCode: string;
    billOutput: boolean;
    Tagkeys: IResourceProfilesTagKey[];
    [key: string]: any;
}
declare interface IResourceProfilesTagKey {
    obsTagKeyId: string;
    tagKeyName: string;
    single: boolean;
    tags: IResourceTagKeyTag[];
    [key: string]: any;
}
declare interface IResourceTagKeyTag {
    obsTagId: string;
    tagName: string;
}
declare interface IResourceTexture {
    obsBrandGoodId: string;
    brandGoodName: string;
    prodCatId: number;
    available: boolean;
    obsAccountId: string;
    brandName: string;
    seriesTagName: string;
    onSale: boolean;
    baseTexture: string;
    [key: string]: any;
}
declare interface IRoomData {
    name: string;
    roomId: string;
    [key: string]: any;
}
declare interface IRoomInfo {
    roomId?: string;
    roomName?: string;
}
declare interface IRuleResult {
    id: string;
    name: string;
    ruleTemplates: RuleTemplate[];
    [s: string]: any;
}
declare interface ISaveFittingDesignOption extends IFittingDesignOption {
    data: IFittingDesignData;
}
declare interface ISlotData {
    path: IPathData;
    deep: string;
    slotCaveType: SlotCaveType;
    [key: string]: any;
}
declare interface ISplitDesignParams {
    elements: Array<ElementId_2>;
    name: string;
    lock?: boolean;
}
declare interface ISplitDesignResponse {
    designId: string;
}
declare interface IStorageGetKeysOption {
    pageSize?: number;
    pageNum?: number;
    prefix?: string;
}
declare interface IStorageGetKeysResult extends IPaginationResult<string> {
    curPage: number;
    totalPage: number;
}
declare interface IStorageItem {
    key: string;
    value: Record<string, any>;
}
declare interface IStoreInfo {
    storeId: string;
    storeName: string;
    contactUser: string;
    contactPhone: string;
    shippingAddr: string;
}
declare interface IStructureDrawingResult {
    url: string;
}
declare interface ISubModel {
    modelId: string;
    rootModelId: string;
    combinationModelId?: string;
}
declare interface ISuccessfulOrder {
    orderId: string;
    orderNo: string;
    orderName: string;
}
declare interface ISweepModel extends IModelBaseInfo {
    texture?: {
        id: string;
        angle?: number;
    };
    profile: IImportPath;
    profileId: string;
    angle?: number;
    path: IImportPath;
}
declare interface ISwitchOrderOption {
    orderId: string;
    miniappUploadId?: string;
}
declare type ITemplateSource = (ITemplateSourceModel | ITemplateSourceAssembly) & {
    type: ECustomModelType;
};
declare interface ITemplateSourceAssembly extends ICustomModelAssemblySimple {
    params: IParamModelLiteParam[];
}
declare interface ITemplateSourceModel extends ICustomModelPureSimple {
    params?: IParamModelLiteParam[];
}
declare interface ITexture {
    topId: string;
    bottomId?: string;
    sideId?: string;
    angle?: number;
    sideAngle?: number;
    pavingStyle?: string;
    offset?: [
        number,
        number
    ];
    size?: number[];
}
declare interface ITriggerBoolEffectResult {
    success: boolean;
    code?: BoolEffectCode;
}
declare interface IUnlockAuditedModelOption {
    modelId: string | string[];
}
declare interface IUpdateCustomerOption extends Partial<ICustomerBaseInfo> {
    customerId: string;
    customerName: string;
    storeId?: string;
}
declare interface IUpdateCustomModelSimpleOption extends Partial<ICustomModelSimpleChangeable> {
    elementId: ElementId_2;
}
declare interface IUpdateOrderModel extends IOrderModel {
    orderId: string;
}
declare interface IUpdateOrderOption {
    orderId: string;
    fields: Record<string, any>;
}
declare interface IUpdateOrderStateRemarkOption {
    remarkId: string;
    orderId: string;
    content: string;
    orderState: number;
    images?: IOrderAttachment[];
}
declare interface IUpdateRefModelOption {
    id: string;
    position?: Number3___qunhe_custom_apass_api;
    rotation?: Number3___qunhe_custom_apass_api;
}
declare interface IUploadFileInfo {
    name: string;
    fileBase64: string;
}
declare interface IUploadFileOption {
    fileInfos: IUploadFileInfo | IUploadFileInfo[];
    parallelCount?: number;
    retryCount?: number;
    bucket?: EUploadFileBucket;
}
declare interface IUploadFileResult {
    code: number;
    name: string;
    url?: string;
    uploadKey?: string;
    msg?: string;
}
declare interface IUserDefinedInfo {
    name: string;
    value: string;
    [key: string]: any;
}
declare interface IUserDetails {
    userId: string;
    company: {
        id: string;
        name: string;
    };
    userName: string;
    businessName: string;
}
declare interface IValidityResult {
    id: string;
    name: string;
    children: IValidityResult[];
    [s: string]: any;
}
declare interface JsonData {
    [key: string]: string | number | boolean | undefined | null | JsonData;
}
declare interface KArc2d extends KBoundedCurve2d_2 {
    readonly center: KPoint2d;
    readonly radius: number;
    readonly middlePoint: KPoint2d;
    readonly isCCW: boolean;
    readonly startAngle: number;
    readonly endAngle: number;
    readonly arcAngle: number;
    readonly circle: KCircle2d;
    getParamFromAngle(angle: number): number;
    getRegularizeAngleFromParam(param: number): number;
    getTangent(point: KPoint2d): KVector2d;
    transform(matrix: KMatrix3): boolean;
    getApproximatePointsByRatio(ratio?: number): KPoint2d[];
    getApproximatePointsBySagitta(sagitta?: number): KPoint2d[];
    getApproximatePointsByAngle(angleSpan?: number): KPoint2d[];
}
declare interface KArc3d_2 extends KBoundedCurve3d_2 {
    readonly center: KPoint3d;
    readonly normal: KVector3d;
    readonly radius: number;
    readonly middlePoint: KPoint3d;
    readonly startAngle: number;
    readonly endAngle: number;
    readonly arcAngle: number;
    readonly circle: KCircle3d;
    getTangent(point: KPoint3d): KVector3d;
    transform(matrix: KMatrix4): boolean;
    regularize(): void;
    getApproximatePointsByRatio(ratio?: number): KPoint3d[];
    getApproximatePointsBySagitta(sagitta?: number): KPoint3d[];
    getApproximatePointsByAngle(angleSpan?: number): KPoint3d[];
}
declare interface KBoundedCurve2d_2 extends KCurve2d {
    readonly startPoint: KPoint2d;
    readonly endPoint: KPoint2d;
    readonly middlePoint: KPoint2d;
    readonly length: number;
    getApproximatePointsInfoByRatio(ratio?: number, areaTol?: number): {
        points: KPoint2d[];
        params: number[];
    };
    getApproximatePointsInfoByNormalTol(normTol?: number, areaTol?: number): {
        points: KPoint2d[];
        params: number[];
    };
    splitByPoints(points: KPoint2d[], distTol?: number): KBoundedCurve2d_2[];
    splitByPoint(point: KPoint2d, distTol?: number): KBoundedCurve2d_2[];
    getBounding(): KBoundingBox2d;
}
declare interface KBoundedCurve3d_2 extends KCurve3d {
    readonly startPoint: KPoint3d;
    readonly endPoint: KPoint3d;
    readonly middlePoint: KPoint3d;
    readonly length: number;
    getApproximatePointsInfoByRatio(ratio?: number, areaTol?: number): {
        points: KPoint3d[];
        params: number[];
    };
    getApproximatePointsInfoByNormalTol(normTol?: number, areaTol?: number): {
        points: KPoint3d[];
        params: number[];
    };
    splitByPoints(points: KPoint3d[], distTol?: number): KBoundedCurve3d_2[];
    splitByPoint(point: KPoint3d, distTol?: number): KBoundedCurve3d_2[];
}
declare interface KBoundingBox2d {
    readonly min: KPoint2d;
    readonly max: KPoint2d;
    readonly center: KPoint2d;
    readonly width: number;
    readonly height: number;
    isEqual(other: KBoundingBox2d, tolerance?: number): boolean;
    addPoint(point: KPoint2d): void;
    expand(delta: number): void;
    merge(box: KBoundingBox2d): void;
    isValid(): boolean;
    isPointInside(point: KPoint2d, tolerance?: number): boolean;
    isInside(other: KBoundingBox2d, tolerance?: number): boolean;
    isOverlapping(other: KBoundingBox2d, tolerance?: number): boolean;
}
declare interface KBoundingBox3d {
    readonly min: KPoint3d;
    readonly max: KPoint3d;
    readonly center: KPoint3d;
    readonly width: number;
    readonly height: number;
    readonly depth: number;
    isEqual(other: KBoundingBox3d, tolerance?: number): boolean;
    addPoint(point: KPoint3d): void;
    expand(delta: number): void;
    merge(box: KBoundingBox3d): void;
    isValid(): boolean;
    isPointInside(point: KPoint3d, tolerance?: number): boolean;
    isInside(other: KBoundingBox3d, tolerance?: number): boolean;
    isOverlapping(other: KBoundingBox3d, tolerance?: number): boolean;
}
declare interface KCircle2d extends KCurve2d {
    readonly center: KPoint2d;
    readonly radius: number;
    getTangent(point: KPoint2d): KVector2d;
    transform(matrix: KMatrix3): boolean;
}
declare interface KCircle3d extends KCurve3d {
    readonly center: KPoint3d;
    readonly radius: number;
    readonly normal: KVector3d;
    getTangent(point: KPoint3d): KVector3d;
    transform(matrix: KMatrix4): boolean;
    getPlane(): KPlane;
}
declare interface KCurve2d {
    getType(): KCurve2dType_2;
    getInterval(): KInterval;
    getPoint(t: number): KPoint2d;
    getParam(point: KPoint2d): number;
    transform(matrix: KMatrix3): boolean;
    isPointOnCurve(point: KPoint2d, tolerance?: number): boolean;
    getClosestPoint(point: KPoint2d, tolerance?: number): KPoint2d;
    isEqual(curve: KCurve2d, tolerance?: number): boolean;
    clone(): KCurve2d;
    reverse(): void;
    getCurvatureVector(point: KPoint2d): KVector2d;
}
declare enum KCurve2dType_2 {
    Line2d = "line2d",
    Circle2d = "circle2d",
    LineSeg2d = "lineSeg2d",
    Arc2d = "arc2d",
    EllipticalArc2d = "ellipticalArc2d",
    PolylineCurve2d = "polylineCurve2d",
    NurbsCurve2d = "nurbsCurve2d"
}
declare interface KCurve3d {
    getType(): KCurve3dType_2;
    getInterval(): KInterval;
    transform(matrix: KMatrix4): boolean;
    getPoint(t: number): KPoint3d;
    getParam(point: KPoint3d): number;
    isPointOnCurve(point: KPoint3d, tolerance?: number): boolean;
    getClosestPoint(point: KPoint3d, tolerance?: number): KPoint3d;
    isEqual(other: KCurve3d, tolerance?: number): boolean;
    clone(): KCurve3d;
    reverse(): void;
    getCurvatureVector(point: KPoint3d): KVector3d;
}
declare enum KCurve3dType_2 {
    Line3d = "line3d",
    Circle3d = "circle3d",
    LineSeg3d = "lineSeg3d",
    Arc3d = "arc3d",
    EllipticalArc3d = "ellipticalArc3d",
    PolylineCurve3d = "polylineCurve3d"
}
declare enum KCurveInLoopType_2 {
    NotIn = 0,
    OnEdge = 1,
    FullyIn = 2,
    PartiallyIn = 3
}
declare interface KCurvesIntersectRet {
    point: KPoint3d;
    isOverlap?: boolean;
}
declare interface KCurveSurfaceIntersectRet {
    intersectType: KCurveSurfaceIntersectType_2;
    point?: KPoint3d;
    curve?: KCurve3d;
}
declare enum KCurveSurfaceIntersectType_2 {
    Point = 1,
    Curve = 2
}
declare interface KEllipticalArc2d extends KBoundedCurve2d_2 {
    readonly center: KPoint2d;
    readonly majorRadius: number;
    readonly minorRadius: number;
    readonly startParam: number;
    readonly endParam: number;
    readonly isCCW: boolean;
    getTangent(point: KPoint2d): KVector2d;
}
declare interface KEuler {
    readonly x: number;
    readonly y: number;
    readonly z: number;
    order: KEulerOrder;
    set(x: number, y: number, z: number, order: KEulerOrder): void;
    clone(): KEuler;
    copyFrom(euler: KEuler): void;
    setFromRotationMatrix(m: KMatrix4, order?: KEulerOrder): void;
    setFromQuaternion(q: KQuaternion, order?: KEulerOrder): void;
    setFromVector3(v: KVector3d, order?: KEulerOrder): void;
    reorder(newOrder: KEulerOrder): void;
    isEqual(euler: KEuler): boolean;
    fromArray(array: any[]): void;
    toArray(): any[];
    toVector(): KVector3d;
}
declare enum KEulerOrder {
    XYZ = 0,
    YZX = 1,
    ZXY = 2,
    XZY = 3,
    YXZ = 4,
    ZYX = 5
}
declare interface KFace2d {
    contour: KBoundedCurve2d_2[];
    holes: KBoundedCurve2d_2[][];
}
declare interface KFace3d_2 {
    contour: KBoundedCurve3d_2[];
    holes: KBoundedCurve3d_2[][];
}
declare interface KFaceBooleanRet {
    success: boolean;
    resultFaces: KFace2d[];
}
declare enum KFaceBooleanType_2 {
    union = 0,
    intersect = 1,
    subtract = 2
}
declare interface KGeomFace2d_2 {
    getContour(): KGeomLoop2d;
    getHoles(): KGeomLoop2d[];
    area(): number;
    isPositive(): boolean;
    isValid(): boolean;
    containsPoint(point: KPoint2d, bIncludeOn?: boolean): boolean;
}
declare interface KGeomLib_2 {
    createPoint2d(x: number, y: number): KPoint2d;
    createPoint3d(x: number, y: number, z: number): KPoint3d;
    createVector2d(x: number, y: number): KVector2d;
    createVector3d(x: number, y: number, z: number): KVector3d;
    createBoundingBox2d(minPoint: KPoint2d, maxPoint: KPoint2d): KBoundingBox2d;
    createBoundingBox3d(minPoint: KPoint3d, maxPoint: KPoint3d): KBoundingBox3d;
    createIdentityMatrix3(): KMatrix4;
    createTranslationMatrix3(x: number, y: number): KMatrix3;
    createRotateMatrix3(angle: number, center: KPoint2d): KMatrix3;
    createScaleMatrix3(x: number, y: number): KMatrix3;
    createIdentityMatrix4(): KMatrix4;
    createTranslationMatrix4(x: number, y: number, z: number): KMatrix4;
    createRotateMatrix4(angle: number, a: KVector3d, center: KPoint3d): KMatrix4;
    createScaleMatrix4(x: number, y: number, z: number): KMatrix4;
    createAlignCCSMatrix4(x: KVector3d, y: KVector3d, z: KVector3d, origin: KPoint3d): KMatrix4;
    createLine2d(point: KPoint2d, direction: KVector2d): KLine2d;
    createLine3d(point: KPoint3d, direction: KVector3d): KLine3d_2;
    createLineSegment2d(startPoint: KPoint2d, endPoint: KPoint2d): KLineSegment2d;
    createLineSegment3d(startPoint: KPoint3d, endPoint: KPoint3d): KLineSegment3d_2;
    createPolylineCurve2d(points: KPoint2d[]): KPolylineCurve2d;
    createPolylineCurve3d(points: KPoint3d[]): KPolylineCurve3d;
    createEllipticalArc2dByStartEndPoints(center: KPoint2d, xDirection: KVector2d, majorRadius: number, minorRadius: number, startPoint: KPoint2d, endPoint: KPoint2d, isCCW: boolean): KEllipticalArc2d;
    createEllipticalArc2dByStartEndParams(center: KPoint2d, xDirection: KVector2d, majorRadius: number, minorRadius: number, startParam: number, endParam: number, isCCW: boolean): KEllipticalArc2d;
    createArc2dByThreePoints(startPoint: KPoint2d, midPoint: KPoint2d, endPoint: KPoint2d): KArc2d | null;
    createArc2dByStartEndPoints(center: KPoint2d, startPoint: KPoint2d, endPoint: KPoint2d, isCCW: boolean): KArc2d;
    createArc2dByStartEndAngles(center: KPoint2d, radius: number, startAngle: number, endAngle: number, isCCW: boolean): KArc2d;
    createArc2dByArcAngle(center: KPoint2d, radius: number, startAngle: number, arcAngle: number): KArc2d;
    createArc2dByChordAndBulge(chord: KLineSegment2d, signedBulge: number): KArc2d;
    createArc2dByPointsAndStartTangent(start: KPoint2d, end: KPoint2d, startTangent: KVector2d, cosTol?: number): KArc2d | null;
    createArc2dByTwoPointsAndRadius(startPoint: KPoint2d, endPoint: KPoint2d, radius: number, isMinorArc: boolean, isCCW: boolean): KArc2d;
    createArc3dByThreePoints(startPoint: KPoint3d, midPoint: KPoint3d, endPoint: KPoint3d): KArc3d_2 | null;
    createArc3dByCenterNormalRadius(center: KPoint3d, normal: KVector3d, radius: number, startPoint: KPoint3d, endPoint: KPoint3d): KArc3d_2;
    createArc3dByStartEndAngles(center: KPoint3d, xDir: KVector3d, yDir: KVector3d, radius: number, startAngle: number, endAngle: number): KArc3d_2;
    createCircle2dByThreePoints(startPoint: KPoint2d, midPoint: KPoint2d, endPoint: KPoint2d): KCircle2d | null;
    createCircle2dByCenterAndPoint(center: KPoint2d, pointOnCircle: KPoint2d): KCircle2d | null;
    createCircle2dByCenterAndRadius(center: KPoint2d, radius: number): KCircle2d;
    createCircle3dByThreePoints(startPoint: KPoint3d, midPoint: KPoint3d, endPoint: KPoint3d): KCircle3d | null;
    createCircle3dByCenterNormalRadius(center: KPoint3d, normal: KVector3d, radius: number, xDir?: KVector3d): KCircle3d;
    createCircle3dByLCSRadius(center: KPoint3d, xDir: KVector3d, yDir: KVector3d, radius: number): KCircle3d;
    createNurbsCurve2d(degree: number, knots: number[], controlPoints: KPoint2d[], weights: number[]): KNurbsCurve2d;
    createPlaneByThreePoints(point1: KPoint3d, point2: KPoint3d, point3: KPoint3d): KPlane | null;
    createPlaneByPointNormal(point: KPoint3d, normal: KVector3d, xDirection?: KVector3d): KPlane;
    createGeomLoop2d(curves: KBoundedCurve2d_2[]): KGeomLoop2d;
    createGeomLoop2dByPoints(points: KPoint2d[]): KGeomLoop2d;
    createGeomFace2d(contour: KGeomLoop2d, holes?: KGeomLoop2d[]): KGeomFace2d_2;
    areCurvesTotallyOverlap(curve1: KCurve3d, curve2: KCurve3d, tolerance?: number): boolean;
    areCurvesOverlap(curve1: KCurve3d, curve2: KCurve3d, tolerance?: number): boolean;
    curveIntersectCurve(curve1: KCurve3d, curve2: KCurve3d, tolerance?: number): KCurvesIntersectRet[];
    curveIntersectSurface(curve: KCurve3d, surface: KSurface, tolerance?: number): KCurveSurfaceIntersectRet[];
    planeIntersectPlane(plane1: KPlane, plane2: KPlane, tolerance?: number): KLine3d_2 | null;
    pointInLoop2D(point: KPoint2d, loop: KLoop2d, tolerance?: number): KPtInLoopResult;
    pointInFace2D(point: KPoint2d, faceLoops: KFace2d, tolerance?: number): KPtInLoopResult;
    loop2DInLoop2D(checkLoop: KLoop2d, baseLoop: KLoop2d, bIncludeOn?: boolean, tolerance?: number): boolean;
    boundedCurve2dInLoop2D(checkCurve: KBoundedCurve2d_2, baseLoop: KLoop2d, tolerance?: number): KCurveInLoopType_2;
    faces2dBoolean(faces1: KFace2d[], faces2: KFace2d[], type: KFaceBooleanType_2): KFaceBooleanRet;
    createRotateMatrix4FromEuler(euler: KEuler): KMatrix4;
    createRotateMatrix4FromQuaternion(q: KQuaternion): KMatrix4;
    createEuler(x?: number, y?: number, z?: number, order?: KEulerOrder): KEuler;
    createQuaternion(x?: number, y?: number, z?: number, w?: number): KQuaternion;
    slerpFlatQuaternion(q1: number[], offset1: number, q2: number[], offset2: number, t: number): number[];
    createInterval(start: number, end: number): KInterval;
    createInvalidInterval(): KInterval;
    createIntervalFromValues(values: number[], numTol: number): KInterval;
    uniteIntervals(intervals: KInterval[]): KInterval[];
    intersectIntervals(intervals1: KInterval[], intervals2: KInterval[]): KInterval[];
    offsetPath2dWithDistances(path: KBoundedCurve2d_2[], deltas: number[], disTol?: number, cosTol?: number): KPath2dOffsetRet;
    curve2dsFromJson(jsonString: string): KCurve2d[];
}
declare interface KGeomLoop2d {
    getCurves(): KBoundedCurve2d_2[];
    area(): number;
    centroid(): KPoint2d;
    isCCW(): boolean;
    isValid(): boolean;
    isSelfIntersect(): boolean;
    containsPoint(point: KPoint2d, bIncludeOn?: boolean): boolean;
}
declare interface KInterval {
    readonly start: number;
    readonly end: number;
    readonly middle: number;
    readonly length: number;
    isEqual(other: KInterval, numTol?: number): boolean;
    clone(): KInterval;
    toString(): String;
    isValid(): boolean;
    offset(num: number): KInterval;
    expanded(length: number): KInterval;
    multiplied(num: number): KInterval;
    interpolate(alpha: number): number;
    contains(point: number, includeEnds: boolean, disTol?: number): boolean;
    containsInterval(interval: KInterval, includeEnds: boolean, disTol?: number): boolean;
    intersects(interval: KInterval, includeEnds: boolean, disTol?: number): boolean;
    intersection(interval: KInterval): KInterval;
    union(interval: KInterval): KInterval;
}
declare interface KLine2d extends KCurve2d {
    readonly origin: KPoint2d;
    readonly direction: KVector2d;
    readonly leftDirection: KVector2d;
    readonly rightDirection: KVector2d;
    side(point: KPoint2d, tol?: number): 1 | 0 | -1;
    onLeftSide(point: KPoint2d, tol?: number): boolean;
    onRightSide(point: KPoint2d, tol?: number): boolean;
}
declare interface KLine3d_2 extends KCurve3d {
    readonly origin: KPoint3d;
    readonly direction: KVector3d;
}
declare interface KLineSegment2d extends KBoundedCurve2d_2 {
    readonly middlePoint: KPoint2d;
    readonly direction: KVector2d;
}
declare interface KLineSegment3d_2 extends KBoundedCurve3d_2 {
    readonly middlePoint: KPoint3d;
    readonly direction: KVector3d;
}
declare interface KLoop2d {
    loop: KBoundedCurve2d_2[];
}
declare interface KLoop3d {
    loop: KBoundedCurve3d_2[];
}
declare interface KMatrix3 {
    inverse(throwOnDegenerate?: boolean): void;
    inversed(throwOnDegenerate?: boolean): KMatrix3;
    multiply(matrix: KMatrix3): void;
    multiplied(matrix: KMatrix3): KMatrix3;
    clone(): KMatrix3;
    isEqual(other: KMatrix3): boolean;
}
declare interface KMatrix4 {
    inverse(throwOnDegenerate?: boolean): void;
    inversed(throwOnDegenerate?: boolean): KMatrix4;
    multiply(matrix: KMatrix4): void;
    multiplied(matrix: KMatrix4): KMatrix4;
    clone(): KMatrix4;
    isEqual(other: KMatrix4, tolerance?: number): boolean;
}
declare interface KNurbsCurve2d extends KBoundedCurve2d_2 {
    readonly degree: number;
    readonly knots: number[];
    readonly controlPoints: KPoint2d[];
    readonly weights: number[];
    getTangent(point: KPoint2d): KVector2d;
}
declare interface KPath2dOffsetRet {
    resultPaths: KBoundedCurve2d_2[][];
    originalCurves: KBoundedCurve2d_2[];
    offsetCurves: KBoundedCurve2d_2[][];
    newCurves: KBoundedCurve2d_2[];
}
declare interface KPlane extends KSurface {
    readonly normal: KVector3d;
}
declare interface KPoint2d {
    readonly x: number;
    readonly y: number;
    isEqual(other: KPoint2d, tolerance?: number): boolean;
    added(vec: KVector2d): KPoint2d;
    subtracted(point: KPoint2d): KVector2d;
    distanceTo(other: KPoint2d): number;
    squaredDistanceTo(other: KPoint2d): number;
    clone(): KPoint2d;
    appliedMatrix3(matrix: KMatrix3): KPoint2d;
}
declare interface KPoint3d {
    readonly x: number;
    readonly y: number;
    readonly z: number;
    isEqual(other: KPoint3d, tolerance?: number): boolean;
    added(vector: KVector3d): KPoint3d;
    subtracted(point: KPoint3d): KVector3d;
    distanceTo(other: KPoint3d): number;
    squaredDistanceTo(other: KPoint3d): number;
    clone(): KPoint3d;
    appliedMatrix4(matrix: KMatrix4): KPoint3d;
}
declare interface KPolylineCurve2d extends KBoundedCurve2d_2 {
    readonly points: KPoint2d[];
    readonly params: number[];
    getTangent(point: KPoint2d): KVector2d;
}
declare interface KPolylineCurve3d extends KBoundedCurve3d_2 {
    readonly points: KPoint3d[];
    readonly params: number[];
    getTangent(point: KPoint3d): KVector3d;
}
declare interface KPtInLoopResult {
    ptInLoopType: KPtInLoopType_2;
    curve?: KBoundedCurve2d_2;
}
declare enum KPtInLoopType_2 {
    Inside = 1,
    Outside = 0,
    OnEdge = -1,
    OnVertex = -2
}
declare interface KQuaternion {
    readonly x: number;
    readonly y: number;
    readonly z: number;
    readonly w: number;
    set(x: number, y: number, z: number, w: number): void;
    clone(): KQuaternion;
    copyFrom(quaternion: KQuaternion): void;
    setFromEuler(euler: KEuler): void;
    setFromAxisAngle(axis: KVector3d, angle: number): void;
    getAxis(): KVector3d;
    getAngle(): number;
    setFromRotationMatrix(m: KMatrix4): void;
    setFromUnitVectors(vFrom: KVector3d, vTo: KVector3d): void;
    reverse(): void;
    conjugate(): void;
    dot(v: KQuaternion): number;
    lengthSq(): number;
    length(): number;
    normalize(): void;
    multiply(q: KQuaternion): void;
    premultiply(q: KQuaternion): void;
    multiplyQuaternions(a: KQuaternion, b: KQuaternion): void;
    slerp(qb: KQuaternion, t: number): void;
    swingTwistDecomposition(direction: KVector3d, twist: KQuaternion, swing: KQuaternion): void;
    isEqual(quaternion: KQuaternion): boolean;
    fromArray(array: number[]): void;
    toArray(): number[];
}
declare interface KSurface {
    getType(): KSurfaceType_2;
    transform(matrix: KMatrix4): boolean;
    isPointOnSurface(point: KPoint3d, tolerance?: number): boolean;
    isCurveOnSurface(curve: KCurve3d, tolerance?: number): boolean;
    getClosestPoint(point: KPoint3d, tolerance?: number): KPoint3d;
    distanceToPoint(point: KPoint3d): number;
    signedDistanceTo(point: KPoint3d): number;
    clone(): KSurface;
    coplanarUnsigned(other: KSurface, tolerance?: number): boolean;
    coplanar(other: KSurface, tolerance?: number): boolean;
    loopToUV(loop: KLoop3d): KUVLoop;
    faceToUV(face: KFace3d_2): KUVFace;
    getUVPoint(point: KPoint3d): KPoint2d;
}
declare enum KSurfaceType_2 {
    Plane = "plane"
}
declare type KUVFace = KFace2d;
declare type KUVLoop = KLoop2d;
declare interface KVector2d {
    readonly x: number;
    readonly y: number;
    readonly length: number;
    normalized(): KVector2d;
    reversed(): KVector2d;
    multiplied(scale: number): KVector2d;
    isEqual(other: KVector2d, distanceTolerance?: number, cosTolerance?: number): boolean;
    isPerpendicular(other: KVector2d, tolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isParallel(other: KVector2d, tolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isZero(tolerance?: number): boolean;
    angle(other: KVector2d): number;
    angleTo(other: KVector2d): number;
    added(other: KVector2d): KVector2d;
    subtracted(other: KVector2d): KVector2d;
    dot(other: KVector2d): number;
    cross(other: KVector2d): number;
    clone(): KVector2d;
    isSameDirection(other: KVector2d, distanceTolerance?: number, cosTolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isOnSameSide(other: KVector2d, distanceTolerance?: number, cosTolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isOpposite(other: KVector2d, distanceTolerance?: number, cosTolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    appliedMatrix3(matrix: KMatrix3): KVector2d;
}
declare interface KVector3d {
    readonly x: number;
    readonly y: number;
    readonly z: number;
    readonly length: number;
    normalized(): KVector3d;
    reversed(): KVector3d;
    multiplied(scale: number): KVector3d;
    isEqual(other: KVector3d, distanceTolerance?: number, cosTolerance?: number): boolean;
    isPerpendicular(other: KVector3d, tolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isParallel(other: KVector3d, tolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isZero(tolerance?: number): boolean;
    angle(other: KVector3d): number;
    angleTo(other: KVector3d, vecRef: KVector3d): number;
    added(other: KVector3d): KVector3d;
    subtracted(other: KVector3d): KVector3d;
    dot(other: KVector3d): number;
    cross(other: KVector3d): KVector3d;
    clone(): KVector3d;
    isSameDirection(other: KVector3d, distanceTolerance?: number, cosTolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isOnSameSide(other: KVector3d, distanceTolerance?: number, cosTolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    isOpposite(other: KVector3d, distanceTolerance?: number, cosTolerance?: number, checkFuzzyZeroVec?: boolean): boolean;
    appliedMatrix4(matrix: KMatrix4): KVector3d;
    appliedEuler(euler: KEuler): KVector3d;
    appliedQuaternion(quaternion: KQuaternion): KVector3d;
}
declare interface LeftPanelMountPointOptions {
}
declare type LeftPanelProductLibrary = "publicLibrary" | "enterpriseLibrary" | "personalLibrary";
declare type Locale = "zh_CN" | "en_US" | "de_DE" | "fr_FR" | "ja_JP" | "zh_TW" | "ru_RU" | "ko_KR" | "es_ES" | "ar_EG" | "en_CN" | "vi" | "it_IT" | "th_TH" | "id_ID" | "pl_PL";
declare interface MainMountPointOptions {
    windowMode?: "windowed" | "topWindowed" | "fullscreen" | "modal";
    position?: {
        x: number;
        y: number;
    };
    draggable?: boolean;
    resizable?: boolean | "width" | "height";
    minWidth?: number | undefined;
    maxWidth?: number | undefined;
    minHeight?: number | undefined;
    maxHeight?: number | undefined;
    widthResponsive?: boolean;
    heightResponsive?: boolean;
    windowBarSize?: "small" | "middle" | "large";
    minimizable?: boolean;
    isMinimized?: boolean;
}
declare interface MiniappUploadDataOption {
    miniappId: string;
    data: string;
}
declare interface MiniappUploadDataResult {
    uniqueId: string;
}
declare interface MixGroup {
    id: IDP.DB.Types.ElementId;
    position: Number3___qunhe_custom_apass_api;
    size: Number3___qunhe_custom_apass_api;
    rotation: Number3___qunhe_custom_apass_api;
    subElements: IDP.DB.Types.ElementId[];
}
declare enum ModelDataType {
    entity = 1,
    furniture = 2,
    primitive = 3,
    virtual = 4,
    assemblyInstance = 5,
    assembly = 6
}
declare interface ModelDoorWindow {
    elementId: IDP.DB.Types.ElementId;
    productId: string;
    type: IDP.DB.Types.ModelDoorWindowType;
    position: KPoint3d;
    rotation: number;
    size: KPoint3d;
    scale: KPoint3d;
    handleFlipped: boolean;
    facingFlipped: boolean;
    hostIds: string[];
    openingId: string;
}
declare interface ModelDoorWindowCreateInfo {
    productId: string;
    position: KPoint3d;
    rotation?: number;
    size?: KPoint3d;
    handleFlipped?: boolean;
    facingFlipped?: boolean;
}
declare interface MountPoints {
    main: number;
    leftPanel: number;
}
declare interface NamedValue<T = any> {
    name: string;
    value: T;
}
declare interface Number2___qunhe_custom_apass_api {
    x: number;
    y: number;
}
declare interface Number2__idp_custom_kada_api {
    x: number;
    y: number;
}
declare interface Number3___qunhe_custom_apass_api extends Number2___qunhe_custom_apass_api {
    z: number;
}
declare interface Number3__idp_custom_kada_api {
    x: number;
    y: number;
    z: number;
}
declare interface Number3Formula {
    x: string;
    y: string;
    z: string;
}
declare enum OperationShowConfig {
    Hidden = 0,
    Show = 1
}
declare enum ProductSearchMode {
    ADDING = 0,
    REPLACE = 1
}
declare type PromiseResult = Result__idp_yundesign_type<IDP.DB.Types.ElementId>;
declare type PromiseResultWithUuid = Result__idp_yundesign_type<IDP.DB.Types.ElementId & {}>;
declare enum RenderMode {
    Normal = "Normal",
    Orbit = "Orbit",
    Panorama = "Panorama",
    Video = "Video"
}
declare interface Result__idp_custom_api<T> {
    code: number;
    errorMessage?: string;
    data?: T;
}
declare interface Result__idp_yundesign_type<T> {
    code: number;
    errorMessage?: string;
    data?: T;
}
declare interface RoutingFaceConnectivityDetectionResult {
    status: "success" | "failed" | "abort";
    requestElementId?: IDP.DB.Types.ElementId[];
    successed: IDP.DB.Types.ElementId[];
    failed: IDP.DB.Types.ElementId[];
}
declare interface RuleTemplate {
    templateId: number;
    templateName: string;
    alertLevel: EAlertLevel;
    [s: string]: any;
}
declare interface SetSelectedLibraryPositionOption {
    libraryPosition: ECustomLibraryPosition;
}
declare enum SlotCaveType {
    FROM_TOP = "1",
    FROM_BOTTOM = "2"
}
declare type StartDragProductPromiseResult = Result__idp_yundesign_type<IDP.DB.Types.ElementId[]>;
declare type Theme = "light-theme" | "dark-theme";
declare interface Toast {
    info(message: string): void;
    warn(message: string): void;
    error(message: string): void;
    success(message: string): void;
}
declare interface View {
    readonly mountPoints: MountPoints;
    readonly defaultFrame: FrameHost;
    createFrame(srcName: string): FrameHost;
    setContainerOptions(frame: FrameHost, options: MainMountPointOptions | LeftPanelMountPointOptions): void;
}
export {};
