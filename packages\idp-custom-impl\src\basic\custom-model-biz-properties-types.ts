import { BasicType } from '@qunhe/kls-abstraction';
import { BooleanType, StringType } from './base-types';

/**
 * ParamModelLite-bzProperty
 */
export const bzParamModelLitePropertiesTypes = {
    type: BasicType.Object,
    properties: {
        ref: undefined!,
        name: StringType,
        namespace: StringType,
        readonly: BooleanType,
        type: StringType,
        value: StringType,
        deleted: BooleanType,
        getName: {
            name: 'getName',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        getType: {
            name: 'getType',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        getNamespace: {
            name: 'getNamespace',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        remove: {
            name: 'remove',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        getValue: {
            name: 'getValue',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        setValue: {
            name: 'setValue',
            varying: false,
            keepArgsHandle: false,
            args: [{ type: BasicType.Unknown }],
            type: BasicType.Function,
            return: {
                type: BasicType.Undefined,
            },
        },
        toJSON: {
            name: 'toJSON',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
    },
};
