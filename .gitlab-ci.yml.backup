# .gitlab-ci.yml (at the root of backend-api-sdk)

# 默认配置：重试机制和超时设置
default:
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
  timeout: 30m
  interruptible: true
 

# Use your custom pre-built image containing Java, Maven, Node, npm, and openapi-generator-cli
# IMPORTANT: Replace this with the actual path to your image in your container registry
# 如果私有 registry 不可用，考虑使用备用镜像
image: registry-qunhe.qunhequnhe.com/display/openapi-generator:latest

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"
  # Docker registry settings
  DOCKER_REGISTRY: "registry-qunhe.qunhequnhe.com"
  DOCKER_IMAGE: "registry-qunhe.qunhequnhe.com/display/openapi-generator:latest"
  DOCKER_DRIVER: overlay2
  # 备用镜像（如果私有 registry 不可用）
  FALLBACK_IMAGE: "registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest"
  # OpenAPI spec file paths
  DIYMODELDW_RESTAPI: specifications/services/diymodeldw-service/openapi.yaml
  DIYMODELDW_CONFIG_JAVA: generators/configs/java/diymodeldw-service.yaml
  DIYMODELDW_OUTPUT_JAVA: build/sdks/diymodeldw-service/java
  FURNITURE_DESIGN_RESTAPI: specifications/services/furniture-design-service/openapi.yaml
  FURNITURE_DESIGN_CONFIG_JAVA: generators/configs/java/furniture-design-service.yaml
  FURNITURE_DESIGN_OUTPUT_JAVA: build/sdks/furniture-design-service/java
  # Node.js tools path
  NODE_PATH: /usr/local/lib/node_modules

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .m2/repository/
    - target/
    - node_modules/
    - /usr/local/lib/node_modules
    - documentation/website/node_modules/
    - documentation/website/.docusaurus/
    - documentation/website/build/
    # Webpack缓存目录
    - documentation/website/.docusaurus/webpack-cache/

stages:
  # MR 流程 stages (合并为单个 stage)
  - mr_pipeline
  # 主干分支流程 stages
  - generate_sdk
  - build
  - test
  - deploy

# --- MR 流程 Jobs (合并版本) ---

# 完整 MR 流程 - 一次性执行所有步骤
mr_complete_pipeline:
  stage: mr_pipeline
  tags:
    - kube-runner
  timeout: 20m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
  variables:
    MANUAL_ENV: "dev"
  script:
    # 执行完整的 MR 流程
    - chmod +x tools/ci-cd/ci/mr/complete-mr-pipeline.sh
    - ./tools/ci-cd/ci/mr/complete-mr-pipeline.sh
  artifacts:
    paths:
      - documentation/website/build/
      - deploy_info.env    # 只保留部署信息，GitLab environment 需要
    reports:
      dotenv:
        - deploy_info.env  # 只保留部署信息
    expire_in: 1 week
    when: always
  environment:
    name: manual-dev-mr-$CI_MERGE_REQUEST_IID
    url: $DEPLOY_URL
    deployment_tier: development
    auto_stop_in: 1 week
  rules:
    - if: '$CI_MERGE_REQUEST_IID'
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_PIPELINE_SOURCE == "web"'

# 兼容性别名 - 为了向后兼容保留 ci_lint job 名称
ci_lint:
  extends: mr_complete_pipeline
  rules:
    - if: '$CI_MERGE_REQUEST_IID'
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_PIPELINE_SOURCE == "web"'



# 清理预览环境
cleanup_preview:
  stage: mr_pipeline
  tags:
    - kube-runner
  script:
    - echo "🧹 清理 MR 预览环境..."
  environment:
    name: manual-dev-mr-$CI_MERGE_REQUEST_IID
    action: stop
  when: manual
  allow_failure: true
  rules:
    - if: '$CI_MERGE_REQUEST_IID'

# --- 主干分支流程 Jobs ---

# SDK 生成
generate_sdks:
  stage: generate_sdk
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/generate-sdks.sh
    - ./tools/ci-cd/ci/deployment/generate-sdks.sh
  artifacts:
    paths:
      - build/
    expire_in: 1 day
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# 构建 SDK
build_sdks:
  stage: build
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/build-sdks.sh
    - ./tools/ci-cd/ci/deployment/build-sdks.sh
  needs: [generate_sdks]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# 测试 SDK
test_sdks:
  stage: test
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/test-sdks.sh
    - ./tools/ci-cd/ci/deployment/test-sdks.sh
  needs: [build_sdks]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_TAG'

# 部署快照版本
deploy_snapshots:
  stage: deploy
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/deploy-snapshots.sh
    - ./tools/ci-cd/ci/deployment/deploy-snapshots.sh
  needs: [test_sdks]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"'

# 部署正式版本
deploy_releases:
  stage: deploy
  tags:
    - kube-runner
  script:
    - chmod +x tools/ci-cd/ci/deployment/deploy-releases.sh
    - ./tools/ci-cd/ci/deployment/deploy-releases.sh
  needs: [test_sdks]
  when: manual
  rules:
    - if: '$CI_COMMIT_TAG'

# === Docusaurus 文档部署 ===

# 部署到群核科技 Manual 网站 (开发环境)
deploy_manual_dev:
  stage: deploy
  tags:
    - kube-runner
  timeout: 20m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - script_failure
  variables:
    MANUAL_ENV: "dev"
  before_script:
    - node --version
    - npm --version
    # 确保docs-site目录存在package-lock.json
    - |
      if [ ! -f "documentation/website/package-lock.json" ]; then
        echo "⚠️ package-lock.json 不存在，生成新的..."
        cd documentation/website
        npm install --package-lock-only
        cd ..
      fi
  script:
    - chmod +x tools/ci-cd/ci/docs/deploy-to-manual.sh
    - ./tools/ci-cd/ci/docs/deploy-to-manual.sh
  artifacts:
    paths:
      - public/
      - deploy_info.env
    reports:
      dotenv: deploy_info.env
    expire_in: 7 days
    when: always
  environment:
    name: manual-dev
    url: $DEPLOY_URL
    deployment_tier: development
  rules:
    - if: '$CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "main" && $CI_COMMIT_TAG == null && $CI_MERGE_REQUEST_IID == null'
      when: manual

# 部署到群核科技 Manual 网站 (预发布环境)
deploy_manual_staging:
  stage: deploy
  tags:
    - kube-runner
  timeout: 20m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - script_failure
  variables:
    MANUAL_ENV: "staging"
  before_script:
    - node --version
    - npm --version
    # 确保docs-site目录存在package-lock.json
    - |
      if [ ! -f "documentation/website/package-lock.json" ]; then
        echo "⚠️ package-lock.json 不存在，生成新的..."
        cd documentation/website
        npm install --package-lock-only
        cd ..
      fi
  script:
    - chmod +x tools/ci-cd/ci/docs/deploy-to-manual.sh
    - ./tools/ci-cd/ci/docs/deploy-to-manual.sh
  artifacts:
    paths:
      - public/
      - deploy_info.env
    reports:
      dotenv: deploy_info.env
    expire_in: 30 days
    when: always
  environment:
    name: manual-staging
    url: $DEPLOY_URL
    deployment_tier: staging
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main"'
      when: manual

# 部署到群核科技 Manual 网站 (生产环境)
deploy_manual_prod:
  stage: deploy
  tags:
    - kube-runner
  timeout: 25m
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure
      - scheduler_failure
      - script_failure
  variables:
    MANUAL_ENV: "prod"
  before_script:
    - node --version
    - npm --version
    # 确保docs-site目录存在package-lock.json
    - |
      if [ ! -f "documentation/website/package-lock.json" ]; then
        echo "⚠️ package-lock.json 不存在，生成新的..."
        cd documentation/website
        npm install --package-lock-only
        cd ..
      fi
  script:
    - chmod +x tools/ci-cd/ci/docs/deploy-to-manual.sh
    - ./tools/ci-cd/ci/docs/deploy-to-manual.sh
  artifacts:
    paths:
      - public/
      - deploy_info.env
    reports:
      dotenv: deploy_info.env
    expire_in: 1 year
    when: always
  environment:
    name: manual-production
    url: $DEPLOY_URL
    deployment_tier: production
  rules:
    - if: '$CI_COMMIT_TAG'
      when: manual

