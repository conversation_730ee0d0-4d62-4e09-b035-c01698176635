# 合并的映射文件，包含所有必要的 schema 映射
schemaMappings:
  # 来自 geom-data-exchange-mappings.yaml
  Arc2d: "com.manycore.geom_data_exchange.geom.Arc2d"
  Arc3d: "com.manycore.geom_data_exchange.geom.Arc3d"
  BezierCurve2d: "com.manycore.geom_data_exchange.geom.BezierCurve2d"
  BezierCurve3d: "com.manycore.geom_data_exchange.geom.BezierCurve3d"
  Body: "com.manycore.geom_data_exchange.topo.Body"
  BodyBase: "com.manycore.geom_data_exchange.topo.BodyBase"
  CCS2d: "com.manycore.geom_data_exchange.geom.CCS2d"
  CCS3d: "com.manycore.geom_data_exchange.geom.CCS3d"
  Circle2d: "com.manycore.geom_data_exchange.geom.Circle2d"
  Circle3d: "com.manycore.geom_data_exchange.geom.Circle3d"
  Component: "com.manycore.geom_data_exchange.topo.Component"
  CompositeCurve2d: "com.manycore.geom_data_exchange.geom.CompositeCurve2d"
  CompositeCurve3d: "com.manycore.geom_data_exchange.geom.CompositeCurve3d"
  Cone: "com.manycore.geom_data_exchange.geom.Cone"
  Curve2d: "com.manycore.geom_data_exchange.geom.Curve2d"
  Curve3d: "com.manycore.geom_data_exchange.geom.Curve3d"
  CurveLoop2d: "com.manycore.geom_data_exchange.geom.CurveLoop2d"
  CurveOnSurface: "com.manycore.geom_data_exchange.geom.CurveOnSurface"
  CurveOnSurfaces: "com.manycore.geom_data_exchange.geom.CurveOnSurfaces"
  CustomConverter: "com.manycore.geom_data_exchange.persistence.json.CustomConverter"
  Cylinder: "com.manycore.geom_data_exchange.geom.Cylinder"
  DefaultSubTypes: "com.manycore.geom_data_exchange.base.DefaultSubTypes"
  DeserializeResult: "com.manycore.geom_data_exchange.persistence.json.DeserializeResult"
  Design2dEdge: "com.manycore.geom_data_exchange.topo.Design2dEdge"
  Design2dFace: "com.manycore.geom_data_exchange.topo.Design2dFace"
  Design2dVertex: "com.manycore.geom_data_exchange.topo.Design2dVertex"
  Edge: "com.manycore.geom_data_exchange.topo.Edge"
  Ellipse2d: "com.manycore.geom_data_exchange.geom.Ellipse2d"
  Ellipse2dDeserializer: "com.manycore.geom_data_exchange.geom.Ellipse2dDeserializer"
  Ellipse3d: "com.manycore.geom_data_exchange.geom.Ellipse3d"
  EllipticalArc2d: "com.manycore.geom_data_exchange.geom.EllipticalArc2d"
  EllipticalArc3d: "com.manycore.geom_data_exchange.geom.EllipticalArc3d"
  EntityFactory: "com.manycore.geom_data_exchange.persistence.json.EntityFactory"
  ExternalResult: "com.manycore.geom_data_exchange.persistence.json.ExternalResult"
  Face: "com.manycore.geom_data_exchange.topo.Face"
  Fin: "com.manycore.geom_data_exchange.topo.Fin"
  GenericCylinder: "com.manycore.geom_data_exchange.geom.GenericCylinder"
  Geometric2dDesign: "com.manycore.geom_data_exchange.topo.Geometric2dDesign"
  Geometry: "com.manycore.geom_data_exchange.geom.Geometry"
  GeomRegion2d: "com.manycore.geom_data_exchange.geom.GeomRegion2d"
  IEnumBase: "com.manycore.geom_data_exchange.base.IEnumBase"
  Instance: "com.manycore.geom_data_exchange.topo.Instance"
  Interval: "com.manycore.geom_data_exchange.geom.Interval"
  Line2d: "com.manycore.geom_data_exchange.geom.Line2d"
  Line3d: "com.manycore.geom_data_exchange.geom.Line3d"
  LineSegment2d: "com.manycore.geom_data_exchange.geom.LineSegment2d"
  LineSegment3d: "com.manycore.geom_data_exchange.geom.LineSegment3d"
  Loop: "com.manycore.geom_data_exchange.topo.Loop"
  Matrix: "com.manycore.geom_data_exchange.geom.Matrix"
  MeshBody: "com.manycore.geom_data_exchange.topo.MeshBody"
  MIX: "com.manycore.geom_data_exchange.MIX"
  ModelTypes: "com.manycore.geom_data_exchange.topo.ModelTypes"
  MutableSubType: "com.manycore.geom_data_exchange.base.MutableSubType"
  Name: "com.manycore.geom_data_exchange.topo.Name"
  NameTypes: "com.manycore.geom_data_exchange.topo.NameTypes"
  NonSubType: "com.manycore.geom_data_exchange.base.NonSubType"
  NumberOp: "com.manycore.geom_data_exchange.utils.NumberOp"
  NurbsCurve2d: "com.manycore.geom_data_exchange.geom.NurbsCurve2d"
  NurbsCurve3d: "com.manycore.geom_data_exchange.geom.NurbsCurve3d"
  NurbsSurface: "com.manycore.geom_data_exchange.geom.NurbsSurface"
  Outline: "com.manycore.geom_data_exchange.topo.Outline"
  Plane: "com.manycore.geom_data_exchange.geom.Plane"
  PlaneDeserializer: "com.manycore.geom_data_exchange.geom.PlaneDeserializer"
  Point2d: "com.manycore.geom_data_exchange.geom.Point2d"
  Point3d: "com.manycore.geom_data_exchange.geom.Point3d"
  Polyline2d: "com.manycore.geom_data_exchange.geom.Polyline2d"
  Polyline3d: "com.manycore.geom_data_exchange.geom.Polyline3d"
  Ray2d: "com.manycore.geom_data_exchange.geom.Ray2d"
  Ray3d: "com.manycore.geom_data_exchange.geom.Ray3d"
  RectTrimmedSurface: "com.manycore.geom_data_exchange.geom.RectTrimmedSurface"
  RevolvedSurface: "com.manycore.geom_data_exchange.geom.RevolvedSurface"
  Serializable: "com.manycore.geom_data_exchange.persistence.json.Serializable"
  SerializableAnnotation: "com.manycore.geom_data_exchange.persistence.json.SerializableAnnotation"
  SerializeHelper: "com.manycore.geom_data_exchange.SerializeHelper"
  Serializer: "com.manycore.geom_data_exchange.persistence.json.Serializer"
  SerializeResult: "com.manycore.geom_data_exchange.persistence.json.SerializeResult"
  SerializeType: "com.manycore.geom_data_exchange.persistence.json.SerializeType"
  StructuredName: "com.manycore.geom_data_exchange.topo.StructuredName"
  SubType: "com.manycore.geom_data_exchange.base.SubType"
  SubTypeConfig: "com.manycore.geom_data_exchange.SubTypeConfig"
  SubTypeInfo: "com.manycore.geom_data_exchange.base.SubTypeInfo"
  Surface: "com.manycore.geom_data_exchange.geom.Surface"
  SurfaceWithFrame: "com.manycore.geom_data_exchange.geom.SurfaceWithFrame"
  TextName: "com.manycore.geom_data_exchange.topo.TextName"
  Topology: "com.manycore.geom_data_exchange.topo.Topology"
  Torus: "com.manycore.geom_data_exchange.geom.Torus"
  TrimmedCurve2d: "com.manycore.geom_data_exchange.geom.TrimmedCurve2d"
  TrimmedCurve3d: "com.manycore.geom_data_exchange.geom.TrimmedCurve3d"
  TrimmedSurface: "com.manycore.geom_data_exchange.geom.TrimmedSurface"
  TrimmedSurfaceBase: "com.manycore.geom_data_exchange.geom.TrimmedSurfaceBase"
  UV: "com.manycore.geom_data_exchange.geom.UV"
  Vector2d: "com.manycore.geom_data_exchange.geom.Vector2d"
  Vector3d: "com.manycore.geom_data_exchange.geom.Vector3d"
  Vertex: "com.manycore.geom_data_exchange.topo.Vertex"
  
  # 来自 restapi-sdk-data-mappings.yaml 的映射
  ApiError: "com.manycore.restapi.error.ApiError"
  Code: "com.manycore.restapi.error.Code"
  ErrorDescribe: "com.manycore.restapi.error.ErrorDescribe"
  ErrorDetails: "com.manycore.restapi.error.ErrorDetails"
  Help: "com.manycore.restapi.error.Help"
  Identifiable: "com.manycore.restapi.data.Identifiable"
  JsonMapperUtil: "com.manycore.restapi.util.JsonMapperUtil"
  LocalizedMessage: "com.manycore.restapi.error.LocalizedMessage"
  LongRunningRequest: "com.manycore.restapi.data.request.LongRunningRequest"
  Operation: "com.manycore.restapi.operation.Operation"
  OperationHelper: "com.manycore.restapi.operation.OperationHelper"
  RestApiBatchRequest: "com.manycore.restapi.data.request.RestApiBatchRequest"
  RestApiBatchResponse: "com.manycore.restapi.data.response.RestApiBatchResponse"
  RestApiClientException: "com.manycore.restapi.exception.RestApiClientException"
  RestApiDomain: "com.manycore.restapi.module.RestApiDomain"
  RestApiListResponse: "com.manycore.restapi.data.response.RestApiListResponse"
  RestApiProcessError: "com.manycore.restapi.exception.RestApiProcessError"
  RestApiProcessException: "com.manycore.restapi.exception.RestApiProcessException"
  RestApiProcessRuntimeException: "com.manycore.restapi.exception.RestApiProcessRuntimeException"
  RestApiRequest: "com.manycore.restapi.data.request.RestApiRequest"
  RestApiResponse: "com.manycore.restapi.data.response.RestApiResponse"
  Validatable: "com.manycore.restapi.data.Validatable"
  WriteControl: "com.manycore.restapi.data.WriteControl"
