var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Integration = {};
    var var_Catalog = {};
    var var_getElementLabelConfigsAsync = {};
    var var_ElementLabelConfig_Array_Promise = {};
    var var_ElementLabelConfig_Array_Promise_then = {};
    var var_ElementLabelConfig_Array_Promise_then_onresolve = {};
    var var_ElementLabelConfig_Array = {};
    var var_ElementLabelConfig = {};
    var var_numberType = {};
    var var_stringType = {};
    var var_stringType_Array = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_DB = {};
    var var_Types = {};
    var var_ElementLabelType = {};
    var var_ElementLabelWriteErrorType = {};
    var var_Methods = {};
    var var_putElementLabelsAsync = {};
    var var_ElementId = {};
    var var_ElementLabelData_Array = {};
    var var_ElementLabelData = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Integration": var_Integration,
        "DB": var_DB,
    };
    var_Integration.type = BasicType.Object;
    var_Integration.properties = {
        "Catalog": var_Catalog,
    };
    var_Catalog.type = BasicType.Object;
    var_Catalog.properties = {
        "getElementLabelConfigsAsync": var_getElementLabelConfigsAsync,
    };
    var_getElementLabelConfigsAsync.type = BasicType.Function;
    var_getElementLabelConfigsAsync.name = "getElementLabelConfigsAsync";
    var_getElementLabelConfigsAsync.varying = false;
    var_getElementLabelConfigsAsync.keepArgsHandle = false;
    var_getElementLabelConfigsAsync.args = [];
    var_getElementLabelConfigsAsync.return = var_ElementLabelConfig_Array_Promise;
    var_ElementLabelConfig_Array_Promise.type = BasicType.Object;
    var_ElementLabelConfig_Array_Promise.properties = {
        "then": var_ElementLabelConfig_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ElementLabelConfig_Array_Promise_then.type = BasicType.Function;
    var_ElementLabelConfig_Array_Promise_then.name = "";
    var_ElementLabelConfig_Array_Promise_then.varying = false;
    var_ElementLabelConfig_Array_Promise_then.keepArgsHandle = true;
    var_ElementLabelConfig_Array_Promise_then.args = [var_ElementLabelConfig_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_ElementLabelConfig_Array_Promise_then.return = var_undefinedType;
    var_ElementLabelConfig_Array_Promise_then_onresolve.type = BasicType.Function;
    var_ElementLabelConfig_Array_Promise_then_onresolve.name = "";
    var_ElementLabelConfig_Array_Promise_then_onresolve.varying = false;
    var_ElementLabelConfig_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_ElementLabelConfig_Array_Promise_then_onresolve.args = [var_ElementLabelConfig_Array];
    var_ElementLabelConfig_Array_Promise_then_onresolve.return = var_undefinedType;
    var_ElementLabelConfig_Array.type = BasicType.Array;
    var_ElementLabelConfig_Array.value = var_ElementLabelConfig;
    var_ElementLabelConfig.type = BasicType.Object;
    var_ElementLabelConfig.properties = {
        "id": var_numberType,
        "key": var_stringType,
        "name": var_stringType,
        "type": var_numberType,
        "options": var_stringType_Array,
    };
    var_numberType.type = BasicType.Number;
    var_stringType.type = BasicType.String;
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
        "ElementLabelType": var_ElementLabelType,
        "ElementLabelWriteErrorType": var_ElementLabelWriteErrorType,
    };
    var_ElementLabelType.type = BasicType.Object;
    var_ElementLabelType.properties = {
        "number": var_numberType,
        "string": var_numberType,
        "option": var_numberType,
    };
    var_ElementLabelWriteErrorType.type = BasicType.Object;
    var_ElementLabelWriteErrorType.properties = {
        "config": var_stringType,
        "value": var_stringType,
        "element": var_stringType,
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "putElementLabelsAsync": var_putElementLabelsAsync,
    };
    var_putElementLabelsAsync.type = BasicType.Function;
    var_putElementLabelsAsync.name = "putElementLabelsAsync";
    var_putElementLabelsAsync.varying = false;
    var_putElementLabelsAsync.keepArgsHandle = false;
    var_putElementLabelsAsync.args = [var_ElementId, var_ElementLabelData_Array];
    var_putElementLabelsAsync.return = var_undefinedType_Promise;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_ElementLabelData_Array.type = BasicType.Array;
    var_ElementLabelData_Array.value = var_ElementLabelData;
    var_ElementLabelData.type = BasicType.Object;
    var_ElementLabelData.properties = {
        "id": var_numberType,
        "value": var_stringType,
    };
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    
    return var_sourceFile;
};
