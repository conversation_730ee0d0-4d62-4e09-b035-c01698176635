# Changelog
## 1.40.1-rc.1 (2024-05-21)
## Features
- 商品插件提供 打开公库下具体类目 `openCategoryInPublicLibrary`, 对二方开放 [YSJGJ4-8485](https://kaptain.qunhequnhe.com/project/detail/sprint/detail?sprint=8560&projectId=50&filter=473443&key=YSJGJ4-8485)

## 1.39.1-rc.1 (2024-02-20)
## Features
- 云图插件提供 进入云图环境接口 `enterDefaultModeAsync`, 对二方开放 [YSJGJ4-7955](https://kaptain.qunhequnhe.com/project/detail/sprint/detail?sprint=7984&projectId=50&filter=88945&key=YSJGJ4-7955)

## 1.38.1-rc.1 (2024-01-16)
## Features
- 商品插件提供 左侧栏内容挂载到指定容器 和 卸载 的接口
    - 增加API：`renderLeftPanelProductResourcesContent`，`unmountLeftPanelProductResourcesContent` ,对二方开放 [YSJGJ4-7681](https://kaptain.qunhequnhe.com/project/detail/sprint/detail?projectId=50&sprint=7970&filter=352396&key=YSJGJ4-7681)

## 1.29.1-rc.0 (2023-08-15)
## Features
- 5.0门窗提供门窗增删查小程序接口；
    - 增加类型：`ModelDoorWindowType`、`ModelDoorWindowCreateInfo`、`ModelDoorWindow`；
    - 增加API：`getAllModelDoorWindowList`、`getModelDoorWindow`、`deleteModelDoorWindow`、`createModelDoorWindowAsync`，对三方开放 [YSJGJ4-6183](https://kaptain.qunhequnhe.com/project/detail/sprint/detail?projectId=50&sprint=6984&filter=88945&key=YSJGJ4-6183)
## Fixes
- 修正 `startDragProductAsync` 返回结果与实际实现不一致 [NO KP]()

## 1.0.6-rc.3 (2022-03-01)
## Features
- 从 `@qunhe/idp-yundesign-impl` 迁移 `FurnitureCreateInfo`，`Furniture`， `StartDragProductPromiseResult`，`PromiseResult` 类型；
- 增加 `FurnitureGroupCreateInfo`、`FurnitureGroup` 类型；增加API：`createFurnitureGroup`、`deleteFurnitureGroup`、`getAllFurnitureGroupList`、`getAllFurnitureList`，均只对二方开放; [YSJGJ4-1850](https://kaptain.qunhequnhe.com/project/detail/issue?projectId=50&filter=226335&key=YSJGJ4-1850)

## 1.0.2-rc.7 (2021-10-26)
## ## Features
- 增加直接创建、查询和删除家具的 API

## 1.0.2-rc.0 (2021-09-28)
## Features
- 拖拽创建设计对象返回 Promise 结果；
