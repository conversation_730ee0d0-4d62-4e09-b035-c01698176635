var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Platform = {};
    var var_enterRenderModeAsync = {};
    var var_stringType = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_enterGalleryAsync = {};
    var var_booleanType = {};
    var var_UI = {};
    var var_setYunrenderLeftSidePanelPaddingLeft = {};
    var var_numberType = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Platform": var_Platform,
        "UI": var_UI,
    };
    var_Platform.type = BasicType.Object;
    var_Platform.properties = {
        "enterRenderModeAsync": var_enterRenderModeAsync,
        "enterGalleryAsync": var_enterGalleryAsync,
    };
    var_enterRenderModeAsync.type = BasicType.Function;
    var_enterRenderModeAsync.name = "enterRenderModeAsync";
    var_enterRenderModeAsync.varying = false;
    var_enterRenderModeAsync.keepArgsHandle = false;
    var_enterRenderModeAsync.args = [var_stringType];
    var_enterRenderModeAsync.return = var_undefinedType_Promise;
    var_stringType.type = BasicType.String;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_enterGalleryAsync.type = BasicType.Function;
    var_enterGalleryAsync.name = "enterGalleryAsync";
    var_enterGalleryAsync.varying = false;
    var_enterGalleryAsync.keepArgsHandle = false;
    var_enterGalleryAsync.args = [var_booleanType, var_booleanType];
    var_enterGalleryAsync.return = var_undefinedType_Promise;
    var_booleanType.type = BasicType.Boolean;
    var_UI.type = BasicType.Object;
    var_UI.properties = {
        "setYunrenderLeftSidePanelPaddingLeft": var_setYunrenderLeftSidePanelPaddingLeft,
    };
    var_setYunrenderLeftSidePanelPaddingLeft.type = BasicType.Function;
    var_setYunrenderLeftSidePanelPaddingLeft.name = "setYunrenderLeftSidePanelPaddingLeft";
    var_setYunrenderLeftSidePanelPaddingLeft.varying = false;
    var_setYunrenderLeftSidePanelPaddingLeft.keepArgsHandle = false;
    var_setYunrenderLeftSidePanelPaddingLeft.args = [var_numberType];
    var_setYunrenderLeftSidePanelPaddingLeft.return = var_undefinedType;
    var_numberType.type = BasicType.Number;
    
    return var_sourceFile;
};
