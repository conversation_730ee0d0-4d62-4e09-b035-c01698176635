import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { injections } from '@qunhe/apaas-type-generator-lib';
import { ADVANCED_TYPES } from './src/openAPI';

export const getVMBindingType = once(() => {
    // TODO: modify your vm type creator params here
    return createVMBindingType({
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        }
    });
});
export const getVMBindingTypeInternal = once(() => {
    // TODO: modify your internal vm type creator params here
    return createVMBindingTypeInternal({
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        },
        types: {
            FloorplanDocumentBatchUpdateRequest: ADVANCED_TYPES.FloorplanDocumentBatchUpdateRequest,
            FloorplanElement: ADVANCED_TYPES.FloorplanElement
        }
    });
});
