{"name": "@qunhe/idp-custom-doorwindow-impl", "version": "1.70.1", "description": "api typings for idp-custom-doorwindow-impl", "keywords": [], "author": "白酒 <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/idp-types.git"}, "scripts": {"build": "node ../../scripts/build-package/build-impl --excludePackages @qunhe/custom-apass-api", "build-package": "npm run build && tsc index.ts --outDir build --declaration && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "dependencies": {"@qunhe/custom-apass-api": "0.52.157", "@qunhe/idp-custom-doorwindow": "1.70.0", "@qunhe/kls-abstraction": "~1.1.15", "lodash": "4.x"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "devDependencies": {"copyfiles": "^2.4.1", "typescript": "^4.2.3"}}