import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { BasicType } from '@qunhe/kls-abstraction';

function getInjections() {
    return {
        types: {
            Parameters: {
                type: BasicType.Unknown
            }
        }
    }
}

export const getVMBindingType = once(() => {
    // TODO: modify your vm type creator params here
    return createVMBindingType();
});
export const getVMBindingTypeInternal = once(() => {
    // TODO: modify your internal vm type creator params here
    return createVMBindingTypeInternal(getInjections());
});
