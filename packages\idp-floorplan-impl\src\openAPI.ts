import { ObjectType } from '@qunhe/kls-abstraction';
import { createTypeBuilders } from '@qunhe/kls-runtime'
import { injections } from '@qunhe/apaas-type-generator-lib';

const { ObjectTypeBuilder } = createTypeBuilders({
    defaultRequired: true,
});

/**
 * @qunhe/math-apaas-api中的类型生成为
 *   {
 *      type: BasicType, // 实际的js使用的都是BasicType.Object，这里使用as转换为ObjectType
 *      properties: {}
 *   }
 */
const KPoint2d = injections['@qunhe/math-apaas-api'].KPoint2d as ObjectType;
const KVector2d = injections['@qunhe/math-apaas-api'].KVector2d as ObjectType;
const KGeomFace2d = injections['@qunhe/math-apaas-api'].KGeomFace2d as ObjectType;
const KBoundedCurve2d = injections['@qunhe/math-apaas-api'].KBoundedCurve2d as ObjectType;
const KPlane = injections['@qunhe/math-apaas-api'].KPlane as ObjectType;
const KVector3d = injections['@qunhe/math-apaas-api'].KVector3d as ObjectType;

/**
 * RectangleProfile | CircleProfile | ArbitraryClosedProfile
 */
const ExtrudeProfile = new ObjectTypeBuilder()
    .addString('ptp')
    .addOptional('center', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KPoint2d)
        })
    })
    .addOptional('radius', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('length', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('width', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('xDirection', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KVector2d)
        })
    })
    .addOptional('faceOnPlane', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KGeomFace2d)
        })
    })
    .build();

/**
 * BoundedAxis | ExtrudeAreaSolid
 */
const GeometricRepresentation = new ObjectTypeBuilder()
    .addString('gtp')
    .addOptional('curve', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KBoundedCurve2d)
        })
    })
    .addOptional('thickness', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('height', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('offGround', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('plane', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KPlane)
        })
    })
    .addOptional('depth', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('extrudeDirection', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KVector3d)
        })
    })
    .addOptional('profile', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(ExtrudeProfile as ObjectType)
        })
    })
    .build();

/**
 * Room | Wall | Beam | Pillar | Chimney | FloorOpening | RoomSeparator | Opening;
 */
const FloorplanElement = new ObjectTypeBuilder()
    .addString('etp')
    .addString('id')
    .addOptional('location', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KPoint2d)
        })
    })
    .addOptional('name', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('roomType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('roomTypeName', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('hasCeiling', (optionalTypeBuilder) => {
        optionalTypeBuilder.withBoolean()
    })
    .addOptional('curve', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KBoundedCurve2d)
        })
    })
    .addOptional('offgroundType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('hostIds', (optionalTypeBuilder) => {
        optionalTypeBuilder.withArray((arrayTypeBuilder) => {
            arrayTypeBuilder.withString()
        })
    })
    .addOptional('openingType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('wallType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('isBearing', (optionalTypeBuilder) => {
        optionalTypeBuilder.withBoolean()
    })
    .addOptional('geometricRepresentation', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(GeometricRepresentation)
        })
    })
    .build();

/**
 * OpeningCreateRequest有嵌套，需要单独写一个
 */
const OpeningCreateRequests = new ObjectTypeBuilder()
    .addString('rtp')
    .addOptional('hostId', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('openingType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('openingSourceType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('geometricRepresentation', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(GeometricRepresentation)
        })
    })
    .build();

/**
  * ElementDeleteRequest |
  * WallCreateRequest |
  * ChimneyCreateRequest |
  * RoomSeparatorCreateRequest |
  * FloorOpeningCreateRequest |
  * BeamCreateRequest |
  * PillarCreateRequest |
  * OpeningCreateRequest |
  * FloorplanConfigUpdateRequest |
  * WallUpdateRequest |
  * ChimneyUpdateRequest |
  * PillarUpdateRequest |
  * BeamUpdateRequest |
  * RoomSeparatorUpdateRequest |
  * FloorOpeningUpdateRequest |
  * RoomUpdateRequest |
  * OpeningUpdateRequest;
 */
const FloorplanDocumentOperateRequest = new ObjectTypeBuilder()
    .addString('rtp')
    .addOptional('id', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('curve', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(KBoundedCurve2d)
        })
    })
    .addOptional('offGroundType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('hostId', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('hostIds', (optionalTypeBuilder) => {
        optionalTypeBuilder.withArray((arrayTypeBuilder) => {
            arrayTypeBuilder.withString()
        })
    })
    .addOptional('name', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('openingType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('openingSourceType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('wallType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('isBearing', (optionalTypeBuilder) => {
        optionalTypeBuilder.withBoolean()
    })
    .addOptional('roomType', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('hasCeiling', (optionalTypeBuilder) => {
        optionalTypeBuilder.withBoolean()
    })
    .addOptional('roomTypeName', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('geometricRepresentation', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(GeometricRepresentation)
        })
    })
    .addOptional('elementJoint', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addOptional('height', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('floorThickness', (optionalTypeBuilder) => {
        optionalTypeBuilder.withNumber()
    })
    .addOptional('openingCreateRequests', (optionalTypeBuilder) => {
        optionalTypeBuilder.withArray((arrayTypeBuilder) => {
            arrayTypeBuilder.withObject((objectTypeBuilder) => {
                objectTypeBuilder.setType(OpeningCreateRequests)
            })
        })
    })
    .addOptional('compass', (optionalTypeBuilder) => {
        optionalTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.addOptional('northDirection', (builder) => {
                builder.withNumber()
            });
            objectTypeBuilder.addOptional('latitude', (builder) => {
                builder.withNumber()
            });
            objectTypeBuilder.addOptional('longitude', (builder) => {
                builder.withNumber()
            });
        });
    })
    .build();

const FloorplanDocumentBatchUpdateRequest = new ObjectTypeBuilder()
    .addOptional('view', (optionalTypeBuilder) => {
        optionalTypeBuilder.withString()
    })
    .addArray('batchRequests', (arrayTypeBuilder) => {
        arrayTypeBuilder.withObject((objectTypeBuilder) => {
            objectTypeBuilder.setType(FloorplanDocumentOperateRequest)
        })
    })
    .build();

/**
 * 自动生成类型不支持高级类型，例如Union\Intersection\Generics\Mapped等Types
 * 这里手搓一个用于正确转换类型，不指定则会走序列化
 */
export const ADVANCED_TYPES = {
    FloorplanElement,
    FloorplanDocumentBatchUpdateRequest,
}
