{"id": "getting-started/error-handling", "title": "错误处理", "description": "HTTP 状态码", "source": "@site/docs/getting-started/error-handling.md", "sourceDirName": "getting-started", "slug": "/getting-started/error-handling", "permalink": "/manycoreapi-demo/0.0.4/docs/getting-started/error-handling", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "error-handling", "title": "错误处理", "sidebar_label": "错误处理"}, "sidebar": "tutorialSidebar", "previous": {"title": "发起请求", "permalink": "/manycoreapi-demo/0.0.4/docs/getting-started/making-requests"}, "next": {"title": "最佳实践", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/best-practices"}}