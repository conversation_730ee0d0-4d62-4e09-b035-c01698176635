{"id": "guides/best-practices", "title": "API 最佳实践", "description": "遵循这些最佳实践，可以帮助您更高效、安全地使用群核科技 API，并确保应用程序的稳定性和性能。", "source": "@site/docs/guides/best-practices.md", "sourceDirName": "guides", "slug": "/guides/best-practices", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/best-practices", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "best-practices", "title": "API 最佳实践", "sidebar_label": "最佳实践"}, "sidebar": "tutorialSidebar", "previous": {"title": "错误处理", "permalink": "/manycoreapi-demo/0.0.4/docs/getting-started/error-handling"}, "next": {"title": "速率限制", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/rate-limiting"}}