#!/bin/bash

# 文档生成工具安装脚本
# 专门处理 peer dependency 警告问题

echo "🔧 安装文档生成工具..."

# 检查 npm 是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

# 检查当前 registry
CURRENT_REGISTRY=$(npm config get registry)
echo "🔍 当前 npm registry: $CURRENT_REGISTRY"

# 如果是内部 registry，提示切换
if [[ $CURRENT_REGISTRY != *"npmjs.org"* ]]; then
    echo "⚠️  检测到内部 npm registry，建议临时切换到官方 registry"
    read -p "是否切换？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        npm config set registry https://registry.npmjs.org/
        SWITCH_BACK=true
    fi
fi

echo ""
echo "📦 尝试安装文档生成工具..."

# 方案1: Redoc CLI (默认推荐)
echo "  1️⃣ 尝试安装 Redoc CLI (推荐)..."
if npm install -g redoc-cli --quiet --no-fund --no-audit; then
    echo "  ✅ Redoc CLI 安装成功"
    TOOL_INSTALLED="redoc"
else
    echo "  ❌ Redoc CLI 安装失败"
    
    # 方案2: 社区 Elements CLI (备选)
    echo "  2️⃣ 尝试安装社区 Elements CLI (备选)..."
    if npm install -g @skriptfabrik/elements-cli --legacy-peer-deps --quiet --no-fund --no-audit; then
        echo "  ✅ 社区 Elements CLI 安装成功"
        TOOL_INSTALLED="elements"
    else
        echo "  ❌ 社区 Elements CLI 安装失败"
        
        # 方案3: Swagger UI 静态生成器
        echo "  3️⃣ 尝试安装 Swagger UI 生成器..."
        if npm install -g swagger-ui-dist --quiet --no-fund --no-audit; then
            echo "  ✅ Swagger UI 静态包安装成功"
            TOOL_INSTALLED="swagger"
        else
            echo "  ❌ 所有工具安装失败"
            TOOL_INSTALLED="none"
        fi
    fi
fi

# 恢复原 registry
if [ "$SWITCH_BACK" = true ]; then
    npm config set registry "$CURRENT_REGISTRY"
    echo "🔄 已恢复原 npm registry"
fi

echo ""
echo "📋 安装结果："

case $TOOL_INSTALLED in
    "redoc")
        echo "✅ Redoc CLI 安装成功 (推荐)"
        echo "📖 使用方法: redoc-cli build openapi/your-service/restapi.yaml --output docs.html"
        ;;
    "elements")
        echo "✅ 社区 Elements CLI 安装成功"
        echo "📖 使用方法: elements export openapi/your-service/restapi.yaml > docs.html"
        ;;
    "swagger")
        echo "✅ Swagger UI 静态包安装成功"
        echo "📖 使用方法: 需要手动创建 HTML 文件，可参考在线示例"
        ;;
    "none")
        echo "❌ 所有工具安装失败"
        echo "🔧 建议使用在线工具："
        echo "  - Swagger Editor: https://editor.swagger.io/"
        echo "  - Redoc 在线版: https://redocly.github.io/redoc/"
        echo "  - Stoplight Studio: https://stoplight.io/studio/"
        echo ""
        echo "📄 或参考 Stoplight Elements 在线文档创建 HTML 模板"
        ;;
esac

echo ""
echo "💡 提示: 如果遇到 peer dependency 警告，这是正常的，不影响工具使用" 