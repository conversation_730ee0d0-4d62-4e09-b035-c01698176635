# ManyCore SDK 发布配置模板
# 复制此文件为 config.env 并填写你的认证信息
# cp config.env.template config.env

# =================
# GPG 签名配置
# =================

# GPG 密钥 ID (长格式，例如：1234567890ABCDEF)
# 获取方法: gpg --list-secret-keys --keyid-format LONG
GPG_KEY_NAME=""

# GPG 密钥密码
GPG_PASSPHRASE=""

# GPG 私钥 (Base64编码，可选 - 仅在Docker容器中需要)
# 注意：如果你已经将密钥导入到本地GPG密钥环，可以不设置此项
# 推荐方式：使用 import-existing-gpg.sh 导入现有密钥到本地密钥环
# GPG_PRIVATE_KEY=""

# =================
# Maven Central 配置
# =================

# Maven Central Portal 用户名
# 从 https://central.sonatype.com/ 获取
CENTRAL_USERNAME=""

# Maven Central Portal 令牌/密码
# 从 https://central.sonatype.com/ 生成
CENTRAL_PASSWORD=""

# =================
# 使用方法
# =================

# 1. 填写上述配置信息
# 2. 加载配置: source scripts/local/config.env
# 3. 发布SDK: scripts/local/local-sdk-manager.sh deploy-rel doorwindow

# =================
# 安全提醒
# =================

# ⚠️  重要：
# - 此文件包含敏感信息，请不要提交到版本控制系统
# - 确保文件权限设置为 600: chmod 600 config.env
# - 定期更换 Maven Central 令牌
# - 妥善保管 GPG 密钥和密码

# =================
# 快速设置指南
# =================

# 步骤1: 导入现有GPG密钥（推荐）
# scripts/local/import-existing-gpg.sh
# 
# 或者生成新密钥：
# scripts/local/setup-local-gpg.sh

# 步骤2: 复制并编辑配置
# cp scripts/local/config.env.template scripts/local/config.env
# nano scripts/local/config.env

# 步骤3: 发布
# source scripts/local/config.env
# scripts/local/local-sdk-manager.sh deploy-rel doorwindow 