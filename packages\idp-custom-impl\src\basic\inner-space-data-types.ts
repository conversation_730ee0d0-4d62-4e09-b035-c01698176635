import { BasicType } from '@qunhe/kls-abstraction';
import { Number3Types } from './number-types';
import { ParamModelLiteBaseType } from './custom-model';

/**
 * 内空监听返回
 */
export const InnerSpaceDataTypes = {
    type: BasicType.Object,
    properties: {
        size: Number3Types,
        position: Number3Types,
        rotate: Number3Types,
        rotation: Number3Types,
        face: {
            type: BasicType.String,
        },
        // 处理内空关联模型
        parent: ParamModelLiteBaseType,
    },
};
