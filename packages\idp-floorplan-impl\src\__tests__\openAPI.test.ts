import { serialize, validateType } from './ObjectTypeUtils';
import { describe, test, expect } from '@jest/globals';
import { ADVANCED_TYPES } from '../openAPI';
import { BasicType } from '@qunhe/kls-abstraction';
import { mockUtils } from './mockUtils';

const {
    FloorplanDocumentBatchUpdateRequest,
    FloorplanElement,
} = ADVANCED_TYPES;

/**
 * 验证Advanced Types 生成的静态类型定义和序列化结果是否正确
 */
describe('OpenAPI Advanced Types Test', () => {
    describe('FloorplanElement Type Test', () => {
        test('Floorplan Elements validate Test', () => {
            const elements = mockUtils.buildFullTypeElements();

            const type = {
                type: BasicType.Array,
                value: FloorplanElement,
            };

            expect(() => validateType(elements, type)).not.toThrow();
        })

        test('Floorplan Elements serialize Test', () => {
            const elements = mockUtils.buildFullTypeElements();

            const type = {
                type: BasicType.Array,
                value: FloorplanElement,
            };

            const serializedElements = serialize(elements, type, {
                validateBeforeSerialize: true,
            });

            expect(serializedElements).toEqual(mockUtils.serializedElements);
        })
    })

    describe('FloorplanDocumentBatchCreateRequest Type Test', () => {
        test('create request validate Test', () => {
            const request = mockUtils.buildBatchCreateRequest();

            expect(() => validateType(request, FloorplanDocumentBatchUpdateRequest)).not.toThrow();
        })
        test('create request serialize Test', () => {
            const request = mockUtils.buildBatchCreateRequest();

            const type = FloorplanDocumentBatchUpdateRequest;

            const serializedRequest = serialize(request, type, {
                validateBeforeSerialize: true,
            });

            expect(serializedRequest).toEqual(mockUtils.serializedBatchCreateRequest);
        })

        test('update request validate Test', () => {
            const request = mockUtils.buildBatchUpdateRequest();

            expect(() => validateType(request, FloorplanDocumentBatchUpdateRequest)).not.toThrow();
        })

        test('update request serialize Test', () => {
            const request = mockUtils.buildBatchUpdateRequest();

            const type = FloorplanDocumentBatchUpdateRequest;

            const serializedRequest = serialize(request, type, {
                validateBeforeSerialize: true,
            });

            expect(serializedRequest).toEqual(mockUtils.serializedBatchUpdateRequest);
        })

        test('delete request validate Test', () => {
            const request = mockUtils.buildBatchDeleteRequest();

            expect(() => validateType(request, FloorplanDocumentBatchUpdateRequest)).not.toThrow();
        })

        test('delete request serialize Test', () => {
            const request = mockUtils.buildBatchDeleteRequest();

            const type = FloorplanDocumentBatchUpdateRequest;

            const serializedRequest = serialize(request, type, {
                validateBeforeSerialize: true,
            });

            expect(serializedRequest).toEqual(mockUtils.serializedBatchDeleteRequest);
        })
    })
});
