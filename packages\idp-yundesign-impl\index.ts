import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { ArrayType, BasicType, ObjectType } from '@qunhe/kls-abstraction';
import { injections } from '@qunhe/apaas-type-generator-lib';
const ElementIdType: ObjectType = {
    type: BasicType.Object,
    properties: {
        id: {
            type: BasicType.String
        },
        type: {
            type: BasicType.String
        }
    }
};

const ElementIdWithUuidType: ObjectType = {
    type: BasicType.Object,
    properties: {
        id: {
            type: BasicType.String
        },
        type: {
            type: BasicType.String
        },
        uuid: {
            type: BasicType.String
        }
    }
};

const PromiseResultType: ObjectType = {
    type: BasicType.Object,
    properties: {
        code: {
            type: BasicType.Number
        },
        errorMessage: {
            type: BasicType.String
        },
        data: ElementIdType
    }
};

const PromiseResultWithUuidType: ObjectType = {
    type: BasicType.Object,
    properties: {
        code: {
            type: BasicType.Number
        },
        errorMessage: {
            type: BasicType.String
        },
        data: ElementIdWithUuidType
    }
};

const StartDragProductPromiseResultType: ObjectType = {
    type: BasicType.Object,
    properties: {
        code: {
            type: BasicType.Number
        },
        errorMessage: {
            type: BasicType.String || BasicType.Undefined
        },
        data: ({
            type: BasicType.Array,
            value: ElementIdType
        }) as ArrayType
    }
};

export const getVMBindingType = once(() => {
    return createVMBindingType({
        types: {
            PromiseResult: PromiseResultType,
            StartDragProductPromiseResult: StartDragProductPromiseResultType,
            PromiseResultWithUuid: PromiseResultWithUuidType
        },
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        }
    });
});

export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal({
        types: {
            PromiseResult: PromiseResultType,
            StartDragProductPromiseResult: StartDragProductPromiseResultType,
            PromiseResultWithUuid: PromiseResultWithUuidType
        },
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        }
    });
});
