import { IDP as IDPCommon } from '@qunhe/idp-common';
import { EToolType } from '@qunhe/custom-apass-api';

interface Number3 {
    x: number;
    y: number;
    z: number;
}

interface Number2 {
    x: number;
    y: number;
}

/**
 * 装配体快照组
 */
interface SnapperGroup {
    id: string;
    /**
     * 装配体在空间中的位置
     */
    position: Number3;
    /**
     * 装配体在空间中的旋转
     */
    rotation: Number3;
    /**
     * 尺寸信息
     */
    size: Number3;
    /**
     * SnapperGroup下的所有装配体
     * @type {Snapper[]}
     */
    snappers: Snapper[];
    /**
     * 装配内，装配单体之间的连接信息
     */
    connections: Connection[];
}

interface Snapper {
    id: string;
    name: string;
    /**
     * 是否为补件
     */
    isJunction: boolean;
    position: Number3;
    rotation: Number3;
    size: Number3;
    connectors: Connector[];
    routings: SnapperRouting[];
}

interface Connection {
    firstSnapperId: string;
    firstConnectorId: string;
    secondSnapperId: string;
    secondConnectorId: string;
}

declare enum EConnectorType {
    /**
     * 连接点
     */
    point,
    /**
     * 连接线
     */
    lineSeg,
    /**
     * 弧线
     */
    arc,
    /**
     * 面
     */
    face
}

interface Connector {
    /**
     * 连接键ID
     */
    id: string;
    /**
     * 连接键名称
     */
    name: string;
    /**
     * 连接键类型
     */
    type: EConnectorType;
}

declare enum OfficeCableBusinessType {
    /**
     * 横向/纵向走线区域辅助面
     */
    officeCableHorSur = 'Office_cable_HorSur',
    /**
     * 天花走线辅助体
     */
    officeCableCeilCab = 'Office_cable_CeilCab',
    /**
     * 屏风下边缘辅助线
     */
    officeCableBotEgLine = 'Office_cable_BotEgLine',
    /**
     * 屏风左边缘辅助线
     */
    officeCableLefEgLine = 'Office_cable_LefEgLine',
    /**
     * 屏风右边缘辅助线
     */
    officeCableRigEgLine = 'Office_cable_RigEgLine',

    /**
     * 纵向走线辅助体/横向走线辅助体
     */
    officeCableHorSurAux = 'Office_cable_HorSurAux',
}

interface SnapperRouting {
    id: string;
    name: string;
    type: OfficeCableBusinessType;
    active: boolean;
    value: string;
}

declare enum ERoutingFaceDirection {
    /**
     * 横向
     */
    horizontal = 'horizontal',
    /**
     * 纵向
     */
    vertical = 'vertical',
}

interface RoutingFaceConnectivityDetectionResult {
    /**
     * 走线检测的整体结果，是否程序出现异常
     * success: 表示全部检测，程序无异常
     * failed: 表示程序运行失败
     * abort: 撤销恢复快速点击中产生的多个检测请求，非最后一次会被取消
     */
    status: 'success' | 'failed' | 'abort',
    /**
     * 此次走线检测时，请求的数据
     */
    requestElementId?: IDPCommon.DB.Types.ElementId[];
    /**
     * 走线检测成功的一线组走线
     */
    successed: IDPCommon.DB.Types.ElementId[];
    /**
     * 走线检测失败的一组组走线
     */
    failed: IDPCommon.DB.Types.ElementId[];
}

interface DetectionResult {
    /**
     * 检测成功的模型
     */
    pass: IDPCommon.DB.Types.ElementId[];
    /**
     * 检测失败的模型
     */
    fail: IDPCommon.DB.Types.ElementId[];
    /**
     * 未经检测处理的模型
     */
    untouched: IDPCommon.DB.Types.ElementId[];
}

// TODO: put yor non exported types here
declare namespace IDP {

    interface EventTypes {
        /**
         * 进入走线设计环境
         * 如果error存在，表明进入走线环境失败
         */
        'IDP.Kada.RoutingFace.enter': undefined | Error;
        /**
         * 退出走线设计环境
         * 如果error存在，表明退出走线环境失败
         */
        'IDP.Kada.RoutingFace.exit': void | Error;
        /**
         * 走线设计环境下，走线实时检测开始执行时触发事件
         */
        'IDP.Kada.RoutingFace.BeforeConnectivityDetection': IDPCommon.DB.Types.ElementId[];
        /**
         * 走线设计环境下，走线检测结束时触发事件
         */
        'IDP.Kada.RoutingFace.AfterConnectivityDetection': RoutingFaceConnectivityDetectionResult;
        /**
         * 走线环境下，走线插座被删除事件
         */
        'IDP.Kada.RoutingSocket.Delete': IDPCommon.DB.Types.ElementId[];
        /**
         * 走线设计环境下，插座添加事件
         */
        'IDP.Kada.RoutingSocket.Add': IDPCommon.DB.Types.ElementId[];
    }

    namespace Custom {
        /**
         * 办公命名空间
         * @internal
         */
        namespace Kada {
            /**
             * 装配模型详细信息
             * @internal
             */
            interface Snapper {
                /** 装配模型/装配体id */
                elementId: IDPCommon.DB.Types.ElementId;
                /** 引用的位置 */
                position: Number3;
                /** 旋转信息 */
                rotation: Number3;
                /** box大小 */
                size: Number3;
                /**
                 * 当前模型所属的工具线
                 */
                toolType: EToolType;
            }

            /**
             * 装配体详细信息
             * @internal
             */
            interface SnapperGroup extends Snapper {
                /** 装配体子模型信息 */
                children: Snapper[];
            }

            /**
             * @vm-type UnknownType
             */
            type KadaModel = Snapper | SnapperGroup;

            /**
             * 定制厨卫装配模型/装配体数据列表
             * @internal
             */
            function findCabinetSnapperList(): KadaModel[];

            /**
             * 定制厨卫副本装配模型/装配体数据列表
             * @internal
             */
            function findCabinetCopySnapperList(): KadaModel[];
            /**
             * 定制全屋家具装配模型/装配体数据列表
             * @internal
             */
            function findWardrobeSnapperList(): KadaModel[];
            /**
             * 定制全屋家具副本装配模型/装配体数据列表
             * @internal
             */
            function findWardrobeCopySnapperList(): KadaModel[];
            /**
             * 是否进入/退出装配环境
             * 位于装配环境，返回当前激活的行业线
             * 不在装配环境，返回undefined
             * @internal
             */
            function isInKada(): EToolType | undefined;
        }
    }

    namespace Kada {

        interface SnapperUniquenessResult {
            result: Array<{
                /**
                 * 屏风ID
                 */
                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 唯一性编码
                 */
                uniqueValue: string;
            }>
        }

        /**
         * 屏风唯一性判断
         * 注意：调用此接口，需要确认方案已经正确保存。可以通过`IDP.Design.save()`来完成方案的保存。
         * @param option elements 与 toolType 至少一个参数；当传入elements参数时，toolType参数将会忽略；唯一性判断只会对传入的elements参数进行判断；当传入toolType参数时，将会按工具线进行唯一性判断
         * @throws {Error} 如果获取唯一性编码失败，会抛出错误
         */
        function getSnapperUniquenessAsync(option: {
            elements?: IDPCommon.DB.Types.ElementId[];
            toolType?: EToolType;
        }): Promise<SnapperUniquenessResult>;

        /**
         * 指定模型ID/工具线, 打开清单页面
         * 非稳定API, 使用前请联系酷家乐技术支持
         * @param option elements 与 toolType 至少一个参数；当传入elements参数时，toolType参数将会忽略；elementIds 当前支持定制模型/组合、引用、冻结、装配体，不传入则按照工具线处理
         * @throws {Error} 参数错误，会抛出错误
         */
        function openBudgetAsync(options: {
            elements?: IDPCommon.DB.Types.ElementId[];
            toolType?: EToolType;
        }): Promise<void>;

        /**
         * 走线设计模块
         * 非稳定模块API，使用前请联系酷家乐技术支持
         */
        namespace RoutingFace {

            interface RoutingSocket {
                /**
                 * 当前五金的产品ID
                 */
                productId: string;
                /**
                 * 插座的ID
                 */
                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 插座的名称
                 */
                name: string;
                /**
                 * 插座的位置信息
                 */
                position: Number3;
                /**
                 * 插座的旋转信息
                 */
                rotation: Number3;
                /**
                 * 插座的大小信息
                 */
                size: Number3;
                /**
                 * 插座是否已经创建
                 */
                isCreated: boolean;
            }

            /**
             * 走线面
             */
            interface RoutingFace {
                /**
                 * 关联的装配单体商品ID
                 */
                linkedProductId: string;
                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 走线面方向
                 */
                type: ERoutingFaceDirection;
                /**
                 * 走线面名称
                 */
                name: string;
                /**
                 * 走线面的外轮廓点；
                 */
                outLoop: Number3[][];
                /**
                 * 是否激活
                 */
                active: boolean;
                /**
                 * 关联的五金插座
                 */
                linkedSockets: RoutingSocket[];
            }

            /**
             * 走线设计辅助体；通常设计在转接接柱处
             * 表达的为天花走线
             */
            interface RoutingCube {
                /**
                 * 关联的装配单体商品ID
                 */
                linkedProductId: string;
                /**
                 * Cube的ID
                 */
                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 走线体名称
                 */
                name: string;
                /**
                 * 是否激活
                 */
                active: boolean;
            }

            /**
             * 走线面组
             */
            interface RoutingFaceGroup {
                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 是否激活
                 */
                active: boolean;
                faces: RoutingFace[];
            }

            interface RoutingFaceForCreateAdsorption {
                /**
                 * 所属的Snapper模型ID
                 */
                belongsToSnapperElementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 当前拖拽的五金插座ID
                 */
                dragRoutingSocket: {
                    productId: string;
                    /**
                     * 五金插座的ID
                     */
                    elementId: IDPCommon.DB.Types.ElementId;
                    /**
                     * 当前五金插座的尺寸信息
                     */
                    size: Number2;
                };
                /**
                 * 走线面/走线组
                 * @vm-type UnknownType
                 */
                routingFace2d: RoutingFace2d | RoutingFaceGroup2d;
            }

            interface RoutingFace2d {
                /**
                 * 关联的装配单体商品ID
                 */
                linkedProductId: string;

                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 走线面名称
                 */
                name: string;
                /**
                 * 是否激活
                 */
                active: boolean;
                /**
                 * 走线面类型
                 */
                type: ERoutingFaceDirection;
                /**
                 * 走线面的在2d下的轮廓
                 */
                outLoop: Number2[][];
                /**
                 * 关联的五金插座
                 */
                linkedSockets: RoutingSocket2d[];
            }

            interface RoutingFaceGroup2d {
                elementId: IDPCommon.DB.Types.ElementId;
                faces: RoutingFace2d[];
            }

            interface RoutingSocket2d {
                productId: string;
                /**
                 * 插座的ID
                 */
                elementId: IDPCommon.DB.Types.ElementId;
                /**
                 * 插座的名称
                 */
                name: string;
                /**
                 * 插座的位置信息
                 */
                position: Number2;
                /**
                 * 插座的旋转信息
                 */
                rotation: Number2;
                /**
                 * 插座的大小信息
                 */
                size: Number2;
                /**
                 * 插座是否已经创建
                 */
                isCreated: boolean;
            }

            interface AdsorptionPointData {
                /**
                 * 关联的走线面ID
                 */
                faceId:  IDPCommon.DB.Types.ElementId;
                point: Number2;
                /**
                 * 当前走线面，最终放置的公式
                 */
                positionFormula?: {
                    x?: string;
                    y?: string;
                    z?: string;
                }
            }

            interface AdsorptionLineData {
                /**
                 * 关联的走线面ID
                 */
                faceId:  IDPCommon.DB.Types.ElementId;
                /**
                 * 吸附起点
                 */
                startPoint: Number2;
                /**
                 * 吸附终点
                 */
                endPoint: Number2;
            }

            /**
             * @vm-type UnknownType
             */
            type AdsorptionData = AdsorptionPointData | AdsorptionLineData;

            interface EnterRoutingFaceOption {
                /**
                 * 走线设计中，所有插座相关的商品ID
                 */
                socketProductIds: string[];
                /**
                 * 根据走线面，创建吸附数据
                 */
                createAdsorptionData(face: RoutingFaceForCreateAdsorption): AdsorptionData[];
            }

            interface RoutingSnapperGroup {
                elementId: IDPCommon.DB.Types.ElementId;
                position: Number3;
                rotation: Number3;
                size: Number3;
                children: RoutingSnapper[];
                connection: RoutingSnapperConnection[];
            }

            interface RoutingSnapperConnection {
                firstId: string;
                firstSnapperId: string;
                firstConnectorId: string;
                secondId: string;
                secondSnapperId: string;
                secondConnectorId: string;
            }

            interface RoutingSnapper {
                productId: string;
                elementId: IDPCommon.DB.Types.ElementId;
                position: Number3;
                rotation: Number3;
                size: Number3;
                connectors: Connector[];
                routingFaces: RoutingFace[];
                routingCubes: RoutingCube[];
            }

            /**
             * 进入走线设计模块
             */
            function enter(option: EnterRoutingFaceOption): void;
            /**
             * 退出走线设计模块
             * @param ignoreErrors 是否强制退出
             * @throws {Error} 如果存在未生成的插座时，点击生成，则会导致
             */
            function exit(ignoreErrors?: boolean): void;

            /**
             * 拖动插座产品，productId必须为进入走线设计模块预置的插座模型
             * @param socketProductId 插座模型ID
             */
            function startDragSocketAsync(socketProductId: string): Promise<IDPCommon.DB.Types.ElementId>;

            /**
             * 将模型上的插座附加到模型上
             * @param elementId
             */
            function attachSocketToModel(elementId?: IDPCommon.DB.Types.ElementId[]): Promise<void>;

            /**
             * @vm-type UnknownType
             */
            type RoutingSnapperGroupListResult = RoutingSnapperGroup | RoutingSnapper;

            /**
             * 获取当前走线设计当中，存在作用的装配体
             */
            function getRoutingSnapperGroupList(): RoutingSnapperGroupListResult[];

            /**
             * 通过走线面ID，查找走线所属的装配体
             */
            function findRoutingSnapperGroupById(faceId: IDPCommon.DB.Types.ElementId): RoutingSnapperGroup | null;

            /**
             * 获取所有插座
             * 仅限走线设计环境开启后，才可以正常调用
             */
            function getRoutingSocketList(): RoutingSocket[];

            /**
             * 删除插座
             * @param elementId
             */
            function deleteRoutingSocket(elementId: IDPCommon.DB.Types.ElementId): void;
        }

        /**
         * 办公检测的API
         */
        namespace Detection {

            /**
             * 指定装配体ID，进行装配检测
             * @param elementIds 传入要检测的装配体/装配单体的ID信息
             * @throws {Error} 如果未传入elementIds时，则会产生异常，当前不支持整个方案级别的检测
             */
            function detectRoutingFaceConnectivityAsync(elementIds?: IDPCommon.DB.Types.ElementId[]): Promise<RoutingFaceConnectivityDetectionResult>;

            /**
             * 对传入的模型(支持：装配体/装配单体/定制模型/组合/块/混组)进行强制连接检测
             * 注意：仅支持办公模式内
             * @param elementIds 传入要检测的模型Id数组,不传则按照装配单体检测场景中所有模型
             * @returns 返回检测结果，结果中的成功/失败/未处理会和传入值一一对应
             * 注意：如果组合/块/混组内存在一个没有通过检测，整个组合/块/混组认为检测失败
             */
            function forceConnectionDetectionAsync(
                elementIds?: IDPCommon.DB.Types.ElementId[]
            ): Promise<DetectionResult>;

            /**
             * 对传入的模型(支持：装配体/装配单体/定制模型/组合/块/混组)进行相近连接检测
             * 注意：仅支持办公模式内
             * @param elementIds 传入要检测的模型Id数组,不传则检测场景中所有模型
             * @returns 返回检测结果，结果中的成功/失败/未处理会和传入值一一对应
             * 注意：如果组合/块/混组内存在一个没有通过检测，整个组合/块/混组认为检测失败
             */
            function spatialProximityDetectionAsync(
                elementIds?: IDPCommon.DB.Types.ElementId[]
            ): Promise<DetectionResult>;
        }
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
