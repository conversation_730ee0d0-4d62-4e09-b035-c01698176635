{"name": "@qunhe/idp-types-packages", "version": "1.0.0", "description": "", "private": true, "workspaces": ["packages/*"], "scripts": {"test": "jest"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kls/miniapp.git"}, "author": "", "resolutions": {"@types/babel__traverse": "7.0.6", "node-sass": "file:./scripts/empty-package"}, "devDependencies": {"@qunhe/apaas-type-generator": "~0.1.8", "@qunhe/def-cli": "^0.11.21", "@qunhe/dts-rollup": "0.4.6", "@qunhe/eslint-plugin": "^1.1.3", "@qunhe/kjl-plugin_builder": "^1.1.1", "@qunhe/kjl-plugin_manual": "~1.6.2", "@qunhe/tools-script": "~0.1.18", "@qunhe/ts-builder": "~1.8.5", "@qunhe/tsconfig": "^0.1.0", "@types/fs-extra": "^9.0.1", "@types/jest": "^26.0.10", "@types/lodash": "4.x", "@yarnpkg/lockfile": "^1.1.0", "copyfiles": "^2.4.1", "fs-extra": "^9.0.1", "hjson": "^3.2.1", "husky": "^4.2.5", "jest": "^29", "lerna": "^4.0.0", "rimraf": "*", "semver": "^7.3.5", "ts-jest": "^29", "ts-node": "^10.3.0", "typescript": "^4.2.3", "yargs": "^16"}, "dependencies": {}, "husky": {"hooks": {"pre-commit": "node ./scripts/lint-ts-files.js"}}}