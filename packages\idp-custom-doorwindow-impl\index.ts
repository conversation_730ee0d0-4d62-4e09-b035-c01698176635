import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { injections } from '@qunhe/apaas-type-generator-lib';

export const getVMBindingType = once(() => {
    // TODO: modify your vm type creator params here
    return createVMBindingType();
});
export const getVMBindingTypeInternal = once(() => {
    // TODO: modify your internal vm type creator params here
    return createVMBindingTypeInternal({
        packages: {
            // @ts-ignore
            '@qunhe/custom-apass-api': injections['@qunhe/custom-apass-api']
        }
    });
});
