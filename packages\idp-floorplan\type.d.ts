import { KBoundingBox2d, KVector2d } from '@qunhe/math-apaas-api';
/**
 * Cad图层信息
 * @internal
 */
interface FloorPlanLayerInfo {
    name: string;
    visible: boolean;
}

/**
 * Cad底图信息
 * @internal
 */
interface FloorPlanCadInfo {
    isCadImported: boolean;
    data?: {
        name: string;
        id: string;
        layers: FloorPlanLayerInfo[];
    }
}

/**
 * Cad纯底图导入生成方案数据
 * @internal
 */
interface CadK2dInfo {
    k2dUrl: string;
    name: string;
    drawingType: number;
    splitBox: KBoundingBox2d;
    levelId: number;
    levelName: string;
    elevation: number;
    height: number;
    offset: KVector2d;
}

/**
 * 打开户型需要的CAD信息
 * @internal
 */
interface CadImportParams {
    recordId: string,
    shouldClearDesign: boolean,
    cadK2dInfos?: CadK2dInfo[],
}

export {
    FloorPlanLayerInfo,
    FloorPlanCadInfo,
    CadK2dInfo,
    CadImportParams
};

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
