#!/usr/bin/node
const path = require('path');
const childProcess = require('child_process');

const files = childProcess.execSync('git diff --cached --name-only --diff-filter=ACMRTUXB')
    .toString()
    .split(/\r?\n/)
    .filter(filename => /\.tsx?$/.test(filename));
if (!files.length) { process.exit(0); }
const cmd = `${path.normalize('./node_modules/.bin/eslint')} ${files.join(' ')}`;
childProcess.exec(cmd, { maxBuffer: 10 * 1024 * 1024 }, (err, stdio, stderr) => {
    if (err) {
        console.log('tslint error:');
        console.log(stdio || err);
        process.exit(1);
    }
    process.exit(0);
});
