#!/bin/bash

# SDK 构建脚本
# 支持单个服务或所有服务的 SDK 构建
# 重构自 tools/ci-cd/ci/deployment/build-sdks.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# Maven 配置
MAVEN_SETTINGS="${MAVEN_SETTINGS:-config/ci-cd/ci-settings.xml}"

# 构建单个服务的 SDK
build_service_sdk() {
    local service="$1"
    local language="${2:-java}"
    local build_phase="${3:-compile}"
    
    log_info "📦 构建 $service SDK ($language)..."
    
    # 验证服务
    if ! is_valid_service "$service"; then
        log_error "无效的服务名: $service"
        increment_counter "build_failed"
        return 1
    fi
    
    # 获取输出目录
    local output_dir=$(get_service_output_path "$service" "$language")
    
    # 检查输出目录
    if ! check_dir_exists "$output_dir" "SDK 输出目录"; then
        log_error "$service SDK 输出目录不存在，请先生成 SDK"
        increment_counter "build_failed"
        return 1
    fi
    
    # 检查是否有 pom.xml (Java 项目)
    if [ "$language" = "java" ]; then
        if ! check_file_exists "$output_dir/pom.xml" "Maven POM 文件"; then
            log_error "$service SDK 缺少 Maven POM 文件"
            increment_counter "build_failed"
            return 1
        fi
    fi
    
    # 保存当前目录
    local project_dir=$(pwd)
    
    # 构建 SDK
    log_info "执行 SDK 构建..."
    local start_time=$(get_timestamp)
    
    if cd "$output_dir"; then
        local build_success=false
        
        case "$language" in
            "java")
                # Maven 构建
                local maven_cmd="mvn $build_phase"
                if [ -f "$project_dir/$MAVEN_SETTINGS" ]; then
                    maven_cmd="$maven_cmd -s $project_dir/$MAVEN_SETTINGS"
                fi
                
                log_debug "执行命令: $maven_cmd"
                
                if $maven_cmd >/dev/null 2>&1; then
                    build_success=true
                else
                    log_error "Maven 构建失败"
                    # 显示错误详情
                    log_info "错误详情:"
                    $maven_cmd 2>&1 | tail -20
                fi
                ;;
            *)
                log_error "不支持的语言: $language"
                ;;
        esac
        
        # 返回项目目录
        cd "$project_dir"
        
        local end_time=$(get_timestamp)
        local duration=$((end_time - start_time))
        
        if [ "$build_success" = true ]; then
            log_success "$service SDK 构建成功 (耗时: ${duration}s)"
            increment_counter "build_success"
            return 0
        else
            log_error "$service SDK 构建失败"
            increment_counter "build_failed"
            return 1
        fi
    else
        log_error "无法进入 SDK 目录: $output_dir"
        increment_counter "build_failed"
        return 1
    fi
}

# 构建所有服务的 SDK
build_all_sdks() {
    local language="${1:-java}"
    local build_phase="${2:-compile}"
    
    log_step "1" "构建所有服务的 SDK ($language)"
    
    local services=$(get_services)
    local overall_status=0
    
    if [ -z "$services" ]; then
        log_warning "未发现任何服务"
        return 0
    fi
    
    log_info "发现的服务: $services"
    
    for service in $services; do
        if build_service_sdk "$service" "$language" "$build_phase"; then
            log_success "服务 $service SDK 构建完成"
        else
            log_error "服务 $service SDK 构建失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 构建变更服务的 SDK
build_changed_sdks() {
    local language="${1:-java}"
    local build_phase="${2:-compile}"
    
    log_step "1" "构建变更服务的 SDK ($language)"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过 SDK 构建"
        return 0
    fi
    
    if [ -z "$CHANGED_SERVICES" ]; then
        log_warning "变更服务列表为空，构建所有服务的 SDK"
        build_all_sdks "$language" "$build_phase"
        return $?
    fi
    
    log_info "变更的服务: $CHANGED_SERVICES"
    
    local overall_status=0
    
    for service in $CHANGED_SERVICES; do
        if build_service_sdk "$service" "$language" "$build_phase"; then
            log_success "变更服务 $service SDK 构建完成"
        else
            log_error "变更服务 $service SDK 构建失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 构建指定服务列表的 SDK
build_service_list() {
    local service_list="$1"
    local language="${2:-java}"
    local build_phase="${3:-compile}"
    
    log_step "1" "构建指定服务的 SDK ($language)"
    
    if [ -z "$service_list" ]; then
        log_error "服务列表为空"
        return 1
    fi
    
    log_info "指定的服务: $service_list"
    
    local overall_status=0
    
    for service in $service_list; do
        if build_service_sdk "$service" "$language" "$build_phase"; then
            log_success "服务 $service SDK 构建完成"
        else
            log_error "服务 $service SDK 构建失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 验证构建环境
verify_build_environment() {
    log_step "0" "验证构建环境"
    
    # 检查 Maven
    if ! mvn -version >/dev/null 2>&1; then
        log_error "Maven 不可用"
        return 1
    fi
    
    # 检查 Maven 配置文件
    if [ -n "$MAVEN_SETTINGS" ] && [ ! -f "$MAVEN_SETTINGS" ]; then
        log_warning "Maven 配置文件不存在: $MAVEN_SETTINGS"
        log_info "将使用默认 Maven 配置"
        MAVEN_SETTINGS=""
    fi
    
    log_success "构建环境验证通过"
    return 0
}

# 显示构建统计
show_build_stats() {
    log_step "2" "显示构建统计"
    
    echo ""
    log_info "📦 SDK 构建结果统计:"
    
    local success=$(get_counter "build_success")
    local failed=$(get_counter "build_failed")
    local total=$((success + failed))
    
    echo "  总服务数: $total"
    echo "  构建成功: $success"
    echo "  构建失败: $failed"
    
    if [ $total -gt 0 ]; then
        local success_rate=$((success * 100 / total))
        echo "  成功率: $success_rate%"
    fi
    
    show_stats "SDK 构建统计"
}

# 主函数
main() {
    local mode="${1:-changed}"     # changed, all, service, list
    local target="${2:-}"          # 服务名或服务列表
    local language="${3:-java}"    # 编程语言
    local build_phase="${4:-compile}"  # 构建阶段
    
    init_script "SDK 构建" "构建 OpenAPI SDK"
    
    # 验证构建环境
    if ! verify_build_environment; then
        finish_script "SDK 构建" "false"
        exit 1
    fi
    
    local build_status=0
    
    case "$mode" in
        "changed")
            log_info "构建模式: 变更服务"
            if ! build_changed_sdks "$language" "$build_phase"; then
                build_status=1
            fi
            ;;
        "all")
            log_info "构建模式: 所有服务"
            if ! build_all_sdks "$language" "$build_phase"; then
                build_status=1
            fi
            ;;
        "service")
            if [ -z "$target" ]; then
                log_error "服务构建模式需要指定服务名"
                finish_script "SDK 构建" "false"
                exit 1
            fi
            log_info "构建模式: 单个服务 ($target)"
            if ! build_service_sdk "$target" "$language" "$build_phase"; then
                build_status=1
            fi
            ;;
        "list")
            if [ -z "$target" ]; then
                log_error "列表构建模式需要指定服务列表"
                finish_script "SDK 构建" "false"
                exit 1
            fi
            log_info "构建模式: 服务列表"
            if ! build_service_list "$target" "$language" "$build_phase"; then
                build_status=1
            fi
            ;;
        *)
            log_error "无效的构建模式: $mode"
            log_info "支持的模式: changed, all, service, list"
            finish_script "SDK 构建" "false"
            exit 1
            ;;
    esac
    
    show_build_stats
    
    if [ $build_status -eq 0 ]; then
        log_success "🎉 SDK 构建完成！"
        finish_script "SDK 构建" "true"
        return 0
    else
        log_error "❌ SDK 构建失败，请检查错误信息"
        finish_script "SDK 构建" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "SDK 构建模块已加载 ✅"
