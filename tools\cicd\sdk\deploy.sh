#!/bin/bash

# SDK 部署脚本
# 支持正式版本和快照版本的 SDK 部署
# 合并自 deploy-releases.sh 和 deploy-snapshots.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# Maven 配置
MAVEN_SETTINGS="${MAVEN_SETTINGS:-config/ci-cd/ci-settings.xml}"

# 部署单个服务的 SDK
deploy_service_sdk() {
    local service="$1"
    local deploy_type="${2:-snapshots}"  # releases, snapshots
    local language="${3:-java}"
    
    log_info "🚀 部署 $service SDK ($deploy_type, $language)..."
    
    # 验证服务
    if ! is_valid_service "$service"; then
        log_error "无效的服务名: $service"
        increment_counter "deploy_failed"
        return 1
    fi
    
    # 获取输出目录
    local output_dir=$(get_service_output_path "$service" "$language")
    
    # 检查输出目录
    if ! check_dir_exists "$output_dir" "SDK 输出目录"; then
        log_error "$service SDK 输出目录不存在，请先生成、构建和测试 SDK"
        increment_counter "deploy_failed"
        return 1
    fi
    
    # 检查是否有 pom.xml (Java 项目)
    if [ "$language" = "java" ]; then
        if ! check_file_exists "$output_dir/pom.xml" "Maven POM 文件"; then
            log_error "$service SDK 缺少 Maven POM 文件"
            increment_counter "deploy_failed"
            return 1
        fi
    fi
    
    # 保存当前目录
    local project_dir=$(pwd)
    
    # 部署 SDK
    log_info "执行 SDK 部署..."
    local start_time=$(get_timestamp)
    
    if cd "$output_dir"; then
        local deploy_success=false
        
        case "$language" in
            "java")
                # Maven 部署
                local maven_cmd="mvn deploy -DskipTests"
                if [ -f "$project_dir/$MAVEN_SETTINGS" ]; then
                    maven_cmd="$maven_cmd -s $project_dir/$MAVEN_SETTINGS"
                fi
                
                # 根据部署类型设置参数
                case "$deploy_type" in
                    "releases")
                        # 正式版本部署，确保版本号不包含 SNAPSHOT
                        maven_cmd="$maven_cmd -DperformRelease=true"
                        ;;
                    "snapshots")
                        # 快照版本部署，确保版本号包含 SNAPSHOT
                        maven_cmd="$maven_cmd -DperformSnapshot=true"
                        ;;
                esac
                
                log_debug "执行命令: $maven_cmd"
                
                if $maven_cmd >/dev/null 2>&1; then
                    deploy_success=true
                    
                    # 检查部署结果
                    if [ -d "target" ]; then
                        local artifacts=$(find target -name "*.jar" -o -name "*.pom" | wc -l)
                        log_debug "部署了 $artifacts 个构件"
                    fi
                else
                    log_error "Maven 部署失败"
                    # 显示错误详情
                    log_info "错误详情:"
                    $maven_cmd 2>&1 | tail -20
                fi
                ;;
            *)
                log_error "不支持的语言: $language"
                ;;
        esac
        
        # 返回项目目录
        cd "$project_dir"
        
        local end_time=$(get_timestamp)
        local duration=$((end_time - start_time))
        
        if [ "$deploy_success" = true ]; then
            log_success "$service SDK 部署成功 (耗时: ${duration}s)"
            increment_counter "deploy_success"
            increment_counter "deploy_${deploy_type}"
            return 0
        else
            log_error "$service SDK 部署失败"
            increment_counter "deploy_failed"
            return 1
        fi
    else
        log_error "无法进入 SDK 目录: $output_dir"
        increment_counter "deploy_failed"
        return 1
    fi
}

# 部署所有服务的 SDK
deploy_all_sdks() {
    local deploy_type="${1:-snapshots}"
    local language="${2:-java}"
    
    log_step "1" "部署所有服务的 SDK ($deploy_type, $language)"
    
    local services=$(get_services)
    local overall_status=0
    
    if [ -z "$services" ]; then
        log_warning "未发现任何服务"
        return 0
    fi
    
    log_info "发现的服务: $services"
    
    for service in $services; do
        if deploy_service_sdk "$service" "$deploy_type" "$language"; then
            log_success "服务 $service SDK 部署完成"
        else
            log_error "服务 $service SDK 部署失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 部署变更服务的 SDK
deploy_changed_sdks() {
    local deploy_type="${1:-snapshots}"
    local language="${2:-java}"
    
    log_step "1" "部署变更服务的 SDK ($deploy_type, $language)"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过 SDK 部署"
        return 0
    fi
    
    if [ -z "$CHANGED_SERVICES" ]; then
        log_warning "变更服务列表为空，部署所有服务的 SDK"
        deploy_all_sdks "$deploy_type" "$language"
        return $?
    fi
    
    log_info "变更的服务: $CHANGED_SERVICES"
    
    local overall_status=0
    
    for service in $CHANGED_SERVICES; do
        if deploy_service_sdk "$service" "$deploy_type" "$language"; then
            log_success "变更服务 $service SDK 部署完成"
        else
            log_error "变更服务 $service SDK 部署失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 部署指定服务列表的 SDK
deploy_service_list() {
    local service_list="$1"
    local deploy_type="${2:-snapshots}"
    local language="${3:-java}"
    
    log_step "1" "部署指定服务的 SDK ($deploy_type, $language)"
    
    if [ -z "$service_list" ]; then
        log_error "服务列表为空"
        return 1
    fi
    
    log_info "指定的服务: $service_list"
    
    local overall_status=0
    
    for service in $service_list; do
        if deploy_service_sdk "$service" "$deploy_type" "$language"; then
            log_success "服务 $service SDK 部署完成"
        else
            log_error "服务 $service SDK 部署失败"
            overall_status=1
        fi
        
        echo ""  # 添加空行分隔
    done
    
    return $overall_status
}

# 验证部署环境
verify_deploy_environment() {
    log_step "0" "验证部署环境"
    
    # 检查 Maven
    if ! mvn -version >/dev/null 2>&1; then
        log_error "Maven 不可用"
        return 1
    fi
    
    # 检查 Java
    if ! java -version >/dev/null 2>&1; then
        log_error "Java 不可用"
        return 1
    fi
    
    # 检查 Maven 配置文件
    if [ -n "$MAVEN_SETTINGS" ] && [ ! -f "$MAVEN_SETTINGS" ]; then
        log_error "Maven 配置文件不存在: $MAVEN_SETTINGS"
        log_error "部署需要正确的 Maven 配置文件"
        return 1
    fi
    
    # 检查部署凭据（通过环境变量）
    if [ -z "$MAVEN_USERNAME" ] && [ -z "$NEXUS_USERNAME" ]; then
        log_warning "未设置 Maven 部署用户名环境变量"
    fi
    
    if [ -z "$MAVEN_PASSWORD" ] && [ -z "$NEXUS_PASSWORD" ]; then
        log_warning "未设置 Maven 部署密码环境变量"
    fi
    
    log_success "部署环境验证通过"
    return 0
}

# 显示部署统计
show_deploy_stats() {
    log_step "2" "显示部署统计"
    
    echo ""
    log_info "🚀 SDK 部署结果统计:"
    
    local success=$(get_counter "deploy_success")
    local failed=$(get_counter "deploy_failed")
    local total=$((success + failed))
    
    echo "  总服务数: $total"
    echo "  部署成功: $success"
    echo "  部署失败: $failed"
    
    if [ $total -gt 0 ]; then
        local success_rate=$((success * 100 / total))
        echo "  成功率: $success_rate%"
    fi
    
    local releases=$(get_counter "deploy_releases")
    local snapshots=$(get_counter "deploy_snapshots")
    
    echo "  正式版本: $releases"
    echo "  快照版本: $snapshots"
    echo "  部署时间: $(date)"
    
    show_stats "SDK 部署统计"
}

# 主函数
main() {
    local mode="${1:-changed}"        # changed, all, service, list
    local deploy_type="${2:-snapshots}"  # releases, snapshots
    local target="${3:-}"             # 服务名或服务列表
    local language="${4:-java}"       # 编程语言
    
    init_script "SDK 部署" "部署 OpenAPI SDK"
    
    # 验证部署类型
    if [[ "$deploy_type" != "releases" && "$deploy_type" != "snapshots" ]]; then
        log_error "无效的部署类型: $deploy_type"
        log_info "支持的部署类型: releases, snapshots"
        finish_script "SDK 部署" "false"
        exit 1
    fi
    
    # 验证部署环境
    if ! verify_deploy_environment; then
        finish_script "SDK 部署" "false"
        exit 1
    fi
    
    local deploy_status=0
    
    case "$mode" in
        "changed")
            log_info "部署模式: 变更服务"
            if ! deploy_changed_sdks "$deploy_type" "$language"; then
                deploy_status=1
            fi
            ;;
        "all")
            log_info "部署模式: 所有服务"
            if ! deploy_all_sdks "$deploy_type" "$language"; then
                deploy_status=1
            fi
            ;;
        "service")
            if [ -z "$target" ]; then
                log_error "服务部署模式需要指定服务名"
                finish_script "SDK 部署" "false"
                exit 1
            fi
            log_info "部署模式: 单个服务 ($target)"
            if ! deploy_service_sdk "$target" "$deploy_type" "$language"; then
                deploy_status=1
            fi
            ;;
        "list")
            if [ -z "$target" ]; then
                log_error "列表部署模式需要指定服务列表"
                finish_script "SDK 部署" "false"
                exit 1
            fi
            log_info "部署模式: 服务列表"
            if ! deploy_service_list "$target" "$deploy_type" "$language"; then
                deploy_status=1
            fi
            ;;
        *)
            log_error "无效的部署模式: $mode"
            log_info "支持的模式: changed, all, service, list"
            finish_script "SDK 部署" "false"
            exit 1
            ;;
    esac
    
    show_deploy_stats
    
    if [ $deploy_status -eq 0 ]; then
        log_success "🎉 SDK 部署完成！"
        finish_script "SDK 部署" "true"
        return 0
    else
        log_error "❌ SDK 部署失败，请检查错误信息"
        finish_script "SDK 部署" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "SDK 部署模块已加载 ✅"
