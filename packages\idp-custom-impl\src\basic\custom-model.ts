import {
    ArrayType,
    BasicType,
    FunctionType,
    ObjectType,
    Type,
} from '@qunhe/kls-abstraction';
import { getPromiseType } from '../helper';
import { BooleanType, NumberType, StringType, UndefinedType, UnknownType } from './base-types';
import { bzParamModelLitePropertiesTypes } from './custom-model-biz-properties-types';
import { ParamModelLiteParamTypes } from './custom-model-param';
import { ParamModelLitePlankPathTypes } from './custom-model-plank-path';
import { ElementId } from './element-id';
import { Number3FormulaTypes, Number3Types } from './number-types';

export const ParamModelLiteBaseType = {
    type: BasicType.Object,
    properties: {
        id: StringType,
        size: Number3Types,
        position: Number3Types,
        rotate: Number3Types,
        rotation: Number3Types,
        rotationFormula: Number3FormulaTypes,
        positionFormula: Number3FormulaTypes,
        name: StringType,
        remark: StringType,
        option: {
            type: BasicType.Object,
            properties: {
                defaultNamespace: StringType,
                bzValueMaxSize: NumberType,
                allowToolTypes: {
                    type: BasicType.Array,
                    value: NumberType,
                } as ArrayType,
                ruleValidate: BooleanType,
                postHiddenVariable: BooleanType,
            },
        },
        params: UnknownType,
        bzProperties: UnknownType,
        _plankPath: ParamModelLitePlankPathTypes,
        productId: StringType,
        category: NumberType,
        isHidden: BooleanType,
        isRoot: BooleanType,
        modelType: NumberType,
        toolType: StringType,
        root: UnknownType,
        type: NumberType,
        legal: BooleanType,
        _paramModelId: StringType,
        _origin: StringType,
        _changeMap: UnknownType,
        _parent: UnknownType,
        version: NumberType,
        children: {
            type: BasicType.Array,
            value: UnknownType,
        },
        accessories: {
            type: BasicType.Array,
            value: UnknownType,
        },
        getRoot: {
            name: 'getRoot',
            varying: false,
            keepArgsHandle: false,
            args: [BooleanType],
            type: BasicType.Function,
            return: UnknownType,
        },
        getParent: {
            name: 'getParent',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: UnknownType,
        },
        getAccessory: {
            name: 'getAccessory',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Array,
                value: UnknownType,
            } as ArrayType,
        },
        appendAccessory: {
            name: 'appendAccessory',
            varying: false,
            keepArgsHandle: false,
            args: [UnknownType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getChild: {
            name: 'getChild',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Array,
                value: UnknownType,
            } as ArrayType,
        },
        appendChild: {
            name: 'appendChild',
            varying: false,
            keepArgsHandle: false,
            args: [UnknownType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getParam: {
            name: 'getParam',
            varying: false,
            keepArgsHandle: false,
            args: [StringType],
            type: BasicType.Function,
            return: ParamModelLiteParamTypes,
        },
        hasParam: {
            name: 'hasParam',
            varying: false,
            keepArgsHandle: false,
            args: [StringType],
            type: BasicType.Function,
            return: BooleanType,
        },
        getBzProperty: {
            name: 'getBzProperty',
            varying: false,
            keepArgsHandle: false,
            args: [StringType, StringType],
            type: BasicType.Function,
            return: bzParamModelLitePropertiesTypes,
        },
        hasBzProperty: {
            name: 'hasBzProperty',
            varying: false,
            keepArgsHandle: false,
            args: [StringType, StringType],
            type: BasicType.Function,
            return: BooleanType,
        },
        removeBzProperty: {
            name: 'removeBzProperty',
            varying: false,
            keepArgsHandle: false,
            args: [StringType, StringType],
            type: BasicType.Function,
            return: BooleanType,
        },
        toJSON: {
            name: 'toJSON',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: UnknownType,
        },
        clone: {
            name: 'clone',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: UnknownType,
        },
        dispose: {
            name: 'dispose',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: UndefinedType,
        },
        serialize: {
            name: 'serialize',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        deserialize: {
            name: 'deserialize',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: UnknownType,
        },
        setName: {
            name: 'setName',
            varying: false,
            keepArgsHandle: true,
            args: [StringType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getName: {
            name: 'getName',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        setRemark: {
            name: 'setRemark',
            varying: false,
            keepArgsHandle: true,
            args: [StringType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getRemark: {
            name: 'getRemark',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        setPosition: {
            name: 'setPosition',
            varying: false,
            keepArgsHandle: true,
            args: [Number3Types],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getPosition: {
            name: 'getPosition',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: Number3Types,
        },
        setPositionFormula: {
            name: 'setPositionFormula',
            varying: false,
            keepArgsHandle: true,
            args: [Number3FormulaTypes],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getPositionFormula: {
            name: 'getPositionFormula',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: Number3FormulaTypes,
        },
        setRotate: {
            name: 'setRotate',
            varying: false,
            keepArgsHandle: true,
            args: [Number3Types],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getRotate: {
            name: 'getRotate',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: Number3Types,
        },
        setRotation: {
            name: 'setRotation',
            varying: false,
            keepArgsHandle: true,
            args: [Number3Types],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getRotation: {
            name: 'getRotation',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: Number3Types,
        },
        setRotationFormula: {
            name: 'setRotationFormula',
            varying: false,
            keepArgsHandle: true,
            args: [Number3FormulaTypes],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getRotationFormula: {
            name: 'getRotationFormula',
            varying: false,
            keepArgsHandle: true,
            args: [],
            type: BasicType.Function,
            return: Number3FormulaTypes,
        },
        setToolType: {
            name: 'setToolType',
            varying: false,
            keepArgsHandle: false,
            args: [StringType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getToolType: {
            name: 'getToolType',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        forceUpdate: {
            name: 'forceUpdate',
            varying: false,
            keepArgsHandle: true,
            args: [BooleanType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getProductCode: {
            name: 'getProductCode',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: StringType,
        },
        getElementId: {
            name: 'getElementId',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: ElementId,
        },
        __trackUpdateField: {
            name: '__trackUpdateField',
            varying: false,
            keepArgsHandle: true,
            args: [UnknownType, UnknownType, UnknownType],
            type: BasicType.Function,
            return: UndefinedType,
        },
        getPlankPathAsync: {
            name: 'getPlankPathAsync',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: getPromiseType(ParamModelLitePlankPathTypes),
        },
        getFloorplanRelation: {
            name: 'getFloorplanRelation',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Object,
                properties: {
                    openingIds: { type: BasicType.Array, value: StringType } as ArrayType,
                    hostIds: { type: BasicType.Array, value: StringType } as ArrayType,
                    reverse: BooleanType,
                },
            },
        },
        setFloorplanRelation: {
            name: 'setFloorplanRelation',
            varying: false,
            keepArgsHandle: false,
            args: [
                {
                    type: BasicType.Object,
                    properties: {
                        openingIds: { type: BasicType.Array, value: StringType } as ArrayType,
                        reverse: BooleanType,
                    },
                },
            ],
            type: BasicType.Function,
            return: UndefinedType,
        },
    },
};

ParamModelLiteBaseType.properties.root = ParamModelLiteBaseType;
(ParamModelLiteBaseType.properties.clone as FunctionType).return = ParamModelLiteBaseType;
(ParamModelLiteBaseType.properties.deserialize as FunctionType).return = ParamModelLiteBaseType;
(ParamModelLiteBaseType.properties.getParent as FunctionType).return = ParamModelLiteBaseType;
(ParamModelLiteBaseType.properties.getRoot as FunctionType).return = ParamModelLiteBaseType;
((ParamModelLiteBaseType.properties.getChild as FunctionType).return as ArrayType).value = ParamModelLiteBaseType;
((ParamModelLiteBaseType.properties.getAccessory as FunctionType).return as ArrayType).value = ParamModelLiteBaseType;
(ParamModelLiteBaseType.properties.children as ArrayType).value = ParamModelLiteBaseType as ObjectType;
(ParamModelLiteBaseType.properties.accessories as ArrayType).value = ParamModelLiteBaseType as ObjectType;
(ParamModelLiteParamTypes.properties.ref as Type<any>) = ParamModelLiteBaseType;
(bzParamModelLitePropertiesTypes.properties.ref as Type<any>) = ParamModelLiteBaseType;
(ParamModelLitePlankPathTypes.properties.ref as Type<any>) = ParamModelLiteBaseType;
