# 小程序接口声明库（模板）

## 目录结构

- `index.d.ts` - 为最终的整合导出模块（仅包含接口类型相关，不包含其他如 vm type 实现等）
- `api.d.ts` - 主要的类型声明文件，此文件的**所有导出**会作为该 package 提供的小程序接口声明
    > 当前内装小程序只提供了一个全局的 `IDP` 类型，因而 `api.d.ts` 中也应该仅导出一个 `IDP` 声明
- `impl/` - 包含接口所对应的 vm type 实现
    - `index.ts` - 提供封装的二方/三方 vm type 创建函数

以下为自动生成的文件（请不要手动修改）：

- `global-decl-public/` - 用于集成到 `@qunhe/idp-sdk` 中提供三方接口类型声明
- `global-decl-internal/` - 用于集成到 `@qunhe/idp-sdk-internal` 中提供二方接口类型声明
- `impl/`
    - `vmTypePublic.js`/`vmTypePublic.d.ts` - 根据 api 自动生成的用于三方的 vm type 创建函数
        > `api.d.ts` 声明中，标记了 JSDocTag `@internal` 的类型会被剔除
    - `vmTypeInternal.js`/`vmTypeInternal.d.ts` - 根据 api 自动生成的用于二方的 vm type 创建函数，包含了 `api.d.ts` 中的全部声明类型

### Package 导出结构

因特殊性，Package 导出不会统一合并到 index 中，而是分子路径导出：

- `index` - 导出常规的接口类型
- `global-decl-public`/`global-decl-internal` - 导出三方/二方接口的全局类型扩展声明
- `impl` - 导出 vm type 实现

> 注：小程序接口库实际上应该提供常规类型、全局扩展类型声明及 vm type 实现三类导出。为了避免互相影响，理论上需要导出三个 package（比如 math 库分别导出了 `@qunhe/math-apaas-api`接口声明、`@qunhe/math-apaas-impl` vm type 实现及 `@qunhe/idp-math/global-decl` 全局类型扩展声明）。
>
> 简化考虑此处将三类导出合并到一个 package 中管理。**因而三类导出之间需要严格避免互相引用**，比如 `index.d.ts` 及其引用的模块中只应存在类型定义，不应引用 vm type 实现相关的代码。
