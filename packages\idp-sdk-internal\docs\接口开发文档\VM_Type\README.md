`VmType`主要功能是用于提供静态转换分析，会在直接影响到接口及对象运行时的转换方式，因此需要严格**正确声明**，缺失属性或者不匹配会直接导致在实际环境无法调用。

> `VmType`的更多说明参考[DevDesign](https://cf.qunhequnhe.com/display/aPaaS/BindingSchema)
>
> `VmType`具体格式及字段含义参考[@qunhe/kls-abstraction::TypeInterface.ts](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/KLS/-/blob/master/packages/kls-abstraction/lib/TypeInterface.ts)
>
> 辅助VM_Type生成器参考[@qunhe/kls-runtime::TypeBuilder](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/KLS/-/blob/master/packages/kls-runtime/lib/BindingSchema/TypeBuilder.ts)


## 手动书写`VmType`并注入的使用方式参考

```ts
import { BasicType, FunctionType } from '@qunhe/kls-abstraction';
import { ObjectTypeBuilder } from '@qunhe/kls-runtime';

// api.example.ts
appService.miniappManager.registerApi(() => ({
    namespace: 'Design',
    value: {
        getDesignId
    },
    type: new ObjectTypeBuilder()
	    .addFunction('getDesignId', fn => fn.withStringReturn())
    	.build(),
}));
// 等价使用
appService.miniappManager.registerApi(() => ({
    namespace: 'Design',
    value: {
        getDesignId
    },
    type: {
        type: BasicType.Object,
        properties: {
            getDesignId: {
                name: 'getDesignId',
                varying: false,
                keepArgsHandle: false,
                type: BasicType.Function,
                args: [],
                return: {
                    type: BasicType.String
                }
            } as FunctionType
        }
    }
}));

// 对应实际api声明 api.d.ts
declare namespace IDP {
    namespace Design {
        /**
         * get current design id
         */
        function getDesignId(): string;
    }
```

## 自动生成绑定类型和使用方式

自动生成`VmType`参考：[VM Type 自动生成工具](./VM_Type_自动生成工具.md)

### 使用方式
1. 在[idp-types](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/idp-types)内创建一个子包，并完成接口导出
2. 通过临时分支发布子包，或者自己通过`npm run build-package`构建子包并使用
3. 在使用时引用新发布的包，并使用 `getVMBindingType`或者`createVMBindingTypeInternal` 创建完整的VM绑定信息
4. 可选：使用 `@qunhe/idp-vmbinding-utils` 从生成的binding信息中获取部分namespace的绑定信息
5. 如需使用外部库已有的绑定信息，如Math，可以参考 [idp-math](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/idp-types/-/tree/master/packages/idp-math)中`build-vm-type`脚本和`index.js`
6. 更多生成工具功能可以参考 [apaas-type-generator](https://gitlab.qunhequnhe.com/tool-frontend/kaf-group/kls/apaas-type-generator), 可以自由修改由模板生成的包内的构建脚本和文件，其中 `impl/vmTypeInternal.js`、`impl/vmTypeInternal.d.ts`、`impl/vmTypePublic.js`、`impl/vmTypePublic.d.ts`、`global-decl-internal`及`global-decl-public`，为自动编译生成，请不要修改


简易使用样例如下
```ts
import { getVMBindingType, getVMBindingTypeInternal } from '@qunhe/idp-my_api_pkg/impl';
import { getVMBindingTypeForNamespace } from '@qunhe/idp-vmbinding-utils';

// TODO: ...... plugin or other...

appService.miniappManager.registerApi(() => { // 有许多入参，参考app-core类型声明
    const vmType = getVMBindingType(/* TODO: 可选入参 */); // 或者 getVMBindingTypeInternal 按需选择

    return {
         namespace: 'MY_NAMESPACE1',
         value: MY_API_OBJECT1
         type: getVMBindingTypeForNamespace('IDP.MY_NAMESPACE1', vmType)
    }
});
appService.miniappManager.registerApi(() => { // 有许多入参，参考app-core类型声明
    const vmType = getVMBindingType(/* TODO: 可选入参 */); // 或者 getVMBindingTypeInternal 按需选择

    return {
         namespace: 'MY_NAMESPACE2',
         value: MY_API_OBJECT2
         type: getVMBindingTypeForNamespace('IDP.MY_NAMESPACE2', vmType)
    }
});

// TODO: ... more
```


## 手写类型时一些推荐做法
目前理论来说自动类型生成能满足大部分需求，如果自动生成无法满足需求会导致不得不手动实现很多类型，可以参考以下做法来解决一些比较难实现的类型。

1. 对于ts的union type 或者 继承结构，**理论上不推荐使用**，目前只能自动兼容 形如 ` MyType | null | undefined`，会针对 `null` 和 `undefined` 自动处理，其余目前没有很好的方式支持，目前自动生成工具不支持，如有需要可以通过手动定义类型，用以下方式实现
```ts
// d.ts
interface A {
    a: number;
}

interface B {
    b: number;
}

interface C extends A {
    b: string;
}

declare namespace IDP {
    namespace XX {
        function f1(): A | B;
        function f2(): A; // may return C
    }
}

export { IDP };

// registion
appService.miniappManager.registerApi(() => ({
    namespace: 'XX',
    value: {
        fn1,
        fn2,
    },
    type: new ObjectTypeBuilder()
        .addFunction('fn1', fn => fn.withObjectReturn(obj => obj.addNumber('a').addNumber('b'))) // 将其视作 { a: number, b: number } 导出，如果为undefined，引擎层会自动处理，确保声明正确即可
        .addFunction('fn2', fn => fn.withObjectReturn(obj => obj.addNumber('a').addString('b'))) // 将其视作 { a: number, b: string } 导出，如果为undefined，引擎层会自动处理，确保声明正确即可
        .build();
}));

// 如果类型过于冗杂，也可以采用`unknown`，但需要确保该对象可以被完整json序列化。
```
**注意事项**
> - 对于上述写法，需要保证类型没有冲突，即对于同一个字段没有类型冲突

2. `Promise`类型处理: `Promise`类型可以通过辅助函数进行定义，如下:
```ts
import { getPromiseType } from '@qunhe/miniapp-loader';

async function getString() {
    return await new Promise((res) => setTimeout(() => res('string'), 1000));
}

const getStringType = new FunctionTypeBuilder()
    .withObjectReturn(o => o.setType(getPromiseType({ type: BasicType.String })))
    .build();

manager.registerApi(() => ({
    namespace: 'Test',
    value: {
        getString
    },
    type: {
        type: BasicType.Object,
        properties: {
            getString: getStringType
        }
    }
}));
```
**注意事项**
> - 对于使用`@qunhe/kls-quickjs<1.1.12`的环境，对`Promise`返回还需要特殊处理，参考如下, **目前云图环境中不需要使用**
> ```ts
>// api.ts
>// 创建一个Promise类型的导出类型，处理返回值为Promise的接口
>import { wrapPromise, getPromiseType } from '@qunhe/miniapp-loader'
>
>// 创建一个内部值为特定类型的promise类型
>const promiseType = getPromiseType(YOUR_TYPE);
>
>function fn(): Promise<YOUR_RETURN> {
>    return wrapPromise(YOUR_WORK_RETURN_PROMISE);
>}
>
>// api-exported.d.ts
>// 返回值为Promise的接口声明
>import { HostPromise } from '@qunhe/miniapp-types'
>
>declare namespace IDP {
>    namespace YOUR_NAMESPACE {
>        function YOUR_FN(): HostPromise<YOUR_TYPE>
>    }
>}
>
>export { IDP };
> ```
> - 对于`Promise`类型，如果不想使用`getPromiseType`，也自行定义类型，核心在于定义好`then`函数第一个的入参

3. 类型循环引用/`return this`相关
```ts
// 暂时builder没有支持循环引用相关的类型支持，但是底层是接受的，不会影响到解析，因此需要一些特殊的方式来实现。

// 假设有如下声明
interface A {
    b: B;
    f(): A;
    f2(): this;
}

interface B {
    a: A;
    ff(): A;
}

// 可以通过如下方式生成
import { FunctionTypeBuilder, ObjectTypeBuilder } from '@qunhe/kls-runtime';
const typeA = new ObjectTypeBuilder().build();
typA.properties['f'] = new FunctionTypeBuilder().withObjectReturn(o => o.setType(typeA));
typA.properties['f2'] = new FunctionTypeBuilder().withObjectReturn(o => o.setType(typeA));

const typeB = new ObjectTypeBuilder().addObject('a', o => o.setType(typeA)).addFunction('ff', f => f.withObjectReturn(o => o.setType(typeA))).build();

typeA.properties['b'] = typeB;
```

4. Enum类型处理
```ts
// api.d.ts
export declare namespace IDP {
    namespace MY_NSP {
        enum MY_ENUM {
            value1: '1', // 这个enum的内部类型为string
            value2: '2'
        }

        function myFn(t: MY_ENUM): void;
    }
}

// type.ts
// 这个type的作用是将Enum本身进行挂载，如将其挂载与IDP.MY_NSP。
const MY_ENYM_TYPE = {
    type: BaiscType.Object;
    properties: {
        value1: { type: BasicType.String },
        value2: { type: BasicType.String }
    }
}

const myFn_TYPE = {
    type: BasicType.Function,
    args: [{ type: BasicType.String }], // 因为需要使用的是Enum某个具体的值，所以需要适用enum的内部类型。
    // ...... other type config...}
```
**注意事项**
> - 对于Enum需要明确你需要使用的是Enum的值还是Enum本身。
