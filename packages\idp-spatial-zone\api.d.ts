
import { KFace3d } from '@qunhe/math-apaas-api';
import { IDP as IDPCommon } from '@qunhe/idp-common';
import { ZoneStyle } from './types';

declare namespace IDP {
    namespace DB {

        namespace Types {
            /**
             * 区域
             * @internal
             */
            interface Zone {
                readonly id: IDPCommon.DB.Types.ElementId;

                /** 区域profile */
                readonly profile: KFace3d;
            }
        }

        namespace Methods {
            /**
             * 获取所有区域信息
             * @internal
             */
            function getAllZoneList(): IDP.DB.Types.Zone[];
        }
    }

    namespace Interaction {
        /**
         * 打开工具区域，数据加载，显示区域
         * @internal
         */
        function openZoneAsync(): Promise<void>;

        /**
         * 更新区域显示状态
         * @internal
         */
        function changeZoneStyle(zoneStyles: ZoneStyle[]): void;

        /**
         * 绘制区域
         * @internal
         */
        function startDrawZone(): void;
    }

    interface EventTypes {
        /**
         * 进入区域绘制环境
         * @internal
         */
        'IDP.Zone.Edit.Enter': void;

        /**
         * 退出区域绘制环境
         * @internal
         */
        'IDP.Zone.Edit.Exit': void;
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
