---
id: get-doc
title: "获取门窗文档"
description: "根据方案ID和楼层ID获取门窗文档信息。返回指定楼层下的所有门窗数据，包括简单门窗（单一门窗单元）和复杂门窗（组合型门窗）。支持不同数据视图以控制返回数据的详细程度，优化查询性能。"
sidebar_label: "获取门窗文档"
hide_title: true
hide_table_of_contents: true
api: {"tags":["门窗管理 API"],"description":"根据方案ID和楼层ID获取门窗文档信息。返回指定楼层下的所有门窗数据，包括简单门窗（单一门窗单元）和复杂门窗（组合型门窗）。支持不同数据视图以控制返回数据的详细程度，优化查询性能。","operationId":"getDoc","parameters":[{"name":"obsDesignId","in":"path","description":"设计方案的唯一标识符","required":true,"schema":{"type":"integer","format":"int64"},"example":"3FO3N47AJ4FR"},{"name":"levelId","in":"path","description":"楼层的唯一标识符，用于标识设计方案中的具体楼层","required":true,"schema":{"type":"string"},"example":"M7AAU7AKTLKBCAABAAAAAAI8"},{"name":"view","in":"query","description":"数据视图类型，控制返回数据的详细程度。BASIC返回基础信息，ADVANCED返回详细信息，FULL返回完整信息包括所有关联数据","required":false,"schema":{"type":"string","enum":["DOOR_WINDOW_DOCUMENT_VIEW","BASIC","ADVANCED","FULL"],"default":"BASIC"},"example":"BASIC"}],"responses":{"200":{"description":"成功获取门窗文档","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗文档的唯一标识符","example":"dw_doc_20241201_001"},"simpleDoors":{"type":"array","description":"简单门列表，包含该楼层下所有的简单门信息。简单门指单一门窗单元，如单开门、双开门、移门等。","example":[{"id":"simple_door_001","doorType":"SINGLE_DOOR","openSide":"LEFT"}],"items":{"required":["id"],"type":"object","description":"简单门数据模型，表示单一门窗单元的门类型，支持单开门、双开门、移门等常见门类型","example":[{"id":"simple_door_001","doorType":"SINGLE_DOOR","openSide":"LEFT"}],"allOf":[{"required":["id"],"type":"object","properties":{"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}}},"description":"简单门窗数据模型基类，定义与单个开口关联的标准门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiSimpleDoorWindow"},{"type":"object","properties":{"doorType":{"type":"string","description":"简单门类型枚举，定义简单门的具体类型和结构特征","example":"SINGLE_DOOR","enum":["SIMPLE_DOOR_TYPE_UNSPECIFIED","SINGLE_DOOR","DOUBLE_DOOR","SLIDING_DOOR","COMPOSITE_DOOR","PASS"]}}}],"title":"RestApiSimpleDoor"}},"complexDoors":{"type":"array","description":"复杂门列表，包含该楼层下所有的复杂门信息。复杂门指组合型门结构，支持多开口组合，如铝合金自由绘制门等。","example":[{"id":"complex_door_001","doorType":"ALUMINUM_DOOR"}],"items":{"required":["id"],"type":"object","description":"复杂门数据模型，表示复杂组合型门结构，支持多开口组合门和自由绘制门等高级门类型","example":[{"id":"complex_door_001","doorType":"ALUMINUM_DOOR"}],"allOf":[{"required":["id"],"type":"object","properties":{"openingRelations":{"type":"array","description":"开口关联关系列表，定义该复杂门窗与多个墙体开口的关联关系。每个关联关系包含主体ID列表和开口ID。","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"items":{"type":"object","properties":{"hostIds":{"type":"array","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":["wall_001","wall_002"],"items":{"type":"string","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":"[\"wall_001\",\"wall_002\"]"}},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息。","example":"opening_corner_001"}},"description":"复杂门窗开口关联关系，定义复杂门窗与多个墙体开口之间的关联关系","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"title":"RestApiComplexOpeningRelation"}}},"description":"复杂门窗数据模型基类，定义支持多开口关联的复杂门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiComplexDoorWindow"},{"type":"object","properties":{"doorType":{"type":"string","description":"复杂门类型枚举，定义复杂门的具体类型和结构特征","example":"ALUMINUM_DOOR","enum":["COMPLEX_DOOR_TYPE_UNSPECIFIED","ALUMINUM_DOOR"]}}}],"title":"RestApiComplexDoor"}},"simpleWindows":{"type":"array","description":"简单窗列表，包含该楼层下所有的简单窗信息。简单窗指单一窗户单元，如普通窗、飘窗、落地窗等。","example":[{"id":"simple_window_001","windowType":"WINDOW"}],"items":{"required":["id"],"type":"object","properties":{"version":{"maxLength":32,"type":"string","description":"门窗模型的版本号，用于版本控制和兼容性管理","example":"1.0.0"},"windowType":{"type":"string","description":"简单窗类型枚举，定义简单窗的具体类型和结构特征","example":"SLIDING_WINDOW","enum":["SIMPLE_WINDOW_TYPE_UNSPECIFIED","SLIDING_WINDOW","CASEMENT_WINDOW","FIXED_WINDOW","BAY_WINDOW"]}},"description":"简单窗数据模型，表示单一窗单元结构，支持推拉窗、平开窗等标准窗类型","allOf":[{"required":["id"],"type":"object","properties":{"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}}},"description":"简单门窗数据模型基类，定义与单个开口关联的标准门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiSimpleDoorWindow"}],"title":"RestApiSimpleWindow"}},"complexWindows":{"type":"array","description":"复杂窗列表，包含该楼层下所有的复杂窗信息。复杂窗指组合型窗结构，如L型窗、U型窗、转角窗等。","example":[{"id":"complex_window_001","windowType":"L_WINDOW"}],"items":{"required":["id"],"type":"object","description":"复杂窗数据模型，表示复杂组合型窗结构，支持L型窗、U型窗、转角窗等高级窗类型","example":[{"id":"complex_window_001","windowType":"L_WINDOW"}],"allOf":[{"required":["id"],"type":"object","properties":{"openingRelations":{"type":"array","description":"开口关联关系列表，定义该复杂门窗与多个墙体开口的关联关系。每个关联关系包含主体ID列表和开口ID。","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"items":{"type":"object","properties":{"hostIds":{"type":"array","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":["wall_001","wall_002"],"items":{"type":"string","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":"[\"wall_001\",\"wall_002\"]"}},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息。","example":"opening_corner_001"}},"description":"复杂门窗开口关联关系，定义复杂门窗与多个墙体开口之间的关联关系","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"title":"RestApiComplexOpeningRelation"}}},"description":"复杂门窗数据模型基类，定义支持多开口关联的复杂门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiComplexDoorWindow"},{"type":"object","properties":{"windowType":{"type":"string","description":"复杂窗类型枚举，定义复杂窗的具体类型和几何结构特征","example":"L_WINDOW","enum":["COMPLEX_WINDOW_TYPE_UNSPECIFIED","L_WINDOW","L_BAY_WINDOW","U_WINDOW","U_BAY_WINDOW","CORNER_WINDOW","ALUMINUM_WINDOW"]}}}],"title":"RestApiComplexWindow"}}},"description":"门窗文档数据模型，包含指定楼层下的所有门窗信息","title":"RestApiDoorWindowDocument"},"examples":{"门窗文档响应示例":{"description":"门窗文档响应示例","value":{"id":"door_window_doc_001","simpleDoors":[{"id":"simple_door_001","productId":"prod_door_001","position":{"x":1,"y":0,"z":2},"size":{"x":0.8,"y":0.1,"z":2.1},"doorType":"SINGLE_DOOR","openSide":"LEFT","moduleType":"MODEL"}],"simpleWindows":[{"id":"simple_window_001","productId":"prod_window_001","position":{"x":3,"y":0,"z":1.2},"size":{"x":1.5,"y":0.2,"z":1.2},"windowType":"WINDOW","moduleType":"MODEL"}],"complexDoors":[],"complexWindows":[]}}}}}},"400":{"description":"请求参数错误，如方案ID格式不正确、楼层ID无效等","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"资源未找到，指定的设计方案或楼层不存在，或用户无权限访问","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误，数据库连接失败或系统异常","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}},"method":"get","path":"/dw/openapi/v1/rest/designs/{obsDesignId}/levels/{levelId}/document","servers":[{"url":"http://localhost:8080","description":"本地开发环境"},{"url":"https://api-dev.qunhe.com","description":"开发测试环境"},{"url":"https://api.qunhe.com","description":"生产环境"}],"info":{"title":"门窗设计管理API","description":"门窗设计服务REST API\n\n为群核旗下设计工具提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。提供高效的批量操作和异步处理机制，确保大规模门窗数据处理的性能和稳定性。\n\n**核心功能：**\n- 门窗文档查询：获取指定楼层的完整门窗信息\n- 批量更新操作：支持多种门窗操作类型的批量处理\n- 多种门窗类型：简单门、简单窗、复杂门窗组合\n- 多视图支持：BASIC、ADVANCED、FULL等不同详细程度的数据视图\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 操作类型丰富：replace（替换）、attach（附加）、detach（分离）、update（更新）\n\n**门窗类型支持：**\n- 简单门窗：单一门窗单元，如普通门、普通窗\n- 复杂门窗：组合型门窗，如L型窗、U型窗、转角门等\n- 门类型：单开门、双开门、推拉门、折叠门等\n- 窗类型：平开窗、推拉窗、百叶窗、落地窗等\n\n**业务应用场景：**\n- 建筑设计软件的门窗配置\n- 门窗产品的三维建模和展示\n- 建筑开口与门窗的关联管理\n- 门窗规格和参数的批量调整\n- 复杂门窗组合的快速创建\n- 门窗数据的批量导入和同步\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 批量限制：单次批量操作最多20个门窗\n- 异步支持：长时间批量操作的异步处理机制","contact":{"name":"群核科技开发团队","url":"https://wiki.manycore.com/doorwindow-design","email":"<EMAIL>"},"license":{"name":"群核科技专有许可证","url":"https://manycore.com/license"},"version":"v1.0.0"},"postman":{"name":"获取门窗文档","description":{"content":"根据方案ID和楼层ID获取门窗文档信息。返回指定楼层下的所有门窗数据，包括简单门窗（单一门窗单元）和复杂门窗（组合型门窗）。支持不同数据视图以控制返回数据的详细程度，优化查询性能。","type":"text/plain"},"url":{"path":["dw","openapi","v1","rest","designs",":obsDesignId","levels",":levelId","document"],"host":["{{baseUrl}}"],"query":[{"disabled":false,"description":{"content":"数据视图类型，控制返回数据的详细程度。BASIC返回基础信息，ADVANCED返回详细信息，FULL返回完整信息包括所有关联数据","type":"text/plain"},"key":"view","value":""}],"variable":[{"disabled":false,"description":{"content":"(Required) 设计方案的唯一标识符","type":"text/plain"},"type":"any","value":"","key":"obsDesignId"},{"disabled":false,"description":{"content":"(Required) 楼层的唯一标识符，用于标识设计方案中的具体楼层","type":"text/plain"},"type":"any","value":"","key":"levelId"}]},"header":[{"key":"Accept","value":"application/json"}],"method":"GET"}}
sidebar_class_name: "get api-method"
info_path: docs/api/doorwindow/门窗设计管理api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"获取门窗文档"}
>
</Heading>

<MethodEndpoint
  method={"get"}
  path={"/dw/openapi/v1/rest/designs/{obsDesignId}/levels/{levelId}/document"}
  context={"endpoint"}
>
  
</MethodEndpoint>



根据方案ID和楼层ID获取门窗文档信息。返回指定楼层下的所有门窗数据，包括简单门窗（单一门窗单元）和复杂门窗（组合型门窗）。支持不同数据视图以控制返回数据的详细程度，优化查询性能。

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"obsDesignId","in":"path","description":"设计方案的唯一标识符","required":true,"schema":{"type":"integer","format":"int64"},"example":"3FO3N47AJ4FR"},{"name":"levelId","in":"path","description":"楼层的唯一标识符，用于标识设计方案中的具体楼层","required":true,"schema":{"type":"string"},"example":"M7AAU7AKTLKBCAABAAAAAAI8"},{"name":"view","in":"query","description":"数据视图类型，控制返回数据的详细程度。BASIC返回基础信息，ADVANCED返回详细信息，FULL返回完整信息包括所有关联数据","required":false,"schema":{"type":"string","enum":["DOOR_WINDOW_DOCUMENT_VIEW","BASIC","ADVANCED","FULL"],"default":"BASIC"},"example":"BASIC"}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={undefined}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"成功获取门窗文档","content":{"application/json":{"schema":{"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗文档的唯一标识符","example":"dw_doc_20241201_001"},"simpleDoors":{"type":"array","description":"简单门列表，包含该楼层下所有的简单门信息。简单门指单一门窗单元，如单开门、双开门、移门等。","example":[{"id":"simple_door_001","doorType":"SINGLE_DOOR","openSide":"LEFT"}],"items":{"required":["id"],"type":"object","description":"简单门数据模型，表示单一门窗单元的门类型，支持单开门、双开门、移门等常见门类型","example":[{"id":"simple_door_001","doorType":"SINGLE_DOOR","openSide":"LEFT"}],"allOf":[{"required":["id"],"type":"object","properties":{"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}}},"description":"简单门窗数据模型基类，定义与单个开口关联的标准门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiSimpleDoorWindow"},{"type":"object","properties":{"doorType":{"type":"string","description":"简单门类型枚举，定义简单门的具体类型和结构特征","example":"SINGLE_DOOR","enum":["SIMPLE_DOOR_TYPE_UNSPECIFIED","SINGLE_DOOR","DOUBLE_DOOR","SLIDING_DOOR","COMPOSITE_DOOR","PASS"]}}}],"title":"RestApiSimpleDoor"}},"complexDoors":{"type":"array","description":"复杂门列表，包含该楼层下所有的复杂门信息。复杂门指组合型门结构，支持多开口组合，如铝合金自由绘制门等。","example":[{"id":"complex_door_001","doorType":"ALUMINUM_DOOR"}],"items":{"required":["id"],"type":"object","description":"复杂门数据模型，表示复杂组合型门结构，支持多开口组合门和自由绘制门等高级门类型","example":[{"id":"complex_door_001","doorType":"ALUMINUM_DOOR"}],"allOf":[{"required":["id"],"type":"object","properties":{"openingRelations":{"type":"array","description":"开口关联关系列表，定义该复杂门窗与多个墙体开口的关联关系。每个关联关系包含主体ID列表和开口ID。","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"items":{"type":"object","properties":{"hostIds":{"type":"array","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":["wall_001","wall_002"],"items":{"type":"string","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":"[\"wall_001\",\"wall_002\"]"}},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息。","example":"opening_corner_001"}},"description":"复杂门窗开口关联关系，定义复杂门窗与多个墙体开口之间的关联关系","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"title":"RestApiComplexOpeningRelation"}}},"description":"复杂门窗数据模型基类，定义支持多开口关联的复杂门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiComplexDoorWindow"},{"type":"object","properties":{"doorType":{"type":"string","description":"复杂门类型枚举，定义复杂门的具体类型和结构特征","example":"ALUMINUM_DOOR","enum":["COMPLEX_DOOR_TYPE_UNSPECIFIED","ALUMINUM_DOOR"]}}}],"title":"RestApiComplexDoor"}},"simpleWindows":{"type":"array","description":"简单窗列表，包含该楼层下所有的简单窗信息。简单窗指单一窗户单元，如普通窗、飘窗、落地窗等。","example":[{"id":"simple_window_001","windowType":"WINDOW"}],"items":{"required":["id"],"type":"object","properties":{"version":{"maxLength":32,"type":"string","description":"门窗模型的版本号，用于版本控制和兼容性管理","example":"1.0.0"},"windowType":{"type":"string","description":"简单窗类型枚举，定义简单窗的具体类型和结构特征","example":"SLIDING_WINDOW","enum":["SIMPLE_WINDOW_TYPE_UNSPECIFIED","SLIDING_WINDOW","CASEMENT_WINDOW","FIXED_WINDOW","BAY_WINDOW"]}},"description":"简单窗数据模型，表示单一窗单元结构，支持推拉窗、平开窗等标准窗类型","allOf":[{"required":["id"],"type":"object","properties":{"openingRelation":{"required":["hostId","openingId"],"type":"object","properties":{"hostId":{"maxLength":64,"type":"string","description":"关联的主体ID，通常是墙体ID。简单门窗只能关联单个墙体，适用于标准的门窗安装场景。","example":"wall_001"},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息，包括位置、尺寸等。","example":"opening_001"}},"description":"简单门窗开口关联关系，定义简单门窗与单个墙体开口之间的一对一关联关系","example":{"hostId":"wall_001","openingId":"opening_001"},"title":"RestApiOpeningRelation"},"openDirection":{"type":"object","title":"Vector3d","description":"三维向量。","properties":{"x":{"type":"number","format":"double","description":"三维向量的x坐标。"},"y":{"type":"number","format":"double","description":"三维向量的y坐标。"},"z":{"type":"number","format":"double","description":"三维向量的z坐标。"}}}},"description":"简单门窗数据模型基类，定义与单个开口关联的标准门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiSimpleDoorWindow"}],"title":"RestApiSimpleWindow"}},"complexWindows":{"type":"array","description":"复杂窗列表，包含该楼层下所有的复杂窗信息。复杂窗指组合型窗结构，如L型窗、U型窗、转角窗等。","example":[{"id":"complex_window_001","windowType":"L_WINDOW"}],"items":{"required":["id"],"type":"object","description":"复杂窗数据模型，表示复杂组合型窗结构，支持L型窗、U型窗、转角窗等高级窗类型","example":[{"id":"complex_window_001","windowType":"L_WINDOW"}],"allOf":[{"required":["id"],"type":"object","properties":{"openingRelations":{"type":"array","description":"开口关联关系列表，定义该复杂门窗与多个墙体开口的关联关系。每个关联关系包含主体ID列表和开口ID。","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"items":{"type":"object","properties":{"hostIds":{"type":"array","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":["wall_001","wall_002"],"items":{"type":"string","description":"关联的主体ID列表，通常是墙体ID列表。复杂门窗可以关联多个墙体，适用于转角窗、L型窗等跨墙体的门窗。","example":"[\"wall_001\",\"wall_002\"]"}},"openingId":{"maxLength":64,"type":"string","description":"开口ID，标识该门窗关联的具体开口位置。开口定义了门窗在墙体上的洞口信息。","example":"opening_corner_001"}},"description":"复杂门窗开口关联关系，定义复杂门窗与多个墙体开口之间的关联关系","example":[{"hostIds":["wall_001","wall_002"],"openingId":"opening_001"},{"hostIds":["wall_002"],"openingId":"opening_002"}],"title":"RestApiComplexOpeningRelation"}}},"description":"复杂门窗数据模型基类，定义支持多开口关联的复杂门窗结构","allOf":[{"required":["id"],"type":"object","properties":{"id":{"maxLength":64,"type":"string","description":"门窗的唯一标识符","example":"dw_001"},"productId":{"maxLength":64,"type":"string","description":"关联的产品ID，用于获取门窗的详细产品信息","example":"prod_door_single_001"},"position":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"size":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}},"moduleType":{"type":"string","description":"门窗模块类型枚举，用于区分门窗的来源和特性","example":"MODEL","enum":["MODULE_TYPE_UNSPECIFIED","MODEL","CUSTOM","CUSTOM_COPY"]}},"description":"门窗数据模型基类，定义所有门窗类型的公共属性","title":"RestApiCommonDoorWindow"}],"title":"RestApiComplexDoorWindow"},{"type":"object","properties":{"windowType":{"type":"string","description":"复杂窗类型枚举，定义复杂窗的具体类型和几何结构特征","example":"L_WINDOW","enum":["COMPLEX_WINDOW_TYPE_UNSPECIFIED","L_WINDOW","L_BAY_WINDOW","U_WINDOW","U_BAY_WINDOW","CORNER_WINDOW","ALUMINUM_WINDOW"]}}}],"title":"RestApiComplexWindow"}}},"description":"门窗文档数据模型，包含指定楼层下的所有门窗信息","title":"RestApiDoorWindowDocument"},"examples":{"门窗文档响应示例":{"description":"门窗文档响应示例","value":{"id":"door_window_doc_001","simpleDoors":[{"id":"simple_door_001","productId":"prod_door_001","position":{"x":1,"y":0,"z":2},"size":{"x":0.8,"y":0.1,"z":2.1},"doorType":"SINGLE_DOOR","openSide":"LEFT","moduleType":"MODEL"}],"simpleWindows":[{"id":"simple_window_001","productId":"prod_window_001","position":{"x":3,"y":0,"z":1.2},"size":{"x":1.5,"y":0.2,"z":1.2},"windowType":"WINDOW","moduleType":"MODEL"}],"complexDoors":[],"complexWindows":[]}}}}}},"400":{"description":"请求参数错误，如方案ID格式不正确、楼层ID无效等","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"资源未找到，指定的设计方案或楼层不存在，或用户无权限访问","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"服务器内部错误，数据库连接失败或系统异常","content":{"application/json":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}}}
>
  
</StatusCodes>


      