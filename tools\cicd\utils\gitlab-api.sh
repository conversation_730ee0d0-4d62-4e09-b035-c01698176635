#!/bin/bash

# GitLab API 工具脚本
# 提供 GitLab API 操作的通用函数
# 重构自原 add-mr-comment.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common.sh"

# GitLab API 配置
GITLAB_API_BASE="${GITLAB_API_BASE:-https://gitlab.com/api/v4}"
GITLAB_PROJECT_ID="${CI_PROJECT_ID:-}"

# 检查 GitLab API 必需的环境变量
check_gitlab_env() {
    log_info "检查 GitLab API 环境变量..."
    
    check_required_env "GITLAB_TOKEN" || return 1
    check_required_env "CI_PROJECT_ID" || return 1
    
    log_success "GitLab API 环境变量检查通过"
    return 0
}

# 发送 GitLab API 请求
gitlab_api_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="${4:-API 请求}"
    
    local url="$GITLAB_API_BASE$endpoint"
    local curl_args=("-X" "$method" "-H" "PRIVATE-TOKEN: $GITLAB_TOKEN" "-H" "Content-Type: application/json")
    
    if [ -n "$data" ]; then
        curl_args+=("-d" "$data")
    fi
    
    log_debug "发送 $method 请求到: $url"
    
    local response
    if response=$(curl -s "${curl_args[@]}" "$url"); then
        log_debug "$description 响应: $response"
        echo "$response"
        return 0
    else
        log_error "$description 失败"
        return 1
    fi
}

# 获取 MR 信息
get_mr_info() {
    local mr_iid="$1"
    
    if [ -z "$mr_iid" ]; then
        log_error "缺少 MR IID"
        return 1
    fi
    
    gitlab_api_request "GET" "/projects/$GITLAB_PROJECT_ID/merge_requests/$mr_iid" "" "获取 MR 信息"
}

# 添加 MR 评论
add_mr_comment() {
    local mr_iid="$1"
    local comment_body="$2"
    
    if [ -z "$mr_iid" ]; then
        log_error "缺少 MR IID"
        return 1
    fi
    
    if [ -z "$comment_body" ]; then
        log_error "缺少评论内容"
        return 1
    fi
    
    # 转义 JSON 特殊字符
    local escaped_body=$(echo "$comment_body" | sed 's/\\/\\\\/g' | sed 's/"/\\"/g' | sed ':a;N;$!ba;s/\n/\\n/g')
    local json_data="{\"body\":\"$escaped_body\"}"
    
    log_info "添加 MR 评论..."
    gitlab_api_request "POST" "/projects/$GITLAB_PROJECT_ID/merge_requests/$mr_iid/notes" "$json_data" "添加 MR 评论"
}

# 生成预览版本号
generate_preview_version() {
    local version=""
    local base_path=""
    
    if [[ -n "$CI_COMMIT_TAG" ]]; then
        # 如果是 tag 构建，使用 tag 作为版本号
        version="$CI_COMMIT_TAG"
        base_path="manycoreapi"
    elif [[ "$CI_COMMIT_BRANCH" == "master" ]] || [[ "$CI_COMMIT_BRANCH" == "main" ]]; then
        # 主分支使用时间戳版本
        version="1.0.0-$(date +%Y%m%d%H%M%S)"
        base_path="manycoreapi-staging"
    else
        # 其他分支使用分支名和 commit 信息
        local branch_name=$(echo "$CI_COMMIT_BRANCH" | sed 's/[^a-zA-Z0-9.-]/-/g')
        local commit_short="$CI_COMMIT_SHORT_SHA"
        version="0.0.1-${branch_name}-${commit_short}"
        base_path="manycoreapi-demo"
    fi
    
    echo "$version|$base_path"
}

# 生成 Manual 网站预览链接
generate_manual_preview_url() {
    local version_info=$(generate_preview_version)
    local version=$(echo "$version_info" | cut -d'|' -f1)
    local base_path=$(echo "$version_info" | cut -d'|' -f2)
    
    echo "https://manual.qunhequnhe.com/${base_path}/${version}/"
}

# 格式化文件列表
format_file_list() {
    local files="$1"
    local file_list=""
    
    # 清理和格式化文件列表
    local clean_files=$(echo "$files" | sed 's/"//g' | tr ' ' '\n' | sed '/^$/d' | sort -u)
    
    while IFS= read -r file; do
        if [[ -n "$file" ]]; then
            file_list="${file_list}- \`${file}\`"$'\n'
        fi
    done <<< "$clean_files"
    
    echo "$file_list"
}

# 生成 API 文档链接
generate_api_doc_links() {
    local changed_files="$1"
    local manual_base_url="$2"
    local doc_links=""
    
    log_info "生成 API 文档链接..."
    
    # 分析变更的文件，生成具体的文档链接
    while IFS= read -r file; do
        if [[ -n "$file" && "$file" == specifications/services/*/openapi.yaml ]]; then
            # 提取服务名
            local service=$(echo "$file" | sed 's|specifications/services/\([^/]*\)/openapi.yaml|\1|')
            
            if is_valid_service "$service"; then
                local doc_url="${manual_base_url}api/${service}/"
                doc_links="${doc_links}📖 [$service API 文档]($doc_url)"$'\n'
                log_debug "生成文档链接: $service -> $doc_url"
            fi
        fi
    done <<< "$(echo "$changed_files" | sed 's/"//g' | tr ' ' '\n' | sed '/^$/d')"
    
    echo "$doc_links"
}

# 生成完整的 CI 状态评论
generate_ci_status_comment() {
    local has_api_changes="${1:-false}"
    local changed_files="${2:-}"
    local build_type="${3:-unknown}"
    local api_changes="${4:-}"
    
    log_info "生成 CI 状态评论..."
    
    local manual_url=$(generate_manual_preview_url)
    local comment_body=""
    
    # 评论头部
    comment_body="## 🚀 CI/CD 流程执行完成\n\n"
    comment_body="${comment_body}✅ **状态**: 成功\n"
    comment_body="${comment_body}🕒 **时间**: $(date '+%Y-%m-%d %H:%M:%S')\n"
    comment_body="${comment_body}🔗 **Manual 预览**: [$manual_url]($manual_url)\n\n"
    
    # API 变更信息
    if [[ "$has_api_changes" == "true" ]]; then
        comment_body="${comment_body}## 📋 API 变更检测\n\n"
        comment_body="${comment_body}🔄 **检测到 API 变更**\n"
        comment_body="${comment_body}📦 **构建类型**: $build_type\n\n"
        
        if [[ -n "$changed_files" ]]; then
            comment_body="${comment_body}### 变更的文件:\n"
            comment_body="${comment_body}$(format_file_list "$changed_files")\n"
            
            # 生成文档链接
            local doc_links=$(generate_api_doc_links "$changed_files" "$manual_url")
            if [[ -n "$doc_links" ]]; then
                comment_body="${comment_body}### 📖 相关文档:\n$doc_links\n"
            fi
        fi
        
        if [[ -n "$api_changes" ]]; then
            comment_body="${comment_body}### 🔍 变更详情:\n\`\`\`\n$api_changes\n\`\`\`\n\n"
        fi
    else
        comment_body="${comment_body}## 📋 API 变更检测\n\n"
        comment_body="${comment_body}✅ **无 API 变更**\n\n"
    fi
    
    # 评论尾部
    comment_body="${comment_body}---\n"
    comment_body="${comment_body}💡 **提示**: 点击上方链接查看完整的 API 文档和预览\n"
    comment_body="${comment_body}🤖 *此评论由 CI/CD 自动生成*"
    
    echo "$comment_body"
}

# 主函数：添加 CI 状态评论
add_ci_status_comment() {
    local mr_iid="$1"
    local has_api_changes="${2:-false}"
    local changed_files="${3:-}"
    local build_type="${4:-unknown}"
    local api_changes="${5:-}"
    
    log_info "开始添加 CI 状态评论..."
    
    # 检查环境变量
    if ! check_gitlab_env; then
        return 1
    fi
    
    # 检查 MR IID
    if [ -z "$mr_iid" ]; then
        log_error "缺少 MR IID"
        return 1
    fi
    
    # 生成评论内容
    local comment_body=$(generate_ci_status_comment "$has_api_changes" "$changed_files" "$build_type" "$api_changes")
    
    # 添加评论
    if add_mr_comment "$mr_iid" "$comment_body"; then
        log_success "CI 状态评论添加成功"
        return 0
    else
        log_error "CI 状态评论添加失败"
        return 1
    fi
}

# 从环境变量添加 CI 状态评论
add_ci_status_comment_from_env() {
    local mr_iid="${CI_MERGE_REQUEST_IID:-}"
    local has_api_changes="${HAS_API_CHANGES:-false}"
    local changed_files="${CHANGED_API_FILES:-}"
    local build_type="${BUILD_TYPE:-unknown}"
    local api_changes="${API_CHANGES:-}"
    
    log_info "从环境变量添加 CI 状态评论..."
    log_debug "MR IID: $mr_iid"
    log_debug "API 变更: $has_api_changes"
    log_debug "变更文件: $changed_files"
    log_debug "构建类型: $build_type"
    
    add_ci_status_comment "$mr_iid" "$has_api_changes" "$changed_files" "$build_type" "$api_changes"
}

# 如果直接执行此脚本，则调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    init_script "GitLab API 工具" "添加 CI 状态评论"
    
    if add_ci_status_comment_from_env; then
        finish_script "GitLab API 工具" "true"
    else
        finish_script "GitLab API 工具" "false"
        exit 1
    fi
fi

log_info "GitLab API 工具已加载 ✅"
