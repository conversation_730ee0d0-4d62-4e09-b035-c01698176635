#!/bin/bash

# Docusaurus 文档环境智能设置脚本
# 检查并补全缺失的文件，不会删除现有配置

set -e

echo "🚀 检查并完善 Docusaurus 文档环境..."

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ 未安装 Node.js，请先安装 Node.js 18.0+"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低 (当前: $NODE_VERSION)，需要 18.0+"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $(node -v)"

# 检查 docs-site 目录
if [ ! -d "docs-site" ]; then
    echo "📁 docs-site 目录不存在，创建新的文档环境..."
    mkdir -p docs-site
    FRESH_INSTALL=true
else
    echo "📁 docs-site 目录已存在，检查现有配置..."
    FRESH_INSTALL=false
fi

# 创建必要的目录结构
echo "📁 确保目录结构完整..."
mkdir -p docs-site/{docs,blog,src/{components,css,pages},static/img}
mkdir -p docs-site/docs/{getting-started,guides,examples,api}

# 切换到 docs-site 目录
cd docs-site

# 检查并处理 package.json
if [ ! -f "package.json" ]; then
    echo "📄 创建 package.json..."
    cat > package.json << 'EOF'
{
  "name": "backend-api-docs",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "docusaurus": "docusaurus",
    "start": "docusaurus start",
    "build": "docusaurus build",
    "swizzle": "docusaurus swizzle",
    "deploy": "docusaurus deploy",
    "clear": "docusaurus clear",
    "serve": "docusaurus serve",
    "write-translations": "docusaurus write-translations",
    "write-heading-ids": "docusaurus write-heading-ids",
    "typecheck": "tsc",
    "gen-api-docs": "docusaurus gen-api-docs",
    "clean-api-docs": "docusaurus clean-api-docs"
  },
  "dependencies": {
    "@docusaurus/core": "^3.8.0",
    "@docusaurus/preset-classic": "^3.8.0",
    "@docusaurus/module-type-aliases": "^3.8.0",
    "@docusaurus/tsconfig": "^3.8.0",
    "@docusaurus/types": "^3.8.0",
    "clsx": "^2.0.0",
    "prism-react-renderer": "^2.3.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "docusaurus-plugin-openapi-docs": "^4.0.0",
    "docusaurus-theme-openapi-docs": "^4.0.0"
  },
  "devDependencies": {
    "@docusaurus/tsconfig": "^3.8.0",
    "typescript": "~5.5.2"
  },
  "browserslist": {
    "production": [
      ">0.5%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 3 chrome version",
      "last 3 firefox version",
      "last 5 safari version"
    ]
  },
  "engines": {
    "node": ">=18.0"
  }
}
EOF
else
    echo "✅ package.json 已存在，跳过创建"
    
    # 检查是否有必要的依赖
    if ! grep -q "docusaurus-plugin-openapi-docs" package.json; then
        echo "⚠️ 检测到 package.json 缺少 OpenAPI 插件依赖"
        echo "💡 建议手动添加以下依赖："
        echo "   docusaurus-plugin-openapi-docs: ^4.0.0"
        echo "   docusaurus-theme-openapi-docs: ^4.0.0"
    fi
fi

# 检查并处理 docusaurus.config.ts
if [ ! -f "docusaurus.config.ts" ]; then
    echo "⚙️ 创建 docusaurus.config.ts..."
    cat > docusaurus.config.ts << 'EOF'
import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';
import type * as OpenApiPlugin from "docusaurus-plugin-openapi-docs";

const config: Config = {
  title: '酷家乐 API 文档中心',
  tagline: '全栈家居云设计平台 API 参考',
  favicon: 'img/favicon.ico',

  // 生产部署配置
  url: 'https://your-company.gitlab.io',
  baseUrl: '/backend-api-sdk/',

  // GitLab Pages 配置
  organizationName: 'your-organization',
  projectName: 'backend-api-sdk',

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // 国际化配置
  i18n: {
    defaultLocale: 'zh-Hans',
    locales: ['zh-Hans', 'en'],
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: './sidebars.ts',
          docItemComponent: "@theme/ApiItem", // 支持 OpenAPI 主题
          editUrl: 'https://gitlab.com/your-org/backend-api-sdk/-/tree/master/docs-site/',
          showLastUpdateAuthor: true,
          showLastUpdateTime: true,
        },
        blog: {
          showReadingTime: true,
          editUrl: 'https://gitlab.com/your-org/backend-api-sdk/-/tree/master/docs-site/',
        },
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  plugins: [
    [
      'docusaurus-plugin-openapi-docs',
      {
        id: "api",
        docsPluginId: "classic",
        config: {
          // DIY模型数据仓库服务
          diymodeldw: {
            specPath: "../openapi/diymodeldw-service/restapi.yaml",
            outputDir: "docs/api/diymodeldw",
            sidebarOptions: {
              groupPathsBy: "tag",
              categoryLinkSource: "tag",
            },
            downloadUrl: "/openapi/diymodeldw-service/restapi.yaml",
            hideSendButton: false,
            showSchemas: true,
          },
          // 家具设计服务
          furnitureDesign: {
            specPath: "../openapi/furniture-design-service/restapi.yaml",
            outputDir: "docs/api/furniture-design",
            sidebarOptions: {
              groupPathsBy: "tag",
              categoryLinkSource: "tag",
            },
            downloadUrl: "/openapi/furniture-design-service/restapi.yaml",
            hideSendButton: false,
            showSchemas: true,
          },
          // 设计信息服务
          designInfo: {
            specPath: "../openapi/designinfoservice/restapi.yaml",
            outputDir: "docs/api/design-info",
            sidebarOptions: {
              groupPathsBy: "tag",
              categoryLinkSource: "tag",
            },
            downloadUrl: "/openapi/designinfoservice/restapi.yaml",
            hideSendButton: false,
            showSchemas: true,
          },
        } satisfies OpenApiPlugin.Options,
      },
    ],
  ],

  themes: ["docusaurus-theme-openapi-docs"],

  themeConfig: {
    image: 'img/docusaurus-social-card.jpg',
    navbar: {
      title: '酷家乐 API',
      logo: {
        alt: '酷家乐 Logo',
        src: 'img/logo.svg',
      },
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: '开发指南',
        },
        {
          type: 'dropdown',
          label: 'API 参考',
          position: 'left',
          items: [
            {
              label: 'DIY模型数据仓库',
              to: '/docs/api/diymodeldw',
            },
            {
              label: '家具设计服务',
              to: '/docs/api/furniture-design',
            },
            {
              label: '设计信息服务',
              to: '/docs/api/design-info',
            },
          ],
        },
        {to: '/blog', label: '更新日志', position: 'left'},
        {
          type: 'localeDropdown',
          position: 'right',
        },
        {
          href: 'https://gitlab.com/your-org/backend-api-sdk',
          label: 'GitLab',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      links: [
        {
          title: '文档',
          items: [
            {
              label: '快速开始',
              to: '/docs/intro',
            },
            {
              label: 'API 参考',
              to: '/docs/api',
            },
          ],
        },
        {
          title: '社区',
          items: [
            {
              label: '开发者论坛',
              href: 'https://developers.kujiale.com',
            },
            {
              label: '技术博客',
              href: 'https://tech.kujiale.com',
            },
          ],
        },
        {
          title: '更多',
          items: [
            {
              label: '更新日志',
              to: '/blog',
            },
            {
              label: 'GitLab',
              href: 'https://gitlab.com/your-org/backend-api-sdk',
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} 酷家乐. Built with Docusaurus.`,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
      additionalLanguages: ['bash', 'diff', 'json'],
    },
    algolia: {
      // 当你准备好时添加 Algolia 搜索配置
      appId: 'YOUR_APP_ID',
      apiKey: 'YOUR_SEARCH_API_KEY',
      indexName: 'YOUR_INDEX_NAME',
      contextualSearch: true,
    },
  } satisfies Preset.ThemeConfig,
};

export default config;
EOF
else
    echo "✅ docusaurus.config.ts 已存在，跳过创建"
    
    # 检查是否包含 OpenAPI 插件配置
    if ! grep -q "docusaurus-plugin-openapi-docs" docusaurus.config.ts; then
        echo "⚠️ 检测到 docusaurus.config.ts 缺少 OpenAPI 插件配置"
        echo "💡 请手动添加 OpenAPI 插件配置"
    fi
fi

# 检查并处理 sidebars.ts
if [ ! -f "sidebars.ts" ]; then
    echo "📋 创建 sidebars.ts..."
    cat > sidebars.ts << 'EOF'
import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

const sidebars: SidebarsConfig = {
  // 开发指南侧边栏
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: '快速开始',
      items: [
        'getting-started/authentication',
        'getting-started/making-requests',
        'getting-started/error-handling',
      ],
    },
    {
      type: 'category',
      label: '开发指南',
      items: [
        'guides/best-practices',
        'guides/rate-limiting',
        'guides/webhooks',
        'guides/sdks',
      ],
    },
    {
      type: 'category',
      label: '示例代码',
      items: [
        'examples/javascript',
        'examples/python',
        'examples/java',
        'examples/curl',
      ],
    },
  ],

  // API 参考侧边栏 (将由 OpenAPI 插件自动生成)
  apiSidebar: [
    {
      type: 'link',
      label: '← 返回开发指南',
      href: '/docs/intro',
    },
    {
      type: 'category',
      label: 'API 概览',
      items: [
        'api/overview',
        'api/changelog',
      ],
    },
    // 以下将由 docusaurus-plugin-openapi-docs 自动生成
  ],
};

export default sidebars;
EOF
else
    echo "✅ sidebars.ts 已存在，跳过创建"
fi

# 检查并安装/更新依赖
echo "📦 检查 Node.js 依赖..."
if [ "$FRESH_INSTALL" = true ] || [ ! -d "node_modules" ]; then
    echo "📦 安装 Docusaurus 依赖..."
    npm install
else
    echo "✅ node_modules 已存在，跳过安装"
    echo "💡 如需更新依赖，请手动运行: npm install"
fi

# 检查并创建 TypeScript 配置
if [ ! -f "tsconfig.json" ]; then
    echo "⚙️ 创建 TypeScript 配置..."
    cat > tsconfig.json << 'EOF'
{
  "extends": "@docusaurus/tsconfig",
  "compilerOptions": {
    "baseUrl": "."
  }
}
EOF
else
    echo "✅ tsconfig.json 已存在，跳过创建"
fi

# 检查并创建自定义 CSS
if [ ! -f "src/css/custom.css" ]; then
    echo "🎨 创建自定义样式..."
    cat > src/css/custom.css << 'EOF'
/**
 * 酷家乐 API 文档自定义样式
 */

:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* HTTP 方法标签样式 */
.http-method {
  display: inline-block;
  padding: 0.2em 0.5em;
  margin-right: 0.5em;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.9em;
  color: white;
  text-transform: uppercase;
}

.http-method.get {
  background-color: #61affe;
}

.http-method.post {
  background-color: #49cc90;
}

.http-method.put {
  background-color: #fca130;
}

.http-method.patch {
  background-color: #50e3c2;
}

.http-method.delete {
  background-color: #f93e3e;
}

/* API 文档增强样式 */
.api-method-summary {
  margin-bottom: 1rem;
}

.api-endpoint {
  background: var(--ifm-code-background);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-family: var(--ifm-font-family-monospace);
  margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .http-method {
    margin-bottom: 0.5em;
  }
}
EOF
else
    echo "✅ src/css/custom.css 已存在，跳过创建"
fi

# 检查并创建示例博客文章
if [ ! -f "blog/2024-01-01-welcome.md" ]; then
    echo "📝 创建示例博客文章..."
    cat > blog/2024-01-01-welcome.md << 'EOF'
---
slug: welcome
title: 欢迎使用酷家乐 API 文档
authors: [api-team]
tags: [welcome, api, documentation]
---

# 欢迎使用酷家乐 API 文档

我们很高兴为您介绍全新的酷家乐 API 文档站点！

## 新功能

- 🔄 自动从 OpenAPI 规范生成文档
- 🎨 现代化的界面设计
- 🔍 强大的搜索功能
- 📱 移动端适配
- 🌍 多语言支持

## 反馈

如果您有任何建议或发现问题，请通过以下方式联系我们：

- 开发者论坛
- GitHub Issues
- 技术支持邮箱

谢谢您的支持！
EOF
else
    echo "✅ 示例博客文章已存在，跳过创建"
fi

# 检查并创建基础文档页面
docs_files=(
    "docs/getting-started/authentication.md"
    "docs/getting-started/making-requests.md"
    "docs/getting-started/error-handling.md"
)

for doc_file in "${docs_files[@]}"; do
    if [ ! -f "$doc_file" ]; then
        echo "📄 创建 $doc_file..."
        case "$doc_file" in
            *authentication.md)
                cat > "$doc_file" << 'EOF'
---
id: authentication
title: 身份认证
sidebar_label: 身份认证
---

# API 身份认证

酷家乐 API 使用 API Key 进行身份认证。

## 获取 API Key

1. 登录开发者控制台
2. 创建应用
3. 生成 API Key

## 使用 API Key

在请求头中添加认证信息：

```http
Authorization: Bearer YOUR_API_KEY
```

## 示例

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.kujiale.com/v1/models
```
EOF
                ;;
            *making-requests.md)
                cat > "$doc_file" << 'EOF'
---
id: making-requests
title: 发起请求
sidebar_label: 发起请求
---

# 发起 API 请求

## 基础 URL

```
https://api.kujiale.com
```

## 请求格式

所有 API 请求都应该：

- 使用 HTTPS
- 设置正确的 Content-Type
- 包含认证头

## 响应格式

API 返回 JSON 格式的响应：

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```
EOF
                ;;
            *error-handling.md)
                cat > "$doc_file" << 'EOF'
---
id: error-handling  
title: 错误处理
sidebar_label: 错误处理
---

# 错误处理

## HTTP 状态码

| 状态码 | 含义 |
|-------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 错误响应格式

```json
{
  "code": 400,
  "message": "Invalid parameter",
  "error": {
    "field": "model_id",
    "reason": "required"
  }
}
```
EOF
                ;;
        esac
    else
        echo "✅ $doc_file 已存在，跳过创建"
    fi
done

# 检查 OpenAPI 文件
echo "🔍 检查 OpenAPI 文件..."
OPENAPI_FILES=(
    "../openapi/diymodeldw-service/restapi.yaml"
    "../openapi/furniture-design-service/restapi.yaml"
    "../openapi/designinfoservice/restapi.yaml"
)

MISSING_FILES=()
for file in "${OPENAPI_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "⚠️ 以下 OpenAPI 文件不存在:"
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    echo "   文档生成时可能会出错，请确保 OpenAPI 文件存在"
else
    echo "✅ 所有 OpenAPI 文件检查通过"
    
    # 尝试生成 API 文档
    if [ -d "node_modules" ] && command -v npm &> /dev/null; then
        echo "📖 尝试生成 API 文档..."
        if npm run gen-api-docs all 2>/dev/null; then
            echo "✅ API 文档生成成功"
        else
            echo "⚠️ API 文档生成失败，可能需要先安装依赖或检查 OpenAPI 文件格式"
            echo "💡 手动运行: npm run gen-api-docs all"
        fi
    else
        echo "💡 请先安装依赖，然后运行: npm run gen-api-docs all"
    fi
fi

# 返回根目录
cd ..

echo ""
echo "🎉 Docusaurus 文档环境检查完成!"
echo ""
echo "📋 下一步操作:"
echo "1. cd docs-site"
echo "2. npm start                    # 启动开发服务器"
echo "3. npm run build               # 构建生产版本"
echo "4. npm run gen-api-docs all    # 重新生成 API 文档"
echo ""
echo "🌐 本地开发地址: http://localhost:3000"
echo "📚 配置文件: docs-site/docusaurus.config.ts"
echo "📝 文档目录: docs-site/docs/"
echo ""
echo "🔄 如果你的配置文件已经存在，脚本会保留它们不做修改"
echo "Happy documenting! 🚀" 