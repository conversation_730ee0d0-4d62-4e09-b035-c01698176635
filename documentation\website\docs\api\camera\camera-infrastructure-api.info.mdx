---
id: camera-infrastructure-api
title: "Camera Infrastructure API"
description: "相机基础设施 REST API"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import ApiLogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: 0.0.1"}
>
</span>

<Export
  url={"/specifications/services/camera/openapi.yaml"}
  proxy={undefined}
>
  
</Export>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"Camera Infrastructure API"}
>
</Heading>



相机基础设施 REST API

为群核旗下多个设计工具提供统一的相机查询和管理功能，支持多种相机类型的预设，
帮助用户在设计过程中快速切换和保存不同的观察角度。

**支持的设计工具：**
详见 `ToolAppType` 枚举定义

**支持的功能：**
- 根据不同设计工具的标识和参数查询相机
- 支持漫游视图、全景图、3D鸟瞰视图
- 相机参数配置管理
- 跨工具的相机数据统一格式

**技术规范：**
- 响应格式：JSON
- 字符编码：UTF-8
- 认证方式：待定


<div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    设计服务开发团队: [<EMAIL>](mailto:<EMAIL>)
  </span><span>
    URL: [https://wiki.qunhe.com/design-api](https://wiki.qunhe.com/design-api)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://qunhe.com/license"}
  >
    Proprietary
  </a>
</div>
      