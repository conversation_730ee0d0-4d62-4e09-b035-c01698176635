# Changelog

## dev
- 多语言支持波兰语，`Locale` 类型中新增 "pl_PL"

## 1.16.1-rc.1(2022-12-12)
- 添加 API `Interaction.getSelectedElements` 和 `Interaction.setSelectedElements`
    - 接口之前在 idp-custom 中声明，现在需要移动到 idp-common 中以作为通用接口，两处类型声明暂时并存，等实现迁移完成再从 idp-custom 移出

## 1.0.11-rc.0 (2022-04-14)
- [2022-05-17] 增加模型门窗和模型踢脚线 ElementType

## 1.0.6-rc.3(2022-02-23)
- [2022-03-01] `ElementType` 增加软装组合家具 `FurnitureGroup` 枚举值，仅对二方开放

## 1.0.5-rc.0(2022-01-17)
- 增加 namespace: `IDP.Platform`
- 添加 API: `IDP.Platform.getURLSearchParams()`

## 1.0.2-rc.3(2021-10-11)

- [20211019] `impl` 导出 `ElementId` 类型
- [20211019] 增加定制的设计对象六种类型
- [20211019] `impl` 导出设计对象类型枚举 `ElementType`

## 1.0.2-rc.0(2021-09-18)

- [20210928] 增加设计对象 ID 公共类型定义

## 1.0.1

- init
