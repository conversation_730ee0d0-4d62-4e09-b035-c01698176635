import { ArrayType, BasicType, ObjectType, OptionalType, Type } from '@qunhe/kls-abstraction';

/**
 * 类型校验函数 - 验证值是否符合类型定义
 * @param value 需要校验的值
 * @param typeDef 类型定义
 * @param path 当前属性路径，用于错误消息
 * @throws {TypeValidationError} 当值不符合类型定义时抛出
 */
export function validateType(value: any, typeDef: Type, path: string = 'root'): void {
    // 处理 Optional 类型
    if (typeDef.type === BasicType.Optional) {
        // 如果值是 undefined 或 null，则校验通过
        if (value === undefined || value === null) {
            return;
        }
        // 递归校验 Optional 包装的实际类型
        return validateType(value, (typeDef as OptionalType).value, path);
    }

    // 校验类型
    switch (typeDef.type) {
        case BasicType.Number:
            if (typeof value !== 'number' || Number.isNaN(value)) {
                throw new Error(`${path}: expect number, actual ${typeof value}`);
            }
            break;

        case BasicType.Boolean:
            if (typeof value !== 'boolean') {
                throw new Error(`${path}: expect boolean, actual ${typeof value}`);
            }
            break;

        case BasicType.String:
            if (typeof value !== 'string') {
                throw new Error(`${path}: expect string, actual ${typeof value}`);
            }
            break;

        case BasicType.Array:
            if (!Array.isArray(value)) {
                throw new Error(`${path}: expect array, actual ${typeof value}`);
            }
            // 校验数组中的每个元素
            const arrayTypeDef = (typeDef as ArrayType).value;
            for (let i = 0; i < value.length; i++) {
                validateType(value[i], arrayTypeDef, `${path}[${i}]`);
            }
            break;

        case BasicType.Object:
            if (!value || typeof value !== 'object' || Array.isArray(value)) {
                throw new Error(`${path}: expect object, actual ${typeof value}`);
            }

            const properties = (typeDef as ObjectType).properties;

            // 校验对象的每个属性
            for (const key in properties) {
                if (properties.hasOwnProperty(key)) {
                    const propPath = `${path}.${key}`;
                    const propTypeDef = properties[key];

                    // 对于非 Optional 类型的属性，检查是否存在
                    if (propTypeDef.type !== BasicType.Optional &&
                        (value[key] === undefined || value[key] === null)) {
                        throw new Error(`${propPath}: required property missing`);
                    }

                    // 如果属性存在，则校验其类型
                    if (value[key] !== undefined && value[key] !== null) {
                        validateType(value[key], propTypeDef, propPath);
                    }
                }
            }
            break;
        case BasicType.Function:
            if (typeof value !== 'function') {
                throw new Error(`${path}: expect function, actual ${typeof value}`)
            };
            break;
        case BasicType.Undefined:
            if (value !== undefined) {
                throw new Error(`${path}: expect undefined, actual ${typeof value}`)
            };
            break;
        case BasicType.Unknown:
            if (typeof value !== 'object') {
                throw new Error(`${path}: expect object, actual ${typeof value}`)
            };
            break;
        default:
            throw new Error(`${path}: unknown type definition`);
    }
}

/**
 * 序列化选项接口
 */
export interface SerializeOptions {
    /** 是否在序列化前进行类型校验 */
    validateBeforeSerialize?: boolean;
}

/**
 * 序列化函数 - 根据类型定义序列化值
 * @param value 需要序列化的值
 * @param typeDef 类型定义
 * @param options 序列化选项
 * @returns 序列化后的值
 */
export function serialize(
    value: any,
    typeDef: Type,
    options: SerializeOptions = {}
): any {
    // 如果开启了校验选项，先进行类型校验
    if (options.validateBeforeSerialize) {
        validateType(value, typeDef);
    }

    // 处理 Optional 类型
    if (typeDef.type === BasicType.Optional) {
        // 如果值是 undefined 或 null，则不包含在输出中
        if (value === undefined || value === null) {
            return undefined;
        }
        // 递归处理 Optional 包装的实际类型
        return serialize(value, (typeDef as OptionalType).value, options);
    }

    // 处理基本类型
    switch (typeDef.type) {
        case BasicType.Number:
        case BasicType.Boolean:
        case BasicType.String:
            return value;

        case BasicType.Array:
            if (!Array.isArray(value)) {
                throw new Error('expect array')
            };
            return value.map(item =>
                serialize(item, (typeDef as ArrayType).value, options)
            ).filter(item => item !== undefined);

        case BasicType.Object:
            if (!value || typeof value !== 'object' || Array.isArray(value)) {
                throw new Error('expect object')
            };

            const result: Record<string, any> = {};
            const properties = (typeDef as ObjectType).properties;

            for (const key in properties) {
                if (properties.hasOwnProperty(key)) {
                    const serializedValue = serialize(value[key], properties[key], options);
                    if (serializedValue !== undefined) {
                        result[key] = serializedValue;
                    }
                }
            }
            return result;
        case BasicType.Function:
            return value;
        case BasicType.Undefined:
            return value;
        case BasicType.Unknown:
            return JSON.stringify(value);
        default:
            throw new Error(`unsupported type: ${typeDef.type}`);
    }
}
