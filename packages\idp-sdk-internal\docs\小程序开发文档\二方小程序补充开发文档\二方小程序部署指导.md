## 背景

二方小程序支持通过 PUB 部署。内部团队在开发小程序时，可以沿用过往流程、复用相关基础设施，获得版本、批量、灰度等能力

接入后，资源将由 PUB 控制，入口仍由开放平台控制。小程序的 `manifest` 由 PUB 动态返回，页面、`VM` 代码仍会上传到 `https://miniapp-cos.kujiale.com`

> 本文旨在串联各个环节，细节部分不展开介绍，阅读前请确保对以下基础设施有大致了解：
>
> - [def-next](https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80305366573)
> - [PUB](https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80196964663)
> - [开放平台](https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80686641461)
> - [工具小程序对外文档](https://open.kujiale.com/pub/saas/open-platform/doc-detail?app_id=15&tree_tab=a&doc_tab=doc&node_id=1628&node_type=1)

## 接入流程

### 新建 PUB 应用

- 在 [PUB 前端单元管理](https://pub.qunhequnhe.com/fcms) 创建 [小程序应用](https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80732888872) 类型应用
- 定义路径、权限
- 示例：

![新建 PUB 应用](./assets/pub.png)

### 新建代码仓库

- 新建代码仓库
- 初始化代码仓库
    - `kjl init` 初始化项目，选择 PUB -> PUB 工具小程序项目模板
    - `kjl gen xxx` 初始化应用，`xxx` 参数为上一节创建的应用名

### 开发小程序

- `kjl dev` 启动本地，获得 `manifest` 地址
- 打开工具页面（注意保证和 `manifest` 地址同域），按照 [小程序开发教程](https://open.kujiale.com/pub/saas/open-platform/doc-detail?app_id=15&node_id=1367&node_type=1&doc_tab=doc&tree_tab=a) 填入 `manifest` 地址，加载小程序
- 点击启动小程序
- 示例：

![开发小程序](./assets/develop.gif)

### 上架小程序

- 与常规流程相同，二方小程序在开放平台：
    - 需要部署、上架一遍
    - 需要内外网各操作一遍（开放平台内外网数据隔离）
- 与常规流程不同，二方小程序在开放平台：
    - 需要在部署时选择 **代码链接**，并填入 PUB 应用元信息中定义过的路径
    > 注意：
    >
    > **部署链接**和**代码链接**是完全不同的两种部署方式，只有认证为酷家乐开发者的账号，才能看到**代码链接**入口
    >
    > [公共酷家乐开发者账号](https://doc.weixin.qq.com/doc/w3_ADoAXgaeACQUPueV4VyS7SkTPDOie)可咨询光月，自用账号认证可咨询开放平台
    - 无需重复部署、上架，后续部署转移至 PUB，构建发布、流转资源
- 示例：

![开发小程序](./assets/open-platform.png)

## 待支持需求

- 配置、分桶、多语言等设施具备接入可能，后续将通过`API`的形式向二方小程序开发，若有相关需求可以向 KAF 小助手提 issue
