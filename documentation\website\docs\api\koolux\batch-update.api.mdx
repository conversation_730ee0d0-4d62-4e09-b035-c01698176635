---
id: batch-update
title: "批量更新照明场景数据"
description: "批量更新照明场景中的模型、ies灯光数据，只针对照明对象生效"
sidebar_label: "批量更新照明场景数据"
hide_title: true
hide_table_of_contents: true
api: {"tags":["KooLuxLight"],"description":"批量更新照明场景中的模型、ies灯光数据，只针对照明对象生效","operationId":"batchUpdate","parameters":[{"name":"kooLuxDesignId","in":"path","description":"encrypted koolux design id","required":true,"schema":{"type":"string"}}],"requestBody":{"description":"batch update light request data","content":{"application/json":{"schema":{"type":"object","properties":{"requestId":{"type":"string","description":"请求ID，用于幂等性控制"},"operationId":{"type":"string","description":"操作ID，用于异步操作跟踪"},"batchRequests":{"type":"array","items":{"required":["opType"],"type":"object","properties":{"opType":{"type":"string","description":"场景操作方式"}},"discriminator":{"propertyName":"opType","mapping":{"model_create":{"required":["id","productId","transform"],"type":"object","description":"添加模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"唯一ID，用uuid生成，和ies的绑定关系需要用到该ID"},"productId":{"type":"string","description":"商品ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"ModelCreateRequest"},"model_update":{"required":["id","transform"],"type":"object","description":"更新模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"需要更新的模型ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"ModelUpdateRequest"},"model_replace":{"required":["id","productId"],"type":"object","description":"替换模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"需要替换的模型ID"},"productId":{"type":"string","description":"商品ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"ModelReplaceRequest"},"model_delete":{"required":["id"],"type":"object","description":"删除模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"模型ID"}}}],"title":"ModelDeleteRequest"},"light_create":{"required":["iesSpotLightData"],"type":"object","description":"添加光源","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"iesSpotLightData":{"type":"object","properties":{"iesId":{"type":"string","description":"用户上传ies id（新增灯光时，iesId和iesFile必传其一）"},"iesFile":{"type":"string","description":"ies file路径（新增灯光时，iesId和iesFile必传其一）"},"brightness":{"type":"number","description":"亮度百分比，0~1（默认0）","format":"double"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"},"maintenanceFactor":{"type":"number","description":"维护系数，0~1（默认1）","format":"float"},"spectrumData":{"type":"object","properties":{"spectrumId":{"type":"string","description":"光谱id（LED光谱必传）"},"type":{"type":"string","description":"光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)","enum":["UNSPECIFIED","PLANCK","LED","RGB"]},"colorTemperature":{"type":"integer","description":"色温，1000K~25000K（默认普朗克光谱4500K）","format":"int32"},"color":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"场景光谱数据","title":"SpectrumData"}},"description":"ies光源数据","title":"IesSpotLightData"},"bindingModelIds":{"type":"array","description":"ies绑定的模型ID列表","items":{"type":"string","description":"ies绑定的模型ID列表"}}}}],"title":"LightCreateRequest"},"light_update":{"required":["id","iesSpotLightData"],"type":"object","description":"更新光源","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"灯光ID"},"iesSpotLightData":{"type":"object","properties":{"iesId":{"type":"string","description":"用户上传ies id（新增灯光时，iesId和iesFile必传其一）"},"iesFile":{"type":"string","description":"ies file路径（新增灯光时，iesId和iesFile必传其一）"},"brightness":{"type":"number","description":"亮度百分比，0~1（默认0）","format":"double"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"},"maintenanceFactor":{"type":"number","description":"维护系数，0~1（默认1）","format":"float"},"spectrumData":{"type":"object","properties":{"spectrumId":{"type":"string","description":"光谱id（LED光谱必传）"},"type":{"type":"string","description":"光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)","enum":["UNSPECIFIED","PLANCK","LED","RGB"]},"colorTemperature":{"type":"integer","description":"色温，1000K~25000K（默认普朗克光谱4500K）","format":"int32"},"color":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"场景光谱数据","title":"SpectrumData"}},"description":"ies光源数据","title":"IesSpotLightData"}}}],"title":"LightUpdateRequest"},"light_delete":{"required":["id"],"type":"object","description":"删除光源","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"灯光ID"}}}],"title":"LightDeleteRequest"}}},"title":"SceneDocumentOperateRequest"}}},"title":"SceneDocumentBatchUpdateRequest"}}},"required":true},"responses":{"200":{"description":"Success","content":{"application/json":{"schema":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"object","properties":{"elementIds":{"type":"array","description":"处理成功的元素id","items":{"type":"string","description":"处理成功的元素id"}}},"title":"SceneElementUpdateResponse"}},"sceneDocument":{"type":"object","properties":{"sceneElements":{"type":"array","description":"每个element的更新结果","items":{"required":["elemetType"],"type":"object","properties":{"id":{"type":"string","description":"元素ID"},"version":{"type":"string","description":"版本号"},"elemetType":{"type":"string","description":"元素类型"}},"discriminator":{"propertyName":"elemetType","mapping":{"model":{"type":"object","description":"模型","allOf":["circular(SceneElement)",{"type":"object","properties":{"productId":{"type":"string","description":"商品ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"Model"},"light":{"type":"object","description":"照明灯光","allOf":["circular(SceneElement)",{"type":"object","properties":{"iesSpotLightData":{"type":"object","properties":{"iesId":{"type":"string","description":"用户上传ies id（新增灯光时，iesId和iesFile必传其一）"},"iesFile":{"type":"string","description":"ies file路径（新增灯光时，iesId和iesFile必传其一）"},"brightness":{"type":"number","description":"亮度百分比，0~1（默认0）","format":"double"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"},"maintenanceFactor":{"type":"number","description":"维护系数，0~1（默认1）","format":"float"},"spectrumData":{"type":"object","properties":{"spectrumId":{"type":"string","description":"光谱id（LED光谱必传）"},"type":{"type":"string","description":"光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)","enum":["UNSPECIFIED","PLANCK","LED","RGB"]},"colorTemperature":{"type":"integer","description":"色温，1000K~25000K（默认普朗克光谱4500K）","format":"int32"},"color":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"场景光谱数据","title":"SpectrumData"}},"description":"ies光源数据","title":"IesSpotLightData"},"bindingModelIds":{"type":"array","description":"ies绑定的模型ID列表","items":{"type":"string","description":"ies绑定的模型ID列表"}}}}],"title":"Light"}}},"title":"SceneElement"}},"version":{"type":"string","description":"版本号"}},"description":"更新后的场景文档","title":"SceneDocument"}},"title":"SceneDocumentBatchUpdateResponse"}}}},"400":{"description":"Invalid Argument","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"401":{"description":"Unauthenticated","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"Model or Light not found","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"Internal Server Error","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}},"x-return-extra":{"is-operation":"true"},"extensions":[{"key":"x-return-extra","value":{"is-operation":"true"}}],"method":"put","path":"/koolux/openapi/rest/v1/designs/{kooLuxDesignId}/light:batchupdate","servers":[{"url":"https://www.koolux.com","description":"KooLux官网地址"}],"jsonRequestBodyExample":{"requestId":"string","operationId":"string","batchRequests":[{"opType":"string"}]},"info":{"title":"KooLux REST API","description":"KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。  \n\n## 功能特性  \n- 支持IES文件的管理  \n- 支持照明模型管理  \n- 批量操作支持（创建、更新、删除、查询）  \n\n## 数据模型  \n- **照明模型**: 支持材质编辑和灯光模型排除  \n- **IES光源**: 包含IES文件全量信息\n","contact":{"name":"照明设计团队","email":"<EMAIL>"},"license":{"name":"内部使用","url":"https://www.manycore.com"},"version":"1.0.0"},"postman":{"name":"批量更新照明场景数据","description":{"content":"批量更新照明场景中的模型、ies灯光数据，只针对照明对象生效","type":"text/plain"},"url":{"path":["koolux","openapi","rest","v1","designs",":kooLuxDesignId","light:batchupdate"],"host":["{{baseUrl}}"],"query":[],"variable":[{"disabled":false,"description":{"content":"(Required) encrypted koolux design id","type":"text/plain"},"type":"any","value":"","key":"kooLuxDesignId"}]},"header":[{"key":"Content-Type","value":"application/json"},{"key":"Accept","value":"application/json"}],"method":"PUT","body":{"mode":"raw","raw":"","options":{"raw":{"language":"json"}}}}}
sidebar_class_name: "put api-method"
info_path: docs/api/koolux/koolux-rest-api
custom_edit_url: null
---

import MethodEndpoint from "@theme/ApiExplorer/MethodEndpoint";
import ParamsDetails from "@theme/ParamsDetails";
import RequestSchema from "@theme/RequestSchema";
import StatusCodes from "@theme/StatusCodes";
import OperationTabs from "@theme/OperationTabs";
import TabItem from "@theme/TabItem";
import Heading from "@theme/Heading";

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"批量更新照明场景数据"}
>
</Heading>

<MethodEndpoint
  method={"put"}
  path={"/koolux/openapi/rest/v1/designs/{kooLuxDesignId}/light:batchupdate"}
  context={"endpoint"}
>
  
</MethodEndpoint>



批量更新照明场景中的模型、ies灯光数据，只针对照明对象生效

<Heading
  id={"request"}
  as={"h2"}
  className={"openapi-tabs__heading"}
  children={"Request"}
>
</Heading>

<ParamsDetails
  parameters={[{"name":"kooLuxDesignId","in":"path","description":"encrypted koolux design id","required":true,"schema":{"type":"string"}}]}
>
  
</ParamsDetails>

<RequestSchema
  title={"Body"}
  body={{"description":"batch update light request data","content":{"application/json":{"schema":{"type":"object","properties":{"requestId":{"type":"string","description":"请求ID，用于幂等性控制"},"operationId":{"type":"string","description":"操作ID，用于异步操作跟踪"},"batchRequests":{"type":"array","items":{"required":["opType"],"type":"object","properties":{"opType":{"type":"string","description":"场景操作方式"}},"discriminator":{"propertyName":"opType","mapping":{"model_create":{"required":["id","productId","transform"],"type":"object","description":"添加模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"唯一ID，用uuid生成，和ies的绑定关系需要用到该ID"},"productId":{"type":"string","description":"商品ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"ModelCreateRequest"},"model_update":{"required":["id","transform"],"type":"object","description":"更新模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"需要更新的模型ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"ModelUpdateRequest"},"model_replace":{"required":["id","productId"],"type":"object","description":"替换模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"需要替换的模型ID"},"productId":{"type":"string","description":"商品ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"ModelReplaceRequest"},"model_delete":{"required":["id"],"type":"object","description":"删除模型","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"模型ID"}}}],"title":"ModelDeleteRequest"},"light_create":{"required":["iesSpotLightData"],"type":"object","description":"添加光源","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"iesSpotLightData":{"type":"object","properties":{"iesId":{"type":"string","description":"用户上传ies id（新增灯光时，iesId和iesFile必传其一）"},"iesFile":{"type":"string","description":"ies file路径（新增灯光时，iesId和iesFile必传其一）"},"brightness":{"type":"number","description":"亮度百分比，0~1（默认0）","format":"double"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"},"maintenanceFactor":{"type":"number","description":"维护系数，0~1（默认1）","format":"float"},"spectrumData":{"type":"object","properties":{"spectrumId":{"type":"string","description":"光谱id（LED光谱必传）"},"type":{"type":"string","description":"光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)","enum":["UNSPECIFIED","PLANCK","LED","RGB"]},"colorTemperature":{"type":"integer","description":"色温，1000K~25000K（默认普朗克光谱4500K）","format":"int32"},"color":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"场景光谱数据","title":"SpectrumData"}},"description":"ies光源数据","title":"IesSpotLightData"},"bindingModelIds":{"type":"array","description":"ies绑定的模型ID列表","items":{"type":"string","description":"ies绑定的模型ID列表"}}}}],"title":"LightCreateRequest"},"light_update":{"required":["id","iesSpotLightData"],"type":"object","description":"更新光源","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"灯光ID"},"iesSpotLightData":{"type":"object","properties":{"iesId":{"type":"string","description":"用户上传ies id（新增灯光时，iesId和iesFile必传其一）"},"iesFile":{"type":"string","description":"ies file路径（新增灯光时，iesId和iesFile必传其一）"},"brightness":{"type":"number","description":"亮度百分比，0~1（默认0）","format":"double"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"},"maintenanceFactor":{"type":"number","description":"维护系数，0~1（默认1）","format":"float"},"spectrumData":{"type":"object","properties":{"spectrumId":{"type":"string","description":"光谱id（LED光谱必传）"},"type":{"type":"string","description":"光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)","enum":["UNSPECIFIED","PLANCK","LED","RGB"]},"colorTemperature":{"type":"integer","description":"色温，1000K~25000K（默认普朗克光谱4500K）","format":"int32"},"color":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"场景光谱数据","title":"SpectrumData"}},"description":"ies光源数据","title":"IesSpotLightData"}}}],"title":"LightUpdateRequest"},"light_delete":{"required":["id"],"type":"object","description":"删除光源","allOf":["circular(SceneDocumentOperateRequest)",{"type":"object","properties":{"id":{"type":"string","description":"灯光ID"}}}],"title":"LightDeleteRequest"}}},"title":"SceneDocumentOperateRequest"}}},"title":"SceneDocumentBatchUpdateRequest"}}},"required":true}}
>
  
</RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{"200":{"description":"Success","content":{"application/json":{"schema":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"object","properties":{"elementIds":{"type":"array","description":"处理成功的元素id","items":{"type":"string","description":"处理成功的元素id"}}},"title":"SceneElementUpdateResponse"}},"sceneDocument":{"type":"object","properties":{"sceneElements":{"type":"array","description":"每个element的更新结果","items":{"required":["elemetType"],"type":"object","properties":{"id":{"type":"string","description":"元素ID"},"version":{"type":"string","description":"版本号"},"elemetType":{"type":"string","description":"元素类型"}},"discriminator":{"propertyName":"elemetType","mapping":{"model":{"type":"object","description":"模型","allOf":["circular(SceneElement)",{"type":"object","properties":{"productId":{"type":"string","description":"商品ID"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"}}}],"title":"Model"},"light":{"type":"object","description":"照明灯光","allOf":["circular(SceneElement)",{"type":"object","properties":{"iesSpotLightData":{"type":"object","properties":{"iesId":{"type":"string","description":"用户上传ies id（新增灯光时，iesId和iesFile必传其一）"},"iesFile":{"type":"string","description":"ies file路径（新增灯光时，iesId和iesFile必传其一）"},"brightness":{"type":"number","description":"亮度百分比，0~1（默认0）","format":"double"},"transform":{"type":"object","properties":{"elements":{"type":"array","items":{"type":"number","format":"double"}}},"description":"transform信息","title":"Matrix"},"maintenanceFactor":{"type":"number","description":"维护系数，0~1（默认1）","format":"float"},"spectrumData":{"type":"object","properties":{"spectrumId":{"type":"string","description":"光谱id（LED光谱必传）"},"type":{"type":"string","description":"光谱类型，UNSPECIFIED(未定义)，PLANCK(普朗克光谱)，LED(LED光谱)，RGB(RGB光谱)","enum":["UNSPECIFIED","PLANCK","LED","RGB"]},"colorTemperature":{"type":"integer","description":"色温，1000K~25000K（默认普朗克光谱4500K）","format":"int32"},"color":{"type":"object","title":"Point3d","description":"三维点。","properties":{"x":{"type":"number","format":"double","description":"x坐标。"},"y":{"type":"number","format":"double","description":"y坐标。"},"z":{"type":"number","format":"double","description":"z坐标。"}}}},"description":"场景光谱数据","title":"SpectrumData"}},"description":"ies光源数据","title":"IesSpotLightData"},"bindingModelIds":{"type":"array","description":"ies绑定的模型ID列表","items":{"type":"string","description":"ies绑定的模型ID列表"}}}}],"title":"Light"}}},"title":"SceneElement"}},"version":{"type":"string","description":"版本号"}},"description":"更新后的场景文档","title":"SceneDocument"}},"title":"SceneDocumentBatchUpdateResponse"}}}},"400":{"description":"Invalid Argument","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"401":{"description":"Unauthenticated","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"404":{"description":"Model or Light not found","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}},"500":{"description":"Internal Server Error","content":{"*/*":{"schema":{"type":"object","title":"ApiError","description":"REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范","required":["code","message","status","details"],"properties":{"code":{"type":"integer","description":"HTTP 状态码（数字），例如 403、404、500等","example":403,"minimum":100,"maximum":599},"message":{"type":"string","description":"面向开发者的错误消息，应该使用英文","example":"Permission denied","minLength":1,"maxLength":1000},"status":{"description":"RestAPI 对应的状态码枚举值","type":"string","title":"Code","enum":["OK","CANCELLED","UNKNOWN","INVALID_ARGUMENT","DEADLINE_EXCEEDED","NOT_FOUND","ALREADY_EXISTS","PERMISSION_DENIED","UNAUTHENTICATED","RESOURCE_EXHAUSTED","FAILED_PRECONDITION","ABORTED","OUT_OF_RANGE","UNIMPLEMENTED","INTERNAL","UNAVAILABLE","DATA_LOSS","PARTIAL_ELEMENT_UPDATE_FAILED"],"x-module":"error","x-category":"错误处理"},"details":{"description":"错误的详细信息","type":"object","title":"ErrorDetails","required":["reason"],"properties":{"reason":{"type":"string","description":"错误原因，标识错误的直接原因。格式为大写蛇形命名","example":"INVALID_REQUEST_FORMAT","pattern":"^[A-Z][A-Z0-9_]*[A-Z0-9]$","maxLength":63},"message":{"type":"string","description":"针对此错误发生的人类可读的解释说明","example":"请求参数格式不正确，缺少必需的字段 'name'","maxLength":500},"domain":{"type":"string","description":"错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称","example":"deco.manycoreapis.com","maxLength":100},"metaData":{"type":"object","description":"关于此错误的附加结构化详细信息，键名限制为64个字符","additionalProperties":{"type":"string","maxLength":200},"example":{"maxAllowedSize":"1000","actualSize":"1500"}}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"localizedMessage":{"description":"错误的本地化消息，可选字段","type":"object","title":"LocalizedMessage","required":["locale","message"],"properties":{"locale":{"type":"string","description":"消息所使用的语言环境","example":"zh-CN","pattern":"^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"},"message":{"type":"string","description":"本地化的消息内容","example":"权限不足，无法访问该资源","minLength":1,"maxLength":1000}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"},"help":{"description":"错误的帮助信息，可选字段","type":"object","title":"Help","required":["desc","url"],"properties":{"desc":{"type":"string","description":"链接描述，说明该链接提供的帮助内容","example":"查看 API 使用指南","minLength":1,"maxLength":200},"url":{"type":"string","description":"帮助链接的 URL 地址","example":"https://docs.example.com/api-guide","format":"uri","maxLength":500}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}},"additionalProperties":false,"x-module":"error","x-category":"错误处理"}}}}}}
>
  
</StatusCodes>


      