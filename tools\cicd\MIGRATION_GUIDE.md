# CI/CD 脚本迁移指南

## 📋 概述

本指南帮助您从旧的 `tools/ci-cd/` 脚本结构迁移到新的 `tools/cicd/` 结构。新结构提供了更好的模块化、可维护性和功能扩展性。

## 🎯 迁移目标

- **模块化设计**：按功能分类组织脚本
- **统一接口**：标准化的脚本调用方式
- **错误处理**：完善的错误检测和恢复机制
- **日志系统**：统一的日志格式和级别
- **配置管理**：集中化的配置管理

## 📁 新旧结构对比

### 旧结构 (`tools/ci-cd/`)
```
tools/ci-cd/
├── ci/
│   ├── deployment/
│   ├── docs/
│   ├── mr/
│   ├── setup/
│   └── validation/
├── detect-api-changes.sh
├── local-mr-review.sh
└── add-mr-comment.sh
```

### 新结构 (`tools/cicd/`)
```
tools/cicd/
├── core/           # 核心功能模块
├── sdk/            # SDK 相关脚本
├── docs/           # 文档相关脚本
├── workflows/      # 工作流脚本
├── utils/          # 工具函数库
└── config/         # 配置文件
```

## 🔄 脚本迁移映射

### 核心功能脚本

| 旧脚本 | 新脚本 | 说明 |
|--------|--------|------|
| `detect-api-changes.sh` | `core/api-changes.sh` | API 变更检测 |
| `ci/setup/environment-check.sh` | `core/environment.sh` | 环境检查 |
| `ci/validation/validate-openapi.sh` | `core/validation.sh` | OpenAPI 验证 |

### SDK 相关脚本

| 旧脚本 | 新脚本 | 说明 |
|--------|--------|------|
| `ci/deployment/generate-*.sh` | `sdk/generate.sh` | SDK 生成 |
| `ci/deployment/build-*.sh` | `sdk/build.sh` | SDK 构建 |
| `ci/deployment/test-*.sh` | `sdk/test.sh` | SDK 测试 |
| `ci/deployment/deploy-*.sh` | `sdk/deploy.sh` | SDK 部署 |

### 文档相关脚本

| 旧脚本 | 新脚本 | 说明 |
|--------|--------|------|
| `build-docusaurus*.sh` | `docs/build.sh` | 文档构建 |
| `preview-docusaurus*.sh` | `docs/preview.sh` | 文档预览 |
| `ci/docs/deploy-to-manual.sh` | `docs/deploy.sh` | 文档部署 |

### 工作流脚本

| 旧脚本 | 新脚本 | 说明 |
|--------|--------|------|
| `ci/mr/complete-mr-pipeline.sh` | `workflows/mr-pipeline.sh` | MR 流水线 |
| `local-mr-review.sh` | `workflows/local-review.sh` | 本地评审 |
| 无 | `workflows/main-pipeline.sh` | 主分支流水线 |

### 工具脚本

| 旧脚本 | 新脚本 | 说明 |
|--------|--------|------|
| `add-mr-comment.sh` | `utils/gitlab-api.sh` | GitLab API 工具 |
| 分散在各脚本中 | `utils/common.sh` | 通用函数库 |

## 🚀 快速迁移步骤

### 1. 备份现有脚本
```bash
# 备份旧脚本目录
cp -r tools/ci-cd tools/ci-cd-backup-$(date +%Y%m%d)
```

### 2. 更新 GitLab CI 配置
更新 `.gitlab-ci.yml` 中的脚本路径：

```yaml
# 旧配置
script:
  - tools/ci-cd/ci/mr/complete-mr-pipeline.sh

# 新配置
script:
  - tools/cicd/workflows/mr-pipeline.sh
```

### 3. 更新本地开发脚本
更新本地开发中使用的脚本路径：

```bash
# 旧命令
./tools/ci-cd/local-mr-review.sh

# 新命令
./tools/cicd/workflows/local-review.sh
```

### 4. 更新环境变量
新脚本支持更多的环境变量配置：

```bash
# 核心配置
export LOG_LEVEL=INFO                    # 日志级别
export SCRIPT_TIMEOUT=3600              # 脚本超时时间

# SDK 配置
export SDK_LANGUAGES="java,typescript"   # 要生成的 SDK 语言
export SKIP_SDK_TESTS=false             # 是否跳过 SDK 测试

# 文档配置
export DOCS_BUILD_MODE=optimized         # 文档构建模式
export MANUAL_ENV=dev                    # Manual 部署环境

# GitLab 集成
export GITLAB_TOKEN=your_token           # GitLab API Token
export ENABLE_MR_COMMENTS=true          # 启用 MR 评论
```

## 📝 使用示例

### MR 流水线
```bash
# 执行完整的 MR 流水线
./tools/cicd/workflows/mr-pipeline.sh

# 跳过文档生成
SKIP_DOCS=true ./tools/cicd/workflows/mr-pipeline.sh

# 跳过 SDK 处理
SKIP_SDKS=true ./tools/cicd/workflows/mr-pipeline.sh
```

### 本地评审
```bash
# 本地评审（使用 Docker）
./tools/cicd/workflows/local-review.sh

# 本地评审（不使用 Docker）
USE_DOCKER=false ./tools/cicd/workflows/local-review.sh

# 本地评审（不清理资源）
CLEANUP_ON_EXIT=false ./tools/cicd/workflows/local-review.sh
```

### 主分支流水线
```bash
# 部署到 staging 环境
DEPLOY_ENVIRONMENT=staging ./tools/cicd/workflows/main-pipeline.sh

# 部署到 production 环境
DEPLOY_ENVIRONMENT=production ./tools/cicd/workflows/main-pipeline.sh

# 跳过测试
SKIP_TESTS=true ./tools/cicd/workflows/main-pipeline.sh
```

### 单独执行功能模块
```bash
# API 变更检测
./tools/cicd/core/api-changes.sh

# OpenAPI 验证
./tools/cicd/core/validation.sh changed

# SDK 生成
./tools/cicd/sdk/generate.sh java

# 文档构建
./tools/cicd/docs/build.sh optimized
```

## ⚠️ 注意事项

### 1. 环境变量变更
一些环境变量名称已更改，请更新您的配置：

| 旧变量名 | 新变量名 | 说明 |
|----------|----------|------|
| `API_CHANGES` | `HAS_API_CHANGES` | API 变更标志 |
| `BUILD_TYPE` | `DOCS_BUILD_MODE` | 文档构建模式 |

### 2. 脚本参数变更
新脚本的参数格式更加统一：

```bash
# 旧格式（不一致）
./generate-java-sdk.sh
./build-typescript-sdk.sh all

# 新格式（统一）
./sdk/generate.sh java
./sdk/build.sh typescript
./sdk/deploy.sh all releases
```

### 3. 输出格式变更
新脚本使用统一的日志格式，可能需要更新日志解析逻辑。

### 4. 依赖关系
新脚本之间有明确的依赖关系，请确保按正确顺序执行。

## 🔧 故障排除

### 常见问题

1. **脚本找不到**
   ```bash
   # 确保脚本有执行权限
   chmod +x tools/cicd/workflows/*.sh
   chmod +x tools/cicd/core/*.sh
   chmod +x tools/cicd/sdk/*.sh
   chmod +x tools/cicd/docs/*.sh
   ```

2. **环境变量未设置**
   ```bash
   # 检查必需的环境变量
   ./tools/cicd/core/environment.sh
   ```

3. **Docker 相关问题**
   ```bash
   # 检查 Docker 状态
   docker info
   
   # 拉取最新镜像
   docker pull registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest
   ```

4. **权限问题**
   ```bash
   # 检查文件权限
   ls -la tools/cicd/

   # 修复权限
   find tools/cicd/ -name "*.sh" -exec chmod +x {} \;
   ```

### 调试模式
启用调试模式获取更详细的日志：

```bash
# 启用调试模式
export LOG_LEVEL=DEBUG
export SCRIPT_DEBUG=true

# 执行脚本
./tools/cicd/workflows/mr-pipeline.sh
```

## 📞 获取帮助

如果在迁移过程中遇到问题，请：

1. 查看脚本的帮助信息：
   ```bash
   ./tools/cicd/workflows/mr-pipeline.sh --help
   ```

2. 检查日志输出中的错误信息

3. 联系 Backend API Team 获取支持

## 🎉 迁移完成检查清单

- [ ] 备份了旧脚本
- [ ] 更新了 GitLab CI 配置
- [ ] 更新了本地开发脚本
- [ ] 更新了环境变量配置
- [ ] 测试了新脚本的基本功能
- [ ] 验证了 MR 流水线
- [ ] 验证了本地评审流程
- [ ] 更新了团队文档
- [ ] 通知了团队成员

完成以上检查清单后，您就可以安全地使用新的 CI/CD 脚本结构了！
