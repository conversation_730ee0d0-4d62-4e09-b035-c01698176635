import type { SidebarsConfig } from "@docusaurus/plugin-content-docs";

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: "doc",
      id: "api/furniture/家具设计管理api",
    },
    {
      type: "category",
      label: "家具管理接口",
      link: {
        type: "doc",
        id: "api/furniture/家具管理接口",
      },
      items: [
        {
          type: "doc",
          id: "api/furniture/get-furniture-list",
          label: "获取家具列表",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/furniture/create-furniture",
          label: "创建单个家具",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/furniture/batch-update-furniture",
          label: "批量更新家具",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/furniture/batch-get-furniture",
          label: "批量获取家具信息",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/furniture/batch-delete-furniture",
          label: "批量删除家具",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/furniture/batch-create-furniture",
          label: "批量创建家具",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/furniture/batch-create-furniture-by-group-product",
          label: "通过组合商品批量创建家具",
          className: "api-method post",
        },
        {
          type: "doc",
          id: "api/furniture/get-single-furniture",
          label: "获取单个家具信息",
          className: "api-method get",
        },
        {
          type: "doc",
          id: "api/furniture/update-furniture",
          label: "更新单个家具",
          className: "api-method put",
        },
        {
          type: "doc",
          id: "api/furniture/delete-furniture",
          label: "删除单个家具",
          className: "api-method delete",
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
