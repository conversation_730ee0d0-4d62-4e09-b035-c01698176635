image: registry.qunhequnhe.com/tool-frontend/node-build-variant:1.1.3

variables:
  NODE_OPTIONS: "--max_old_space_size=4096"
  GIT_DEPTH: "3"

include:
  - https://moon.qunhequnhe.com/moon/gitLabCiPipeline/getCommonYml?fileName=ci_lint.yml

stages:
  - test
  - deploy
  - post-deploy

before_script:
  - yarn

yarn-dedupe-check:
  stage: test
  before_script:
    - echo ''
  script:
    - yarn global add yarn-dedupe
    - yarn-dedupe -c -p @qunhe.*
  tags:
    - kube-runner

test:
  stage: test
  script:
    - npx lerna run build-package
  tags:
    - kube-runner
  coverage: /All files\s*\|\s*\d+(?:\.\d+)?/

uni-test:
  stage: test
  script:
    - yarn test
  tags:
    - kube-runner

build-project:
  stage: test
  script:
    - node ./scripts/build-project --auto-commit
  tags:
    - kube-runner
  except:
    refs:
      - master
      - tags
    variables:
      - $GITLAB_USER_NAME == "tools-gitlab"
  when: manual
  allow_failure: false

ci_lint:
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH =~ /^feature.*$/
    - if: $CI_COMMIT_BRANCH =~ /^fix.*$/
    - if: $CI_COMMIT_BRANCH =~ /^bugfix.*$/
    - if: $CI_COMMIT_BRANCH =~ /^bug.*$/
    - if: $CI_COMMIT_BRANCH =~ /^feat.*$/
    - if: $CI_COMMIT_BRANCH =~ /^release.*$/
    - if: $CI_COMMIT_BRANCH =~ /^hotfix.*$/
    - if: $CI_COMMIT_TAG =~ /ci_nightly_build*/
  tags:
    - kube-runner
  allow_failure: false

publish:
  stage: deploy
  script: &publish_scripts
    - npx lerna run build-package # prebuild use lerna before use tools-script, lerna will run in sequence of dependency graph
    - npm install -g @qunhe/tools-script
    - tools-script publish-package ./packages/ --multi --ignore-uncommitted-changes yarn.lock
  only:
    - tags
  tags:
    - kube-runner


.auto_on_master_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: on_success
      allow_failure: true
    - if: $CI_COMMIT_TAG
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: manual
      allow_failure: true

.auto_on_tags_rules:
   rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_TAG
      when: on_success
      allow_failure: false
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: never

publish-prerelease:
  stage: deploy
  before_script:
    - yarn
    - node ./scripts/check-prerelease
  script: *publish_scripts
  rules: !reference [.auto_on_master_rules, rules]
  tags:
    - kube-runner

create-public-release:
  stage: post-deploy
  script:
    - node ./scripts/create-public-release
  only:
    - master
  when: manual
  tags:
    - kube-runner

.publish-sdk-docs-base:
  image: registry.qunhequnhe.com/infra/base-builder-v18:latest
  stage: post-deploy
  tags:
    - kube-runner
  retry: 2
  script:
    - yarn global add @qunhe/tsdoc-utils@0.7.0-rc.8
    - perl -lpe 's/\@qunhe\/idp-sdk/\@manycore\/idp-sdk/g' -i packages/idp-sdk/package.json
    - node ./scripts/build-project
    - cd packages/idp-sdk
    - node ../../scripts/kjl.js p manual state
    - TSDOC_ENABLE_ALIAS_TRANSFORM=true tsdoc-utils --api-json build/api.json --baseUrl ${BASE_URL}
    - node ../../scripts/kjl.js p manual upload --src public/ --amend ${EXTRA_UPLOAD_ARGS}

publish-sdk-docs:
  extends: .publish-sdk-docs-base
  variables:
    BASE_URL: '"/idp-sdk/{packageVersion}/"'
    EXTRA_UPLOAD_ARGS: ''
  rules: !reference [.auto_on_tags_rules, rules]

publish-sdk-docs-latest:
  extends: .publish-sdk-docs-base
  variables:
    BASE_URL: '"/idp-sdk/latest/"'
    EXTRA_UPLOAD_ARGS: '--define-version latest'
  when: manual
  only:
    - tags

publish-sdk-docs-dev:
  extends: .publish-sdk-docs-base
  variables:
    BASE_URL: '"/idp-sdk/dev/"'
    EXTRA_UPLOAD_ARGS: '--define-version dev'
  rules: !reference [.auto_on_master_rules, rules]

.publish-docs-internal-base:
  image: registry.qunhequnhe.com/infra/base-builder-v18:latest
  stage: post-deploy
  retry: 2
  tags:
    - kube-runner
  script:
    - yarn global add @qunhe/tsdoc-utils@0.7.0-rc.8
    - node ./scripts/build-project
    - cd packages/idp-sdk-internal
    - node ../../scripts/kjl.js p manual state
    - cp -r ../../node_modules/@qunhe/apaas-type-generator/docs/VM_Type_自动生成工具.md docs/接口开发文档/VM_Type/
    - TSDOC_ENABLE_ALIAS_TRANSFORM=true tsdoc-utils --api-json build/api.json --baseUrl $BASE_URL
    - node ../../scripts/kjl.js p manual upload --src public/ --amend ${EXTRA_UPLOAD_ARGS}

publish-internal-docs:
  extends: .publish-docs-internal-base
  variables:
    BASE_URL: '"/idp-sdk-internal/{packageVersion}/"'
    EXTRA_UPLOAD_ARGS: ''
  rules: !reference [.auto_on_tags_rules, rules]

publish-internal-docs-dev:
  extends: .publish-docs-internal-base
  variables:
    BASE_URL: '"/idp-sdk-internal/dev/"'
    EXTRA_UPLOAD_ARGS: '--define-version dev'
  rules: !reference [.auto_on_master_rules, rules]
