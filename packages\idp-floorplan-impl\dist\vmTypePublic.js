var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_KGeomFace2d = injections.packages["@qunhe/math-apaas-api"]["KGeomFace2d"];
    var var_injection_KFace3d = injections.packages["@qunhe/math-apaas-api"]["KFace3d"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Types = {};
    var var_Methods = {};
    var var_getAllWallList = {};
    var var_Wall_Array = {};
    var var_Wall = {};
    var var_ElementId = {};
    var var_stringType = {};
    var var_injection_KGeomFace2d_Array = {};
    var var_GrepFace_Array = {};
    var var_GrepFace = {};
    var var_ElementId_Array = {};
    var var_getAllRoomList = {};
    var var_Room_Array = {};
    var var_Room = {};
    var var_numberType = {};
    var var_getAllOpeningList = {};
    var var_Opening_Array = {};
    var var_Opening = {};
    var var_Design = {};
    var var_Interaction = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_KGeomFace2d, packageName: "@qunhe/math-apaas-api", exportName: "KGeomFace2d" },
        { value: var_injection_KFace3d, packageName: "@qunhe/math-apaas-api", exportName: "KFace3d" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
        "Design": var_Design,
        "Interaction": var_Interaction,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "getAllWallList": var_getAllWallList,
        "getAllRoomList": var_getAllRoomList,
        "getAllOpeningList": var_getAllOpeningList,
    };
    var_getAllWallList.type = BasicType.Function;
    var_getAllWallList.name = "getAllWallList";
    var_getAllWallList.varying = false;
    var_getAllWallList.keepArgsHandle = false;
    var_getAllWallList.args = [];
    var_getAllWallList.return = var_Wall_Array;
    var_Wall_Array.type = BasicType.Array;
    var_Wall_Array.value = var_Wall;
    var_Wall.type = BasicType.Object;
    var_Wall.properties = {
        "id": var_ElementId,
        "profile2d": var_injection_KGeomFace2d_Array,
        "geometry3d": var_GrepFace_Array,
    };
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_injection_KGeomFace2d_Array.type = BasicType.Array;
    var_injection_KGeomFace2d_Array.value = var_injection_KGeomFace2d;
    var_GrepFace_Array.type = BasicType.Array;
    var_GrepFace_Array.value = var_GrepFace;
    var_GrepFace.type = BasicType.Object;
    var_GrepFace.properties = {
        "id": var_stringType,
        "geometry": var_injection_KFace3d,
        "roomIds": var_ElementId_Array,
    };
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_getAllRoomList.type = BasicType.Function;
    var_getAllRoomList.name = "getAllRoomList";
    var_getAllRoomList.varying = false;
    var_getAllRoomList.keepArgsHandle = false;
    var_getAllRoomList.args = [];
    var_getAllRoomList.return = var_Room_Array;
    var_Room_Array.type = BasicType.Array;
    var_Room_Array.value = var_Room;
    var_Room.type = BasicType.Object;
    var_Room.properties = {
        "id": var_ElementId,
        "type": var_numberType,
        "name": var_stringType,
        "profile2d": var_injection_KGeomFace2d_Array,
    };
    var_numberType.type = BasicType.Number;
    var_getAllOpeningList.type = BasicType.Function;
    var_getAllOpeningList.name = "getAllOpeningList";
    var_getAllOpeningList.varying = false;
    var_getAllOpeningList.keepArgsHandle = false;
    var_getAllOpeningList.args = [];
    var_getAllOpeningList.return = var_Opening_Array;
    var_Opening_Array.type = BasicType.Array;
    var_Opening_Array.value = var_Opening;
    var_Opening.type = BasicType.Object;
    var_Opening.properties = {
        "id": var_ElementId,
        "hostId": var_ElementId,
        "profile2d": var_injection_KGeomFace2d_Array,
    };
    var_Design.type = BasicType.Object;
    var_Design.properties = {
    };
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
    };
    
    return var_sourceFile;
};
