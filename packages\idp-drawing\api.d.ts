// TODO: put yor non exported types here
/**
 * @internal
 */
interface EnterOption {
    source: string; // 调用来源
    leftPadding?: number; // 左侧栏预留空间
    hideLeftViewToggler?: boolean; // 是否隐藏左侧栏展开/收回按钮
    topbarTitle?: string; // 顶部栏左侧的标题
    leftViewParentDomId?: string; // 左侧栏替换挂载的根节点
}

declare namespace IDP {

    namespace Platform {
        /**
         * 进入施工图环境
         * @internal
         */
        function enterDrawingMode(option?: EnterOption): Promise<void>;
        /**
         * 退出施工图环境
         * @internal
         */
        function exitDrawingMode(): void;
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
