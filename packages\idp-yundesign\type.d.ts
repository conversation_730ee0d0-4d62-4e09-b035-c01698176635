import { IDP as IDPCommon } from '@qunhe/idp-common';
import { KEuler, KPoint3d, KVector3d } from '@qunhe/math-apaas-api';
import { IDP } from './api';

/**
 * Promise 结果范型
 */
interface Result<T> {
    /**
     * 状态码
     *   - 0：成功
     *   - 1：取消
     *   - 2：失败
     */
    code: number;
    /** 错误信息描述 */
    errorMessage?: string;
    /** 数据 */
    data?: T;
}

/** @vm-type PromiseResult */
type PromiseResult = Result<IDPCommon.DB.Types.ElementId>;

/** @vm-type PromiseResultWithUuid */
type PromiseResultWithUuid = Result<IDPCommon.DB.Types.ElementId & {
    /**
     * 唯一标识uuid
     * 1. 新逻辑只用uuid做对接
     * 2. 未来ElementID.id 也会改成 uuid
     * @internal
     */
    uuid: string
}>;

/**
 * 拖拽创建返回的 Promise 结果类型
 * @vm-type StartDragProductPromiseResult
 */
type StartDragProductPromiseResult = Result<IDPCommon.DB.Types.ElementId[]>;

/**
 * 创建软装家具所需的信息
 */
interface FurnitureCreateInfo {
    /** 商品 ID */
    productId: string;
    /** 位置 */
    position?: KPoint3d;
    /** 旋转 */
    rotation?: KPoint3d;
    /** 缩放 */
    scale?: KVector3d;
}

/**
 * 创建软装组合家具所需的信息
 * @internal
 */
interface FurnitureGroupCreateInfo {
    /** 商品 ID */
    productId: string;
    /** 位置 */
    position?: KPoint3d;
    /** 旋转 */
    rotation?: KEuler;
    /** 大小 */
    size?: KVector3d;
}

/**
 * 软装家具详细信息
 */
interface Furniture {
    /** 设计对象 ID */
    elementId: IDPCommon.DB.Types.ElementId;
    /** 商品 ID */
    productId: string;
    /** 位置 */
    position: KPoint3d;
    /** 旋转 */
    rotation: KPoint3d;
    /** 大小 */
    size: KPoint3d;
    /** 缩放 */
    scale: KPoint3d;
    /**
     * 所属家具组
     * @internal
     */
    groupId?: IDPCommon.DB.Types.ElementId;
    /**
     * 唯一标识uuid
     * 1. 新逻辑只用uuid做对接
     * 2. 未来ElementID.id 也会改成 uuid
     * @internal
     */
    uuid: string;
}

/**
 * 软装组合家具详细信息
 * @internal
 */
interface FurnitureGroup {
    /** 设计对象 ID */
    elementId: IDPCommon.DB.Types.ElementId;
    /** 商品 ID */
    productId: Promise<string | undefined>;
    /** 位置 */
    position: KPoint3d;
    /** 旋转 */
    rotation: KEuler;
    /** 大小 */
    size: KVector3d;
    /** 子家具 */
    children: Furniture[];
    /**
     * 唯一标识uuid
     * 1. 新逻辑只用uuid做对接
     * 2. 未来ElementID.id 也会改成 uuid
     * @internal
     */
    uuid: string;
}

/**
 * 模型踢脚线详细信息
 * @internal
 */
interface ModelMolding {
    /** 设计对象 ID */
    elementId: IDPCommon.DB.Types.ElementId;
    /** 商品 ID */
    productId: string | undefined;
}

/**
 * 创建模型门窗所需信息
 */
interface ModelDoorWindowCreateInfo {
    /** 商品 ID */
    productId: string;
    /** 位置 */
    position: KPoint3d;
    /** 旋转 */
    rotation?: number;
    /** 大小 */
    size?: KPoint3d;
    /**
     * 把手位置
     * [handleFlipped=false] 把手在推门方向的右边, 向左开门
     * [handleFlipped=true] 把手在推门方向的左边, 向右开门
     */
    handleFlipped?: boolean;
    /**
     * 开门方向
     * [facingFlipped=false] 推门
     * [facingFlipped=true] 拉门
     */
    facingFlipped?: boolean;
}

/**
 * 模型门窗详细信息
 */
interface ModelDoorWindow {
    /** 设计对象 ID */
    elementId: IDPCommon.DB.Types.ElementId;
    /** 商品 ID */
    productId: string;
    /** 门窗类型 */
    type: IDP.DB.Types.ModelDoorWindowType;
    /** 位置 */
    position: KPoint3d;
    /** 旋转 */
    rotation: number;
    /** 大小 */
    size: KPoint3d;
    /** 缩放 */
    scale: KPoint3d;
    /**
     * 把手位置
     * [handleFlipped=false] 把手在推门方向的右边, 向左开门
     * [handleFlipped=true ] 把手在推门方向的左边, 向右开门
     */
    handleFlipped: boolean;
    /**
     * 开门方向
     * [facingFlipped=false] 推门
     * [facingFlipped=true] 拉门
     */
    facingFlipped: boolean;
    /** 宿主（墙）id */
    hostIds: string[];
    /** 洞id */
    openingId: string;
}

/**
 * 云图工具左侧栏content挂载到指定容器后，当前库类型（素材库｜企业库｜个人库）
 */
type LeftPanelProductLibrary = 'publicLibrary' |  'enterpriseLibrary' | 'personalLibrary';

export {
    StartDragProductPromiseResult,
    Furniture,
    FurnitureCreateInfo,
    FurnitureGroup,
    FurnitureGroupCreateInfo,
    PromiseResult,
    ModelMolding,
    ModelDoorWindowCreateInfo,
    ModelDoorWindow,
    LeftPanelProductLibrary,
    PromiseResultWithUuid
};

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
