var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Payment = {};
    var var_showPaymentDialog = {};
    var var_PaymentDialogProps = {};
    var var_stringType = {};
    var var_PaymentDialogProps_isvOrderInfo_objectLiteral = {};
    var var_numberType = {};
    var var_PaymentDialogProps_isvOrderInfo_duration_objectLiteral = {};
    var var_undefinedType = {};
    var var_PaymentStatus = {};
    var var_Types = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Payment": var_Payment,
    };
    var_Payment.type = BasicType.Object;
    var_Payment.properties = {
        "showPaymentDialog": var_showPaymentDialog,
        "PaymentStatus": var_PaymentStatus,
        "Types": var_Types,
    };
    var_showPaymentDialog.type = BasicType.Function;
    var_showPaymentDialog.name = "showPaymentDialog";
    var_showPaymentDialog.varying = false;
    var_showPaymentDialog.keepArgsHandle = false;
    var_showPaymentDialog.args = [var_PaymentDialogProps];
    var_showPaymentDialog.return = var_undefinedType;
    var_PaymentDialogProps.type = BasicType.Object;
    var_PaymentDialogProps.properties = {
        "isvOrderNo": var_stringType,
        "isvOrderInfo": var_PaymentDialogProps_isvOrderInfo_objectLiteral,
    };
    var_stringType.type = BasicType.String;
    var_PaymentDialogProps_isvOrderInfo_objectLiteral.type = BasicType.Object;
    var_PaymentDialogProps_isvOrderInfo_objectLiteral.properties = {
        "skuCode": var_stringType,
        "num": var_numberType,
        "duration": var_PaymentDialogProps_isvOrderInfo_duration_objectLiteral,
    };
    var_numberType.type = BasicType.Number;
    var_PaymentDialogProps_isvOrderInfo_duration_objectLiteral.type = BasicType.Object;
    var_PaymentDialogProps_isvOrderInfo_duration_objectLiteral.properties = {
        "year": var_numberType,
        "month": var_numberType,
        "day": var_numberType,
    };
    var_undefinedType.type = BasicType.Undefined;
    var_PaymentStatus.type = BasicType.Object;
    var_PaymentStatus.properties = {
        "UNPAID": var_stringType,
        "PAID": var_stringType,
        "FAILED": var_stringType,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
    };
    
    return var_sourceFile;
};
