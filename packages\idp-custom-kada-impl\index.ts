import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { UnknownType } from './src';

const types = {
    UnknownType,
}

export const getVMBindingType = once(() => {
    return createVMBindingType({
        types,
    });
});
export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal({
        types,
    });
});
