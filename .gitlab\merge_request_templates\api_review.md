## API 评审 Merge Request

### 📋 基本信息

**API 变更类型：**
- [ ] API 预审（设计阶段）
- [ ] API 终审（开发完成阶段）

**变更类型：**
- [ ] 新增 API
- [ ] 修改已有 API
- [ ] 删除 API
- [ ] 文档更新

**影响的服务：**
- [ ] diymodeldw-service
- [ ] furniture-design-service
- [ ] 其他：__________

### 📄 变更描述

**变更背景：**
> 请描述为什么需要这次 API 变更，业务场景是什么

**主要变更内容：**
> 详细描述本次 API 的主要变更点

**设计决策：**
> 解释重要的设计决策和考虑因素

### 🔍 需要重点关注的点

**安全性考虑：**
> 本次变更涉及的安全性问题和解决方案

**向后兼容性：**
> 评估对现有客户端的影响

**性能影响：**
> 预期的性能影响和优化方案

### ✅ 自检清单

**提交前检查：**
- [ ] OpenAPI 规范语法正确
- [ ] 所有 API 操作都有描述和摘要
- [ ] 错误响应格式符合标准
- [ ] 版本号遵循语义化版本规范
- [ ] 本地运行 `spectral lint` 通过
- [ ] 本地生成文档预览无误

**API 设计检查：**
- [ ] 遵循 RESTful 设计原则
- [ ] 路径和参数命名清晰一致
- [ ] 安全性考虑充分
- [ ] 错误处理完整
- [ ] 文档描述准确详细

### 📚 相关资源

**文档链接：**
- [API 评审流程文档](../docs/api-review-process.md)
- [快速开始指南](../docs/quick-start-guide.md)

**相关 Issue/MR：**
> 链接到相关的 Issue 或其他 MR

### 🏷️ 标签设置

请为此 MR 添加适当的标签：
- `API-Review::Pending-Pre-Review` - 等待预审
- `API-Review::Pending-Final-Review` - 等待终审

### 👥 评审者

请 @mention 相关的评审人员：
- @后端API小组
- @相关业务开发人员

### 📖 文档预览

CI 流水线完成后，评审者可以通过以下方式查看 API 文档预览：

**方法1（推荐）：**
- 查看 CI 自动添加的评论，按照说明操作

**方法2：**
1. 点击上方的 **Pipelines** 标签
2. 点击最新的流水线
3. 找到 `pages` job，点击 **Browse** 或下载 **artifacts**
4. 打开 `index.html` 查看文档总览

---

**📝 评审者注意事项：**

1. **文档预览**：评审者可以在 CI 流水线的 `pages` job artifacts 中下载生成的 API 文档进行查看
2. **评审标签**：请在评审完成后更新 MR 标签为 `API-Review::Approved` 或 `API-Review::Changes-Requested`
3. **最终确认**：评审通过后，请手动触发 `api_review_mr_checkpoint` job 