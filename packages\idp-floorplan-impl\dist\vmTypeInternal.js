var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_KBoundedCurve2d = injections.packages["@qunhe/math-apaas-api"]["KBoundedCurve2d"];
    var var_injection_KGeomFace2d = injections.packages["@qunhe/math-apaas-api"]["KGeomFace2d"];
    var var_injection_KFace3d = injections.packages["@qunhe/math-apaas-api"]["KFace3d"];
    var var_injection_KBoundedCurve3d = injections.packages["@qunhe/math-apaas-api"]["KBoundedCurve3d"];
    var var_injection_KPoint2d = injections.packages["@qunhe/math-apaas-api"]["KPoint2d"];
    var var_injection_FloorplanElement = injections.types["FloorplanElement"];
    var var_injection_FloorplanDocumentBatchUpdateRequest = injections.types["FloorplanDocumentBatchUpdateRequest"];
    var var_injection_KBoundingBox2d = injections.packages["@qunhe/math-apaas-api"]["KBoundingBox2d"];
    var var_injection_KVector2d = injections.packages["@qunhe/math-apaas-api"]["KVector2d"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Types = {};
    var var_CourseType = {};
    var var_stringType = {};
    var var_StructuralWallUsage = {};
    var var_RoomSeparatorType = {};
    var var_Floorplan = {};
    var var_DocumentView = {};
    var var_ElementJoint = {};
    var var_OpeningType = {};
    var var_WallType = {};
    var var_OffgroundType = {};
    var var_OpeningSourceType = {};
    var var_ElementType = {};
    var var_RoomType = {};
    var var_numberType = {};
    var var_Methods = {};
    var var_getAllWallList = {};
    var var_Wall_Array = {};
    var var_Wall = {};
    var var_ElementId = {};
    var var_injection_KGeomFace2d_Array = {};
    var var_GrepFace_Array = {};
    var var_GrepFace = {};
    var var_ElementId_Array = {};
    var var_getAllRoomList = {};
    var var_Room_Array = {};
    var var_Room = {};
    var var_booleanType = {};
    var var_getAllOpeningList = {};
    var var_Opening_Array = {};
    var var_Opening = {};
    var var_injection_KBoundedCurve3d_Array = {};
    var var_getAllSquareColumnList = {};
    var var_SquareColumn_Array = {};
    var var_SquareColumn = {};
    var var_getAllBeamList = {};
    var var_Beam_Array = {};
    var var_Beam = {};
    var var_getAllFloorList = {};
    var var_Floor_Array = {};
    var var_Floor = {};
    var var_getAllDoorOpeningList = {};
    var var_DoorOpening_Array = {};
    var var_DoorOpening = {};
    var var_getAllWindowOpeningList = {};
    var var_WindowOpening_Array = {};
    var var_WindowOpening = {};
    var var_getAllRoomSeparatorList = {};
    var var_RoomSeparator_Array = {};
    var var_RoomSeparator = {};
    var var_getAllFloorOpeningList = {};
    var var_FloorOpening_Array = {};
    var var_FloorOpening = {};
    var var_getCurrentLevelHeight = {};
    var var_updateRoomName = {};
    var var_undefinedType = {};
    var var_updateRoomType = {};
    var var_updateRoomRoofVisible = {};
    var var___deprecated__createConstructionLayer = {};
    var var_CreateConstructionLayerParam = {};
    var var_RoomConstructionLayerRequest_Array = {};
    var var_RoomConstructionLayerRequest = {};
    var var_AreaConstructionLayerRequest_Array = {};
    var var_AreaConstructionLayerRequest = {};
    var var_HorizontalConstructionCourseRequest_Array = {};
    var var_HorizontalConstructionCourseRequest = {};
    var var_VerticalConstructionLayerRequest_Array = {};
    var var_VerticalConstructionLayerRequest = {};
    var var_VerticalConstructionCourseRequest_Array = {};
    var var_VerticalConstructionCourseRequest = {};
    var var___deprecated__createConstructionLayer_objectLiteral_Promise = {};
    var var___deprecated__createConstructionLayer_objectLiteral_Promise_then = {};
    var var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve = {};
    var var___deprecated__createConstructionLayer_objectLiteral = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_getSpatialDataAsync = {};
    var var_stringType_Promise = {};
    var var_stringType_Promise_then = {};
    var var_stringType_Promise_then_onresolve = {};
    var var_saveSpatialDataAsync = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_deleteConstructionLayerBySourceFaceIds = {};
    var var_stringType_Array = {};
    var var_getFloorplanDocument = {};
    var var_FloorplanDocument_Promise = {};
    var var_FloorplanDocument_Promise_then = {};
    var var_FloorplanDocument_Promise_then_onresolve = {};
    var var_FloorplanDocument = {};
    var var_injection_FloorplanElement_Array = {};
    var var_FloorplanConfig = {};
    var var_Compass = {};
    var var_updateFloorplanDocument = {};
    var var_FloorplanDocumentBatchUpdateResponse_Promise = {};
    var var_FloorplanDocumentBatchUpdateResponse_Promise_then = {};
    var var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve = {};
    var var_FloorplanDocumentBatchUpdateResponse = {};
    var var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral_Array = {};
    var var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral = {};
    var var_Design = {};
    var var_getDesignConfig = {};
    var var_getDesignConfig_objectLiteral = {};
    var var_Interaction = {};
    var var_triggerCadImport = {};
    var var_getCadInfo = {};
    var var_FloorPlanCadInfo = {};
    var var_FloorPlanCadInfo_data_objectLiteral = {};
    var var_FloorPlanLayerInfo_Array = {};
    var var_FloorPlanLayerInfo = {};
    var var_changeCadLayerVisible = {};
    var var_startDrawWall = {};
    var var_startDrawColumn = {};
    var var_startDrawFlue = {};
    var var_startDrawBeam = {};
    var var_startDrawDoorOpening = {};
    var var_startDrawWindowOpening = {};
    var var_startDrawNiche = {};
    var var_startDrawRoof = {};
    var var_Integration = {};
    var var_CADImport = {};
    var var_loadFloorplanFromCad = {};
    var var_CadImportParams = {};
    var var_CadK2dInfo_Array = {};
    var var_CadK2dInfo = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_KBoundedCurve2d, packageName: "@qunhe/math-apaas-api", exportName: "KBoundedCurve2d" },
        { value: var_injection_KGeomFace2d, packageName: "@qunhe/math-apaas-api", exportName: "KGeomFace2d" },
        { value: var_injection_KFace3d, packageName: "@qunhe/math-apaas-api", exportName: "KFace3d" },
        { value: var_injection_KBoundedCurve3d, packageName: "@qunhe/math-apaas-api", exportName: "KBoundedCurve3d" },
        { value: var_injection_KPoint2d, packageName: "@qunhe/math-apaas-api", exportName: "KPoint2d" },
        { value: var_injection_FloorplanElement, exportName: "FloorplanElement" },
        { value: var_injection_FloorplanDocumentBatchUpdateRequest, exportName: "FloorplanDocumentBatchUpdateRequest" },
        { value: var_injection_KBoundingBox2d, packageName: "@qunhe/math-apaas-api", exportName: "KBoundingBox2d" },
        { value: var_injection_KVector2d, packageName: "@qunhe/math-apaas-api", exportName: "KVector2d" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
        "Design": var_Design,
        "Interaction": var_Interaction,
        "Integration": var_Integration,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
        "CourseType": var_CourseType,
        "StructuralWallUsage": var_StructuralWallUsage,
        "RoomSeparatorType": var_RoomSeparatorType,
        "Floorplan": var_Floorplan,
    };
    var_CourseType.type = BasicType.Object;
    var_CourseType.properties = {
        "OTHER": var_stringType,
        "SURFACE": var_stringType,
        "COMBINED": var_stringType,
        "TROWELING": var_stringType,
        "ISOLATING": var_stringType,
        "DAMP": var_stringType,
        "FILLER": var_stringType,
        "INSULATION": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_StructuralWallUsage.type = BasicType.Object;
    var_StructuralWallUsage.properties = {
        "NonBearing": var_stringType,
        "Bearing": var_stringType,
    };
    var_RoomSeparatorType.type = BasicType.Object;
    var_RoomSeparatorType.properties = {
        "OnlyFloor": var_stringType,
        "AllTheWay": var_stringType,
    };
    var_Floorplan.type = BasicType.Object;
    var_Floorplan.properties = {
        "DocumentView": var_DocumentView,
        "ElementJoint": var_ElementJoint,
        "OpeningType": var_OpeningType,
        "WallType": var_WallType,
        "OffgroundType": var_OffgroundType,
        "OpeningSourceType": var_OpeningSourceType,
        "ElementType": var_ElementType,
        "RoomType": var_RoomType,
    };
    var_DocumentView.type = BasicType.Object;
    var_DocumentView.properties = {
        "BASIC": var_stringType,
    };
    var_ElementJoint.type = BasicType.Object;
    var_ElementJoint.properties = {
        "OBLIQUE": var_stringType,
        "ABUTMENT": var_stringType,
    };
    var_OpeningType.type = BasicType.Object;
    var_OpeningType.properties = {
        "OPENING": var_stringType,
        "DOOR": var_stringType,
        "WINDOW": var_stringType,
        "NICHE": var_stringType,
    };
    var_WallType.type = BasicType.Object;
    var_WallType.properties = {
        "NORMAL": var_stringType,
        "PARTITION": var_stringType,
    };
    var_OffgroundType.type = BasicType.Object;
    var_OffgroundType.properties = {
        "CEILING": var_stringType,
        "FLOOR": var_stringType,
    };
    var_OpeningSourceType.type = BasicType.Object;
    var_OpeningSourceType.properties = {
        "MODEL_DOOR_WINDOW": var_stringType,
        "CUSTOM_DOOR_WINDOW": var_stringType,
        "FLOOR_PLAN": var_stringType,
    };
    var_ElementType.type = BasicType.Object;
    var_ElementType.properties = {
        "Wall": var_stringType,
        "Beam": var_stringType,
        "Room": var_stringType,
        "Pillar": var_stringType,
        "Floor": var_stringType,
        "Opening": var_stringType,
        "Chimney": var_stringType,
        "FloorOpening": var_stringType,
        "RoomSeparator": var_stringType,
    };
    var_RoomType.type = BasicType.Object;
    var_RoomType.properties = {
        "UNSPECIFIED": var_numberType,
        "COMPLETE_SET": var_numberType,
        "PARLOR": var_numberType,
        "CORRIDOR": var_numberType,
        "DINING_ROOM": var_numberType,
        "MASTER_BEDROOM": var_numberType,
        "CHILDREN_ROOM": var_numberType,
        "STUDY": var_numberType,
        "KITCHEN": var_numberType,
        "BATHROOM": var_numberType,
        "MULTI_PURPOSE_ROOM": var_numberType,
        "SECONDARY_BEDROOM": var_numberType,
        "GUEST_BEDROOM": var_numberType,
        "ELDERLY_ROOM": var_numberType,
        "BALCONY": var_numberType,
        "ENTRANCE": var_numberType,
        "BEDROOM": var_numberType,
        "HALLWAY": var_numberType,
        "CUSTOM": var_numberType,
        "STORAGE_ROOM": var_numberType,
        "WALK_IN_CLOSET": var_numberType,
        "EXTERIOR": var_numberType,
        "LIVING_ROOM": var_numberType,
        "LIVING_DINING_ROOM": var_numberType,
        "TERRACE": var_numberType,
        "MOVIE_ROOM": var_numberType,
        "ENTRANCE_GARDEN": var_numberType,
        "HALLWAY_ENTRANCE": var_numberType,
        "LAUNDRY_ROOM": var_numberType,
        "OFFICE": var_numberType,
        "BASEMENT": var_numberType,
        "GARAGE": var_numberType,
        "UNNAMED": var_numberType,
        "OUTDOOR": var_numberType,
        "NANNY_ROOM": var_numberType,
        "GYM": var_numberType,
        "FLOWER_ROOM": var_numberType,
        "STAIRWELL": var_numberType,
        "BALCONY_WITH_VIEW": var_numberType,
        "LIVING_BALCONY": var_numberType,
        "DEVICE_PLATFORM": var_numberType,
        "MASTER_BATHROOM": var_numberType,
        "SECONDARY_BATHROOM": var_numberType,
        "CHINESE_KITCHEN": var_numberType,
        "WESTERN_KITCHEN": var_numberType,
        "SHOPPING_MALL_COUNTER": var_numberType,
        "SPECIALTY_STORE": var_numberType,
        "SHOPPING_MALL_STORE": var_numberType,
        "MATERNITY_AND_BABY_STORE": var_numberType,
        "SUPERMARKET": var_numberType,
        "JEWELRY_STORE": var_numberType,
        "CLOTHING_STORE": var_numberType,
        "BEAUTY_SALON": var_numberType,
        "DIGITAL_STORE": var_numberType,
        "FRUIT_SHOP": var_numberType,
        "BRIDAL_SHOP": var_numberType,
        "TOBACCO_SHOP": var_numberType,
        "SALES_OFFICE": var_numberType,
        "FASHION_TOY_STORE": var_numberType,
        "BOOKSTORE": var_numberType,
        "RESTAURANT": var_numberType,
        "BACK_KITCHEN": var_numberType,
        "CAFE": var_numberType,
        "BEVERAGE_SHOP": var_numberType,
        "DESSERT_SHOP": var_numberType,
        "HOT_POT_RESTAURANT": var_numberType,
        "BBQ_RESTAURANT": var_numberType,
        "BAKERY": var_numberType,
        "TEA_HOUSE": var_numberType,
        "PRIVATE_DINING_ROOM": var_numberType,
        "FAST_FOOD_RESTAURANTS": var_numberType,
        "EXHIBITION_HALL": var_numberType,
        "BOOTH": var_numberType,
        "SHOW_WINDOW": var_numberType,
        "KTV": var_numberType,
        "INTERNET_CAFE": var_numberType,
        "BAR": var_numberType,
        "CINEMA": var_numberType,
        "PLAYGROUND": var_numberType,
        "ENTERTAINMENT_ROOM": var_numberType,
        "GUEST_ROOM": var_numberType,
        "HOTEL_RECEPTION": var_numberType,
        "BANQUET_HALL": var_numberType,
        "HOTEL_TEA_ROOM": var_numberType,
        "LEISURE_SPACE": var_numberType,
        "LINEN_ROOM": var_numberType,
        "SPA": var_numberType,
        "CHESS_AND_CARD_ROOM": var_numberType,
        "MANAGER_OFFICE": var_numberType,
        "BUREAU": var_numberType,
        "OFFICE_AREA": var_numberType,
        "NEGOTIATION_ROOM": var_numberType,
        "MEETING_ROOM": var_numberType,
        "RECEPTION_AREA": var_numberType,
        "PANTRY": var_numberType,
        "RECREATION_AREA": var_numberType,
        "LOBBY_RECEPTION": var_numberType,
        "SPORTS_AND_FITNESS": var_numberType,
        "LABORATORY": var_numberType,
        "CLASSROOM": var_numberType,
        "HOSPITAL_OUTPATIENT": var_numberType,
        "SWIMMING_FACILITY": var_numberType,
        "ARENA": var_numberType,
        "LIBRARY": var_numberType,
        "TRAINING_ROOM": var_numberType,
        "THEATER": var_numberType,
        "READING_ROOM": var_numberType,
        "AISLE": var_numberType,
        "ELEVATOR_ROOM": var_numberType,
        "PATIO": var_numberType,
        "TOILET": var_numberType,
        "STOREHOUSE": var_numberType,
        "EQUIPMENT_ROOM": var_numberType,
        "MACHINE_ROOM": var_numberType,
        "OTHER": var_numberType,
    };
    var_numberType.type = BasicType.Number;
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "getAllWallList": var_getAllWallList,
        "getAllRoomList": var_getAllRoomList,
        "getAllOpeningList": var_getAllOpeningList,
        "getAllSquareColumnList": var_getAllSquareColumnList,
        "getAllBeamList": var_getAllBeamList,
        "getAllFloorList": var_getAllFloorList,
        "getAllDoorOpeningList": var_getAllDoorOpeningList,
        "getAllWindowOpeningList": var_getAllWindowOpeningList,
        "getAllRoomSeparatorList": var_getAllRoomSeparatorList,
        "getAllFloorOpeningList": var_getAllFloorOpeningList,
        "getCurrentLevelHeight": var_getCurrentLevelHeight,
        "updateRoomName": var_updateRoomName,
        "updateRoomType": var_updateRoomType,
        "updateRoomRoofVisible": var_updateRoomRoofVisible,
        "__deprecated__createConstructionLayer": var___deprecated__createConstructionLayer,
        "getSpatialDataAsync": var_getSpatialDataAsync,
        "saveSpatialDataAsync": var_saveSpatialDataAsync,
        "deleteConstructionLayerBySourceFaceIds": var_deleteConstructionLayerBySourceFaceIds,
        "getFloorplanDocument": var_getFloorplanDocument,
        "updateFloorplanDocument": var_updateFloorplanDocument,
    };
    var_getAllWallList.type = BasicType.Function;
    var_getAllWallList.name = "getAllWallList";
    var_getAllWallList.varying = false;
    var_getAllWallList.keepArgsHandle = false;
    var_getAllWallList.args = [];
    var_getAllWallList.return = var_Wall_Array;
    var_Wall_Array.type = BasicType.Array;
    var_Wall_Array.value = var_Wall;
    var_Wall.type = BasicType.Object;
    var_Wall.properties = {
        "id": var_ElementId,
        "location": var_injection_KBoundedCurve2d,
        "thickness": var_numberType,
        "height": var_numberType,
        "structuralUsage": var_stringType,
        "profile2d": var_injection_KGeomFace2d_Array,
        "geometry3d": var_GrepFace_Array,
    };
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_injection_KGeomFace2d_Array.type = BasicType.Array;
    var_injection_KGeomFace2d_Array.value = var_injection_KGeomFace2d;
    var_GrepFace_Array.type = BasicType.Array;
    var_GrepFace_Array.value = var_GrepFace;
    var_GrepFace.type = BasicType.Object;
    var_GrepFace.properties = {
        "id": var_stringType,
        "geometry": var_injection_KFace3d,
        "roomIds": var_ElementId_Array,
    };
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_getAllRoomList.type = BasicType.Function;
    var_getAllRoomList.name = "getAllRoomList";
    var_getAllRoomList.varying = false;
    var_getAllRoomList.keepArgsHandle = false;
    var_getAllRoomList.args = [];
    var_getAllRoomList.return = var_Room_Array;
    var_Room_Array.type = BasicType.Array;
    var_Room_Array.value = var_Room;
    var_Room.type = BasicType.Object;
    var_Room.properties = {
        "id": var_ElementId,
        "type": var_numberType,
        "name": var_stringType,
        "profile2d": var_injection_KGeomFace2d_Array,
        "hasRoof": var_booleanType,
    };
    var_booleanType.type = BasicType.Boolean;
    var_getAllOpeningList.type = BasicType.Function;
    var_getAllOpeningList.name = "getAllOpeningList";
    var_getAllOpeningList.varying = false;
    var_getAllOpeningList.keepArgsHandle = false;
    var_getAllOpeningList.args = [];
    var_getAllOpeningList.return = var_Opening_Array;
    var_Opening_Array.type = BasicType.Array;
    var_Opening_Array.value = var_Opening;
    var_Opening.type = BasicType.Object;
    var_Opening.properties = {
        "id": var_ElementId,
        "hostId": var_ElementId,
        "contour": var_injection_KBoundedCurve3d_Array,
        "profile2d": var_injection_KGeomFace2d_Array,
        "archHeight": var_numberType,
    };
    var_injection_KBoundedCurve3d_Array.type = BasicType.Array;
    var_injection_KBoundedCurve3d_Array.value = var_injection_KBoundedCurve3d;
    var_getAllSquareColumnList.type = BasicType.Function;
    var_getAllSquareColumnList.name = "getAllSquareColumnList";
    var_getAllSquareColumnList.varying = false;
    var_getAllSquareColumnList.keepArgsHandle = false;
    var_getAllSquareColumnList.args = [];
    var_getAllSquareColumnList.return = var_SquareColumn_Array;
    var_SquareColumn_Array.type = BasicType.Array;
    var_SquareColumn_Array.value = var_SquareColumn;
    var_SquareColumn.type = BasicType.Object;
    var_SquareColumn.properties = {
        "id": var_ElementId,
        "width": var_numberType,
        "depth": var_numberType,
        "height": var_numberType,
    };
    var_getAllBeamList.type = BasicType.Function;
    var_getAllBeamList.name = "getAllBeamList";
    var_getAllBeamList.varying = false;
    var_getAllBeamList.keepArgsHandle = false;
    var_getAllBeamList.args = [];
    var_getAllBeamList.return = var_Beam_Array;
    var_Beam_Array.type = BasicType.Array;
    var_Beam_Array.value = var_Beam;
    var_Beam.type = BasicType.Object;
    var_Beam.properties = {
        "id": var_ElementId,
        "location": var_injection_KBoundedCurve2d,
        "thickness": var_numberType,
        "height": var_numberType,
        "topElevation": var_numberType,
    };
    var_getAllFloorList.type = BasicType.Function;
    var_getAllFloorList.name = "getAllFloorList";
    var_getAllFloorList.varying = false;
    var_getAllFloorList.keepArgsHandle = false;
    var_getAllFloorList.args = [];
    var_getAllFloorList.return = var_Floor_Array;
    var_Floor_Array.type = BasicType.Array;
    var_Floor_Array.value = var_Floor;
    var_Floor.type = BasicType.Object;
    var_Floor.properties = {
        "id": var_ElementId,
        "profile2d": var_injection_KGeomFace2d_Array,
    };
    var_getAllDoorOpeningList.type = BasicType.Function;
    var_getAllDoorOpeningList.name = "getAllDoorOpeningList";
    var_getAllDoorOpeningList.varying = false;
    var_getAllDoorOpeningList.keepArgsHandle = false;
    var_getAllDoorOpeningList.args = [];
    var_getAllDoorOpeningList.return = var_DoorOpening_Array;
    var_DoorOpening_Array.type = BasicType.Array;
    var_DoorOpening_Array.value = var_DoorOpening;
    var_DoorOpening.type = BasicType.Object;
    var_DoorOpening.properties = {
        "id": var_ElementId,
        "subElements": var_ElementId_Array,
    };
    var_getAllWindowOpeningList.type = BasicType.Function;
    var_getAllWindowOpeningList.name = "getAllWindowOpeningList";
    var_getAllWindowOpeningList.varying = false;
    var_getAllWindowOpeningList.keepArgsHandle = false;
    var_getAllWindowOpeningList.args = [];
    var_getAllWindowOpeningList.return = var_WindowOpening_Array;
    var_WindowOpening_Array.type = BasicType.Array;
    var_WindowOpening_Array.value = var_WindowOpening;
    var_WindowOpening.type = BasicType.Object;
    var_WindowOpening.properties = {
        "id": var_ElementId,
        "subElements": var_ElementId_Array,
    };
    var_getAllRoomSeparatorList.type = BasicType.Function;
    var_getAllRoomSeparatorList.name = "getAllRoomSeparatorList";
    var_getAllRoomSeparatorList.varying = false;
    var_getAllRoomSeparatorList.keepArgsHandle = false;
    var_getAllRoomSeparatorList.args = [];
    var_getAllRoomSeparatorList.return = var_RoomSeparator_Array;
    var_RoomSeparator_Array.type = BasicType.Array;
    var_RoomSeparator_Array.value = var_RoomSeparator;
    var_RoomSeparator.type = BasicType.Object;
    var_RoomSeparator.properties = {
        "id": var_ElementId,
        "type": var_stringType,
    };
    var_getAllFloorOpeningList.type = BasicType.Function;
    var_getAllFloorOpeningList.name = "getAllFloorOpeningList";
    var_getAllFloorOpeningList.varying = false;
    var_getAllFloorOpeningList.keepArgsHandle = false;
    var_getAllFloorOpeningList.args = [];
    var_getAllFloorOpeningList.return = var_FloorOpening_Array;
    var_FloorOpening_Array.type = BasicType.Array;
    var_FloorOpening_Array.value = var_FloorOpening;
    var_FloorOpening.type = BasicType.Object;
    var_FloorOpening.properties = {
        "id": var_ElementId,
        "depth": var_numberType,
    };
    var_getCurrentLevelHeight.type = BasicType.Function;
    var_getCurrentLevelHeight.name = "getCurrentLevelHeight";
    var_getCurrentLevelHeight.varying = false;
    var_getCurrentLevelHeight.keepArgsHandle = false;
    var_getCurrentLevelHeight.args = [];
    var_getCurrentLevelHeight.return = var_numberType;
    var_updateRoomName.type = BasicType.Function;
    var_updateRoomName.name = "updateRoomName";
    var_updateRoomName.varying = false;
    var_updateRoomName.keepArgsHandle = false;
    var_updateRoomName.args = [var_ElementId, var_stringType];
    var_updateRoomName.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_updateRoomType.type = BasicType.Function;
    var_updateRoomType.name = "updateRoomType";
    var_updateRoomType.varying = false;
    var_updateRoomType.keepArgsHandle = false;
    var_updateRoomType.args = [var_ElementId, var_numberType];
    var_updateRoomType.return = var_undefinedType;
    var_updateRoomRoofVisible.type = BasicType.Function;
    var_updateRoomRoofVisible.name = "updateRoomRoofVisible";
    var_updateRoomRoofVisible.varying = false;
    var_updateRoomRoofVisible.keepArgsHandle = false;
    var_updateRoomRoofVisible.args = [var_ElementId, var_booleanType];
    var_updateRoomRoofVisible.return = var_undefinedType;
    var___deprecated__createConstructionLayer.type = BasicType.Function;
    var___deprecated__createConstructionLayer.name = "__deprecated__createConstructionLayer";
    var___deprecated__createConstructionLayer.varying = false;
    var___deprecated__createConstructionLayer.keepArgsHandle = false;
    var___deprecated__createConstructionLayer.args = [var_CreateConstructionLayerParam];
    var___deprecated__createConstructionLayer.return = var___deprecated__createConstructionLayer_objectLiteral_Promise;
    var_CreateConstructionLayerParam.type = BasicType.Object;
    var_CreateConstructionLayerParam.properties = {
        "requestList": var_RoomConstructionLayerRequest_Array,
    };
    var_RoomConstructionLayerRequest_Array.type = BasicType.Array;
    var_RoomConstructionLayerRequest_Array.value = var_RoomConstructionLayerRequest;
    var_RoomConstructionLayerRequest.type = BasicType.Object;
    var_RoomConstructionLayerRequest.properties = {
        "roomId": var_stringType,
        "baseline": var_numberType,
        "roomAreaLayers": var_AreaConstructionLayerRequest_Array,
        "roomRootCourses": var_HorizontalConstructionCourseRequest_Array,
        "verticalLayers": var_VerticalConstructionLayerRequest_Array,
    };
    var_AreaConstructionLayerRequest_Array.type = BasicType.Array;
    var_AreaConstructionLayerRequest_Array.value = var_AreaConstructionLayerRequest;
    var_AreaConstructionLayerRequest.type = BasicType.Object;
    var_AreaConstructionLayerRequest.properties = {
        "area": var_injection_KGeomFace2d,
        "relativeHeight": var_numberType,
        "areaCourses": var_HorizontalConstructionCourseRequest_Array,
    };
    var_HorizontalConstructionCourseRequest_Array.type = BasicType.Array;
    var_HorizontalConstructionCourseRequest_Array.value = var_HorizontalConstructionCourseRequest;
    var_HorizontalConstructionCourseRequest.type = BasicType.Object;
    var_HorizontalConstructionCourseRequest.properties = {
        "courseType": var_stringType,
        "confId": var_stringType,
        "thickness": var_numberType,
        "relativeLine": var_numberType,
    };
    var_VerticalConstructionLayerRequest_Array.type = BasicType.Array;
    var_VerticalConstructionLayerRequest_Array.value = var_VerticalConstructionLayerRequest;
    var_VerticalConstructionLayerRequest.type = BasicType.Object;
    var_VerticalConstructionLayerRequest.properties = {
        "faceId": var_stringType,
        "splitPoint": var_injection_KPoint2d,
        "pointInLayer": var_injection_KPoint2d,
        "verticalCourses": var_VerticalConstructionCourseRequest_Array,
    };
    var_VerticalConstructionCourseRequest_Array.type = BasicType.Array;
    var_VerticalConstructionCourseRequest_Array.value = var_VerticalConstructionCourseRequest;
    var_VerticalConstructionCourseRequest.type = BasicType.Object;
    var_VerticalConstructionCourseRequest.properties = {
        "courseType": var_stringType,
        "confId": var_stringType,
        "height": var_numberType,
    };
    var___deprecated__createConstructionLayer_objectLiteral_Promise.type = BasicType.Object;
    var___deprecated__createConstructionLayer_objectLiteral_Promise.properties = {
        "then": var___deprecated__createConstructionLayer_objectLiteral_Promise_then,
        "catch": var_Promise_catch,
    };
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then.type = BasicType.Function;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then.name = "";
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then.varying = false;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then.keepArgsHandle = true;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then.args = [var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve, var_Promise_then_onreject];
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then.return = var_undefinedType;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve.type = BasicType.Function;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve.name = "";
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve.varying = false;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve.keepArgsHandle = false;
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve.args = [var___deprecated__createConstructionLayer_objectLiteral];
    var___deprecated__createConstructionLayer_objectLiteral_Promise_then_onresolve.return = var_undefinedType;
    var___deprecated__createConstructionLayer_objectLiteral.type = BasicType.Object;
    var___deprecated__createConstructionLayer_objectLiteral.properties = {
        "result": var_booleanType,
        "errorCode": var_stringType,
        "errorInfo": var_stringType,
    };
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_getSpatialDataAsync.type = BasicType.Function;
    var_getSpatialDataAsync.name = "getSpatialDataAsync";
    var_getSpatialDataAsync.varying = false;
    var_getSpatialDataAsync.keepArgsHandle = false;
    var_getSpatialDataAsync.args = [];
    var_getSpatialDataAsync.return = var_stringType_Promise;
    var_stringType_Promise.type = BasicType.Object;
    var_stringType_Promise.properties = {
        "then": var_stringType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_stringType_Promise_then.type = BasicType.Function;
    var_stringType_Promise_then.name = "";
    var_stringType_Promise_then.varying = false;
    var_stringType_Promise_then.keepArgsHandle = true;
    var_stringType_Promise_then.args = [var_stringType_Promise_then_onresolve, var_Promise_then_onreject];
    var_stringType_Promise_then.return = var_undefinedType;
    var_stringType_Promise_then_onresolve.type = BasicType.Function;
    var_stringType_Promise_then_onresolve.name = "";
    var_stringType_Promise_then_onresolve.varying = false;
    var_stringType_Promise_then_onresolve.keepArgsHandle = false;
    var_stringType_Promise_then_onresolve.args = [var_stringType];
    var_stringType_Promise_then_onresolve.return = var_undefinedType;
    var_saveSpatialDataAsync.type = BasicType.Function;
    var_saveSpatialDataAsync.name = "saveSpatialDataAsync";
    var_saveSpatialDataAsync.varying = false;
    var_saveSpatialDataAsync.keepArgsHandle = false;
    var_saveSpatialDataAsync.args = [var_stringType];
    var_saveSpatialDataAsync.return = var_undefinedType_Promise;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_deleteConstructionLayerBySourceFaceIds.type = BasicType.Function;
    var_deleteConstructionLayerBySourceFaceIds.name = "deleteConstructionLayerBySourceFaceIds";
    var_deleteConstructionLayerBySourceFaceIds.varying = false;
    var_deleteConstructionLayerBySourceFaceIds.keepArgsHandle = false;
    var_deleteConstructionLayerBySourceFaceIds.args = [var_stringType_Array];
    var_deleteConstructionLayerBySourceFaceIds.return = var_undefinedType;
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_getFloorplanDocument.type = BasicType.Function;
    var_getFloorplanDocument.name = "getFloorplanDocument";
    var_getFloorplanDocument.varying = false;
    var_getFloorplanDocument.keepArgsHandle = false;
    var_getFloorplanDocument.args = [var_stringType];
    var_getFloorplanDocument.return = var_FloorplanDocument_Promise;
    var_FloorplanDocument_Promise.type = BasicType.Object;
    var_FloorplanDocument_Promise.properties = {
        "then": var_FloorplanDocument_Promise_then,
        "catch": var_Promise_catch,
    };
    var_FloorplanDocument_Promise_then.type = BasicType.Function;
    var_FloorplanDocument_Promise_then.name = "";
    var_FloorplanDocument_Promise_then.varying = false;
    var_FloorplanDocument_Promise_then.keepArgsHandle = true;
    var_FloorplanDocument_Promise_then.args = [var_FloorplanDocument_Promise_then_onresolve, var_Promise_then_onreject];
    var_FloorplanDocument_Promise_then.return = var_undefinedType;
    var_FloorplanDocument_Promise_then_onresolve.type = BasicType.Function;
    var_FloorplanDocument_Promise_then_onresolve.name = "";
    var_FloorplanDocument_Promise_then_onresolve.varying = false;
    var_FloorplanDocument_Promise_then_onresolve.keepArgsHandle = false;
    var_FloorplanDocument_Promise_then_onresolve.args = [var_FloorplanDocument];
    var_FloorplanDocument_Promise_then_onresolve.return = var_undefinedType;
    var_FloorplanDocument.type = BasicType.Object;
    var_FloorplanDocument.properties = {
        "floorplanElements": var_injection_FloorplanElement_Array,
        "floorplanConfig": var_FloorplanConfig,
        "version": var_stringType,
    };
    var_injection_FloorplanElement_Array.type = BasicType.Array;
    var_injection_FloorplanElement_Array.value = var_injection_FloorplanElement;
    var_FloorplanConfig.type = BasicType.Object;
    var_FloorplanConfig.properties = {
        "elementJoint": var_stringType,
        "height": var_numberType,
        "floorThickness": var_numberType,
        "compass": var_Compass,
    };
    var_Compass.type = BasicType.Object;
    var_Compass.properties = {
        "northDirection": var_numberType,
        "latitude": var_numberType,
        "longitude": var_numberType,
    };
    var_updateFloorplanDocument.type = BasicType.Function;
    var_updateFloorplanDocument.name = "updateFloorplanDocument";
    var_updateFloorplanDocument.varying = false;
    var_updateFloorplanDocument.keepArgsHandle = false;
    var_updateFloorplanDocument.args = [var_injection_FloorplanDocumentBatchUpdateRequest];
    var_updateFloorplanDocument.return = var_FloorplanDocumentBatchUpdateResponse_Promise;
    var_FloorplanDocumentBatchUpdateResponse_Promise.type = BasicType.Object;
    var_FloorplanDocumentBatchUpdateResponse_Promise.properties = {
        "then": var_FloorplanDocumentBatchUpdateResponse_Promise_then,
        "catch": var_Promise_catch,
    };
    var_FloorplanDocumentBatchUpdateResponse_Promise_then.type = BasicType.Function;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then.name = "";
    var_FloorplanDocumentBatchUpdateResponse_Promise_then.varying = false;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then.keepArgsHandle = true;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then.args = [var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve, var_Promise_then_onreject];
    var_FloorplanDocumentBatchUpdateResponse_Promise_then.return = var_undefinedType;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve.type = BasicType.Function;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve.name = "";
    var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve.varying = false;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve.keepArgsHandle = false;
    var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve.args = [var_FloorplanDocumentBatchUpdateResponse];
    var_FloorplanDocumentBatchUpdateResponse_Promise_then_onresolve.return = var_undefinedType;
    var_FloorplanDocumentBatchUpdateResponse.type = BasicType.Object;
    var_FloorplanDocumentBatchUpdateResponse.properties = {
        "floorplanDocument": var_FloorplanDocument,
        "elements": var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral_Array,
    };
    var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral_Array.type = BasicType.Array;
    var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral_Array.value = var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral;
    var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral.type = BasicType.Object;
    var_FloorplanDocumentBatchUpdateResponse_elements_objectLiteral.properties = {
        "elementIds": var_stringType_Array,
    };
    var_Design.type = BasicType.Object;
    var_Design.properties = {
        "getDesignConfig": var_getDesignConfig,
    };
    var_getDesignConfig.type = BasicType.Function;
    var_getDesignConfig.name = "getDesignConfig";
    var_getDesignConfig.varying = false;
    var_getDesignConfig.keepArgsHandle = false;
    var_getDesignConfig.args = [];
    var_getDesignConfig.return = var_getDesignConfig_objectLiteral;
    var_getDesignConfig_objectLiteral.type = BasicType.Object;
    var_getDesignConfig_objectLiteral.properties = {
        "designId": var_stringType,
        "levelId": var_stringType,
    };
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "triggerCadImport": var_triggerCadImport,
        "getCadInfo": var_getCadInfo,
        "changeCadLayerVisible": var_changeCadLayerVisible,
        "startDrawWall": var_startDrawWall,
        "startDrawColumn": var_startDrawColumn,
        "startDrawFlue": var_startDrawFlue,
        "startDrawBeam": var_startDrawBeam,
        "startDrawDoorOpening": var_startDrawDoorOpening,
        "startDrawWindowOpening": var_startDrawWindowOpening,
        "startDrawNiche": var_startDrawNiche,
        "startDrawRoof": var_startDrawRoof,
    };
    var_triggerCadImport.type = BasicType.Function;
    var_triggerCadImport.name = "triggerCadImport";
    var_triggerCadImport.varying = false;
    var_triggerCadImport.keepArgsHandle = false;
    var_triggerCadImport.args = [var_stringType];
    var_triggerCadImport.return = var_undefinedType;
    var_getCadInfo.type = BasicType.Function;
    var_getCadInfo.name = "getCadInfo";
    var_getCadInfo.varying = false;
    var_getCadInfo.keepArgsHandle = false;
    var_getCadInfo.args = [];
    var_getCadInfo.return = var_FloorPlanCadInfo;
    var_FloorPlanCadInfo.type = BasicType.Object;
    var_FloorPlanCadInfo.properties = {
        "isCadImported": var_booleanType,
        "data": var_FloorPlanCadInfo_data_objectLiteral,
    };
    var_FloorPlanCadInfo_data_objectLiteral.type = BasicType.Object;
    var_FloorPlanCadInfo_data_objectLiteral.properties = {
        "name": var_stringType,
        "id": var_stringType,
        "layers": var_FloorPlanLayerInfo_Array,
    };
    var_FloorPlanLayerInfo_Array.type = BasicType.Array;
    var_FloorPlanLayerInfo_Array.value = var_FloorPlanLayerInfo;
    var_FloorPlanLayerInfo.type = BasicType.Object;
    var_FloorPlanLayerInfo.properties = {
        "name": var_stringType,
        "visible": var_booleanType,
    };
    var_changeCadLayerVisible.type = BasicType.Function;
    var_changeCadLayerVisible.name = "changeCadLayerVisible";
    var_changeCadLayerVisible.varying = false;
    var_changeCadLayerVisible.keepArgsHandle = false;
    var_changeCadLayerVisible.args = [var_FloorPlanLayerInfo_Array];
    var_changeCadLayerVisible.return = var_undefinedType;
    var_startDrawWall.type = BasicType.Function;
    var_startDrawWall.name = "startDrawWall";
    var_startDrawWall.varying = false;
    var_startDrawWall.keepArgsHandle = false;
    var_startDrawWall.args = [var_stringType];
    var_startDrawWall.return = var_undefinedType;
    var_startDrawColumn.type = BasicType.Function;
    var_startDrawColumn.name = "startDrawColumn";
    var_startDrawColumn.varying = false;
    var_startDrawColumn.keepArgsHandle = false;
    var_startDrawColumn.args = [var_stringType];
    var_startDrawColumn.return = var_undefinedType;
    var_startDrawFlue.type = BasicType.Function;
    var_startDrawFlue.name = "startDrawFlue";
    var_startDrawFlue.varying = false;
    var_startDrawFlue.keepArgsHandle = false;
    var_startDrawFlue.args = [];
    var_startDrawFlue.return = var_undefinedType;
    var_startDrawBeam.type = BasicType.Function;
    var_startDrawBeam.name = "startDrawBeam";
    var_startDrawBeam.varying = false;
    var_startDrawBeam.keepArgsHandle = false;
    var_startDrawBeam.args = [];
    var_startDrawBeam.return = var_undefinedType;
    var_startDrawDoorOpening.type = BasicType.Function;
    var_startDrawDoorOpening.name = "startDrawDoorOpening";
    var_startDrawDoorOpening.varying = false;
    var_startDrawDoorOpening.keepArgsHandle = false;
    var_startDrawDoorOpening.args = [];
    var_startDrawDoorOpening.return = var_undefinedType;
    var_startDrawWindowOpening.type = BasicType.Function;
    var_startDrawWindowOpening.name = "startDrawWindowOpening";
    var_startDrawWindowOpening.varying = false;
    var_startDrawWindowOpening.keepArgsHandle = false;
    var_startDrawWindowOpening.args = [];
    var_startDrawWindowOpening.return = var_undefinedType;
    var_startDrawNiche.type = BasicType.Function;
    var_startDrawNiche.name = "startDrawNiche";
    var_startDrawNiche.varying = false;
    var_startDrawNiche.keepArgsHandle = false;
    var_startDrawNiche.args = [];
    var_startDrawNiche.return = var_undefinedType;
    var_startDrawRoof.type = BasicType.Function;
    var_startDrawRoof.name = "startDrawRoof";
    var_startDrawRoof.varying = false;
    var_startDrawRoof.keepArgsHandle = false;
    var_startDrawRoof.args = [];
    var_startDrawRoof.return = var_undefinedType;
    var_Integration.type = BasicType.Object;
    var_Integration.properties = {
        "CADImport": var_CADImport,
    };
    var_CADImport.type = BasicType.Object;
    var_CADImport.properties = {
        "loadFloorplanFromCad": var_loadFloorplanFromCad,
    };
    var_loadFloorplanFromCad.type = BasicType.Function;
    var_loadFloorplanFromCad.name = "loadFloorplanFromCad";
    var_loadFloorplanFromCad.varying = false;
    var_loadFloorplanFromCad.keepArgsHandle = false;
    var_loadFloorplanFromCad.args = [var_CadImportParams];
    var_loadFloorplanFromCad.return = var_undefinedType_Promise;
    var_CadImportParams.type = BasicType.Object;
    var_CadImportParams.properties = {
        "recordId": var_stringType,
        "shouldClearDesign": var_booleanType,
        "cadK2dInfos": var_CadK2dInfo_Array,
    };
    var_CadK2dInfo_Array.type = BasicType.Array;
    var_CadK2dInfo_Array.value = var_CadK2dInfo;
    var_CadK2dInfo.type = BasicType.Object;
    var_CadK2dInfo.properties = {
        "k2dUrl": var_stringType,
        "name": var_stringType,
        "drawingType": var_numberType,
        "splitBox": var_injection_KBoundingBox2d,
        "levelId": var_numberType,
        "levelName": var_stringType,
        "elevation": var_numberType,
        "height": var_numberType,
        "offset": var_injection_KVector2d,
    };
    
    return var_sourceFile;
};
