import { ArrayType, BasicType, ObjectType } from '@qunhe/kls-abstraction';
import { merge } from 'lodash';
import { BooleanType, ElementId, NumberType, ParamModelLiteBaseType } from '../basic';
import { getPromiseType } from '../helper';

export const FindParamModelOptionTypes = {
    type: BasicType.Object,
    properties: {
        categories: {
            type: BasicType.Array,
            value: NumberType,
        },
        toolType: {
            type: BasicType.Unknown,
        },
        maxLevel: NumberType,
        includeLocked: BooleanType,
        includeHidden: BooleanType,
        includeSubmitted: BooleanType,
        includeReadonly: BooleanType,
        includeExclusive: BooleanType,
        locked: BooleanType,
        hidden: BooleanType,
        submitted:BooleanType,
        readonly: BooleanType,
        exclusive: BooleanType,
    },
};

// ICustomDesignParamModelSaveOrRemoveOption 类型对应的声明
export const SaveOrDeleteTopModelsAsyncApiArgsTypes = merge({}, FindParamModelOptionTypes, {
    properties: {
        models: {
            type: BasicType.Array,
            value: ParamModelLiteBaseType,
        },
    },
});

/**
 * 通过真分类获取模型
 */
export const newCustomModelByCategoryAsync = {
    name: 'newCustomModelByCategoryAsync',
    varying: false,
    keepArgsHandle: false,
    args: [
        {
            type: BasicType.Number
        }
    ],
    type: BasicType.Function,
    return: getPromiseType(ParamModelLiteBaseType)
};

/**
 * 通过商品创建ID
 */
export const newCustomModelByProductIdAsync = {
    name: 'newCustomModelByProductIdAsync',
    varying: false,
    keepArgsHandle: true,
    args: [
        {
            type: BasicType.String
        }
    ],
    type: BasicType.Function,
    return: getPromiseType(ParamModelLiteBaseType)
}

/**
 * 通过商品ID查询模型
 */
export const getCustomModelByModelIdAsync = {
    name: 'getCustomModelByModelIdAsync',
    varying: false,
    keepArgsHandle: true,
    args: [
        // 因为传入多种类型，暂时只能这样
        // 最新声明类型：IFindParamModelByIdOptionTypes
        {
            type: BasicType.Unknown
        }
    ],
    type: BasicType.Function,
    return: getPromiseType(ParamModelLiteBaseType),
}

/**
 * 模型查询方法
 */
export const findTopModelsAsync = {
    name: 'findTopModelsAsync',
    varying: false,
    keepArgsHandle: true,
    args: [FindParamModelOptionTypes],
    type: BasicType.Function,
    return: getPromiseType({
        type: BasicType.Array,
        value: ParamModelLiteBaseType
    } as ArrayType)
};

/**
 * 更新模型方法
 */
export const updateCustomModelAsync = {
    name: 'updateAsync',
    varying: false,
    keepArgsHandle: true,
    args: [
        SaveOrDeleteTopModelsAsyncApiArgsTypes
    ],
    type: BasicType.Function,
    return: getPromiseType({type: BasicType.Undefined})
};

/**
 * 删除模型类型声明
 */
export const deleteTopModelsAsync = {
    name: 'deleteTopModelsAsync',
    varying: false,
    keepArgsHandle: true,
    args: [SaveOrDeleteTopModelsAsyncApiArgsTypes],
    type: BasicType.Function,
    return: getPromiseType({ type: BasicType.Undefined })
}

/** 拖拽创建定制设计对象返回的 Promise 结果类型 */
export const dragCustomProductPromiseResultType: ObjectType = {
    type: BasicType.Object,
    properties: {
        code: {
            type: BasicType.Number
        },
        errorMessage: {
            type: BasicType.String || BasicType.Undefined
        },
        data: ({
            type: BasicType.Array,
            value: ElementId
        }) as ArrayType
    }
}

/**
 * kioLog api vmType
 */
export const KioLog = {
    name: 'KioLog',
    varying: false,
    keepArgsHandle: true,
    args: [
        {
            type: BasicType.Unknown,
        }
    ],
    type: BasicType.Function,
    return: {
        type: BasicType.Undefined,
    },
};
