# Docusaurus 构建输出和缓存
build/
.docusaurus/
.docz

# 依赖文件
node_modules/
package-lock.json

# OpenAPI 插件自动生成的文档文件
# API 接口文档 (.api.mdx)
docs/api/*/*.api.mdx

# OpenAPI 定义信息文档 (.info.mdx)
docs/api/*/*.info.mdx

# API 标签文档 (.tag.mdx)
docs/api/*/*.tag.mdx

# Schema 定义文档 (.schema.mdx)
docs/api/*/schemas/*.schema.mdx

# 自动生成的侧边栏配置
docs/api/*/sidebar.ts

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 文件
.vscode/
.idea/

# macOS 文件
.DS_Store

# Windows 文件
Thumbs.db 