'!include': '../../shared/shared-data-mappings.yaml'
generatorName: "java"
inputSpec: "specifications/services/doorwindow/openapi.yaml"
outputDir: "build/sdks/doorwindow/java"
templateDir: "generators/sdks/templates/java"
additionalProperties:
  groupId: "com.manycoreapis"
  artifactId: "doorwindow-rest-client"
  artifactVersion: "0.0.1"
  modelPackage: "com.manycore.doorwindow.client.model"
  apiPackage: "com.manycore.doorwindow.client.api"
  invokerPackage: "com.manycore.doorwindow.client"
  java8: true
  dateLibrary: "java8"
  library: "resttemplate"
  sourceFolder: "src/main/java"
  hideGenerationTimestamp: true
  serializationLibrary: "jackson"
  useRuntimeException: false

    

