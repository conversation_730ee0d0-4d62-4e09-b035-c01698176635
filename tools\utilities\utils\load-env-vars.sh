#!/bin/bash

# 环境变量加载工具
# 用于加载和验证 changed_files.env

# 函数：加载环境变量
# 支持容错机制
load_env_vars() {
    echo "🔍 环境变量调试："
    echo "  HAS_API_CHANGES: $HAS_API_CHANGES"
    echo "  CHANGED_API_FILES: $CHANGED_API_FILES"
    
    # 容错机制
    if [ -z "$HAS_API_CHANGES" ] && [ -f "changed_files.env" ]; then
        echo "🔧 从文件读取环境变量..."
        echo "📄 文件内容："
        cat changed_files.env
        source changed_files.env
        echo "📄 读取后的环境变量："
        echo "  HAS_API_CHANGES: $HAS_API_CHANGES"
        echo "  CHANGED_API_FILES: $CHANGED_API_FILES"
    elif [ -z "$HAS_API_CHANGES" ]; then
        echo "❌ 环境变量为空且找不到 changed_files.env 文件"
        echo "🔍 当前目录文件列表："
        ls -la
        echo "🔍 检查是否存在环境变量文件："
        find . -name "changed_files.env" -type f 2>/dev/null || echo "未找到文件"
        return 1
    else
        echo "✅ 环境变量已存在，无需从文件读取"
    fi
    
    return 0
}

# 函数：验证环境变量
# 检查必要的环境变量是否正确设置
validate_env_vars() {
    if [ -z "$HAS_API_CHANGES" ]; then
        echo "❌ HAS_API_CHANGES 未设置"
        return 1
    fi
    
    if [ "$HAS_API_CHANGES" = "true" ] && [ -z "$CHANGED_API_FILES" ]; then
        echo "❌ HAS_API_CHANGES=true 但 CHANGED_API_FILES 为空"
        return 1
    fi
    
    echo "✅ 环境变量验证通过"
    return 0
}

# 函数：显示环境变量信息
show_env_info() {
    echo ""
    echo "📊 环境变量信息："
    echo "  HAS_API_CHANGES: '$HAS_API_CHANGES'"
    echo "  CHANGED_API_FILES: '$CHANGED_API_FILES'"
    
    if [ "$HAS_API_CHANGES" = "true" ] && [ -n "$CHANGED_API_FILES" ]; then
        echo ""
        echo "📋 变更的文件："
        source tools/utilities/utils/process-file-list.sh
        process_file_list "$CHANGED_API_FILES" | while read file; do
            echo "    📄 $file"
        done
    fi
} 