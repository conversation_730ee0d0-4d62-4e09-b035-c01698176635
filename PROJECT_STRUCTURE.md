# Backend API SDK 项目结构

本文档描述了重构后的项目目录结构和组织方式。

## 📁 目录结构概览

```
backend-api-sdk/
├── specifications/              # API 规范文件
│   ├── services/               # 各服务的 OpenAPI 规范
│   │   ├── camera/
│   │   │   └── openapi.yaml   # 相机基础设施 API 规范
│   │   ├── doorwindow/
│   │   │   └── openapi.yaml   # 门窗服务 API 规范
│   │   ├── furniture/
│   │   │   └── openapi.yaml   # 家具设计 API 规范
│   │   ├── koolux/
│   │   │   └── openapi.yaml   # Koolux 服务 API 规范
│   │   ├── layout/
│   │   │   └── openapi.yaml   # 布局服务 API 规范
│   │   └── pdm/
│   │       └── openapi.yaml   # PDM 服务 API 规范
│   ├── common/                 # 通用数据模型和组件
│   └── shared/                 # 共享数据映射
├── generators/                  # SDK 和文档生成器
│   ├── configs/                # 生成器配置文件
│   │   ├── java/              # Java SDK 配置
│   │   │   ├── camera.yaml
│   │   │   ├── doorwindow.yaml
│   │   │   ├── furniture.yaml
│   │   │   ├── koolux.yaml
│   │   │   ├── layout.yaml
│   │   │   └── pdm.yaml
│   │   └── typescript/         # TypeScript SDK 配置（预留）
│   ├── templates/              # 生成器模板
│   │   ├── java/              # Java SDK 模板
│   │   │   └── libraries/
│   │   │       └── resttemplate/
│   │   ├── typescript/         # TypeScript 模板（预留）
│   │   └── docs/              # 文档生成模板
│   │       ├── api-preview.html
│   │       ├── no-api-changes.html
│   │       ├── preview-info.html
│   │       └── service-card.html
│   └── scripts/                # 生成器脚本
│       ├── generate-all-dynamic-content.sh
│       ├── generate-docs-content.js
│       ├── generate-openapi-config.js
│       ├── generate-mappings.sh
│       └── generate-restapi-sdk-data-mappings.sh
├── build/                      # 构建输出目录（被 .gitignore）
│   ├── sdks/                  # 生成的 SDK
│   │   ├── camera/
│   │   │   ├── java/
│   │   │   └── typescript/
│   │   ├── doorwindow/
│   │   │   ├── java/
│   │   │   └── typescript/
│   │   └── ...                # 其他服务
│   ├── docs/                  # 生成的文档
│   └── artifacts/             # 其他构建产物
├── documentation/              # 项目文档
│   ├── website/               # Docusaurus 文档站点
│   │   ├── docs/             # 文档内容
│   │   ├── blog/             # 博客文章
│   │   ├── src/              # 自定义组件
│   │   ├── static/           # 静态资源
│   │   ├── docusaurus.config.ts
│   │   ├── package.json
│   │   └── ...               # 其他配置文件
│   ├── guides/               # 开发指南
│   │   ├── api-review-process.md
│   │   └── gitlab-setup-guide.md
│   └── api-specs/            # API 规范文档（预留）
├── tools/                     # 开发和运维工具
│   ├── ci-cd/                # CI/CD 脚本
│   │   ├── ci/              # 原有 CI 子系统
│   │   │   ├── deployment/
│   │   │   ├── docs/
│   │   │   ├── review/
│   │   │   ├── setup/
│   │   │   └── validation/
│   │   ├── add-mr-comment.sh
│   │   ├── check-local-ci.sh
│   │   ├── detect-api-changes.sh
│   │   └── local_run.sh
│   ├── deployment/           # 部署脚本
│   │   └── deploy-docs-to-manual.sh
│   ├── development/          # 本地开发工具
│   │   ├── local/           # 本地开发子系统
│   │   ├── setup-docs.sh
│   │   ├── setup-local-env.sh
│   │   ├── setup-local-env-final.sh
│   │   ├── install-docs-tools.sh
│   │   ├── switch-npm-registry.sh
│   │   ├── pre-commit-hook.sh
│   │   └── check-project-status.sh
│   └── utilities/            # 通用工具
│       ├── utils/
│       │   ├── load-env-vars.sh
│       │   ├── process-file-list.sh
│       │   └── process-template.sh
│       └── README-mappings.md
├── config/                    # 项目配置文件
│   ├── ci-cd/                # CI/CD 相关配置
│   │   └── ci-settings.xml
│   ├── docker/               # Docker 相关配置
│   │   ├── Dockerfile
│   │   └── build-docker.sh
│   └── project/              # 项目级配置
│       └── code-stage.json
├── examples/                  # 示例代码（保持不变）
├── geom-data-exchange-java/   # 几何数据交换 Java 库（保持不变）
├── test-results/              # 测试结果（保持不变）
├── ui/                        # UI 组件（保持不变）
├── .gitignore                 # Git 忽略配置
├── .gitlab-ci.yml             # GitLab CI 配置
├── package.json               # Node.js 项目配置
├── package-lock.json          # 依赖锁定文件
├── README.md                  # 项目说明
└── PROJECT_STRUCTURE.md       # 本文档
```

## 🎯 重构目标和优势

### 1. **清晰的功能分离**
- **specifications/** - 纯粹的 API 规范管理，与生成配置分离
- **generators/** - 所有生成相关的工具、模板和配置集中管理
- **tools/** - 开发和运维工具按功能分类
- **documentation/** - 文档相关内容统一管理

### 2. **更好的可维护性**
- 相关文件集中存放，减少查找时间
- 统一的命名规范，降低认知负担
- 清晰的层级结构，便于新人理解

### 3. **增强的扩展性**
- 新增服务有明确的放置位置
- 新增语言支持结构清晰
- 工具脚本分类明确，便于扩展

## 🔄 主要变更

| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `openapi/` | `specifications/services/` | API 规范文件 |
| `output/` | `build/` | 构建输出目录 |
| `docs-site/` | `documentation/website/` | 文档站点 |
| `docs/` | `documentation/guides/` | 开发指南 |
| `scripts/` | `tools/` + `generators/scripts/` | 脚本工具分类 |
| `templates/` | `generators/templates/docs/` | 文档模板 |
| `generator/templates/` | `generators/templates/java/` | Java 模板 |
| `restapi.yaml` | `openapi.yaml` | API 规范文件名 |
| `config-java.yaml` | `generators/configs/java/*.yaml` | SDK 配置 |

## 📝 使用说明

### 添加新服务
1. 在 `specifications/services/new-service/` 创建 `openapi.yaml`
2. 在 `generators/configs/java/` 创建 `new-service.yaml`
3. 运行 `generators/docs/scripts/generate-all-dynamic-content.sh`

### 本地开发
- 环境设置：`tools/development/setup-local-env-final.sh`
- 文档预览：`tools/development/setup-docs.sh`
- 本地构建：`tools/development/local/`

### CI/CD
所有 CI/CD 相关脚本位于 `tools/ci-cd/` 目录下，GitLab CI 配置已更新路径引用。

## 🔧 配置更新

重构过程中已更新以下配置文件的路径引用：
- `.gitlab-ci.yml` - CI/CD 流程配置
- `documentation/website/openapi-config.json` - 文档站点配置
- `generators/docs/scripts/generate-all-dynamic-content.sh` - 生成器脚本
- 所有 SDK 生成配置文件中的路径引用

## ⚠️ 注意事项

1. **构建输出** - `build/` 目录在 `.gitignore` 中，不会被版本控制
2. **模板文件** - `temp_templates/` 是本地参考用的模板，已在 `.gitignore` 中
3. **路径引用** - 所有脚本和配置文件中的路径引用已更新
4. **向后兼容** - 此重构不保持向后兼容性，需要更新所有引用

---

*此文档描述了项目重构后的最终结构。如有疑问，请参考各目录下的 README 文件或联系开发团队。*
