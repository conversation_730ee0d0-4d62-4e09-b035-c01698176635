import { Type } from '@qunhe/kls-abstraction';
import * as APIType from '@qunhe/idp-custom/api';
export declare function createVMBindingType(typeInjections: {
    types: {
        "KioLog": Type;
        "AsyncFunctionType": Type;
        "CustomModel": Type;
        "ISelectionEvtType": Type;
        "UnknownType": Type;
        "NewCustomModelByCategoryAsync": Type;
        "NewCustomModelByProductIdAsync": Type;
        "GetCustomModelByModelIdAsync": Type;
        "FindTopModelsAsync": Type;
        "UpdateCustomModelAsync": Type;
        "DeleteTopModelsAsync": Type;
        "DragCustomProductPromiseResult": Type;
    };
}): Type & { VALUE_TYPE: typeof APIType; };
