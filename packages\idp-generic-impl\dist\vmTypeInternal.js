var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_Attributes = injections.types["Attributes"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Methods = {};
    var var_getGenericModel = {};
    var var_ElementId = {};
    var var_stringType = {};
    var var_GenericModel = {};
    var var_updateGenericModel = {};
    var var_ModelUpdateInfo = {};
    var var_undefinedType = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_Attributes, exportName: "Attributes" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Methods": var_Methods,
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "getGenericModel": var_getGenericModel,
        "updateGenericModel": var_updateGenericModel,
    };
    var_getGenericModel.type = BasicType.Function;
    var_getGenericModel.name = "getGenericModel";
    var_getGenericModel.varying = false;
    var_getGenericModel.keepArgsHandle = false;
    var_getGenericModel.args = [var_ElementId];
    var_getGenericModel.return = var_GenericModel;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_GenericModel.type = BasicType.Object;
    var_GenericModel.properties = {
        "productId": var_stringType,
        "attributes": var_injection_Attributes,
    };
    var_updateGenericModel.type = BasicType.Function;
    var_updateGenericModel.name = "updateGenericModel";
    var_updateGenericModel.varying = false;
    var_updateGenericModel.keepArgsHandle = false;
    var_updateGenericModel.args = [var_ModelUpdateInfo];
    var_updateGenericModel.return = var_undefinedType;
    var_ModelUpdateInfo.type = BasicType.Object;
    var_ModelUpdateInfo.properties = {
        "elementId": var_ElementId,
        "attributes": var_injection_Attributes,
    };
    var_undefinedType.type = BasicType.Undefined;
    
    return var_sourceFile;
};
