import { IDP as IDPCommon } from '@qunhe/idp-common';
/**
 * 区域显示信息
 * @internal
 */
export interface ZoneStyle {
    id: IDPCommon.DB.Types.ElementId;
    /** 区域颜色 */
    color: number;
    /** 区域文本 */
    labelStyle?: {
        /** 文本内容 */
        content: string;
        /** 位置 */
        position: { x: number, y: number };
        /** 角度 */
        rotation: number;
        /** 文字类型 */
        fontFamily?: string;
        /** 文字大小 */
        fontSize?: number;
        /** 颜色 */
        color?: number;
        /** 文本的锚点位置 */
        anchor?: { x: number, y: number };
        /** 内边距 */
        padding?: number[];
        /** 背景色 */
        backgroundColor?: number;
    };
}
