import { once } from 'lodash';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { createVMBindingType } from './dist/vmTypePublic';

export const getVMBindingType = once(() => {
    return createVMBindingType();
});
export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal();
});

/**
 * 设计对象标签类型
 */
export enum ElementLabelType {
    /**
     * 数字
     */
    number = 0,
    /**
     * 字符
     */
    string = 1,
    /**
     * 单选(字符)
     */
    option = 2
}

/**
 * 设计对象标签写入错误类型
 */
export enum ElementLabelWriteErrorType {
    /**
     * 标签配置校验失败: id不存在(或不可编辑)、标签超过数量上限(30)、key重复(如复制其它商家的方案已有相同key)等
     */
    config = 'ElementLabel.ConfigError',
    /**
     * 标签值校验失败: 非数字(自动转换)、值不在可选项列表等
     */
    value = 'ElementLabel.ValueError',
    /**
     * 设计对象校验失败: 待更新标签对象不存在、不支持、不可写入等
     */
    element = 'ElementLabel.ElementError'
}

/**
 * 设计对象标签配置
 */
export interface ElementLabelConfig {
    /**
     * 全局唯一标识
     */
    id: number;
    /**
     * 标签键(商家维度唯一, 不同商家之间可能有相同)
     */
    key: string;
    /**
     * 标签名
     */
    name: string;
    /**
     * 标签类型
     */
    type: ElementLabelType;
    /**
     * 标签选项 仅ElementLabelType.option有
     */
    options?: string[];
}

/**
 * 设计对象标签数据
 */
export interface ElementLabelData {
    /**
     * ElementLabelConfig id
     */
    id: number;

    /**
     * 标签值
     */
    value: string;
}
