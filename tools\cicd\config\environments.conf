# 环境配置文件
# 定义不同环境的配置参数

# =============================================================================
# 开发环境配置 (dev)
# =============================================================================

# Manual 文档网站配置
DEV_MANUAL_URL="https://manual.qunhequnhe.com/manycoreapi-dev/"
DEV_MANUAL_ENV="dev"

# SDK 仓库配置
DEV_SDK_REGISTRY="snapshots"
DEV_MAVEN_REGISTRY_URL="http://nexus.qunhequnhe.com/repository/maven-snapshots/"
DEV_NPM_REGISTRY_URL="http://npm-registry.qunhequnhe.com"

# Docker 配置
DEV_DOCKER_REGISTRY="registry-qunhe02.qunhequnhe.com"
DEV_OPENAPI_GENERATOR_IMAGE="registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest"

# 通知配置
DEV_ENABLE_NOTIFICATIONS="false"
DEV_SLACK_WEBHOOK=""

# =============================================================================
# 测试环境配置 (staging)
# =============================================================================

# Manual 文档网站配置
STAGING_MANUAL_URL="https://manual.qunhequnhe.com/manycoreapi-staging/"
STAGING_MANUAL_ENV="staging"

# SDK 仓库配置
STAGING_SDK_REGISTRY="snapshots"
STAGING_MAVEN_REGISTRY_URL="http://nexus.qunhequnhe.com/repository/maven-snapshots/"
STAGING_NPM_REGISTRY_URL="http://npm-registry.qunhequnhe.com"

# Docker 配置
STAGING_DOCKER_REGISTRY="registry-qunhe02.qunhequnhe.com"
STAGING_OPENAPI_GENERATOR_IMAGE="registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest"

# 通知配置
STAGING_ENABLE_NOTIFICATIONS="true"
STAGING_SLACK_WEBHOOK="${SLACK_WEBHOOK_STAGING}"

# =============================================================================
# 生产环境配置 (production)
# =============================================================================

# Manual 文档网站配置
PRODUCTION_MANUAL_URL="https://manual.qunhequnhe.com/manycoreapi/"
PRODUCTION_MANUAL_ENV="prod"

# SDK 仓库配置
PRODUCTION_SDK_REGISTRY="releases"
PRODUCTION_MAVEN_REGISTRY_URL="http://nexus.qunhequnhe.com/repository/maven-releases/"
PRODUCTION_NPM_REGISTRY_URL="http://npm-registry.qunhequnhe.com"

# Docker 配置
PRODUCTION_DOCKER_REGISTRY="registry-qunhe02.qunhequnhe.com"
PRODUCTION_OPENAPI_GENERATOR_IMAGE="registry-qunhe02.qunhequnhe.com/display/openapi-generator:stable"

# 通知配置
PRODUCTION_ENABLE_NOTIFICATIONS="true"
PRODUCTION_SLACK_WEBHOOK="${SLACK_WEBHOOK_PRODUCTION}"

# =============================================================================
# 通用配置
# =============================================================================

# 超时配置（秒）
DEFAULT_SCRIPT_TIMEOUT="3600"
DEFAULT_PIPELINE_TIMEOUT="7200"
DEFAULT_DOCKER_TIMEOUT="1800"

# 重试配置
DEFAULT_RETRY_COUNT="3"
DEFAULT_RETRY_DELAY="5"

# 日志配置
DEFAULT_LOG_LEVEL="INFO"
DEFAULT_LOG_FORMAT="[%Y-%m-%d %H:%M:%S] [%LEVEL%] [%MODULE%] %MESSAGE%"

# 文件路径配置
OPENAPI_SPECS_DIR="openapi-specs"
SDK_OUTPUT_DIR="generated-sdks"
DOCS_BUILD_DIR="docs-site/build"
TEMP_DIR="tmp"

# Git 配置
DEFAULT_BASE_BRANCH="origin/main"
GIT_DIFF_CONTEXT_LINES="3"

# API 变更检测配置
API_SPEC_EXTENSIONS=("yaml" "yml" "json")
API_SPEC_PATTERNS=("*.yaml" "*.yml" "*.json")

# SDK 语言配置
SUPPORTED_SDK_LANGUAGES=("java" "typescript" "python" "go")
DEFAULT_SDK_LANGUAGES="java,typescript"

# 文档配置
DOCS_CONFIG_FILES=("documentation/website/docusaurus.config.ts" "docs-site/docusaurus.config.ts")
DOCS_BUILD_MODES=("normal" "optimized")

# =============================================================================
# 环境变量映射函数
# =============================================================================

# 获取环境特定的配置值
# 用法: get_env_config "MANUAL_URL" "dev"
get_env_config() {
    local config_name="$1"
    local environment="$2"
    
    # 转换环境名称为大写
    local env_upper=$(echo "$environment" | tr '[:lower:]' '[:upper:]')
    
    # 构建变量名
    local var_name="${env_upper}_${config_name}"
    
    # 获取变量值
    local value="${!var_name}"
    
    # 如果没有找到环境特定的值，尝试使用默认值
    if [ -z "$value" ]; then
        local default_var_name="DEFAULT_${config_name}"
        value="${!default_var_name}"
    fi
    
    echo "$value"
}

# 加载环境配置
# 用法: load_env_config "dev"
load_env_config() {
    local environment="${1:-dev}"
    
    # 设置当前环境
    export CURRENT_ENVIRONMENT="$environment"
    
    # 加载环境特定的配置
    export MANUAL_URL=$(get_env_config "MANUAL_URL" "$environment")
    export MANUAL_ENV=$(get_env_config "MANUAL_ENV" "$environment")
    export SDK_REGISTRY=$(get_env_config "SDK_REGISTRY" "$environment")
    export MAVEN_REGISTRY_URL=$(get_env_config "MAVEN_REGISTRY_URL" "$environment")
    export NPM_REGISTRY_URL=$(get_env_config "NPM_REGISTRY_URL" "$environment")
    export DOCKER_REGISTRY=$(get_env_config "DOCKER_REGISTRY" "$environment")
    export OPENAPI_GENERATOR_IMAGE=$(get_env_config "OPENAPI_GENERATOR_IMAGE" "$environment")
    export ENABLE_NOTIFICATIONS=$(get_env_config "ENABLE_NOTIFICATIONS" "$environment")
    export SLACK_WEBHOOK=$(get_env_config "SLACK_WEBHOOK" "$environment")
    
    # 加载通用配置
    export SCRIPT_TIMEOUT="${SCRIPT_TIMEOUT:-$DEFAULT_SCRIPT_TIMEOUT}"
    export PIPELINE_TIMEOUT="${PIPELINE_TIMEOUT:-$DEFAULT_PIPELINE_TIMEOUT}"
    export DOCKER_TIMEOUT="${DOCKER_TIMEOUT:-$DEFAULT_DOCKER_TIMEOUT}"
    export RETRY_COUNT="${RETRY_COUNT:-$DEFAULT_RETRY_COUNT}"
    export RETRY_DELAY="${RETRY_DELAY:-$DEFAULT_RETRY_DELAY}"
    export LOG_LEVEL="${LOG_LEVEL:-$DEFAULT_LOG_LEVEL}"
    export LOG_FORMAT="${LOG_FORMAT:-$DEFAULT_LOG_FORMAT}"
    
    # 设置路径配置
    export OPENAPI_SPECS_DIR="${OPENAPI_SPECS_DIR:-openapi-specs}"
    export SDK_OUTPUT_DIR="${SDK_OUTPUT_DIR:-generated-sdks}"
    export DOCS_BUILD_DIR="${DOCS_BUILD_DIR:-docs-site/build}"
    export TEMP_DIR="${TEMP_DIR:-tmp}"
    
    # 设置 Git 配置
    export BASE_BRANCH="${BASE_BRANCH:-$DEFAULT_BASE_BRANCH}"
    export GIT_DIFF_CONTEXT_LINES="${GIT_DIFF_CONTEXT_LINES:-3}"
    
    # 设置 SDK 配置
    export SDK_LANGUAGES="${SDK_LANGUAGES:-$DEFAULT_SDK_LANGUAGES}"
    
    echo "已加载环境配置: $environment"
}

# 显示当前环境配置
show_env_config() {
    echo "当前环境配置:"
    echo "  环境: ${CURRENT_ENVIRONMENT:-未设置}"
    echo "  Manual URL: ${MANUAL_URL:-未设置}"
    echo "  Manual 环境: ${MANUAL_ENV:-未设置}"
    echo "  SDK 仓库类型: ${SDK_REGISTRY:-未设置}"
    echo "  Maven 仓库: ${MAVEN_REGISTRY_URL:-未设置}"
    echo "  npm 仓库: ${NPM_REGISTRY_URL:-未设置}"
    echo "  Docker 仓库: ${DOCKER_REGISTRY:-未设置}"
    echo "  OpenAPI 生成器镜像: ${OPENAPI_GENERATOR_IMAGE:-未设置}"
    echo "  启用通知: ${ENABLE_NOTIFICATIONS:-未设置}"
    echo "  脚本超时: ${SCRIPT_TIMEOUT:-未设置}s"
    echo "  流水线超时: ${PIPELINE_TIMEOUT:-未设置}s"
    echo "  日志级别: ${LOG_LEVEL:-未设置}"
    echo "  SDK 语言: ${SDK_LANGUAGES:-未设置}"
    echo "  基准分支: ${BASE_BRANCH:-未设置}"
}

# 验证环境配置
validate_env_config() {
    local errors=0
    
    # 检查必需的配置
    if [ -z "$MANUAL_URL" ]; then
        echo "错误: MANUAL_URL 未设置"
        ((errors++))
    fi
    
    if [ -z "$MANUAL_ENV" ]; then
        echo "错误: MANUAL_ENV 未设置"
        ((errors++))
    fi
    
    if [ -z "$SDK_REGISTRY" ]; then
        echo "错误: SDK_REGISTRY 未设置"
        ((errors++))
    fi
    
    if [ -z "$OPENAPI_GENERATOR_IMAGE" ]; then
        echo "错误: OPENAPI_GENERATOR_IMAGE 未设置"
        ((errors++))
    fi
    
    if [ $errors -gt 0 ]; then
        echo "环境配置验证失败，发现 $errors 个错误"
        return 1
    else
        echo "环境配置验证通过"
        return 0
    fi
}

# 如果直接执行此配置文件，显示帮助信息
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "环境配置文件"
    echo ""
    echo "用法:"
    echo "  source config/environments.conf"
    echo "  load_env_config dev"
    echo "  show_env_config"
    echo "  validate_env_config"
    echo ""
    echo "支持的环境:"
    echo "  dev        - 开发环境"
    echo "  staging    - 测试环境"
    echo "  production - 生产环境"
fi
