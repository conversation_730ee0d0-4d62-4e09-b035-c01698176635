# Maven Central 发布设置指南

本文档说明如何设置发布 SDK 到 Maven Central 所需的认证信息。

## 需要的认证信息

发布到 Maven Central 需要两套独立的认证信息：

### 1. GPG 签名认证
用于对 JAR 文件进行数字签名，这是 Maven Central 的强制要求。

**环境变量：**
- `GPG_KEY_NAME`: GPG 密钥 ID
- `GPG_PASSPHRASE`: GPG 密钥密码

### 2. Maven Central Portal 认证
用于将签名后的构件上传到 Maven Central Portal。

**环境变量：**
- `CENTRAL_USERNAME`: Maven Central Portal 用户名
- `CENTRAL_PASSWORD`: Maven Central Portal 密码/令牌

## 设置步骤

### 步骤 1: 创建和配置 GPG 密钥

1. **生成 GPG 密钥对**：
   ```bash
   gpg --gen-key
   ```

2. **查看密钥 ID**：
   ```bash
   gpg --list-secret-keys --keyid-format LONG
   ```
   输出示例：
   ```
   sec   rsa4096/1234567890ABCDEF 2024-01-01 [SC]
   ```
   这里 `1234567890ABCDEF` 就是你的密钥 ID

3. **上传公钥到密钥服务器**：
   ```bash
   gpg --keyserver keyserver.ubuntu.com --send-keys 1234567890ABCDEF
   gpg --keyserver keys.openpgp.org --send-keys 1234567890ABCDEF
   ```

### 步骤 2: 注册 Maven Central Portal

1. **访问 Maven Central Portal**：
   - 地址：https://central.sonatype.com/
   - 使用 GitHub、Google 或其他支持的账户登录

2. **获取认证令牌**：
   - 登录后，转到 "View Account" 页面
   - 生成一个新的用户令牌（User Token）
   - 记录用户名和令牌

### 步骤 3: 验证命名空间

1. **验证 groupId**：
   - 对于 `com.manycoreapis`，需要验证对 `manycoreapis.com` 域名的控制权
   - 或者使用 GitHub 相关的命名空间如 `io.github.yourname`

2. **提交验证**：
   - 在 Central Portal 中提交命名空间验证请求
   - 按照指示完成验证流程

## 使用示例

### 设置环境变量

```bash
# GPG 认证
export GPG_KEY_NAME="1234567890ABCDEF"
export GPG_PASSPHRASE="your-gpg-passphrase"

# Maven Central 认证
export CENTRAL_USERNAME="your-central-username"
export CENTRAL_PASSWORD="your-central-token"
```

### 发布命令

```bash
# 发布所有服务到 Maven Central
GPG_KEY_NAME="1234567890ABCDEF" \
GPG_PASSPHRASE="your-passphrase" \
CENTRAL_USERNAME="your-username" \
CENTRAL_PASSWORD="your-token" \
./local-sdk-manager.sh deploy-rel all

# 发布特定服务
GPG_KEY_NAME="1234567890ABCDEF" \
GPG_PASSPHRASE="your-passphrase" \
CENTRAL_USERNAME="your-username" \
CENTRAL_PASSWORD="your-token" \
./local-sdk-manager.sh deploy-rel doorwindow
```

## 安全建议

1. **环境变量保护**：
   ```bash
   # 使用 .env 文件（记得加入 .gitignore）
   echo "GPG_KEY_NAME=your-key-id" >> .env
   echo "GPG_PASSPHRASE=your-passphrase" >> .env
   echo "CENTRAL_USERNAME=your-username" >> .env  
   echo "CENTRAL_PASSWORD=your-token" >> .env
   
   # 加载环境变量
   source .env
   ```

2. **CI/CD 中的密钥管理**：
   - 使用 GitLab Variables 或 GitHub Secrets
   - 设置为 protected 和 masked
   - 定期轮换令牌

3. **本地开发**：
   - 不要将认证信息提交到版本控制
   - 使用专用的发布脚本
   - 考虑使用密钥管理工具

## 故障排除

### 常见问题

1. **GPG 签名失败**：
   ```
   gpg: signing failed: No such file or directory
   ```
   - 检查 GPG_KEY_NAME 是否正确
   - 确认密钥在系统中存在：`gpg --list-secret-keys`

2. **认证失败**：
   ```
   401 Unauthorized
   ```
   - 检查 CENTRAL_USERNAME 和 CENTRAL_PASSWORD
   - 确认令牌未过期
   - 验证命名空间权限

3. **网络问题**：
   - 检查防火墙设置
   - 尝试不同的密钥服务器
   - 验证 Maven Central Portal 连接

### 验证设置

运行以下命令验证配置：

```bash
# 验证 GPG 设置
gpg --list-secret-keys

# 验证环境变量
echo "GPG Key: $GPG_KEY_NAME"
echo "Central User: $CENTRAL_USERNAME" 
echo "Password set: $([ -n "$CENTRAL_PASSWORD" ] && echo "Yes" || echo "No")"

# 测试发布（干运行）
./local-sdk-manager.sh --dry-run deploy-rel doorwindow
```

## 相关链接

- [Maven Central Portal 文档](https://central.sonatype.org/publish/)
- [GPG 签名指南](https://central.sonatype.org/publish/requirements/gpg/)
- [命名空间验证](https://central.sonatype.org/publish/requirements/coordinates/)
- [Central Publishing Plugin](https://central.sonatype.org/publish/publish-portal-maven/) 