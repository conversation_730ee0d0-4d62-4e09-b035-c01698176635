#!/bin/bash

# 模板处理工具脚本
# 用于替换 HTML 模板中的变量

set -e

# 使用方法
usage() {
    echo "用法: $0 <template_file> <output_file>"
    echo "示例: $0 templates/preview-info.html public/preview-info.html"
    echo ""
    echo "支持的变量："
    echo "  {{CI_MERGE_REQUEST_IID}}        - MR ID"
    echo "  {{CI_COMMIT_REF_NAME}}          - 分支名"
    echo "  {{CI_COMMIT_SHA}}               - 提交哈希"
    echo "  {{BUILD_TIME}}                  - 构建时间"
    echo "  {{CHANGED_FILES}}               - 变更文件列表"
    echo "  {{CI_MERGE_REQUEST_PROJECT_URL}} - 项目 URL"
}

# 检查参数
if [ $# -ne 2 ]; then
    usage
    exit 1
fi

TEMPLATE_FILE="$1"
OUTPUT_FILE="$2"

# 检查模板文件是否存在
if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "❌ 模板文件不存在: $TEMPLATE_FILE"
    exit 1
fi

# 确保输出目录存在
OUTPUT_DIR=$(dirname "$OUTPUT_FILE")
mkdir -p "$OUTPUT_DIR"

echo "🔄 处理模板: $TEMPLATE_FILE -> $OUTPUT_FILE"

# 获取变更文件列表
CHANGED_FILES_CONTENT=""
if [ -n "$CHANGED_API_FILES" ]; then
    source tools/utilities/utils/process-file-list.sh
    CHANGED_FILES_CONTENT=$(process_file_list "$CHANGED_API_FILES" | grep -E '\.(yaml|yml)$' || echo "无 API 文件变更")
else
    CHANGED_FILES_CONTENT="无 API 文件变更"
fi

# 获取当前时间
BUILD_TIME=$(date)

# 复制模板并替换变量
cp "$TEMPLATE_FILE" "$OUTPUT_FILE"

# 替换变量
sed -i "s|{{CI_MERGE_REQUEST_IID}}|${CI_MERGE_REQUEST_IID:-}|g" "$OUTPUT_FILE"
sed -i "s|{{CI_COMMIT_REF_NAME}}|${CI_COMMIT_REF_NAME:-}|g" "$OUTPUT_FILE"
sed -i "s|{{CI_COMMIT_SHA}}|${CI_COMMIT_SHA:-}|g" "$OUTPUT_FILE"
sed -i "s|{{BUILD_TIME}}|${BUILD_TIME}|g" "$OUTPUT_FILE"
sed -i "s|{{CI_MERGE_REQUEST_PROJECT_URL}}|${CI_MERGE_REQUEST_PROJECT_URL:-}|g" "$OUTPUT_FILE"

# 处理变更文件列表（需要转义特殊字符）
ESCAPED_FILES=$(echo "$CHANGED_FILES_CONTENT" | sed 's/[[\.*^$()+?{|]/\\&/g')
sed -i "s|{{CHANGED_FILES}}|${ESCAPED_FILES}|g" "$OUTPUT_FILE"

echo "✅ 模板处理完成: $OUTPUT_FILE" 