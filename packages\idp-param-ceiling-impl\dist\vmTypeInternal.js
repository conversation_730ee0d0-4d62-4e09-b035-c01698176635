var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_Parameters = injections.types["Parameters"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Methods = {};
    var var_createParamCeilingListAsync = {};
    var var_IParamCeilingParam_Array = {};
    var var_IParamCeilingParam = {};
    var var_stringType = {};
    var var_KVector3d = {};
    var var_numberType = {};
    var var_KVector3d_normalized = {};
    var var_KVector3d_reversed = {};
    var var_KVector3d_multiplied = {};
    var var_KVector3d_isEqual = {};
    var var_booleanType = {};
    var var_KVector3d_isPerpendicular = {};
    var var_KVector3d_isParallel = {};
    var var_KVector3d_isZero = {};
    var var_KVector3d_angle = {};
    var var_KVector3d_angleTo = {};
    var var_KVector3d_added = {};
    var var_KVector3d_subtracted = {};
    var var_KVector3d_dot = {};
    var var_KVector3d_cross = {};
    var var_KVector3d_clone = {};
    var var_KVector3d_isSameDirection = {};
    var var_KVector3d_isOnSameSide = {};
    var var_KVector3d_isOpposite = {};
    var var_KVector3d_appliedMatrix4 = {};
    var var_KMatrix4 = {};
    var var_KMatrix4_inverse = {};
    var var_undefinedType = {};
    var var_KMatrix4_inversed = {};
    var var_KMatrix4_multiply = {};
    var var_KMatrix4_multiplied = {};
    var var_KMatrix4_clone = {};
    var var_KMatrix4_isEqual = {};
    var var_KVector3d_appliedEuler = {};
    var var_KEuler = {};
    var var_KEuler_set = {};
    var var_KEuler_clone = {};
    var var_KEuler_copyFrom = {};
    var var_KEuler_setFromRotationMatrix = {};
    var var_KEuler_setFromQuaternion = {};
    var var_KQuaternion = {};
    var var_KQuaternion_set = {};
    var var_KQuaternion_clone = {};
    var var_KQuaternion_copyFrom = {};
    var var_KQuaternion_setFromEuler = {};
    var var_KQuaternion_setFromAxisAngle = {};
    var var_KQuaternion_getAxis = {};
    var var_KQuaternion_getAngle = {};
    var var_KQuaternion_setFromRotationMatrix = {};
    var var_KQuaternion_setFromUnitVectors = {};
    var var_KQuaternion_reverse = {};
    var var_KQuaternion_conjugate = {};
    var var_KQuaternion_dot = {};
    var var_KQuaternion_lengthSq = {};
    var var_KQuaternion_length = {};
    var var_KQuaternion_normalize = {};
    var var_KQuaternion_multiply = {};
    var var_KQuaternion_premultiply = {};
    var var_KQuaternion_multiplyQuaternions = {};
    var var_KQuaternion_slerp = {};
    var var_KQuaternion_swingTwistDecomposition = {};
    var var_KQuaternion_isEqual = {};
    var var_KQuaternion_fromArray = {};
    var var_numberType_Array = {};
    var var_KQuaternion_toArray = {};
    var var_KEuler_setFromVector3 = {};
    var var_KEuler_reorder = {};
    var var_KEuler_isEqual = {};
    var var_KEuler_fromArray = {};
    var var_unknownType_Array = {};
    var var_unknownType = {};
    var var_KEuler_toArray = {};
    var var_KEuler_toVector = {};
    var var_KVector3d_appliedQuaternion = {};
    var var_ElementId_Array_Promise = {};
    var var_ElementId_Array_Promise_then = {};
    var var_ElementId_Array_Promise_then_onresolve = {};
    var var_ElementId_Array = {};
    var var_ElementId = {};
    var var_Promise_then_onreject = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_getParamCeilingList = {};
    var var_IParamCeilingResult_Array = {};
    var var_IParamCeilingResult = {};
    var var_KPoint3d = {};
    var var_KPoint3d_isEqual = {};
    var var_KPoint3d_added = {};
    var var_KPoint3d_subtracted = {};
    var var_KPoint3d_distanceTo = {};
    var var_KPoint3d_squaredDistanceTo = {};
    var var_KPoint3d_clone = {};
    var var_KPoint3d_appliedMatrix4 = {};
    var var_KBoundingBox3d = {};
    var var_KBoundingBox3d_isEqual = {};
    var var_KBoundingBox3d_addPoint = {};
    var var_KBoundingBox3d_expand = {};
    var var_KBoundingBox3d_merge = {};
    var var_KBoundingBox3d_isValid = {};
    var var_KBoundingBox3d_isPointInside = {};
    var var_KBoundingBox3d_isInside = {};
    var var_KBoundingBox3d_isOverlapping = {};
    var var_deleteParamCeilingList = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_Parameters, exportName: "Parameters" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Methods": var_Methods,
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "createParamCeilingListAsync": var_createParamCeilingListAsync,
        "getParamCeilingList": var_getParamCeilingList,
        "deleteParamCeilingList": var_deleteParamCeilingList,
    };
    var_createParamCeilingListAsync.type = BasicType.Function;
    var_createParamCeilingListAsync.name = "createParamCeilingListAsync";
    var_createParamCeilingListAsync.varying = false;
    var_createParamCeilingListAsync.keepArgsHandle = false;
    var_createParamCeilingListAsync.args = [var_IParamCeilingParam_Array];
    var_createParamCeilingListAsync.return = var_ElementId_Array_Promise;
    var_IParamCeilingParam_Array.type = BasicType.Array;
    var_IParamCeilingParam_Array.value = var_IParamCeilingParam;
    var_IParamCeilingParam.type = BasicType.Object;
    var_IParamCeilingParam.properties = {
        "symbolId": var_stringType,
        "parameters": var_injection_Parameters,
        "position": var_KVector3d,
        "rotation": var_KVector3d,
    };
    var_stringType.type = BasicType.String;
    var_KVector3d.type = BasicType.Object;
    var_KVector3d.properties = {
        "x": var_numberType,
        "y": var_numberType,
        "z": var_numberType,
        "length": var_numberType,
        "normalized": var_KVector3d_normalized,
        "reversed": var_KVector3d_reversed,
        "multiplied": var_KVector3d_multiplied,
        "isEqual": var_KVector3d_isEqual,
        "isPerpendicular": var_KVector3d_isPerpendicular,
        "isParallel": var_KVector3d_isParallel,
        "isZero": var_KVector3d_isZero,
        "angle": var_KVector3d_angle,
        "angleTo": var_KVector3d_angleTo,
        "added": var_KVector3d_added,
        "subtracted": var_KVector3d_subtracted,
        "dot": var_KVector3d_dot,
        "cross": var_KVector3d_cross,
        "clone": var_KVector3d_clone,
        "isSameDirection": var_KVector3d_isSameDirection,
        "isOnSameSide": var_KVector3d_isOnSameSide,
        "isOpposite": var_KVector3d_isOpposite,
        "appliedMatrix4": var_KVector3d_appliedMatrix4,
        "appliedEuler": var_KVector3d_appliedEuler,
        "appliedQuaternion": var_KVector3d_appliedQuaternion,
    };
    var_numberType.type = BasicType.Number;
    var_KVector3d_normalized.type = BasicType.Function;
    var_KVector3d_normalized.name = "normalized";
    var_KVector3d_normalized.varying = false;
    var_KVector3d_normalized.keepArgsHandle = false;
    var_KVector3d_normalized.args = [];
    var_KVector3d_normalized.return = var_KVector3d;
    var_KVector3d_reversed.type = BasicType.Function;
    var_KVector3d_reversed.name = "reversed";
    var_KVector3d_reversed.varying = false;
    var_KVector3d_reversed.keepArgsHandle = false;
    var_KVector3d_reversed.args = [];
    var_KVector3d_reversed.return = var_KVector3d;
    var_KVector3d_multiplied.type = BasicType.Function;
    var_KVector3d_multiplied.name = "multiplied";
    var_KVector3d_multiplied.varying = false;
    var_KVector3d_multiplied.keepArgsHandle = false;
    var_KVector3d_multiplied.args = [var_numberType];
    var_KVector3d_multiplied.return = var_KVector3d;
    var_KVector3d_isEqual.type = BasicType.Function;
    var_KVector3d_isEqual.name = "isEqual";
    var_KVector3d_isEqual.varying = false;
    var_KVector3d_isEqual.keepArgsHandle = false;
    var_KVector3d_isEqual.args = [var_KVector3d, var_numberType, var_numberType];
    var_KVector3d_isEqual.return = var_booleanType;
    var_booleanType.type = BasicType.Boolean;
    var_KVector3d_isPerpendicular.type = BasicType.Function;
    var_KVector3d_isPerpendicular.name = "isPerpendicular";
    var_KVector3d_isPerpendicular.varying = false;
    var_KVector3d_isPerpendicular.keepArgsHandle = false;
    var_KVector3d_isPerpendicular.args = [var_KVector3d, var_numberType, var_booleanType];
    var_KVector3d_isPerpendicular.return = var_booleanType;
    var_KVector3d_isParallel.type = BasicType.Function;
    var_KVector3d_isParallel.name = "isParallel";
    var_KVector3d_isParallel.varying = false;
    var_KVector3d_isParallel.keepArgsHandle = false;
    var_KVector3d_isParallel.args = [var_KVector3d, var_numberType, var_booleanType];
    var_KVector3d_isParallel.return = var_booleanType;
    var_KVector3d_isZero.type = BasicType.Function;
    var_KVector3d_isZero.name = "isZero";
    var_KVector3d_isZero.varying = false;
    var_KVector3d_isZero.keepArgsHandle = false;
    var_KVector3d_isZero.args = [var_numberType];
    var_KVector3d_isZero.return = var_booleanType;
    var_KVector3d_angle.type = BasicType.Function;
    var_KVector3d_angle.name = "angle";
    var_KVector3d_angle.varying = false;
    var_KVector3d_angle.keepArgsHandle = false;
    var_KVector3d_angle.args = [var_KVector3d];
    var_KVector3d_angle.return = var_numberType;
    var_KVector3d_angleTo.type = BasicType.Function;
    var_KVector3d_angleTo.name = "angleTo";
    var_KVector3d_angleTo.varying = false;
    var_KVector3d_angleTo.keepArgsHandle = false;
    var_KVector3d_angleTo.args = [var_KVector3d, var_KVector3d];
    var_KVector3d_angleTo.return = var_numberType;
    var_KVector3d_added.type = BasicType.Function;
    var_KVector3d_added.name = "added";
    var_KVector3d_added.varying = false;
    var_KVector3d_added.keepArgsHandle = false;
    var_KVector3d_added.args = [var_KVector3d];
    var_KVector3d_added.return = var_KVector3d;
    var_KVector3d_subtracted.type = BasicType.Function;
    var_KVector3d_subtracted.name = "subtracted";
    var_KVector3d_subtracted.varying = false;
    var_KVector3d_subtracted.keepArgsHandle = false;
    var_KVector3d_subtracted.args = [var_KVector3d];
    var_KVector3d_subtracted.return = var_KVector3d;
    var_KVector3d_dot.type = BasicType.Function;
    var_KVector3d_dot.name = "dot";
    var_KVector3d_dot.varying = false;
    var_KVector3d_dot.keepArgsHandle = false;
    var_KVector3d_dot.args = [var_KVector3d];
    var_KVector3d_dot.return = var_numberType;
    var_KVector3d_cross.type = BasicType.Function;
    var_KVector3d_cross.name = "cross";
    var_KVector3d_cross.varying = false;
    var_KVector3d_cross.keepArgsHandle = false;
    var_KVector3d_cross.args = [var_KVector3d];
    var_KVector3d_cross.return = var_KVector3d;
    var_KVector3d_clone.type = BasicType.Function;
    var_KVector3d_clone.name = "clone";
    var_KVector3d_clone.varying = false;
    var_KVector3d_clone.keepArgsHandle = false;
    var_KVector3d_clone.args = [];
    var_KVector3d_clone.return = var_KVector3d;
    var_KVector3d_isSameDirection.type = BasicType.Function;
    var_KVector3d_isSameDirection.name = "isSameDirection";
    var_KVector3d_isSameDirection.varying = false;
    var_KVector3d_isSameDirection.keepArgsHandle = false;
    var_KVector3d_isSameDirection.args = [var_KVector3d, var_numberType, var_numberType, var_booleanType];
    var_KVector3d_isSameDirection.return = var_booleanType;
    var_KVector3d_isOnSameSide.type = BasicType.Function;
    var_KVector3d_isOnSameSide.name = "isOnSameSide";
    var_KVector3d_isOnSameSide.varying = false;
    var_KVector3d_isOnSameSide.keepArgsHandle = false;
    var_KVector3d_isOnSameSide.args = [var_KVector3d, var_numberType, var_numberType, var_booleanType];
    var_KVector3d_isOnSameSide.return = var_booleanType;
    var_KVector3d_isOpposite.type = BasicType.Function;
    var_KVector3d_isOpposite.name = "isOpposite";
    var_KVector3d_isOpposite.varying = false;
    var_KVector3d_isOpposite.keepArgsHandle = false;
    var_KVector3d_isOpposite.args = [var_KVector3d, var_numberType, var_numberType, var_booleanType];
    var_KVector3d_isOpposite.return = var_booleanType;
    var_KVector3d_appliedMatrix4.type = BasicType.Function;
    var_KVector3d_appliedMatrix4.name = "appliedMatrix4";
    var_KVector3d_appliedMatrix4.varying = false;
    var_KVector3d_appliedMatrix4.keepArgsHandle = false;
    var_KVector3d_appliedMatrix4.args = [var_KMatrix4];
    var_KVector3d_appliedMatrix4.return = var_KVector3d;
    var_KMatrix4.type = BasicType.Object;
    var_KMatrix4.properties = {
        "inverse": var_KMatrix4_inverse,
        "inversed": var_KMatrix4_inversed,
        "multiply": var_KMatrix4_multiply,
        "multiplied": var_KMatrix4_multiplied,
        "clone": var_KMatrix4_clone,
        "isEqual": var_KMatrix4_isEqual,
    };
    var_KMatrix4_inverse.type = BasicType.Function;
    var_KMatrix4_inverse.name = "inverse";
    var_KMatrix4_inverse.varying = false;
    var_KMatrix4_inverse.keepArgsHandle = false;
    var_KMatrix4_inverse.args = [var_booleanType];
    var_KMatrix4_inverse.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_KMatrix4_inversed.type = BasicType.Function;
    var_KMatrix4_inversed.name = "inversed";
    var_KMatrix4_inversed.varying = false;
    var_KMatrix4_inversed.keepArgsHandle = false;
    var_KMatrix4_inversed.args = [var_booleanType];
    var_KMatrix4_inversed.return = var_KMatrix4;
    var_KMatrix4_multiply.type = BasicType.Function;
    var_KMatrix4_multiply.name = "multiply";
    var_KMatrix4_multiply.varying = false;
    var_KMatrix4_multiply.keepArgsHandle = false;
    var_KMatrix4_multiply.args = [var_KMatrix4];
    var_KMatrix4_multiply.return = var_undefinedType;
    var_KMatrix4_multiplied.type = BasicType.Function;
    var_KMatrix4_multiplied.name = "multiplied";
    var_KMatrix4_multiplied.varying = false;
    var_KMatrix4_multiplied.keepArgsHandle = false;
    var_KMatrix4_multiplied.args = [var_KMatrix4];
    var_KMatrix4_multiplied.return = var_KMatrix4;
    var_KMatrix4_clone.type = BasicType.Function;
    var_KMatrix4_clone.name = "clone";
    var_KMatrix4_clone.varying = false;
    var_KMatrix4_clone.keepArgsHandle = false;
    var_KMatrix4_clone.args = [];
    var_KMatrix4_clone.return = var_KMatrix4;
    var_KMatrix4_isEqual.type = BasicType.Function;
    var_KMatrix4_isEqual.name = "isEqual";
    var_KMatrix4_isEqual.varying = false;
    var_KMatrix4_isEqual.keepArgsHandle = false;
    var_KMatrix4_isEqual.args = [var_KMatrix4, var_numberType];
    var_KMatrix4_isEqual.return = var_booleanType;
    var_KVector3d_appliedEuler.type = BasicType.Function;
    var_KVector3d_appliedEuler.name = "appliedEuler";
    var_KVector3d_appliedEuler.varying = false;
    var_KVector3d_appliedEuler.keepArgsHandle = false;
    var_KVector3d_appliedEuler.args = [var_KEuler];
    var_KVector3d_appliedEuler.return = var_KVector3d;
    var_KEuler.type = BasicType.Object;
    var_KEuler.properties = {
        "x": var_numberType,
        "y": var_numberType,
        "z": var_numberType,
        "order": var_numberType,
        "set": var_KEuler_set,
        "clone": var_KEuler_clone,
        "copyFrom": var_KEuler_copyFrom,
        "setFromRotationMatrix": var_KEuler_setFromRotationMatrix,
        "setFromQuaternion": var_KEuler_setFromQuaternion,
        "setFromVector3": var_KEuler_setFromVector3,
        "reorder": var_KEuler_reorder,
        "isEqual": var_KEuler_isEqual,
        "fromArray": var_KEuler_fromArray,
        "toArray": var_KEuler_toArray,
        "toVector": var_KEuler_toVector,
    };
    var_KEuler_set.type = BasicType.Function;
    var_KEuler_set.name = "set";
    var_KEuler_set.varying = false;
    var_KEuler_set.keepArgsHandle = false;
    var_KEuler_set.args = [var_numberType, var_numberType, var_numberType, var_numberType];
    var_KEuler_set.return = var_undefinedType;
    var_KEuler_clone.type = BasicType.Function;
    var_KEuler_clone.name = "clone";
    var_KEuler_clone.varying = false;
    var_KEuler_clone.keepArgsHandle = false;
    var_KEuler_clone.args = [];
    var_KEuler_clone.return = var_KEuler;
    var_KEuler_copyFrom.type = BasicType.Function;
    var_KEuler_copyFrom.name = "copyFrom";
    var_KEuler_copyFrom.varying = false;
    var_KEuler_copyFrom.keepArgsHandle = false;
    var_KEuler_copyFrom.args = [var_KEuler];
    var_KEuler_copyFrom.return = var_undefinedType;
    var_KEuler_setFromRotationMatrix.type = BasicType.Function;
    var_KEuler_setFromRotationMatrix.name = "setFromRotationMatrix";
    var_KEuler_setFromRotationMatrix.varying = false;
    var_KEuler_setFromRotationMatrix.keepArgsHandle = false;
    var_KEuler_setFromRotationMatrix.args = [var_KMatrix4, var_numberType];
    var_KEuler_setFromRotationMatrix.return = var_undefinedType;
    var_KEuler_setFromQuaternion.type = BasicType.Function;
    var_KEuler_setFromQuaternion.name = "setFromQuaternion";
    var_KEuler_setFromQuaternion.varying = false;
    var_KEuler_setFromQuaternion.keepArgsHandle = false;
    var_KEuler_setFromQuaternion.args = [var_KQuaternion, var_numberType];
    var_KEuler_setFromQuaternion.return = var_undefinedType;
    var_KQuaternion.type = BasicType.Object;
    var_KQuaternion.properties = {
        "x": var_numberType,
        "y": var_numberType,
        "z": var_numberType,
        "w": var_numberType,
        "set": var_KQuaternion_set,
        "clone": var_KQuaternion_clone,
        "copyFrom": var_KQuaternion_copyFrom,
        "setFromEuler": var_KQuaternion_setFromEuler,
        "setFromAxisAngle": var_KQuaternion_setFromAxisAngle,
        "getAxis": var_KQuaternion_getAxis,
        "getAngle": var_KQuaternion_getAngle,
        "setFromRotationMatrix": var_KQuaternion_setFromRotationMatrix,
        "setFromUnitVectors": var_KQuaternion_setFromUnitVectors,
        "reverse": var_KQuaternion_reverse,
        "conjugate": var_KQuaternion_conjugate,
        "dot": var_KQuaternion_dot,
        "lengthSq": var_KQuaternion_lengthSq,
        "length": var_KQuaternion_length,
        "normalize": var_KQuaternion_normalize,
        "multiply": var_KQuaternion_multiply,
        "premultiply": var_KQuaternion_premultiply,
        "multiplyQuaternions": var_KQuaternion_multiplyQuaternions,
        "slerp": var_KQuaternion_slerp,
        "swingTwistDecomposition": var_KQuaternion_swingTwistDecomposition,
        "isEqual": var_KQuaternion_isEqual,
        "fromArray": var_KQuaternion_fromArray,
        "toArray": var_KQuaternion_toArray,
    };
    var_KQuaternion_set.type = BasicType.Function;
    var_KQuaternion_set.name = "set";
    var_KQuaternion_set.varying = false;
    var_KQuaternion_set.keepArgsHandle = false;
    var_KQuaternion_set.args = [var_numberType, var_numberType, var_numberType, var_numberType];
    var_KQuaternion_set.return = var_undefinedType;
    var_KQuaternion_clone.type = BasicType.Function;
    var_KQuaternion_clone.name = "clone";
    var_KQuaternion_clone.varying = false;
    var_KQuaternion_clone.keepArgsHandle = false;
    var_KQuaternion_clone.args = [];
    var_KQuaternion_clone.return = var_KQuaternion;
    var_KQuaternion_copyFrom.type = BasicType.Function;
    var_KQuaternion_copyFrom.name = "copyFrom";
    var_KQuaternion_copyFrom.varying = false;
    var_KQuaternion_copyFrom.keepArgsHandle = false;
    var_KQuaternion_copyFrom.args = [var_KQuaternion];
    var_KQuaternion_copyFrom.return = var_undefinedType;
    var_KQuaternion_setFromEuler.type = BasicType.Function;
    var_KQuaternion_setFromEuler.name = "setFromEuler";
    var_KQuaternion_setFromEuler.varying = false;
    var_KQuaternion_setFromEuler.keepArgsHandle = false;
    var_KQuaternion_setFromEuler.args = [var_KEuler];
    var_KQuaternion_setFromEuler.return = var_undefinedType;
    var_KQuaternion_setFromAxisAngle.type = BasicType.Function;
    var_KQuaternion_setFromAxisAngle.name = "setFromAxisAngle";
    var_KQuaternion_setFromAxisAngle.varying = false;
    var_KQuaternion_setFromAxisAngle.keepArgsHandle = false;
    var_KQuaternion_setFromAxisAngle.args = [var_KVector3d, var_numberType];
    var_KQuaternion_setFromAxisAngle.return = var_undefinedType;
    var_KQuaternion_getAxis.type = BasicType.Function;
    var_KQuaternion_getAxis.name = "getAxis";
    var_KQuaternion_getAxis.varying = false;
    var_KQuaternion_getAxis.keepArgsHandle = false;
    var_KQuaternion_getAxis.args = [];
    var_KQuaternion_getAxis.return = var_KVector3d;
    var_KQuaternion_getAngle.type = BasicType.Function;
    var_KQuaternion_getAngle.name = "getAngle";
    var_KQuaternion_getAngle.varying = false;
    var_KQuaternion_getAngle.keepArgsHandle = false;
    var_KQuaternion_getAngle.args = [];
    var_KQuaternion_getAngle.return = var_numberType;
    var_KQuaternion_setFromRotationMatrix.type = BasicType.Function;
    var_KQuaternion_setFromRotationMatrix.name = "setFromRotationMatrix";
    var_KQuaternion_setFromRotationMatrix.varying = false;
    var_KQuaternion_setFromRotationMatrix.keepArgsHandle = false;
    var_KQuaternion_setFromRotationMatrix.args = [var_KMatrix4];
    var_KQuaternion_setFromRotationMatrix.return = var_undefinedType;
    var_KQuaternion_setFromUnitVectors.type = BasicType.Function;
    var_KQuaternion_setFromUnitVectors.name = "setFromUnitVectors";
    var_KQuaternion_setFromUnitVectors.varying = false;
    var_KQuaternion_setFromUnitVectors.keepArgsHandle = false;
    var_KQuaternion_setFromUnitVectors.args = [var_KVector3d, var_KVector3d];
    var_KQuaternion_setFromUnitVectors.return = var_undefinedType;
    var_KQuaternion_reverse.type = BasicType.Function;
    var_KQuaternion_reverse.name = "reverse";
    var_KQuaternion_reverse.varying = false;
    var_KQuaternion_reverse.keepArgsHandle = false;
    var_KQuaternion_reverse.args = [];
    var_KQuaternion_reverse.return = var_undefinedType;
    var_KQuaternion_conjugate.type = BasicType.Function;
    var_KQuaternion_conjugate.name = "conjugate";
    var_KQuaternion_conjugate.varying = false;
    var_KQuaternion_conjugate.keepArgsHandle = false;
    var_KQuaternion_conjugate.args = [];
    var_KQuaternion_conjugate.return = var_undefinedType;
    var_KQuaternion_dot.type = BasicType.Function;
    var_KQuaternion_dot.name = "dot";
    var_KQuaternion_dot.varying = false;
    var_KQuaternion_dot.keepArgsHandle = false;
    var_KQuaternion_dot.args = [var_KQuaternion];
    var_KQuaternion_dot.return = var_numberType;
    var_KQuaternion_lengthSq.type = BasicType.Function;
    var_KQuaternion_lengthSq.name = "lengthSq";
    var_KQuaternion_lengthSq.varying = false;
    var_KQuaternion_lengthSq.keepArgsHandle = false;
    var_KQuaternion_lengthSq.args = [];
    var_KQuaternion_lengthSq.return = var_numberType;
    var_KQuaternion_length.type = BasicType.Function;
    var_KQuaternion_length.name = "length";
    var_KQuaternion_length.varying = false;
    var_KQuaternion_length.keepArgsHandle = false;
    var_KQuaternion_length.args = [];
    var_KQuaternion_length.return = var_numberType;
    var_KQuaternion_normalize.type = BasicType.Function;
    var_KQuaternion_normalize.name = "normalize";
    var_KQuaternion_normalize.varying = false;
    var_KQuaternion_normalize.keepArgsHandle = false;
    var_KQuaternion_normalize.args = [];
    var_KQuaternion_normalize.return = var_undefinedType;
    var_KQuaternion_multiply.type = BasicType.Function;
    var_KQuaternion_multiply.name = "multiply";
    var_KQuaternion_multiply.varying = false;
    var_KQuaternion_multiply.keepArgsHandle = false;
    var_KQuaternion_multiply.args = [var_KQuaternion];
    var_KQuaternion_multiply.return = var_undefinedType;
    var_KQuaternion_premultiply.type = BasicType.Function;
    var_KQuaternion_premultiply.name = "premultiply";
    var_KQuaternion_premultiply.varying = false;
    var_KQuaternion_premultiply.keepArgsHandle = false;
    var_KQuaternion_premultiply.args = [var_KQuaternion];
    var_KQuaternion_premultiply.return = var_undefinedType;
    var_KQuaternion_multiplyQuaternions.type = BasicType.Function;
    var_KQuaternion_multiplyQuaternions.name = "multiplyQuaternions";
    var_KQuaternion_multiplyQuaternions.varying = false;
    var_KQuaternion_multiplyQuaternions.keepArgsHandle = false;
    var_KQuaternion_multiplyQuaternions.args = [var_KQuaternion, var_KQuaternion];
    var_KQuaternion_multiplyQuaternions.return = var_undefinedType;
    var_KQuaternion_slerp.type = BasicType.Function;
    var_KQuaternion_slerp.name = "slerp";
    var_KQuaternion_slerp.varying = false;
    var_KQuaternion_slerp.keepArgsHandle = false;
    var_KQuaternion_slerp.args = [var_KQuaternion, var_numberType];
    var_KQuaternion_slerp.return = var_undefinedType;
    var_KQuaternion_swingTwistDecomposition.type = BasicType.Function;
    var_KQuaternion_swingTwistDecomposition.name = "swingTwistDecomposition";
    var_KQuaternion_swingTwistDecomposition.varying = false;
    var_KQuaternion_swingTwistDecomposition.keepArgsHandle = false;
    var_KQuaternion_swingTwistDecomposition.args = [var_KVector3d, var_KQuaternion, var_KQuaternion];
    var_KQuaternion_swingTwistDecomposition.return = var_undefinedType;
    var_KQuaternion_isEqual.type = BasicType.Function;
    var_KQuaternion_isEqual.name = "isEqual";
    var_KQuaternion_isEqual.varying = false;
    var_KQuaternion_isEqual.keepArgsHandle = false;
    var_KQuaternion_isEqual.args = [var_KQuaternion];
    var_KQuaternion_isEqual.return = var_booleanType;
    var_KQuaternion_fromArray.type = BasicType.Function;
    var_KQuaternion_fromArray.name = "fromArray";
    var_KQuaternion_fromArray.varying = false;
    var_KQuaternion_fromArray.keepArgsHandle = false;
    var_KQuaternion_fromArray.args = [var_numberType_Array];
    var_KQuaternion_fromArray.return = var_undefinedType;
    var_numberType_Array.type = BasicType.Array;
    var_numberType_Array.value = var_numberType;
    var_KQuaternion_toArray.type = BasicType.Function;
    var_KQuaternion_toArray.name = "toArray";
    var_KQuaternion_toArray.varying = false;
    var_KQuaternion_toArray.keepArgsHandle = false;
    var_KQuaternion_toArray.args = [];
    var_KQuaternion_toArray.return = var_numberType_Array;
    var_KEuler_setFromVector3.type = BasicType.Function;
    var_KEuler_setFromVector3.name = "setFromVector3";
    var_KEuler_setFromVector3.varying = false;
    var_KEuler_setFromVector3.keepArgsHandle = false;
    var_KEuler_setFromVector3.args = [var_KVector3d, var_numberType];
    var_KEuler_setFromVector3.return = var_undefinedType;
    var_KEuler_reorder.type = BasicType.Function;
    var_KEuler_reorder.name = "reorder";
    var_KEuler_reorder.varying = false;
    var_KEuler_reorder.keepArgsHandle = false;
    var_KEuler_reorder.args = [var_numberType];
    var_KEuler_reorder.return = var_undefinedType;
    var_KEuler_isEqual.type = BasicType.Function;
    var_KEuler_isEqual.name = "isEqual";
    var_KEuler_isEqual.varying = false;
    var_KEuler_isEqual.keepArgsHandle = false;
    var_KEuler_isEqual.args = [var_KEuler];
    var_KEuler_isEqual.return = var_booleanType;
    var_KEuler_fromArray.type = BasicType.Function;
    var_KEuler_fromArray.name = "fromArray";
    var_KEuler_fromArray.varying = false;
    var_KEuler_fromArray.keepArgsHandle = false;
    var_KEuler_fromArray.args = [var_unknownType_Array];
    var_KEuler_fromArray.return = var_undefinedType;
    var_unknownType_Array.type = BasicType.Array;
    var_unknownType_Array.value = var_unknownType;
    var_unknownType.type = BasicType.Unknown;
    var_KEuler_toArray.type = BasicType.Function;
    var_KEuler_toArray.name = "toArray";
    var_KEuler_toArray.varying = false;
    var_KEuler_toArray.keepArgsHandle = false;
    var_KEuler_toArray.args = [];
    var_KEuler_toArray.return = var_unknownType_Array;
    var_KEuler_toVector.type = BasicType.Function;
    var_KEuler_toVector.name = "toVector";
    var_KEuler_toVector.varying = false;
    var_KEuler_toVector.keepArgsHandle = false;
    var_KEuler_toVector.args = [];
    var_KEuler_toVector.return = var_KVector3d;
    var_KVector3d_appliedQuaternion.type = BasicType.Function;
    var_KVector3d_appliedQuaternion.name = "appliedQuaternion";
    var_KVector3d_appliedQuaternion.varying = false;
    var_KVector3d_appliedQuaternion.keepArgsHandle = false;
    var_KVector3d_appliedQuaternion.args = [var_KQuaternion];
    var_KVector3d_appliedQuaternion.return = var_KVector3d;
    var_ElementId_Array_Promise.type = BasicType.Object;
    var_ElementId_Array_Promise.properties = {
        "then": var_ElementId_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_ElementId_Array_Promise_then.type = BasicType.Function;
    var_ElementId_Array_Promise_then.name = "";
    var_ElementId_Array_Promise_then.varying = false;
    var_ElementId_Array_Promise_then.keepArgsHandle = true;
    var_ElementId_Array_Promise_then.args = [var_ElementId_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_ElementId_Array_Promise_then.return = var_undefinedType;
    var_ElementId_Array_Promise_then_onresolve.type = BasicType.Function;
    var_ElementId_Array_Promise_then_onresolve.name = "";
    var_ElementId_Array_Promise_then_onresolve.varying = false;
    var_ElementId_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_ElementId_Array_Promise_then_onresolve.args = [var_ElementId_Array];
    var_ElementId_Array_Promise_then_onresolve.return = var_undefinedType;
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_getParamCeilingList.type = BasicType.Function;
    var_getParamCeilingList.name = "getParamCeilingList";
    var_getParamCeilingList.varying = false;
    var_getParamCeilingList.keepArgsHandle = false;
    var_getParamCeilingList.args = [var_ElementId_Array];
    var_getParamCeilingList.return = var_IParamCeilingResult_Array;
    var_IParamCeilingResult_Array.type = BasicType.Array;
    var_IParamCeilingResult_Array.value = var_IParamCeilingResult;
    var_IParamCeilingResult.type = BasicType.Object;
    var_IParamCeilingResult.properties = {
        "symbolId": var_stringType,
        "elementId": var_stringType,
        "position": var_KVector3d,
        "rotation": var_KVector3d,
        "parameters": var_injection_Parameters,
        "size": var_KPoint3d,
        "bbox3d": var_KBoundingBox3d,
    };
    var_KPoint3d.type = BasicType.Object;
    var_KPoint3d.properties = {
        "x": var_numberType,
        "y": var_numberType,
        "z": var_numberType,
        "isEqual": var_KPoint3d_isEqual,
        "added": var_KPoint3d_added,
        "subtracted": var_KPoint3d_subtracted,
        "distanceTo": var_KPoint3d_distanceTo,
        "squaredDistanceTo": var_KPoint3d_squaredDistanceTo,
        "clone": var_KPoint3d_clone,
        "appliedMatrix4": var_KPoint3d_appliedMatrix4,
    };
    var_KPoint3d_isEqual.type = BasicType.Function;
    var_KPoint3d_isEqual.name = "isEqual";
    var_KPoint3d_isEqual.varying = false;
    var_KPoint3d_isEqual.keepArgsHandle = false;
    var_KPoint3d_isEqual.args = [var_KPoint3d, var_numberType];
    var_KPoint3d_isEqual.return = var_booleanType;
    var_KPoint3d_added.type = BasicType.Function;
    var_KPoint3d_added.name = "added";
    var_KPoint3d_added.varying = false;
    var_KPoint3d_added.keepArgsHandle = false;
    var_KPoint3d_added.args = [var_KVector3d];
    var_KPoint3d_added.return = var_KPoint3d;
    var_KPoint3d_subtracted.type = BasicType.Function;
    var_KPoint3d_subtracted.name = "subtracted";
    var_KPoint3d_subtracted.varying = false;
    var_KPoint3d_subtracted.keepArgsHandle = false;
    var_KPoint3d_subtracted.args = [var_KPoint3d];
    var_KPoint3d_subtracted.return = var_KVector3d;
    var_KPoint3d_distanceTo.type = BasicType.Function;
    var_KPoint3d_distanceTo.name = "distanceTo";
    var_KPoint3d_distanceTo.varying = false;
    var_KPoint3d_distanceTo.keepArgsHandle = false;
    var_KPoint3d_distanceTo.args = [var_KPoint3d];
    var_KPoint3d_distanceTo.return = var_numberType;
    var_KPoint3d_squaredDistanceTo.type = BasicType.Function;
    var_KPoint3d_squaredDistanceTo.name = "squaredDistanceTo";
    var_KPoint3d_squaredDistanceTo.varying = false;
    var_KPoint3d_squaredDistanceTo.keepArgsHandle = false;
    var_KPoint3d_squaredDistanceTo.args = [var_KPoint3d];
    var_KPoint3d_squaredDistanceTo.return = var_numberType;
    var_KPoint3d_clone.type = BasicType.Function;
    var_KPoint3d_clone.name = "clone";
    var_KPoint3d_clone.varying = false;
    var_KPoint3d_clone.keepArgsHandle = false;
    var_KPoint3d_clone.args = [];
    var_KPoint3d_clone.return = var_KPoint3d;
    var_KPoint3d_appliedMatrix4.type = BasicType.Function;
    var_KPoint3d_appliedMatrix4.name = "appliedMatrix4";
    var_KPoint3d_appliedMatrix4.varying = false;
    var_KPoint3d_appliedMatrix4.keepArgsHandle = false;
    var_KPoint3d_appliedMatrix4.args = [var_KMatrix4];
    var_KPoint3d_appliedMatrix4.return = var_KPoint3d;
    var_KBoundingBox3d.type = BasicType.Object;
    var_KBoundingBox3d.properties = {
        "min": var_KPoint3d,
        "max": var_KPoint3d,
        "center": var_KPoint3d,
        "width": var_numberType,
        "height": var_numberType,
        "depth": var_numberType,
        "isEqual": var_KBoundingBox3d_isEqual,
        "addPoint": var_KBoundingBox3d_addPoint,
        "expand": var_KBoundingBox3d_expand,
        "merge": var_KBoundingBox3d_merge,
        "isValid": var_KBoundingBox3d_isValid,
        "isPointInside": var_KBoundingBox3d_isPointInside,
        "isInside": var_KBoundingBox3d_isInside,
        "isOverlapping": var_KBoundingBox3d_isOverlapping,
    };
    var_KBoundingBox3d_isEqual.type = BasicType.Function;
    var_KBoundingBox3d_isEqual.name = "isEqual";
    var_KBoundingBox3d_isEqual.varying = false;
    var_KBoundingBox3d_isEqual.keepArgsHandle = false;
    var_KBoundingBox3d_isEqual.args = [var_KBoundingBox3d, var_numberType];
    var_KBoundingBox3d_isEqual.return = var_booleanType;
    var_KBoundingBox3d_addPoint.type = BasicType.Function;
    var_KBoundingBox3d_addPoint.name = "addPoint";
    var_KBoundingBox3d_addPoint.varying = false;
    var_KBoundingBox3d_addPoint.keepArgsHandle = false;
    var_KBoundingBox3d_addPoint.args = [var_KPoint3d];
    var_KBoundingBox3d_addPoint.return = var_undefinedType;
    var_KBoundingBox3d_expand.type = BasicType.Function;
    var_KBoundingBox3d_expand.name = "expand";
    var_KBoundingBox3d_expand.varying = false;
    var_KBoundingBox3d_expand.keepArgsHandle = false;
    var_KBoundingBox3d_expand.args = [var_numberType];
    var_KBoundingBox3d_expand.return = var_undefinedType;
    var_KBoundingBox3d_merge.type = BasicType.Function;
    var_KBoundingBox3d_merge.name = "merge";
    var_KBoundingBox3d_merge.varying = false;
    var_KBoundingBox3d_merge.keepArgsHandle = false;
    var_KBoundingBox3d_merge.args = [var_KBoundingBox3d];
    var_KBoundingBox3d_merge.return = var_undefinedType;
    var_KBoundingBox3d_isValid.type = BasicType.Function;
    var_KBoundingBox3d_isValid.name = "isValid";
    var_KBoundingBox3d_isValid.varying = false;
    var_KBoundingBox3d_isValid.keepArgsHandle = false;
    var_KBoundingBox3d_isValid.args = [];
    var_KBoundingBox3d_isValid.return = var_booleanType;
    var_KBoundingBox3d_isPointInside.type = BasicType.Function;
    var_KBoundingBox3d_isPointInside.name = "isPointInside";
    var_KBoundingBox3d_isPointInside.varying = false;
    var_KBoundingBox3d_isPointInside.keepArgsHandle = false;
    var_KBoundingBox3d_isPointInside.args = [var_KPoint3d, var_numberType];
    var_KBoundingBox3d_isPointInside.return = var_booleanType;
    var_KBoundingBox3d_isInside.type = BasicType.Function;
    var_KBoundingBox3d_isInside.name = "isInside";
    var_KBoundingBox3d_isInside.varying = false;
    var_KBoundingBox3d_isInside.keepArgsHandle = false;
    var_KBoundingBox3d_isInside.args = [var_KBoundingBox3d, var_numberType];
    var_KBoundingBox3d_isInside.return = var_booleanType;
    var_KBoundingBox3d_isOverlapping.type = BasicType.Function;
    var_KBoundingBox3d_isOverlapping.name = "isOverlapping";
    var_KBoundingBox3d_isOverlapping.varying = false;
    var_KBoundingBox3d_isOverlapping.keepArgsHandle = false;
    var_KBoundingBox3d_isOverlapping.args = [var_KBoundingBox3d, var_numberType];
    var_KBoundingBox3d_isOverlapping.return = var_booleanType;
    var_deleteParamCeilingList.type = BasicType.Function;
    var_deleteParamCeilingList.name = "deleteParamCeilingList";
    var_deleteParamCeilingList.varying = false;
    var_deleteParamCeilingList.keepArgsHandle = false;
    var_deleteParamCeilingList.args = [var_ElementId_Array];
    var_deleteParamCeilingList.return = var_booleanType;
    
    return var_sourceFile;
};
