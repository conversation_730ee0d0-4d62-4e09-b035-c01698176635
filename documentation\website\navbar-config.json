{"services": [{"id": "camera", "name": "Camera Infrastructure API", "icon": "🏠", "description": "相机基础设施 REST API\n\n为群核旗下多个设计工具提供统一的相机查询和管理功能，支持多种相机类型的预设，\n帮助用户在设计过程中快速切换和保存不同的观察角度。\n\n**支持的设计工具：**\n详见 `ToolAppType` 枚举定义\n\n**支持的功能：**\n- 根据不同设计工具的标识和参数查询相机\n- 支持漫游视图、全景图、3D鸟瞰视图\n- 相机参数配置管理\n- 跨工具的相机数据统一格式\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：待定\n", "version": "0.0.1", "baseUrl": "/camera/v1", "modules": [{"name": "Camera Infrastructure", "description": "相机基础设施接口\n\n提供统一的相机查询功能，支持多个设计工具的不同参数需求。\n每个相机包含完整的相机参数配置，可用于渲染引擎恢复用户保存的观察状态。\n\n**支持的相机类型：**\n- `normal`: 漫游视图 - 用于室内漫游浏览\n- `panorama`: 全景图 - 360度全景查看\n- `view3d`: 3D鸟瞰视图 - 整体空间俯视\n\n**参数需求：**\n- **酷家乐 (kujiale)**: 需要 designId + levelId\n\n  \n"}], "servers": [{"url": "http://localhost:8083", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "endpointsCount": 1}, {"id": "doorwindow", "name": "门窗设计管理API", "icon": "🏠", "description": "门窗设计服务REST API\n\n为群核旗下设计工具提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。提供高效的批量操作和异步处理机制，确保大规模门窗数据处理的性能和稳定性。\n\n**核心功能：**\n- 门窗文档查询：获取指定楼层的完整门窗信息\n- 批量更新操作：支持多种门窗操作类型的批量处理\n- 多种门窗类型：简单门、简单窗、复杂门窗组合\n- 多视图支持：BASIC、ADVANCED、FULL等不同详细程度的数据视图\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 操作类型丰富：replace（替换）、attach（附加）、detach（分离）、update（更新）\n\n**门窗类型支持：**\n- 简单门窗：单一门窗单元，如普通门、普通窗\n- 复杂门窗：组合型门窗，如L型窗、U型窗、转角门等\n- 门类型：单开门、双开门、推拉门、折叠门等\n- 窗类型：平开窗、推拉窗、百叶窗、落地窗等\n\n**业务应用场景：**\n- 建筑设计软件的门窗配置\n- 门窗产品的三维建模和展示\n- 建筑开口与门窗的关联管理\n- 门窗规格和参数的批量调整\n- 复杂门窗组合的快速创建\n- 门窗数据的批量导入和同步\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 批量限制：单次批量操作最多20个门窗\n- 异步支持：长时间批量操作的异步处理机制", "version": "v1.0.0", "baseUrl": "/doorwindow/v1", "modules": [{"name": "门窗管理 API", "description": "门窗设计服务的 REST API，提供门窗模型的查询和批量更新操作。支持简单门窗（单一门窗单元）和复杂门窗（组合型门窗，如L型、U型等复杂结构）的完整生命周期管理。"}], "servers": [{"url": "http://localhost:8080", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "endpointsCount": 2}, {"id": "furniture", "name": "家具设计管理API", "icon": "🏠", "description": "家具设计服务REST API\n\n为群核旗下设计工具提供家具数据的完整生命周期管理接口，支持家具模型的创建、编辑、查询和管理。提供单个和批量操作，支持不同数据视图以优化性能，适用于各种家具设计和管理场景。\n\n**核心功能：**\n- 家具CRUD操作：创建、获取、更新、删除家具实例\n- 批量操作支持：高效处理大量家具数据的批量操作\n- 多视图支持：BASIC和FULL视图，针对不同场景优化性能\n- 分页查询：支持大量家具数据的分页展示和管理\n- 产品组合：支持通过组合商品批量创建相关家具\n- 异步处理：批量操作采用异步机制，保证系统响应性能\n- 幂等性控制：通过requestId确保操作的幂等性\n\n**业务应用场景：**\n- 室内设计软件的家具管理\n- 家具产品的三维展示和编辑\n- 场景模板和套装家具的快速应用\n- 家具数据的批量导入和同步\n- 个性化家具定制和配置\n- 空间布局优化和家具摆放\n\n**技术特性：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 异步支持：长时间批量操作的异步处理机制", "version": "1.0.0", "baseUrl": "/furniture/v1", "modules": [{"name": "家具管理接口", "description": "提供家具数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询等功能。支持不同数据视图以优化性能。"}], "servers": [{"url": "http://localhost:8083", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "endpointsCount": 7}, {"id": "k<PERSON><PERSON>", "name": "KooLux REST API", "icon": "🏠", "description": "KooLux照明设计的 REST API，提供照明模型、IES的全生命周期管理。  \n\n## 功能特性  \n- 支持IES文件的管理  \n- 支持照明模型管理  \n- 批量操作支持（创建、更新、删除、查询）  \n\n## 数据模型  \n- **照明模型**: 支持材质编辑和灯光模型排除  \n- **IES光源**: 包含IES文件全量信息\n", "version": "1.0.0", "baseUrl": "/koolux/v1", "modules": [{"name": "KooLuxLight", "description": "照明灯光场景编辑接口"}], "servers": [{"url": "https://www.koolux.com", "description": "KooLux官网地址"}], "endpointsCount": 3}, {"id": "layout", "name": "户型图例管理API", "icon": "🏠", "description": "户型图例管理服务REST API\n\n提供图例数据的完整生命周期管理接口，图例是户型设计中用于标识空间元素的核心组件。支持单个和批量操作，包括图例的创建、查询、更新、删除以及复杂的图例组合功能。\n\n**主要功能特性：**\n- 单个图例CRUD操作：创建、获取、更新、删除单个图例\n- 批量操作支持：高效的批量创建、获取、更新、删除操作\n- 分页查询：支持大量图例数据的分页展示和管理\n- 图例组合：支持创建图例组合，实现复杂的空间布局\n- 异步处理：批量操作采用异步处理机制，确保大数据量操作的性能\n- 权限控制：严格的读写权限控制，保障数据安全\n\n**业务场景应用：**\n- 户型设计工具中的图例管理\n- 空间布局设计和优化\n- 图例模板和组合的快速应用\n- 户型数据的批量导入导出\n- 三维空间定位和图层管理\n\n**技术规范：**\n- 响应格式：JSON\n- 字符编码：UTF-8\n- 认证方式：基于设计方案的权限验证\n- 异步操作：支持长时间批量操作的异步处理", "version": "1.0.0", "baseUrl": "/layout/v1", "modules": [{"name": "图例管理接口", "description": "提供图例数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询、图例组合等功能。图例是户型设计中用于标识空间元素的核心组件。"}], "servers": [{"url": "http://localhost:8080", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "endpointsCount": 8}, {"id": "pdm", "name": "平面造型设计API", "icon": "🏠", "description": "平面造型设计REST API\n\n    ## 功能特性\n    - 平面造型的 CRUD 操作\n    - 批量操作支持（创建、更新、删除、查询）\n    - 分页查询支持\n    \n    ## 数据模型\n    - **平面造型**: 包含平面造型所在的面、平面造型形状信息\n    \n    ## 分页机制\n    使用基于页码的分页，支持自定义页面大小", "version": "1.0.0", "baseUrl": "/pdm/v1", "modules": [{"name": "平面造型设计管理接口", "description": "提供平面造型设计数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询等功能。支持不同数据视图以优化性能。"}], "servers": [{"url": "http://localhost:8083", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "endpointsCount": 8}], "dropdownItems": "            {\n              type: 'docSidebar',\n              sidebarId: 'cameraSidebar',\n              label: '🏠 Camera Infrastructure API',\n            },\n            {\n              type: 'docSidebar',\n              sidebarId: 'doorwindowSidebar',\n              label: '🏠 门窗设计管理API',\n            },\n            {\n              type: 'docSidebar',\n              sidebarId: 'furnitureSidebar',\n              label: '🏠 家具设计管理API',\n            },\n            {\n              type: 'docSidebar',\n              sidebarId: 'kooluxSidebar',\n              label: '🏠 KooLux REST API',\n            },\n            {\n              type: 'docSidebar',\n              sidebarId: 'layoutSidebar',\n              label: '🏠 户型图例管理API',\n            },\n            {\n              type: 'docSidebar',\n              sidebarId: 'pdmSidebar',\n              label: '🏠 平面造型设计API',\n            },"}