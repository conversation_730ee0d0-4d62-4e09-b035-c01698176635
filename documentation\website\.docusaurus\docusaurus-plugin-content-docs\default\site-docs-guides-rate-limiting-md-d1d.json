{"id": "guides/rate-limiting", "title": "速率限制", "description": "为了确保服务的稳定性和公平使用，群核科技 API 实施了速率限制机制。了解这些限制并在您的应用中正确处理，是构建稳定应用的关键。", "source": "@site/docs/guides/rate-limiting.md", "sourceDirName": "guides", "slug": "/guides/rate-limiting", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/rate-limiting", "draft": false, "unlisted": false, "tags": [], "version": "current", "frontMatter": {"id": "rate-limiting", "title": "速率限制", "sidebar_label": "速率限制"}, "sidebar": "tutorialSidebar", "previous": {"title": "最佳实践", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/best-practices"}, "next": {"title": "Webhooks", "permalink": "/manycoreapi-demo/0.0.4/docs/guides/webhooks"}}