#!/bin/bash

# 导入现有 GPG 密钥脚本
# 用于在新机器上导入已有的 GPG 密钥

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查 GPG 是否安装
check_gpg() {
    if ! command -v gpg >/dev/null 2>&1; then
        print_error "GPG 未安装，请先安装 GPG"
        echo "Ubuntu/Debian: sudo apt-get install gnupg"
        echo "macOS: brew install gnupg"
        echo "Windows: 下载 GPG4Win"
        exit 1
    fi
}

# 导入方式选择
select_import_method() {
    echo "🔑 选择 GPG 密钥导入方式："
    echo "1. 从文件导入（推荐）"
    echo "2. 从 Base64 字符串导入"
    echo "3. 从密钥服务器导入公钥（如果私钥已在其他地方）"
    echo ""
    read -p "请选择 (1-3): " method
    
    case $method in
        1) import_from_file ;;
        2) import_from_base64 ;;
        3) import_from_keyserver ;;
        *) print_error "无效选择"; exit 1 ;;
    esac
}

# 从文件导入
import_from_file() {
    print_status "从文件导入 GPG 密钥"
    echo ""
    echo "请将你的 GPG 私钥文件放到当前目录或提供文件路径"
    echo "例如：private-key.asc, my-gpg-key.txt 等"
    echo ""
    read -p "请输入私钥文件路径: " key_file
    
    if [ ! -f "$key_file" ]; then
        print_error "文件不存在: $key_file"
        exit 1
    fi
    
    print_status "导入私钥文件: $key_file"
    if gpg --import "$key_file"; then
        print_success "私钥导入成功"
    else
        print_error "私钥导入失败"
        exit 1
    fi
    
    # 检查是否还有公钥文件
    echo ""
    read -p "是否还有公钥文件需要导入？(y/N): " import_public
    if [ "$import_public" = "y" ] || [ "$import_public" = "Y" ]; then
        read -p "请输入公钥文件路径: " pub_file
        if [ -f "$pub_file" ]; then
            gpg --import "$pub_file"
            print_success "公钥导入成功"
        fi
    fi
}

# 从 Base64 字符串导入
import_from_base64() {
    print_status "从 Base64 字符串导入 GPG 密钥"
    echo ""
    echo "请粘贴你的 Base64 编码的 GPG 私钥："
    echo "(多行内容，完成后按 Ctrl+D)"
    echo ""
    
    local base64_key=""
    while IFS= read -r line; do
        base64_key="$base64_key$line"
    done
    
    if [ -z "$base64_key" ]; then
        print_error "未输入 Base64 密钥"
        exit 1
    fi
    
    print_status "解码并导入密钥..."
    if echo "$base64_key" | base64 -d | gpg --import; then
        print_success "密钥导入成功"
    else
        print_error "密钥导入失败，请检查 Base64 格式"
        exit 1
    fi
}

# 从密钥服务器导入公钥
import_from_keyserver() {
    print_status "从密钥服务器导入公钥"
    echo ""
    echo "注意：这种方式只能导入公钥，私钥需要其他方式导入"
    echo ""
    read -p "请输入 GPG 密钥 ID (例如: 1234567890ABCDEF): " key_id
    
    if [ -z "$key_id" ]; then
        print_error "密钥 ID 不能为空"
        exit 1
    fi
    
    print_status "从密钥服务器导入公钥: $key_id"
    
    local servers=("keyserver.ubuntu.com" "keys.openpgp.org" "pgp.mit.edu")
    local imported=false
    
    for server in "${servers[@]}"; do
        print_status "尝试从 $server 导入..."
        if gpg --keyserver "$server" --recv-keys "$key_id"; then
            print_success "从 $server 导入成功"
            imported=true
            break
        else
            print_warning "从 $server 导入失败"
        fi
    done
    
    if [ "$imported" = false ]; then
        print_error "从所有密钥服务器导入都失败"
        exit 1
    fi
    
    print_warning "注意：只导入了公钥，你仍需要私钥来进行签名"
}

# 验证导入的密钥
verify_import() {
    print_status "验证导入的密钥..."
    echo ""
    
    local secret_keys=$(gpg --list-secret-keys --keyid-format LONG 2>/dev/null)
    if [ -z "$secret_keys" ]; then
        print_warning "未找到私钥，只有公钥无法进行签名"
        return 1
    fi
    
    print_success "发现私钥："
    echo "$secret_keys"
    echo ""
    
    # 获取密钥 ID
    local key_id=$(echo "$secret_keys" | grep sec | head -1 | sed 's/.*\/\([A-F0-9]*\) .*/\1/')
    if [ -n "$key_id" ]; then
        print_status "检测到密钥 ID: $key_id"
        
        # 测试签名
        print_status "测试签名功能..."
        if echo "test" | gpg --armor --local-user "$key_id" --sign >/dev/null 2>&1; then
            print_success "GPG 签名测试通过"
            return 0
        else
            print_warning "GPG 签名测试失败，可能需要输入密码"
            return 1
        fi
    fi
    
    return 1
}

# 创建配置文件
create_config() {
    local key_id=""
    
    # 获取密钥 ID
    local secret_keys=$(gpg --list-secret-keys --keyid-format LONG 2>/dev/null)
    if [ -n "$secret_keys" ]; then
        key_id=$(echo "$secret_keys" | grep sec | head -1 | sed 's/.*\/\([A-F0-9]*\) .*/\1/')
    fi
    
    if [ -z "$key_id" ]; then
        print_warning "无法自动获取密钥 ID，请手动设置"
        read -p "请输入你的 GPG 密钥 ID: " key_id
    fi
    
    local config_file="$HOME/.manycore-sdk-config"
    
    print_status "创建配置文件: $config_file"
    
    # 询问 GPG 密码
    read -s -p "请输入 GPG 密钥密码: " gpg_passphrase
    echo ""
    
    cat > "$config_file" << EOF
# ManyCore SDK 发布配置
# 此文件包含本地 GPG 设置，请妥善保管

# GPG 配置（已导入到本地密钥环）
export GPG_KEY_NAME="$key_id"
export GPG_PASSPHRASE="$gpg_passphrase"

# Maven Central 配置（需要手动填写）
export CENTRAL_USERNAME="你的Maven Central用户名"
export CENTRAL_PASSWORD="你的Maven Central令牌"

# 使用方法：
# source ~/.manycore-sdk-config
# scripts/local/publish.sh doorwindow
EOF

    chmod 600 "$config_file"
    print_success "配置文件已创建"
    print_warning "请编辑该文件，填写你的 Maven Central 认证信息"
}

# 显示后续步骤
show_next_steps() {
    echo ""
    print_success "GPG 密钥导入完成！"
    echo ""
    print_status "下一步："
    echo "1. 编辑配置文件填写 Maven Central 认证信息:"
    echo "   nano ~/.manycore-sdk-config"
    echo ""
    echo "2. 测试发布:"
    echo "   source ~/.manycore-sdk-config"
    echo "   scripts/local/publish.sh --dry-run doorwindow"
    echo ""
    echo "3. 正式发布:"
    echo "   scripts/local/publish.sh doorwindow"
    echo ""
}

# 主函数
main() {
    echo "🔑 导入现有 GPG 密钥助手"
    echo "========================"
    echo ""
    
    check_gpg
    
    # 检查是否已有密钥
    if gpg --list-secret-keys 2>/dev/null | grep -q "sec"; then
        print_warning "发现现有的 GPG 密钥："
        gpg --list-secret-keys --keyid-format LONG
        echo ""
        read -p "是否要继续导入新密钥？(y/N): " continue_import
        if [ "$continue_import" != "y" ] && [ "$continue_import" != "Y" ]; then
            print_status "使用现有密钥创建配置"
            create_config
            show_next_steps
            exit 0
        fi
    fi
    
    # 选择导入方式
    select_import_method
    
    # 验证导入
    if verify_import; then
        # 创建配置文件
        create_config
        show_next_steps
    else
        print_error "密钥导入验证失败"
        echo ""
        print_status "可能的原因："
        echo "- 私钥格式错误"
        echo "- 密钥已损坏"
        echo "- 只导入了公钥，缺少私钥"
        echo ""
        echo "请检查并重新尝试"
    fi
}

main "$@" 