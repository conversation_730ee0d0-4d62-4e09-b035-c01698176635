const child_process = require('child_process');
const fse = require('fs-extra');

const prevVersion = JSON.parse(fse.readFileSync('./lerna.json', 'utf8')).version

child_process.execSync('npx lerna version minor --yes');

const newVersion = JSON.parse(fse.readFileSync('./lerna.json', 'utf8')).version

function echoAndExec(cmd) {
    console.log(`$ ${cmd}`);
    child_process.execSync(cmd);
};

if (prevVersion !== newVersion) {
    child_process.execSync('git add -A');
    const gitUtils = require('@qunhe/tools-script/utils/gitUtils');
    const branchName = gitUtils.getCurrentBranchName();
    if (!branchName) {
        throw new Error('cannot get current branch name');
    }
    echoAndExec(`git checkout ${branchName}`);
    gitUtils.setupWritableRepository();
    echoAndExec(`git commit -m "chore(release): ${newVersion}"`);
    echoAndExec(`git tag -a ${newVersion} -m "${newVersion}"`);
    echoAndExec('git push');
    echoAndExec(`git push origin ${newVersion}`);
    console.log(`# ${newVersion} is ready to be published`);
} else {
    console.log("# No new version found");
}

