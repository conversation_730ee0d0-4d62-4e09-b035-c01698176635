import * as path from 'path';
import * as fse from 'fs-extra';
import * as ts from 'typescript';
import * as _ from 'lodash';
import { ensureGetSymbol, follow<PERSON>lias, getFilePosition, getNodeDesc, throwIfHasCompilerErrors, toModuleReference } from './utils';

function getLocalVarNames(sourceFile: ts.SourceFile) {
    const localVarMap = (sourceFile as any).locals as Map<string, ts.Symbol>;
    return Array.from(localVarMap.keys())
        .filter(name => name !== '__global');  // 去除特殊的 global 局部变量
}

/**
 * 将多个文件导出的类型声明合并在一起导出
 *
 * e.g. 将：
 *  ```ts
 *  // api_1.d.ts
 *  export declare namespace NS {
 *      function func(): void;
 *  }
 *  // api_2.d.ts
 *  export declare namespace NS {
 *      interface Bar {}
 *  }
 *  ```
 *
 *  转换为：
 *  ```ts
 *  // api-merged.d.ts
 *  export declare namespace NS {
 *      function func(): void;
 *  }
 *  export declare namespace NS {
 *      namespace NS {
 *          interface Bar {}
 *      }
 *  }
 *  ```
 */
export function mergeApis(apiDtsFiles: string[], outputMergedApiFile: string) {
    apiDtsFiles = apiDtsFiles.map(f => path.resolve(f));
    outputMergedApiFile = path.resolve(outputMergedApiFile);

    // 创建 ts program
    const printer = ts.createPrinter();
    const program = ts.createProgram(apiDtsFiles, { types: [] });
    const typeChecker = program.getTypeChecker();
    throwIfHasCompilerErrors(program);
    const apiSourceFiles = apiDtsFiles.map(f => program.getSourceFile(f)!);

    // step 1: 获取所有导出接口类型声明（当前只支持 namespace 接口导出）
    const exportedDeclarationsByFile = new Map<ts.SourceFile, Array<{
        declaration: ts.ModuleDeclaration;
        /** 该声明在 API 入口文件中的导出名 */
        finalExportName: string;
        /** 该声明的局部命名 */
        localName: string;
        /** 该声明是否为内联导出，即在该声明上直接导出，如 `export interface Foo {}` */
        isInlineExported: boolean;
    }>>();
    apiSourceFiles.forEach(apiSourceFile => {
        typeChecker.getExportsOfModule(ensureGetSymbol(apiSourceFile, typeChecker)).forEach(exportSymbol => {
            const followedSymbol = followAlias(exportSymbol, typeChecker);
            if (!followedSymbol.declarations?.length) {
                throw new Error(`找不到导出 symbol "${followedSymbol.name}" 的声明. 当前 api 文件为: ${apiSourceFile.fileName}`);
            }
            followedSymbol.declarations.forEach(declaration => {
                if (!ts.isModuleDeclaration(declaration)) {
                    throw new Error(`仅支持 namespace 导出，当前声明为: ${getNodeDesc(declaration)}`);
                }

                let exportedDeclarationsOfFile = exportedDeclarationsByFile.get(declaration.getSourceFile());
                if (!exportedDeclarationsOfFile) {
                    exportedDeclarationsOfFile = [];
                    exportedDeclarationsByFile.set(declaration.getSourceFile(), exportedDeclarationsOfFile);
                }
                exportedDeclarationsOfFile.push({
                    declaration,
                    finalExportName: exportSymbol.name,
                    localName: declaration.name.text,
                    isInlineExported: (declaration.modifiers || []).some(m => m.kind === ts.SyntaxKind.ExportKeyword),
                });
            });
        });
    });

    // step 2: 更改接口声明源文件：移除接口类型声明，导出所有局部变量，并从合并文件中重新导出原接口
    exportedDeclarationsByFile.forEach((declarations, sourceFile) => {
        const localNamesOfExported = new Set(declarations.map(d => d.localName));
        const transformResult = ts.transform(sourceFile, [(context) => (sourceFileNode) => {
            return ts.visitEachChild(sourceFileNode, (node) => {
                if (declarations.some(d => d.declaration === node)) {
                    // 移除源接口定义
                    return undefined;
                }
                return node;
            }, context);
        }]);

        fse.outputFileSync(sourceFile.fileName, [
            // 添加声明，从合并文件中重新导出接口
            `import { ${
                _.uniqWith(declarations, (a, b) => a.localName === b.localName)
                    .map(d => `${d.finalExportName} as ${d.localName}`)
                    .join(', ')
            } } from '${toModuleReference(sourceFile.fileName, outputMergedApiFile)}';`,
            `export { ${_.uniq(declarations.filter(d => d.isInlineExported).map(d => d.localName)).join(', ')} };`,

            printer.printBundle(ts.factory.createBundle(transformResult.transformed)),

            // 添加局部变量导出
            `export { ${getLocalVarNames(sourceFile).filter(n => !localNamesOfExported.has(n)).join(', ')} };`,

            `export {}`
        ].join('\n'));
    });

    // step 3: 合并接口声明，生成新的 dts 文件
    const getModuleVar = (() => {
        const moduleVarBySourceFile = new Map<ts.SourceFile, string>();
        let moduleVarCount = 0;
        return (sourceFile: ts.SourceFile) => {
            let moduleVar = moduleVarBySourceFile.get(sourceFile);
            if (!moduleVar) {
                moduleVar = `$module_var_${moduleVarCount++}`;
                moduleVarBySourceFile.set(sourceFile, moduleVar);
            }
            return moduleVar;
        }
    })();

    const mergedApiFileContent = [
        Array.from(exportedDeclarationsByFile.keys())
            .map((sourceFile) => {
                return `import * as ${getModuleVar(sourceFile)} from "${toModuleReference(outputMergedApiFile, sourceFile.fileName)}";`;
            })
            .join('\n'),

        Array.from(exportedDeclarationsByFile.entries()).map(([sourceFile, declarations]) => {
            const moduleVar = getModuleVar(sourceFile);
            return declarations.map(({ declaration }) => {
                if (!(ts.isModuleDeclaration(declaration) && declaration.body && ts.isModuleBlock(declaration.body))) {
                    throw new Error(`暂时只支持 namespace 形式声明，不支持节点: ${getFilePosition(declaration)} ${declaration.getText()}`);
                }
                const newNode = ts.factory.updateModuleDeclaration(
                    declaration,
                    undefined,
                    [ts.factory.createModifier(ts.SyntaxKind.ExportKeyword), ts.factory.createModifier(ts.SyntaxKind.DeclareKeyword)],
                    declaration.name,
                    ts.factory.updateModuleBlock(
                        declaration.body,
                        getLocalVarNames(sourceFile).map((localVarName): ts.Statement => {
                            return ts.factory.createImportEqualsDeclaration(
                                undefined,
                                undefined,
                                false,
                                localVarName,
                                ts.factory.createQualifiedName(
                                    ts.factory.createIdentifier(moduleVar),
                                    localVarName
                                )
                            );
                        }).concat(declaration.body.statements)
                    )
                );

                return printer.printNode(ts.EmitHint.Unspecified, newNode, sourceFile);
            }).join('\n');
        }).join('\n'),

        `export {}`
    ].join('\n');

    fse.outputFileSync(outputMergedApiFile, mergedApiFileContent);
}
