openapi: 3.1.0
info:
  title: REST API SDK Data Schemas
  description: |-
    REST API SDK 数据结构 OpenAPI 规范文档

    本文档定义了 REST API SDK 中所有数据传输对象的结构规范，包括请求、响应、错误处理等相关的数据类型。
    
    该规范遵循 Google API 设计指南和 OpenAPI 3.0 标准，确保 API 设计的一致性和可维护性。
  version: 1.0.0
  contact:
    name: Qunhe Tech
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://www.manycore.com/license

components:
  schemas:
    # =====================================
    # 错误处理相关数据结构
    # =====================================
    ApiError:
      type: object
      title: ApiError
      description: REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范
      required:
        - code
        - message
        - status
        - details
      properties:
        code:
          type: integer
          description: HTTP 状态码（数字），例如 403、404、500等
          example: 403
          minimum: 100
          maximum: 599
        message:
          type: string
          description: 面向开发者的错误消息，应该使用英文
          example: "Permission denied"
          minLength: 1
          maxLength: 1000
        status:
          $ref: "#/components/schemas/Code"
          description: RestAPI 对应的状态码枚举值
        details:
          $ref: "#/components/schemas/ErrorDetails"
          description: 错误的详细信息
        localizedMessage:
          $ref: "#/components/schemas/LocalizedMessage"
          description: 错误的本地化消息，可选字段
        help:
          $ref: "#/components/schemas/Help"
          description: 错误的帮助信息，可选字段
      additionalProperties: false
      x-module: error
      x-category: 错误处理

    Code:
      type: string
      title: Code
      description: API 错误代码枚举，定义了所有可能的错误类型及其 HTTP 状态码映射
      enum:
        - OK
        - CANCELLED
        - UNKNOWN
        - INVALID_ARGUMENT
        - DEADLINE_EXCEEDED
        - NOT_FOUND
        - ALREADY_EXISTS
        - PERMISSION_DENIED
        - UNAUTHENTICATED
        - RESOURCE_EXHAUSTED
        - FAILED_PRECONDITION
        - ABORTED
        - OUT_OF_RANGE
        - UNIMPLEMENTED
        - INTERNAL
        - UNAVAILABLE
        - DATA_LOSS
        - PARTIAL_ELEMENT_UPDATE_FAILED
      x-module: error
      x-category: 错误处理

    ErrorDetails:
      type: object
      title: ErrorDetails
      description: 错误详情类，提供错误的详细信息和上下文
      required:
        - reason
      properties:
        reason:
          type: string
          description: 错误原因，标识错误的直接原因。格式为大写蛇形命名
          example: "INVALID_REQUEST_FORMAT"
          pattern: "^[A-Z][A-Z0-9_]*[A-Z0-9]$"
          maxLength: 63
        message:
          type: string
          description: 针对此错误发生的人类可读的解释说明
          example: "请求参数格式不正确，缺少必需的字段 'name'"
          maxLength: 500
        domain:
          type: string
          description: 错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称
          example: "deco.manycoreapis.com"
          maxLength: 100
        metaData:
          type: object
          description: 关于此错误的附加结构化详细信息，键名限制为64个字符
          additionalProperties:
            type: string
            maxLength: 200
          example:
            maxAllowedSize: "1000"
            actualSize: "1500"
      additionalProperties: false
      x-module: error
      x-category: 错误处理

    Help:
      type: object
      title: Help
      description: 帮助信息类，提供解决问题的帮助链接和描述
      required:
        - desc
        - url
      properties:
        desc:
          type: string
          description: 链接描述，说明该链接提供的帮助内容
          example: "查看 API 使用指南"
          minLength: 1
          maxLength: 200
        url:
          type: string
          description: 帮助链接的 URL 地址
          example: "https://docs.example.com/api-guide"
          format: uri
          maxLength: 500
      additionalProperties: false
      x-module: error
      x-category: 错误处理

    LocalizedMessage:
      type: object
      title: LocalizedMessage
      description: 本地化消息类，包含特定语言环境的消息内容
      required:
        - locale
        - message
      properties:
        locale:
          type: string
          description: 消息所使用的语言环境
          example: "zh-CN"
          pattern: "^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"
        message:
          type: string
          description: 本地化的消息内容
          example: "权限不足，无法访问该资源"
          minLength: 1
          maxLength: 1000
      additionalProperties: false
      x-module: error
      x-category: 错误处理

    ErrorDescribe:
      type: object
      title: ErrorDescribe
      description: 错误描述接口的标记，用于类型标识
      additionalProperties: true
      x-module: error
      x-category: 错误处理

    # =====================================
    # 请求相关数据结构
    # =====================================
    RestApiRequest:
      type: object
      title: RestApiRequest
      description: REST API 请求基类，所有 API 请求的基础数据结构
      properties:
        requestId:
          type: string
          description: 请求标识符，用于追踪和调试请求
          example: "req_12345678"
          pattern: "^[a-zA-Z0-9_-]+$"
          maxLength: 64
      x-module: request
      x-category: 请求数据

    LongRunningRequest:
      allOf:
        - $ref: "#/components/schemas/RestApiRequest"
        - type: object
          title: LongRunningRequest
          description: 长时间运行请求基类，用于异步处理的 API 请求
          properties:
            operationId:
              type: string
              description: 操作标识符，用于查询长时间运行操作的状态和结果
              example: "op_12345678"
              pattern: "^[a-zA-Z0-9_-]+$"
              maxLength: 64
      x-module: request
      x-category: 请求数据

    RestApiBatchRequest:
      allOf:
        - $ref: "#/components/schemas/LongRunningRequest"
        - type: object
          title: RestApiBatchRequest
          description: 批量请求基类，用于处理多个相同类型的请求操作
          properties:
            batchRequests:
              type: array
              items:
                type: object
                description: 泛型类型对象，具体类型由实现类定义
              description: 批量请求列表，包含多个待处理的请求对象
              example: [{}]
      x-module: request
      x-category: 请求数据

    # =====================================
    # 响应相关数据结构
    # =====================================
    RestApiResponse:
      type: object
      title: RestApiResponse
      description: REST API 响应基类，所有 API 响应的基础数据结构
      x-module: response
      x-category: 响应数据

    RestApiListResponse:
      allOf:
        - $ref: "#/components/schemas/RestApiResponse"
        - type: object
          title: RestApiListResponse
          description: 列表响应基类，用于返回分页或列表查询的结果
          properties:
            totalCount:
              type: integer
              description: 总记录数，表示符合查询条件的记录总数
              example: 150
              minimum: 0
      x-module: response
      x-category: 响应数据

    RestApiBatchResponse:
      allOf:
        - $ref: "#/components/schemas/RestApiResponse"
        - type: object
          title: RestApiBatchResponse
          description: 批量响应类，用于返回批量操作的结果
          properties:
            elements:
              type: array
              items:
                type: object
                description: 泛型类型对象，具体类型由实现类定义
              description: 批量操作结果列表，包含所有处理完成的元素
              example: [{}]
      x-module: response
      x-category: 响应数据

    # =====================================
    # 操作管理相关数据结构
    # =====================================
    Operation:
      type: object
      title: Operation
      description: 操作类，表示异步操作的状态和结果，用于批量处理场景
      required:
        - operationId
        - done
      properties:
        operationId:
          type: string
          description: 操作标识符，用于唯一标识一个操作实例
          example: "op_12345678"
          pattern: "^[a-zA-Z0-9_-]+$"
          maxLength: 64
        metaData:
          type: object
          description: 操作的元数据信息，包含操作的附加信息
          additionalProperties: true
          example:
            startTime: "2025-01-01T10:00:00Z"
            priority: "high"
        done:
          type: boolean
          description: 操作是否已完成，true 表示已完成（成功或失败），false 表示仍在处理中
          example: true
        result:
          type: object
          description: 操作成功时的结果数据，仅当 done=true 且无错误时有值
          additionalProperties: true
        error:
          $ref: "#/components/schemas/ApiError"
          description: 操作失败时的错误信息，仅当 done=true 且操作失败时有值
      x-module: operation
      x-category: 操作管理

    # =====================================
    # 异常处理相关数据结构
    # =====================================
    RestApiClientException:
      type: object
      title: RestApiClientException
      description: REST API 客户端异常，客户端调用 API 时发生的异常
      properties:
        apiError:
          $ref: "#/components/schemas/ApiError"
          description: API 错误信息，包含错误的详细描述和状态码
        detailMessage:
          type: string
          description: 异常的详细消息
        cause:
          $ref: "#/components/schemas/Throwable"
          description: 异常的原因
        stackTrace:
          type: array
          items:
            $ref: "#/components/schemas/StackTraceElement"
          description: 异常的堆栈跟踪信息
        suppressedExceptions:
          type: array
          items:
            $ref: "#/components/schemas/Throwable"
          description: 被抑制的异常列表
      x-module: exception
      x-category: 异常处理

    RestApiProcessException:
      type: object
      title: RestApiProcessException
      description: REST API 处理异常，服务端处理请求时发生的检查异常
      required:
        - status
        - details
      properties:
        status:
          $ref: "#/components/schemas/Code"
          description: 错误状态码，表示错误的类型
        details:
          $ref: "#/components/schemas/ErrorDetails"
          description: 错误详细信息，包含错误原因和描述
        detailMessage:
          type: string
          description: 异常的详细消息
        cause:
          $ref: "#/components/schemas/Throwable"
          description: 异常的原因
        stackTrace:
          type: array
          items:
            $ref: "#/components/schemas/StackTraceElement"
          description: 异常的堆栈跟踪信息
        suppressedExceptions:
          type: array
          items:
            $ref: "#/components/schemas/Throwable"
          description: 被抑制的异常列表
      x-module: exception
      x-category: 异常处理

    RestApiProcessRuntimeException:
      type: object
      title: RestApiProcessRuntimeException
      description: REST API 处理运行时异常，服务端处理请求时发生的运行时异常
      required:
        - status
        - details
      properties:
        status:
          $ref: "#/components/schemas/Code"
          description: 错误状态码，表示错误的类型
        details:
          $ref: "#/components/schemas/ErrorDetails"
          description: 错误详细信息，包含错误原因和描述
        detailMessage:
          type: string
          description: 异常的详细消息
        cause:
          $ref: "#/components/schemas/Throwable"
          description: 异常的原因
        stackTrace:
          type: array
          items:
            $ref: "#/components/schemas/StackTraceElement"
          description: 异常的堆栈跟踪信息
        suppressedExceptions:
          type: array
          items:
            $ref: "#/components/schemas/Throwable"
          description: 被抑制的异常列表
      x-module: exception
      x-category: 异常处理

    RestApiProcessError:
      type: string
      title: RestApiProcessError
      description: REST API 处理错误枚举，定义业务处理错误类型
      enum:
        - REST_API_PROCESS_ERROR_UNSPECIFIED
        - USER_NOT_LOGIN
        - AUTHENTICATE_FAILED
        - API_NEED_AUTH_CHECK
        - INVALID_ARGUMENT
        - SAME_REQUEST_OVER_LIMIT
        - DESIGN_NOT_EXIST
        - DESIGN_DELETED
        - DESIGN_AND_LEVEL_NOT_MATCH
        - INTERNAL_ERROR
        - REQUEST_RATE_LIMIT
      x-module: exception
      x-category: 异常处理

    # =====================================
    # 数据传输相关数据结构
    # =====================================
    WriteControl:
      type: object
      title: WriteControl
      description: 写入控制类，用于控制数据写入操作的并发安全性
      properties:
        requiredRevisionId:
          type: string
          description: 必需的版本标识符，用于乐观锁控制，确保数据写入时的一致性
          example: "rev_12345678"
          pattern: "^[a-zA-Z0-9_-]+$"
          maxLength: 64
      x-module: data
      x-category: 数据传输

    Identifiable:
      type: object
      title: Identifiable
      description: 可识别接口，提供 ID 标识功能
      properties:
        id:
          type: string
          description: 唯一标识符
          example: "id_12345678"
      x-module: data
      x-category: 数据传输

    Validatable:
      type: object
      title: Validatable
      description: 可验证接口，提供数据验证功能
      x-module: data
      x-category: 数据传输

    # =====================================
    # 业务域相关数据结构
    # =====================================
    RestApiDomain:
      type: string
      title: RestApiDomain
      description: REST API 域枚举，定义系统中所有业务域的分类
      enum:
        - REST_API_DOMAIN_UNSPECIFIED
        - FURNITURE
        - DECO_PAVING
        - MOLDING
        - BRAND_GOOD
        - DOOR_WINDOW
        - LAYOUT_DESIGN
        - BLDG
        - CUSTOM_PARAM_MODEL
        - CUSTOM_DESIGN
        - MEP
        - RENDER
        - PROJECT_DESIGN
        - PDM
        - CAMERA
      x-module: module
      x-category: 业务域

    # =====================================
    # 通用辅助类型
    # =====================================
    Throwable:
      type: object
      title: Throwable
      description: 异常基类，Java 异常的基础类型
      properties:
        message:
          type: string
          description: 异常消息
        cause:
          $ref: "#/components/schemas/Throwable"
          description: 异常原因
        stackTrace:
          type: array
          items:
            $ref: "#/components/schemas/StackTraceElement"
          description: 堆栈跟踪信息
        suppressedExceptions:
          type: array
          items:
            $ref: "#/components/schemas/Throwable"
          description: 被抑制的异常列表
      x-module: util
      x-category: 通用类型

    StackTraceElement:
      type: object
      title: StackTraceElement
      description: 堆栈跟踪元素，表示异常堆栈中的单个帧
      properties:
        className:
          type: string
          description: 类名
        methodName:
          type: string
          description: 方法名
        fileName:
          type: string
          description: 文件名
        lineNumber:
          type: integer
          description: 行号
      x-module: util
      x-category: 通用类型

    Object:
      type: object
      title: Object
      description: 通用对象类型，用于泛型场景
      additionalProperties: true
      x-module: util
      x-category: 通用类型

    Locale:
      type: object
      title: Locale
      description: 地区信息类型，表示语言和地区设置
      properties:
        language:
          type: string
          description: 语言代码
          example: "zh"
        country:
          type: string
          description: 国家代码
          example: "CN"
        variant:
          type: string
          description: 变体
      x-module: util
      x-category: 通用类型

# =====================================
# 文档统计信息
# =====================================
x-statistics:
  totalSchemas: 24
  moduleBreakdown:
    error: 6
    request: 3
    response: 3
    operation: 1
    exception: 4
    data: 3
    module: 1
    util: 3
  categoryBreakdown:
    错误处理: 6
    请求数据: 3
    响应数据: 3
    操作管理: 1
    异常处理: 4
    数据传输: 3
    业务域: 1
    通用类型: 3

# =====================================
# 扩展属性说明
# =====================================
x-extensions:
  x-module: 表示该 Schema 所属的功能模块
  x-category: 表示该 Schema 所属的分类
  x-statistics: 文档统计信息，包含 Schema 总数和模块分布
  x-extensions: 扩展属性说明文档
