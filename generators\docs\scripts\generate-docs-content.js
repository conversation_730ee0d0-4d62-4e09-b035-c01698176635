#!/usr/bin/env node

/**
 * 动态文档内容生成器
 * 根据 OpenAPI 配置动态生成 Docusaurus 相关的文档内容
 */

const fs = require('node:fs');
const path = require('node:path');
const yaml = require('js-yaml');

/**
 * 获取服务的详细信息
 */
function getServiceInfo(configId, openApiConfig) {
  const config = openApiConfig[configId];
  if (!config) return null;

  // 尝试从 OpenAPI 规范文件中读取信息
  const specPath = path.resolve(config.specPath);
  let serviceInfo = {
    id: configId,
    name: getServiceDisplayName(configId),
    icon: getServiceIcon(configId),
    description: `${getServiceDisplayName(configId)} API 接口文档`,
    version: '1.0.0',
    baseUrl: getServiceBaseUrl(configId),
  };

  if (fs.existsSync(specPath)) {
    try {
      const yamlContent = fs.readFileSync(specPath, 'utf8');
      const spec = yaml.load(yamlContent);
      
      if (spec.info) {
        serviceInfo.name = spec.info.title || serviceInfo.name;
        serviceInfo.description = spec.info.description || serviceInfo.description;
        serviceInfo.version = spec.info.version || serviceInfo.version;
      }

      // 提取 tags 信息作为功能模块
      if (spec.tags && Array.isArray(spec.tags)) {
        serviceInfo.modules = spec.tags.map(tag => ({
          name: tag.name,
          description: tag.description || `${tag.name} 相关接口`,
        }));
      }

      // 提取 servers 信息
      if (spec.servers && spec.servers.length > 0) {
        serviceInfo.servers = spec.servers.map(server => ({
          url: server.url,
          description: server.description || '服务器地址',
        }));
      }

      // 统计 paths 数量
      if (spec.paths) {
        serviceInfo.endpointsCount = Object.keys(spec.paths).length;
      }

    } catch (error) {
      console.warn(`⚠️ 无法解析 OpenAPI 规范文件 ${specPath}:`, error.message);
    }
  }

  return serviceInfo;
}

/**
 * 获取服务显示名称
 */
function getServiceDisplayName(configId) {
  // 移除硬编码映射，改为基于 configId 的智能格式化
  return configId
    .replace(/([a-z])([A-Z])/g, '$1 $2') // 驼峰转空格
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ') + ' 服务';
}

/**
 * 获取服务图标
 */
function getServiceIcon(configId) {
  return '🏠'; // 默认图标
}

/**
 * 获取服务基础URL
 */
function getServiceBaseUrl(configId) {
  // 移除硬编码映射，基于 configId 动态生成
  return `/${configId}/v1`;
}

/**
 * 根据 configId 和 openApiConfig 获取实际的文档目录路径  
 * 完全基于文件夹名称，无硬编码映射
 */
function getServiceDirFromConfig(configId, openApiConfig) {
  const config = openApiConfig[configId];
  if (!config || !config.specPath) {
    return configId; // fallback
  }
  
  // 从 specPath 中提取原始目录名: ../../specifications/services/service-dir/openapi.yaml → service-dir
  const match = config.specPath.match(/\.\.\/\.\.\/specifications\/services\/([^\/]+)\/openapi\.yaml/);
  const originalDir = match ? match[1] : configId;
  
  // 直接使用原始目录名，不做任何人为映射
  return originalDir;
}

/**
 * 生成动态的 sidebars.ts 内容
 */
function generateSidebarsContent(services, openApiConfig) {
  // 生成动态导入逻辑
  const dynamicImports = services.map(service => {
    const outputDir = getServiceDirFromConfig(service.id, openApiConfig);
    const variableName = service.id.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
    return `let ${variableName}Sidebar: any[] = [];
try {
  const sidebarModule = require('./docs/api/${outputDir}/sidebar');
  ${variableName}Sidebar = sidebarModule.default || sidebarModule.apisidebar || sidebarModule || [];
} catch (e) {
  console.warn('无法加载 ${outputDir} sidebar:', e.message);
}`;
  }).join('\n\n');

  const individualSidebars = services.map(service => {
    const variableName = service.id.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
    return `  ${variableName}Sidebar: ${variableName}Sidebar,`;
  }).join('\n');

  return `import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

// 动态导入 OpenAPI 生成的 sidebar 配置
// 如果导入失败，使用空的配置作为fallback
${dynamicImports}

const sidebars: SidebarsConfig = {
  // 开发指南侧边栏
  tutorialSidebar: [
    'intro',
    {
      type: 'category',
      label: '快速开始',
      items: [
        'getting-started/authentication',
        'getting-started/making-requests',
        'getting-started/error-handling',
      ],
    },
    {
      type: 'category',
      label: '开发指南',
      items: [
        'guides/best-practices',
        'guides/rate-limiting',
        'guides/webhooks',
        'guides/sdks',
      ],
    },
    // 示例代码部分已移除
    // {
    //   type: 'category',
    //   label: '示例代码',
    //   items: [
    //     'examples/javascript',
    //     // 'examples/python',
    //     // 'examples/java',
    //     // 'examples/curl',
    //   ],
    // },
  ],

  // 各个服务的独立侧边栏（直接使用 OpenAPI 生成的内容）
${individualSidebars}
};

export default sidebars;`;
}

/**
 * 将连字符分隔的字符串转换为驼峰命名（用于 JavaScript 变量名）
 */
function toCamelCase(str) {
  return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * 生成动态的导航配置
 */
function generateNavbarConfig(services) {
  const dropdownItems = [];

  services.forEach(service => {
    // 将 service.id 转换为驼峰命名用于 JavaScript 变量名
    const camelCaseId = toCamelCase(service.id);
    
    dropdownItems.push(`            {
              type: 'docSidebar',
              sidebarId: '${camelCaseId}Sidebar',
              label: '${service.icon} ${service.name}',
            },`);
  });

  return dropdownItems.join('\n');
}

/**
 * 生成动态的 intro.md 内容
 */
function generateIntroContent(services, openApiConfig) {
  const serviceCards = services.map(service => {
    const outputDir = getServiceDirFromConfig(service.id, openApiConfig);
    return `### ${service.icon} ${service.name}
${service.description}

**核心功能：**
${service.modules ? service.modules.map(module => `- ${module.description}`).join('\n') : '- 专业 API 接口服务'}

[查看 ${service.name} API →](/docs/api/${outputDir}/${outputDir})`;
  }).join('\n\n');

  return `---
id: intro
title: 群核科技 API 文档
sidebar_label: 开始使用
slug: /
---

# 群核科技 API 文档中心

欢迎来到群核科技 API 文档中心！这里为开发者提供完整的 API 参考文档、开发指南和示例代码，帮助您快速集成群核科技的全栈家居云设计能力。

## 🎯 平台概览

群核科技（酷家乐）是领先的云端家居设计平台，为设计师、家居企业和开发者提供：

- **3D 设计引擎** - 强大的在线 3D 设计工具
- **海量模型库** - 数百万高质量 3D 家居模型
- **智能设计** - AI 驱动的设计建议和自动化
- **渲染服务** - 云端高性能渲染
- **VR/AR 体验** - 沉浸式虚拟现实展示

## 🚀 核心服务

${serviceCards}

## ⚡ 快速开始

### 1. 获取 API 密钥

首先需要在[开发者控制台](https://developers.qunheco.com)注册并获取 API 密钥。

### 2. 身份认证

所有 API 请求都需要在请求头中包含认证信息：

\`\`\`http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
\`\`\`

### 3. 发起第一个请求

\`\`\`bash
curl -X GET \\
  'https://api.qunheco.com${services[0]?.baseUrl || '/api/v1'}/endpoint' \\
  -H 'Authorization: Bearer YOUR_API_KEY' \\
  -H 'Content-Type: application/json'
\`\`\`

### 4. 集成 SDK

我们提供多种语言的 SDK，简化集成过程：

- [Java SDK](/docs/guides/sdks#java-sdk)
- [JavaScript SDK](/docs/guides/sdks#javascript-sdk)
- [Python SDK](/docs/guides/sdks#python-sdk)

## 📚 学习路径

### 🔰 初学者
1. [身份认证](/docs/getting-started/authentication) - 了解如何认证 API 请求
2. [发起请求](/docs/getting-started/making-requests) - 学习基本的 API 调用
3. [错误处理](/docs/getting-started/error-handling) - 掌握错误处理最佳实践

### 🛠️ 开发者
1. [最佳实践](/docs/guides/best-practices) - API 集成最佳实践
2. [速率限制](/docs/guides/rate-limiting) - 了解 API 使用限制
3. [Webhooks](/docs/guides/webhooks) - 配置事件通知

### 🎨 设计师
1. [最佳实践](/docs/guides/best-practices) - 设计师专用最佳实践
2. [SDK 开发包](/docs/guides/sdks) - 高效使用 SDK 工具
3. [速率限制](/docs/guides/rate-limiting) - 了解使用限制

## 🌐 API 基础信息

| 环境 | 基础 URL | 说明 |
|------|----------|------|
| 生产环境 | \`https://api.qunheco.com\` | 正式生产环境 |
| 测试环境 | \`https://api-test.qunheco.com\` | 开发测试使用 |

**API 版本：** v1  
**数据格式：** JSON  
**字符编码：** UTF-8  
**请求方法：** GET, POST, PUT, DELETE  

## 🔗 相关资源

- [开发者门户](https://developers.qunheco.com) - 开发者注册和管理
- [技术博客](https://tech.qunheco.com) - 技术文章和案例分享
- [开发者社区](https://forum.qunheco.com) - 技术讨论和支持
- [状态页面](https://status.qunheco.com) - API 服务状态
- [GitHub](https://github.com/qunhe) - 开源项目和SDK

## 💬 获取帮助

遇到问题？我们提供多种支持渠道：

- **📖 文档搜索** - 使用顶部搜索框快速查找
- **💬 在线客服** - 工作日 9:00-18:00 在线支持
- **📧 邮件支持** - <EMAIL>
- **🎫 工单系统** - 通过开发者控制台提交技术工单

---

**准备好开始了吗？** 👈 从左侧导航开始探索，或直接查看 [API 参考文档](/docs/api) 🚀`;
}

/**
 * 生成动态的 overview.md 内容
 */
function generateOverviewContent(services, openApiConfig) {
  const serviceCards = services.map(service => {
    const outputDir = getServiceDirFromConfig(service.id, openApiConfig);
    const firstEndpoint = service.modules && service.modules.length > 0 
      ? service.modules[0].name.toLowerCase().replace(/\s+/g, '-')
      : 'endpoint';
    
    // 确保描述是纯文本，去掉换行符和特殊字符
    const cleanDescription = service.description
      .split('\n')[0]  // 取第一行
      .split('.')[0]   // 取第一句
      .replace(/\*\*/g, '')  // 移除 Markdown 加粗
      .replace(/[\-\*]/g, '')  // 移除列表符号
      .trim()
      .substring(0, 100);  // 限制长度为100字符
    
    // 如果描述被截断，添加省略号
    const finalDescription = cleanDescription.length < service.description.length ? 
      cleanDescription + '...' : cleanDescription;
    
    return `<div className="api-service-card api-service-card--${service.id}">
  <h3>${service.icon} ${service.name}</h3>
  <p>${finalDescription}</p>
  <div className="api-service-buttons">
    <a href="/docs/api/${outputDir}" className="button button--primary">查看 API</a>
    <a href="/docs/api/${outputDir}/${firstEndpoint}" className="button button--success">快速开始</a>
  </div>
</div>`;
  }).join('\n\n');

  const serviceOverviews = services.map(service => {
    const outputDir = getServiceDirFromConfig(service.id, openApiConfig);
    const modules = service.modules || [];
    const endpoints = modules.map(module => `- \`${module.name}\` - ${module.description}`).join('\n');
    
    return `### ${service.icon} ${service.name}
${service.description}

**主要功能：**
${modules.length > 0 ? modules.map(m => `- ${m.description}`).join('\n') : '- 专业 API 接口服务'}

**基础URL：** \`https://api.qunheco.com${service.baseUrl}\`

${service.endpointsCount ? `**接口数量：** ${service.endpointsCount} 个` : ''}

[查看完整文档 →](/docs/api/${outputDir})`;
  }).join('\n\n');

  return `---
id: overview
title: API 概览
sidebar_label: API 概览
slug: /api
---

# 群核科技 API 概览

群核科技 API 提供强大的家居云设计能力，帮助开发者构建创新的家居应用。我们的 API 服务涵盖 3D 模型管理、家具设计、场景渲染等核心功能。

## 🎯 快速导航

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<div className="api-service-cards">

${serviceCards}

</div>

## 🏗️ 架构概览

\`\`\`mermaid
graph TB
    A[客户端应用] --> B[API 网关]
    B --> C[认证服务]
    ${services.map((service, index) => `B --> ${String.fromCharCode(68 + index)}[${service.name}]`).join('\n    ')}
    
    ${services.map((service, index) => `${String.fromCharCode(68 + index)} --> ${String.fromCharCode(71 + index)}[${service.name}存储]`).join('\n    ')}
    
    J[Webhooks] --> A
    B --> J
\`\`\`

## 🚀 API 服务总览

${serviceOverviews}

## 🔑 认证方式

所有 API 请求都需要有效的 API 密钥：

\`\`\`http
Authorization: Bearer YOUR_API_KEY
\`\`\`

### 获取 API 密钥

1. 注册 [开发者账号](https://developers.kujiale.com)
2. 创建应用
3. 生成 API 密钥
4. 配置权限和限制

[详细认证指南 →](/docs/getting-started/authentication)

## 🌐 API 基础信息

### 环境地址

| 环境 | 基础 URL | 用途 |
|------|----------|------|
| 生产环境 | \`https://api.qunheco.com\` | 正式业务使用 |
| 测试环境 | \`https://api-test.qunheco.com\` | 开发测试验证 |

### 请求格式

- **协议**：HTTPS
- **格式**：JSON
- **编码**：UTF-8
- **方法**：GET, POST, PUT, DELETE

### 响应格式

\`\`\`json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体响应数据
  },
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 100
  }
}
\`\`\`

### 错误响应

\`\`\`json
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "参数验证失败",
    "details": {
      "field": "name",
      "reason": "不能为空"
    }
  }
}
\`\`\`

## 🚀 快速集成

### 1. 安装 SDK

\`\`\`bash
# JavaScript/TypeScript
npm install @kujiale/api-sdk

# Python
pip install kujiale-api-sdk

# Java (Maven)
<dependency>
  <groupId>com.kujiale</groupId>
  <artifactId>kujiale-api-sdk</artifactId>
  <version>1.0.0</version>
</dependency>
\`\`\`

### 2. 初始化客户端

\`\`\`javascript
import { KujialeSdk } from '@kujiale/api-sdk';

const client = new KujialeSdk({
  apiKey: 'your-api-key',
  environment: 'production'
});
\`\`\`

### 3. 发起请求

\`\`\`javascript
// 示例：调用第一个服务
${services[0] ? `const result = await client.${services[0].id}.someMethod({
  // 请求参数
});` : '// 配置你的 API 调用'}
\`\`\`

// [查看完整示例 →](/docs/examples/javascript) - 示例页面已移除

## 📊 API 限制

### 速率限制

| 计划类型 | 每分钟请求数 | 每日请求数 | 并发连接数 |
|----------|-------------|-----------|------------|
| 免费版 | 100 | 10,000 | 5 |
| 基础版 | 500 | 100,000 | 10 |
| 专业版 | 2,000 | 500,000 | 20 |
| 企业版 | 5,000 | 2,000,000 | 50 |

### 数据限制

- **请求大小**：最大 10MB
- **响应超时**：30 秒
- **批量操作**：单次最多 100 条记录

[详细限制说明 →](/docs/guides/rate-limiting)

## 🔄 版本控制

我们使用语义化版本控制：

- **主版本**：不兼容的 API 更改
- **次版本**：向后兼容的功能新增
- **修订版本**：向后兼容的问题修正

### 当前版本

- **API 版本**：v1
- **SDK 版本**：1.0.0
- **最后更新**：${new Date().toISOString().split('T')[0]}

### 版本策略

- 新功能首先在 beta 版本发布
- 主版本更改提前 3 个月通知
- 旧版本维护期至少 12 个月

## 🛡️ 安全性

### 数据保护

- **传输加密**：TLS 1.2+
- **数据存储**：AES-256 加密
- **访问控制**：基于角色的权限管理

### 合规认证

- ✅ ISO 27001 信息安全管理
- ✅ SOC 2 Type II 合规
- ✅ GDPR 数据保护合规

## 📈 性能指标

### 服务等级协议 (SLA)

- **可用性**：99.9% 月度正常运行时间
- **响应时间**：95% 的请求在 500ms 内响应
- **错误率**：小于 0.1%

### 实时状态

检查我们的 [状态页面](https://status.kujiale.com) 了解实时服务状态。

## 🔧 开发工具

### 调试工具

- [API 调试控制台](https://developers.kujiale.com/console)
- [Postman 集合](https://postman.com/kujiale-api)
- [OpenAPI 规范文件](https://api.kujiale.com/docs/openapi.yaml)

### 代码生成器

我们支持从 OpenAPI 规范自动生成多种语言的客户端代码：

- JavaScript/TypeScript
- Python
- Java
- C#
- Go
- Ruby

## 📚 学习资源

### 文档

- [快速开始指南](/docs/getting-started/authentication)
- [最佳实践](/docs/guides/best-practices)
- [SDK 文档](/docs/guides/sdks)
// - [示例代码](/docs/examples/javascript) - 示例页面已移除

### 社区

- [开发者论坛](https://forum.kujiale.com)
- [技术博客](https://tech.kujiale.com)
- [GitHub 组织](https://github.com/kujiale)
- [Stack Overflow 标签](https://stackoverflow.com/questions/tagged/kujiale)

## 💬 技术支持

### 支持渠道

- **文档反馈**：通过文档页面的反馈按钮
- **技术咨询**：<EMAIL>
- **商务合作**：<EMAIL>
- **紧急支持**：7x24 小时热线（企业版）

### 响应时间

| 支持级别 | 响应时间 | 可用性 |
|----------|----------|--------|
| 社区支持 | 48 小时 | 工作日 |
| 基础支持 | 24 小时 | 工作日 |
| 专业支持 | 4 小时 | 7x24 |
| 企业支持 | 1 小时 | 7x24 |

---

**开始您的开发之旅！** 🚀 选择一个 API 服务开始探索，或查看我们的[快速开始指南](/docs/getting-started/authentication)。`;
}

/**
 * 生成单个服务的概览页面内容
 */
function generateServiceOverviewContent(service, openApiConfig) {
  const outputDir = getServiceDirFromConfig(service.id, openApiConfig);
  const modules = service.modules || [];
  const servers = service.servers || [];
  
  // 生成功能模块列表
  const modulesList = modules.length > 0 
    ? modules.map(module => `- **${module.name}** - ${module.description.split('\n')[0]}`).join('\n')
    : '- 专业 API 接口服务';

  // 生成服务端点列表
  const serversList = servers.length > 0
    ? servers.map(server => `- **${server.description}**: ${server.url}`).join('\n')
    : '- 待配置';

  // 生成 API 文档链接
  const apiLinks = modules.length > 0
    ? modules.map(module => {
        const moduleSlug = module.name.toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^\w\-]/g, '');
        return `- [${module.name}](./${outputDir}/${moduleSlug}) - ${module.description.split('\n')[0]}`;
      }).join('\n')
    : `- [API 详细文档](./${outputDir}/${service.id.replace(/[^a-z0-9]/gi, '-')}) - 完整的 API 接口文档`;

  return `---
id: ${service.id}
title: ${service.name}
sidebar_label: API 概览
slug: /api/${outputDir}
---

# ${service.name}

${service.description.split('\n')[0]}

## ${service.icon} 服务概览

${service.name} 是群核科技提供的专业服务，主要功能包括：

### 🎯 核心功能
${modulesList}

### 🔧 技术规范
- **API 版本**: ${service.version}
- **响应格式**: JSON
- **字符编码**: UTF-8
- **基础路径**: \`${service.baseUrl}\`

### 🌐 服务端点
${serversList}

## 📚 API 文档

### 主要接口
${apiLinks}

${modules.length > 0 ? `### 数据模型
${modules.map(module => `- [${module.name} 相关模型](./${outputDir}/schemas) - ${module.name} 接口的数据结构定义`).join('\n')}` : ''}

## 🚀 快速开始

### 基础请求示例

\`\`\`bash
# 示例 API 调用
curl -X GET \\
  '${servers.length > 0 ? servers[0].url : 'https://api.qunhe.com'}${service.baseUrl}/endpoint' \\
  -H 'Content-Type: application/json'
\`\`\`

${service.endpointsCount ? `### 接口统计

本服务共提供 **${service.endpointsCount}** 个 API 接口，涵盖完整的业务功能。` : ''}

${modules.length > 1 ? `### 功能模块

| 模块 | 说明 |
|------|------|
${modules.map(module => `| ${module.name} | ${module.description.split('\n')[0]} |`).join('\n')}` : ''}

## 🔗 相关链接

- [API 详细文档](./${outputDir}/${service.id.replace(/[^a-z0-9]/gi, '-')})
${modules.length > 0 ? `- [主要接口](./${outputDir}/${modules[0].name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]/g, '')})` : ''}
- [数据模型参考](./${outputDir}/schemas)${modules.length > 0 ? `
- [标签分组](./${outputDir}/${modules[0].name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]/g, '')})` : ''} `;
}

/**
 * 主函数：生成所有动态内容
 */
function main() {
  console.log('📋 生成动态文档内容...\n');

  // 读取 openapi-config.json
  const currentDir = process.cwd();
  let configPath;
  
  if (currentDir.endsWith('docs-site') || currentDir.endsWith('website')) {
    configPath = path.join(currentDir, 'openapi-config.json');
  } else {
    configPath = path.join(currentDir, 'documentation', 'website', 'openapi-config.json');
  }

  if (!fs.existsSync(configPath)) {
    console.error('❌ openapi-config.json 不存在，请先运行 generate-openapi-config.js');
    process.exit(1);
  }

  const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  const openApiConfig = configData.openApiConfig || {};

  // 获取所有服务信息
  const services = Object.keys(openApiConfig).map(configId => 
    getServiceInfo(configId, openApiConfig)
  ).filter(Boolean);

  console.log(`🔍 发现 ${services.length} 个服务:`);
  services.forEach(service => {
    console.log(`  - ${service.icon} ${service.name} (${service.id})`);
  });

  // 生成内容
  const generatedContent = {
    sidebars: generateSidebarsContent(services, openApiConfig),
    navbar: generateNavbarConfig(services),
    intro: generateIntroContent(services, openApiConfig),
  };

  // 输出目录
  const outputDir = (currentDir.endsWith('docs-site') || currentDir.endsWith('website')) ? currentDir : path.join(currentDir, 'documentation', 'website');

  // 写入文件
  console.log('\n📝 写入生成的内容...');

  // 1. 更新 sidebars.ts
  fs.writeFileSync(path.join(outputDir, 'sidebars.ts'), generatedContent.sidebars);
  console.log('✅ sidebars.ts 已更新');

  // 2. 更新 intro.md
  fs.writeFileSync(path.join(outputDir, 'docs', 'intro.md'), generatedContent.intro);
  console.log('✅ docs/intro.md 已更新');

  // 5. 生成导航配置到临时文件，供 docusaurus.config.ts 使用
  const navbarConfigPath = path.join(outputDir, 'navbar-config.json');
  fs.writeFileSync(navbarConfigPath, JSON.stringify({
    services: services,
    dropdownItems: generatedContent.navbar
  }, null, 2));
  console.log('✅ navbar-config.json 已生成');

  console.log('\n🎉 动态文档内容生成完成!');
  console.log(`📊 处理了 ${services.length} 个服务的文档配置`);

  return {
    services,
    generatedContent
  };
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  getServiceInfo,
  generateSidebarsContent,
  generateNavbarConfig,
  generateIntroContent,
  main
}; 