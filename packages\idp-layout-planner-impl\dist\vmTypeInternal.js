var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Types = {};
    var var_Methods = {};
    var var_getAllFurnitureLegendList = {};
    var var_FurnitureLegend_Array = {};
    var var_FurnitureLegend = {};
    var var_ElementId = {};
    var var_stringType = {};
    var var_numberType = {};
    var var_getAllParamLegendList = {};
    var var_ParamLegend_Array = {};
    var var_ParamLegend = {};
    var var_getAllLegendGroupList = {};
    var var_LegendGroup_Array = {};
    var var_LegendGroup = {};
    var var_ElementId_Array = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "getAllFurnitureLegendList": var_getAllFurnitureLegendList,
        "getAllParamLegendList": var_getAllParamLegendList,
        "getAllLegendGroupList": var_getAllLegendGroupList,
    };
    var_getAllFurnitureLegendList.type = BasicType.Function;
    var_getAllFurnitureLegendList.name = "getAllFurnitureLegendList";
    var_getAllFurnitureLegendList.varying = false;
    var_getAllFurnitureLegendList.keepArgsHandle = false;
    var_getAllFurnitureLegendList.args = [];
    var_getAllFurnitureLegendList.return = var_FurnitureLegend_Array;
    var_FurnitureLegend_Array.type = BasicType.Array;
    var_FurnitureLegend_Array.value = var_FurnitureLegend;
    var_FurnitureLegend.type = BasicType.Object;
    var_FurnitureLegend.properties = {
        "id": var_ElementId,
        "area": var_numberType,
    };
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_numberType.type = BasicType.Number;
    var_getAllParamLegendList.type = BasicType.Function;
    var_getAllParamLegendList.name = "getAllParamLegendList";
    var_getAllParamLegendList.varying = false;
    var_getAllParamLegendList.keepArgsHandle = false;
    var_getAllParamLegendList.args = [];
    var_getAllParamLegendList.return = var_ParamLegend_Array;
    var_ParamLegend_Array.type = BasicType.Array;
    var_ParamLegend_Array.value = var_ParamLegend;
    var_ParamLegend.type = BasicType.Object;
    var_ParamLegend.properties = {
        "id": var_ElementId,
        "area": var_numberType,
    };
    var_getAllLegendGroupList.type = BasicType.Function;
    var_getAllLegendGroupList.name = "getAllLegendGroupList";
    var_getAllLegendGroupList.varying = false;
    var_getAllLegendGroupList.keepArgsHandle = false;
    var_getAllLegendGroupList.args = [];
    var_getAllLegendGroupList.return = var_LegendGroup_Array;
    var_LegendGroup_Array.type = BasicType.Array;
    var_LegendGroup_Array.value = var_LegendGroup;
    var_LegendGroup.type = BasicType.Object;
    var_LegendGroup.properties = {
        "id": var_ElementId,
        "subElements": var_ElementId_Array,
    };
    var_ElementId_Array.type = BasicType.Array;
    var_ElementId_Array.value = var_ElementId;
    
    return var_sourceFile;
};
