import {
    <PERSON><PERSON>eom<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    KCurve2dType as Curve2dType,
    KCurve3dType as Curve3dType,
    KPtInLoopType as PtInLoopType,
    KCurveSurfaceIntersectType as CurveSurfaceIntersectType,
    KSur<PERSON>Type as SurfaceType,
    KCurveInLoopType as CurveInLoopType,
    KFaceBooleanType as FaceBooleanType,
    KBoundedCurve2d as BoundedCurve2d,
    KBoundedCurve3d as BoundedCurve3d,
    KLineSegment3d as LineSegment3d,
    KGeomFace2d as GeomFace2d,
    KLine3d as Line3d,
    KArc3d as Arc3d,
    KFace3d as Face3d,
} from '@qunhe/math-apaas-api';

export namespace IDP {
    namespace Math {
        const KGeomLib: GeomLib;
        const KCurve2dType: typeof Curve2dType;
        const KCurve3dType: typeof Curve3dType;
        const KPtInLoopType: typeof PtInLoopType;
        const KCurveSurfaceIntersectType: typeof CurveSurfaceIntersectType;
        const KSurfaceType: typeof SurfaceType;
        const KCurveInLoopType: typeof CurveInLoopType;
        const KFaceBooleanType: typeof FaceBooleanType;
        type KBoundedCurve2d = BoundedCurve2d;
        type KBoundedCurve3d = BoundedCurve3d;
        type KLineSegment3d = LineSegment3d;
        type KGeomFace2d = GeomFace2d;
        type KLine3d = Line3d;
        type KArc3d = Arc3d;
        type KFace3d = Face3d;
    }
}
