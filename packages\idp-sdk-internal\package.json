{"name": "@qunhe/idp-sdk-internal", "version": "1.70.1", "description": "idp sdk internal typings", "keywords": [], "author": "wufan <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/idp.types.git"}, "scripts": {"build": "ts-node ../../scripts/build-idp-sdk", "build-package": "npm run build && copyfiles *.json *.md -e \"build/**/*.{d.ts,js,json,md}\" build && node ../../scripts/publish/prepare-sdk-package"}, "dependencies": {"@qunhe/idp-app-core": "1.70.0", "@qunhe/idp-common": "1.70.0", "@qunhe/idp-custom": "1.70.0", "@qunhe/idp-custom-doorwindow": "1.70.0", "@qunhe/idp-decoration": "1.70.0", "@qunhe/idp-drawing": "1.70.0", "@qunhe/idp-element-label": "1.70.0", "@qunhe/idp-floorplan": "1.70.1", "@qunhe/idp-intelligent": "1.70.0", "@qunhe/idp-layout-planner": "1.70.0", "@qunhe/idp-math": "1.70.0", "@qunhe/idp-param-ceiling": "1.70.0", "@qunhe/idp-payment": "1.70.0", "@qunhe/idp-spatial-zone": "1.70.0", "@qunhe/idp-yundesign": "1.70.0", "@qunhe/idp-yunrender": "1.70.0"}, "devDependencies": {"copyfiles": "^2.4.1", "ts-node": "^10.3.0"}, "kjl-template": {"plugins": {"manual": "@qunhe/kjl-plugin_manual"}}, "docSiteConfig": {"title": "小程序平台", "favicon": "../../assets/kujialeLogo.ico", "navbarLogo": "../../assets/kujialeLogo.svg"}, "manual": "88d12f98-0f84-4e99-a0f5-b00d7d92611a", "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}}