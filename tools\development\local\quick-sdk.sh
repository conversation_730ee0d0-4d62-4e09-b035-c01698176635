#!/bin/bash

# 快速 SDK 操作脚本
# 提供常用操作的快捷命令

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SDK_MANAGER="$SCRIPT_DIR/local-sdk-manager.sh"

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_usage() {
    echo -e "${BLUE}快速 SDK 操作工具${NC}"
    echo ""
    echo -e "${YELLOW}常用命令:${NC}"
    echo "  quick-sdk gen [service]     # 快速生成 SDK"
    echo "  quick-sdk build [service]   # 快速构建 SDK"
    echo "  quick-sdk test [service]    # 快速测试 SDK"
    echo "  quick-sdk full [service]    # 执行完整流程"
    echo "  quick-sdk snap [service]    # 部署快照版本"
    echo ""
    echo -e "${YELLOW}服务选项:${NC}"
    # 动态获取支持的服务列表
    local services
    services=$("$SDK_MANAGER" --dry-run generate all 2>/dev/null | grep -o "支持的服务:.*all" | sed 's/支持的服务: //' | sed 's/ all$//' 2>/dev/null || echo "doorwindow furniture")
    if [ -n "$services" ]; then
        for service in $services; do
            echo "  $service  # $service 服务"
        done
    fi
    echo "  all         # 所有服务 (默认)"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    local first_service=$(echo $services | awk '{print $1}')
    local second_service=$(echo $services | awk '{print $2}')
    [ -n "$first_service" ] && echo "  $0 gen $first_service    # 生成 $first_service 服务 SDK"
    [ -n "$second_service" ] && echo "  $0 full $second_service     # 对 $second_service 服务执行完整流程"
    echo "  $0 build             # 构建所有服务 SDK"
}

if [ $# -eq 0 ]; then
    print_usage
    exit 0
fi

ACTION=$1
SERVICE=${2:-"all"}

case $ACTION in
    "gen"|"generate")
        echo -e "${GREEN}🚀 生成 SDK: $SERVICE${NC}"
        "$SDK_MANAGER" --pull generate "$SERVICE"
        ;;
    "build")
        echo -e "${GREEN}🔨 构建 SDK: $SERVICE${NC}"
        "$SDK_MANAGER" build "$SERVICE"
        ;;
    "test")
        echo -e "${GREEN}🧪 测试 SDK: $SERVICE${NC}"
        "$SDK_MANAGER" test "$SERVICE"
        ;;
    "full"|"all")
        echo -e "${GREEN}⚡ 执行完整流程: $SERVICE${NC}"
        "$SDK_MANAGER" --pull all "$SERVICE"
        ;;
    "snap"|"snapshot")
        echo -e "${GREEN}📦 部署快照版本: $SERVICE${NC}"
        "$SDK_MANAGER" deploy-snap "$SERVICE"
        ;;
    "release"|"rel")
        echo -e "${GREEN}🎯 部署正式版本: $SERVICE${NC}"
        "$SDK_MANAGER" deploy-rel "$SERVICE"
        ;;
    "dry"|"preview")
        echo -e "${GREEN}👀 预览操作 (不执行): $SERVICE${NC}"
        "$SDK_MANAGER" --dry-run generate "$SERVICE"
        ;;
    "-h"|"--help"|"help")
        print_usage
        ;;
    *)
        echo "未知命令: $ACTION"
        print_usage
        exit 1
        ;;
esac 