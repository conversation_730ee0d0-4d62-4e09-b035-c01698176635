import {
    IParamModelLite,
} from '@qunhe/custom-apass-api';

// TODO: put yor non exported types here
declare namespace IDP {
    // TODO: put your exported apis here
    namespace Custom {
        namespace DoorWindow {

            /**
             * 门窗模型数据
             * @internal
             */
            interface DWModelData {
                model: IParamModelLite;
                openingIds: string[];
            }

            /**
             * 根据门洞id获取门洞内的门窗
             * @param openingIds 门洞id
             * @internal
             */
            function findDWModelByOpeningIds(openingIds: string[]): DWModelData[];

            /**
             * 门窗模型替换信息
             * @deprecated
             * @internal
             */
            interface DWReplaceModelInfo {
                modelId: string;  // 需要被替换的门窗模型id
                brandGoodId: string;  // 需要替换成新的商品id
                options?: {             // 额外的替换信息 为业务服务
                    lock?: string;      // 锁具的obsBrandGoodId
                    material?: string;  // 材质的obsBrandGoodId
                };
            }

            /**
             * 门窗替换完成的回调
             * @deprecated
             * @internal
             */
            interface DWReplaceResponse extends DWReplaceModelInfo {
                result: boolean; // 替换结果
                message?: string;
            }

            /**
             * 替换门窗模型
             * @deprecated Please use replaceDWModelV2Async
             * @internal
             */
            function replaceDWModelAsync(replaceModels: DWReplaceModelInfo[]): Promise<DWReplaceResponse[]>

            /**
             * 门窗模型替换信息
             * @internal
             */
            interface DWReplaceModelInfoV2 {
                /**
                 * 需要被替换的模型id路径 自顶而下
                 */
                modelIdPath: string[];
                /**
                 * 替换的加密商品id
                 */
                productId?: string;
                /**
                 * 门窗模型替换全部参数是从旧模型中继承，该字段可控制部分参数不从旧模型中继承，默认AB面材质参数不继承
                 * @default [materialBrandGoodId,materialBrandGoodIdB]
                 */
                unInheritParams?: string[];
                options?: {
                    /**
                     * 替换锁具的加密商品id
                     */
                    lock?: string;
                    /**
                     * 替换材质的加密商品id
                     */
                    material?: string;
                    /**
                     * 替换参数列表
                     */
                    params?: {
                        key: string;
                        value: string;
                    }[]
                };
            }

            /**
             * 替换完成出参
             * @internal
             */
            interface DWReplaceResponseV2 extends DWReplaceModelInfoV2 {
                result: boolean; // 替换结果
                message?: string; // 错误提示
            }

            /**
             * 门窗替换接口
             * 支持模型替换和参数替换
             * @internal
             */
            function replaceDWModelV2Async(replaceModels: DWReplaceModelInfoV2[]): Promise<DWReplaceResponseV2[]>
        }
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
