# DEV

### Features
- ✨ 小程序 `MainMountPointOptions` 新增参数 `isMinimized` 用于设置小程序容器是否是最小化状态。

# 1.50.0 (2024-08-02)
### Features
- ✨ 小程序 `MainMountPointOptions` 新增参数 `minimizable` 用于设置小程序是否可以最小化。

# 1.43.0 (2024-04-16)
### Features
- ✨ 小程序 `MainMountPointOptions` 新增参数 `windowBarSize`，支持设置窗口模式下的顶、底部尺寸规格

# 1.35.0 (2023-11-21)
### Features
- ✨ 小程序 `MainMountPointOptions` 属性 `resizable` 新增类型 `width`、`height'`, 支持配置窗口拉伸规则

# 1.34.0 (2023-11-07)
### Features
- ✨ 小程序 `MainMountPointOptions` 新增参数 `widthResponsive`、`heightResponsive`，支持面板宽度、高度随浏览器自适应

# 1.33.0 (2023-10-10)
### Features
- ✨ 小程序 `IDP.EventTypes` 新增事件 `IDP.UI.Layout.WindowResize`

# 1.29.0 (2023-08-01)
### Features
- ✨ 小程序 `MainMountPointOptions` `windowMode` 新增类型 `topWindowed`，窗口模式，高显示优先级

# 1.23.0 (2023-04-18)
### Features
- ✨ 小程序 `IDP.Platform` 新增接口 `IDP.Platform.getLocale`

# 1.21.0 (2023-03-21)
### Features
- ✨ 小程序 `IDP.EventTypes` 新增事件 `IDP.Design.Save`

# 1.20.0 (2023-03-07)
### Features
- ✨ 小程序 `IDP.Integration.Bom` Namespace 中新增如下API：
  1. `deleteGroupsByProductIdAsync` 通过产品ID，批量删除分组
  2. `findGroupsByProductIdAsync` 通过产品ID，查询分组
  3. `deleteMaterialsByOrderIdAsync` 通过订单ID，删除物料
- ✨ 小程序 `IDP.Integration.Bom` Namespace 中如下API，更新了参数更新：
  1. `deletePlanksByProductIdsAsync` 通过产品ID，删除板信息时，新增必传参数orderId
  2. `findPlankListAsync` 通过产品ID，查询板件信息时，新增必传参数orderId
  3. `deleteFinishedProductsAsync` 通过产品ID删除成品五金时，新增必传参数orderId
  4. `findFinishedProductsAsync` 通过产品ID查询成品五金时，新增必传参数orderId

# 1.19.0 (2023-02-21)
### Features
- ✨ 小程序 `IDP.Integration.Bom` Namespace 中修改如下接口：
  1. `createGroupsAsync` 创建分组时，新增非必填字段： `comment`

# 1.18.0 (2023-02-07)
### Features
- ✨ 小程序 `IDP.Integration.Bom` Namespace 中修改如下接口：
  1. `updatePlankAsync`, `updatePlanksAsync`, `updateFinishedProductsAsync`, `updateMoldingsAsync`, `createGroupsAsync` 新增品类与属性相关非必须字段： `category`, `attributes`
  2. `findPlankListAsync`, `getPlankListAsync`, `findFinishedProductsAsync`, `findMoldingsAsync`, `findGroupsByRootIdAsync`, `findGroupsByBomIdAsync`, `findGroupsByOrderIdAsync` 新增必须返回字段: `category`, `attributes`
  3. `updateGroupsAsync` 支持批量更新分组的品类与属性
- ✨ 小程序 `IDP.Custom.Drawing` Namespace 中修改如下接口：
  1. `DivisionType` 施工图出图方式新增柜组出图枚举
  1. `getConstructionDrawingByElementsAsync` 支持柜组出图

# 1.16.0 (2022-12-07)
### Features
- ✨ 小程序 `IDP.Integration.Bom` Namespace 中新增如下接口：
  1. `createBomsAsync` 批量创建物料
- ✨ 命名空间 `IDP.Platform` 下：
  1. 新增 `AppMode` 枚举值，列举已知应用模式标识
  2. 新增 `getAppMode` 方法，获取当前应用模式
- ✨ 命名空间 `IDP.DB.Types` 下新增 `Opening` 类型
- ✨ 命名空间 `IDP.DB.Methods` 下：
  1. 新增 `getAllWallList` 方法
  2. 新增 `getAllRoomList` 方法
  3. 新增 `getAllOpeningList` 方法

# 1.14.0 (2022-11-08)
### Features
- ✨ 小程序 定制模型参数 功能完善：
  1. 支持复合公式，参数覆写等功能
  2. 支持获取参数完整信息
  3. 标示『setPrimitiveOverride』『getPrimitiveOverride』标记为过期方法
- ✨ 命令空间 `IDP.User` 下，新增方法：
  1. 新增 `getUserDetailsAsync` 方法，获取用户详情信息

# 1.0.1
- 初次发布
