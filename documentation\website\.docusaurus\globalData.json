{"docusaurus-plugin-content-docs": {"default": {"path": "/manycoreapi-demo/0.0.4/docs", "versions": [{"name": "current", "label": "Next", "isLast": true, "path": "/manycoreapi-demo/0.0.4/docs", "mainDocId": "intro", "docs": [{"id": "api/camera/camera-infrastructure", "path": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure", "sidebar": "cameraSidebar"}, {"id": "api/camera/camera-infrastructure-api", "path": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api", "sidebar": "cameraSidebar"}, {"id": "api/camera/get-cameras", "path": "/manycoreapi-demo/0.0.4/docs/api/camera/get-cameras", "sidebar": "cameraSidebar"}, {"id": "api/doorwindow/batch-update", "path": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/batch-update", "sidebar": "doorwindowSidebar"}, {"id": "api/doorwindow/get-doc", "path": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/get-doc", "sidebar": "doorwindowSidebar"}, {"id": "api/doorwindow/门窗管理-api", "path": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗管理-api", "sidebar": "doorwindowSidebar"}, {"id": "api/doorwindow/门窗设计管理api", "path": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗设计管理api", "sidebar": "doorwindowSidebar"}, {"id": "api/furniture/batch-create-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/batch-create-furniture-by-group-product", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-create-furniture-by-group-product", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/batch-delete-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-delete-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/batch-get-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-get-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/batch-update-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/batch-update-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/create-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/create-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/delete-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/delete-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/get-furniture-list", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/get-furniture-list", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/get-single-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/get-single-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/update-furniture", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/update-furniture", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/家具管理接口", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具管理接口", "sidebar": "furnitureSidebar"}, {"id": "api/furniture/家具设计管理api", "path": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具设计管理api", "sidebar": "furnitureSidebar"}, {"id": "api/koolux/batch-update", "path": "/manycoreapi-demo/0.0.4/docs/api/koolux/batch-update", "sidebar": "kooluxSidebar"}, {"id": "api/koolux/get-scene-document", "path": "/manycoreapi-demo/0.0.4/docs/api/koolux/get-scene-document", "sidebar": "kooluxSidebar"}, {"id": "api/koolux/import-data-source", "path": "/manycoreapi-demo/0.0.4/docs/api/koolux/import-data-source", "sidebar": "kooluxSidebar"}, {"id": "api/koolux/koo-lux-light", "path": "/manycoreapi-demo/0.0.4/docs/api/koolux/koo-lux-light", "sidebar": "kooluxSidebar"}, {"id": "api/koolux/koolux-rest-api", "path": "/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api", "sidebar": "kooluxSidebar"}, {"id": "api/layout/batch-create-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/batch-create-legend-group", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-create-legend-group", "sidebar": "layoutSidebar"}, {"id": "api/layout/batch-delete-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-delete-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/batch-get-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-get-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/batch-update-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/batch-update-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/create-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/create-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/delete-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/delete-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/get-colored-floor-plan", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/get-colored-floor-plan", "sidebar": "layoutSidebar"}, {"id": "api/layout/get-single-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/get-single-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/list-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/list-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/update-legend", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/update-legend", "sidebar": "layoutSidebar"}, {"id": "api/layout/图例管理接口", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/图例管理接口", "sidebar": "layoutSidebar"}, {"id": "api/layout/户型图例管理api", "path": "/manycoreapi-demo/0.0.4/docs/api/layout/户型图例管理api", "sidebar": "layoutSidebar"}, {"id": "api/pdm/batch-delete-planar-model", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-delete-planar-model", "sidebar": "pdmSidebar"}, {"id": "api/pdm/batch-fetch-planar-model", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model", "sidebar": "pdmSidebar"}, {"id": "api/pdm/batch-fetch-planar-model-build-result", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-fetch-planar-model-build-result", "sidebar": "pdmSidebar"}, {"id": "api/pdm/batch-save-planar-model-v-2", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-save-planar-model-v-2", "sidebar": "pdmSidebar"}, {"id": "api/pdm/delete-planar-model", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/delete-planar-model", "sidebar": "pdmSidebar"}, {"id": "api/pdm/fetch-planar-model", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model", "sidebar": "pdmSidebar"}, {"id": "api/pdm/fetch-planar-model-list", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/fetch-planar-model-list", "sidebar": "pdmSidebar"}, {"id": "api/pdm/save-planar-model-v-2", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/save-planar-model-v-2", "sidebar": "pdmSidebar"}, {"id": "api/pdm/平面造型设计api", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计api", "sidebar": "pdmSidebar"}, {"id": "api/pdm/平面造型设计管理接口", "path": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计管理接口", "sidebar": "pdmSidebar"}, {"id": "getting-started/authentication", "path": "/manycoreapi-demo/0.0.4/docs/getting-started/authentication", "sidebar": "tutorialSidebar"}, {"id": "getting-started/error-handling", "path": "/manycoreapi-demo/0.0.4/docs/getting-started/error-handling", "sidebar": "tutorialSidebar"}, {"id": "getting-started/making-requests", "path": "/manycoreapi-demo/0.0.4/docs/getting-started/making-requests", "sidebar": "tutorialSidebar"}, {"id": "guides/best-practices", "path": "/manycoreapi-demo/0.0.4/docs/guides/best-practices", "sidebar": "tutorialSidebar"}, {"id": "guides/rate-limiting", "path": "/manycoreapi-demo/0.0.4/docs/guides/rate-limiting", "sidebar": "tutorialSidebar"}, {"id": "guides/sdks", "path": "/manycoreapi-demo/0.0.4/docs/guides/sdks", "sidebar": "tutorialSidebar"}, {"id": "guides/webhooks", "path": "/manycoreapi-demo/0.0.4/docs/guides/webhooks", "sidebar": "tutorialSidebar"}, {"id": "intro", "path": "/manycoreapi-demo/0.0.4/docs/", "sidebar": "tutorialSidebar"}], "draftIds": [], "sidebars": {"tutorialSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/", "label": "intro"}}, "cameraSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api", "label": "api/camera/camera-infrastructure-api"}}, "doorwindowSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/api/doorwindow/门窗设计管理api", "label": "api/doorwindow/门窗设计管理api"}}, "furnitureSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/api/furniture/家具设计管理api", "label": "api/furniture/家具设计管理api"}}, "kooluxSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/api/koolux/koolux-rest-api", "label": "api/koolux/koolux-rest-api"}}, "layoutSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/api/layout/户型图例管理api", "label": "api/layout/户型图例管理api"}}, "pdmSidebar": {"link": {"path": "/manycoreapi-demo/0.0.4/docs/api/pdm/平面造型设计api", "label": "api/pdm/平面造型设计api"}}}}], "breadcrumbs": false}}}