import React from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import CodeBlock from '@theme/CodeBlock';
import styles from './index.module.css';

// 功能特性数据
const FeatureList = [
  {
    title: '🏠 海量 3D 模型库',
    description: (
      <>
        数百万高质量 3D 家居模型，涵盖家具、装饰、建材等各类产品。
        支持实时检索、批量操作和版本管理。
      </>
    ),
  },
  {
    title: '🎨 智能设计引擎',
    description: (
      <>
        AI 驱动的家居设计工具，提供智能布局建议、材质搭配和空间优化，
        让每个人都能成为设计师。
      </>
    ),
  },
  {
    title: '☁️ 云端渲染服务',
    description: (
      <>
        强大的云端渲染能力，支持高质量图片和 360° 全景渲染，
        为您的设计作品提供专业级的视觉效果。
      </>
    ),
  },
  {
    title: '🔌 开放 API 平台',
    description: (
      <>
        完整的 RESTful API 接口，支持多种编程语言 SDK，
        轻松集成群核科技的设计能力到您的应用中。
      </>
    ),
  },
  {
    title: '📱 多端适配',
    description: (
      <>
        支持 Web、移动端、VR/AR 等多种平台，
        为用户提供一致的跨平台体验。
      </>
    ),
  },
  {
    title: '🛡️ 企业级安全',
    description: (
      <>
        ISO 27001 认证，SOC 2 合规，99.9% 可用性保障，
        为企业客户提供安全可靠的服务。
      </>
    ),
  },
];

// 统计数据
const StatsData = [
  {
    number: '1000万+',
    label: '注册用户',
    icon: '👥',
  },
  {
    number: '500万+',
    label: '3D 模型',
    icon: '🏠',
  },
  {
    number: '99.9%',
    label: '服务可用性',
    icon: '⚡',
  },
  {
    number: '24/7',
    label: '技术支持',
    icon: '🛠️',
  },
];

// 客户案例
const Customers = [
  { name: '红星美凯龙', logo: '🏢' },
  { name: '居然之家', logo: '🏪' },
  { name: '宜家家居', logo: '🛋️' },
  { name: '顾家家居', logo: '🏡' },
  { name: '欧派家居', logo: '🚪' },
  { name: '索菲亚', logo: '🗄️' },
];

function Feature({title, description}) {
  return (
    <div className={clsx('col col--4', styles.feature)}>
      <div className={styles.featureCard}>
        <h3 className={styles.featureTitle}>{title}</h3>
        <p className={styles.featureDescription}>{description}</p>
      </div>
    </div>
  );
}

function StatCard({number, label, icon}) {
  return (
    <div className={styles.statCard}>
      <div className={styles.statIcon}>{icon}</div>
      <div className={styles.statNumber}>{number}</div>
      <div className={styles.statLabel}>{label}</div>
    </div>
  );
}

function HeroSection() {
  const {siteConfig} = useDocusaurusContext();
  
  return (
    <header className={styles.heroBanner}>
      <div className="container">
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <h1 className={styles.heroTitle}>
              群核科技 API
              <span className={styles.heroSubtitle}>开发者平台</span>
            </h1>
            <p className={styles.heroDescription}>
              全球领先的家居云设计平台 API，为开发者提供强大的 3D 设计、模型管理、
              渲染服务等能力。让您的应用拥有专业级的家居设计功能。
            </p>
            <div className={styles.heroButtons}>
              <Link
                className={`button button--primary button--lg ${styles.heroButton}`}
                to="/docs">
                🚀 开始使用
              </Link>
              <Link
                className={`button button--secondary button--lg ${styles.heroButton}`}
                to="/docs/api">
                📖 API 文档
              </Link>
            </div>
          </div>
          <div className={styles.heroDemo}>
            <div className={styles.demoWindow}>
              <div className={styles.demoHeader}>
                <div className={styles.demoButtons}>
                  <span />
                  <span />
                  <span />
                </div>
                <div className={styles.demoTitle}>群核科技 API 示例</div>
              </div>
              <CodeBlock language="javascript" className={styles.demoCode}>
{`import { QunheSdk } from '@qunhe/api-sdk';

const client = new QunheSdk({
  apiKey: 'your-api-key'
});

// 创建 3D 家具模型
const furniture = await client.furniture.create({
  name: '现代沙发',
  category: 'sofa',
  dimensions: { 
    width: 200, 
    height: 80, 
    depth: 90 
  }
});

// 获取设计方案渲染图
const render = await client.design.render({
  designId: 'design-123',
  quality: 'high',
  format: 'jpg'
});

console.log('渲染完成:', render.url);`}
              </CodeBlock>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

function StatsSection() {
  return (
    <section className={styles.statsSection}>
      <div className="container">
        <div className={styles.statsGrid}>
          {StatsData.map((stat) => (
            <StatCard key={stat.label} {...stat} />
          ))}
        </div>
      </div>
    </section>
  );
}

function FeaturesSection() {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>为什么选择群核科技 API？</h2>
          <p className={styles.sectionDescription}>
            我们提供业界领先的家居设计技术和服务，助力开发者构建创新应用
          </p>
        </div>
        <div className="row">
          {FeatureList.map((props) => (
            <Feature key={props.title} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}

function QuickStartSection() {
  return (
    <section className={styles.quickStart}>
      <div className="container">
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>5 分钟快速开始</h2>
          <p className={styles.sectionDescription}>
            跟随我们的快速指南，立即开始使用群核科技 API
          </p>
        </div>
        <div className={styles.quickStartGrid}>
          <div className={styles.quickStartStep}>
            <div className={styles.stepNumber}>1</div>
            <h3>注册账号</h3>
            <p>访问开发者控制台注册账号并创建应用</p>
            <Link to="https://developers.qunheco.com" className="button button--outline">
              注册账号 →
            </Link>
          </div>
          <div className={styles.quickStartStep}>
            <div className={styles.stepNumber}>2</div>
            <h3>获取 API 密钥</h3>
            <p>在控制台中生成您的 API 密钥</p>
            <Link to="/docs/getting-started/authentication" className="button button--outline">
              查看指南 →
            </Link>
          </div>
          <div className={styles.quickStartStep}>
            <div className={styles.stepNumber}>3</div>
            <h3>安装 SDK</h3>
            <p>选择您喜欢的编程语言开始开发</p>
            <Link to="/docs/guides/sdks" className="button button--outline">
              选择 SDK →
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}

function TrustedBySection() {
  return (
    <section className={styles.trustedBy}>
      <div className="container">
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>受到行业领导者信赖</h2>
          <p className={styles.sectionDescription}>
            全球 10000+ 家企业使用群核科技 API 构建家居应用
          </p>
        </div>
        <div className={styles.customersGrid}>
          {Customers.map((customer) => (
            <div key={customer.name} className={styles.customerCard}>
              <div className={styles.customerLogo}>{customer.logo}</div>
              <div className={styles.customerName}>{customer.name}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function CTASection() {
  return (
    <section className={styles.cta}>
      <div className="container">
        <div className={styles.ctaContent}>
          <h2 className={styles.ctaTitle}>准备开始构建？</h2>
          <p className={styles.ctaDescription}>
            立即开始使用群核科技 API，释放家居设计的无限可能
          </p>
          <div className={styles.ctaButtons}>
            <Link
              className="button button--primary button--lg"
              to="/docs">
              免费开始
            </Link>
            <Link
              className="button button--secondary button--lg"
              to="mailto:<EMAIL>">
              联系销售
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function Home() {
  const {siteConfig} = useDocusaurusContext();
  return (
    <Layout
      title="群核科技 API 开发者平台"
      description="全球领先的家居云设计平台 API，为开发者提供 3D 设计、模型管理、渲染服务等强大能力">
      <HeroSection />
      <StatsSection />
      <FeaturesSection />
      <QuickStartSection />
      <TrustedBySection />
      <CTASection />
    </Layout>
  );
} 