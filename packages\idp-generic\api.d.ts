import { IDP as IDPCommon } from '@qunhe/idp-common';

/**
 * @internal
 */
interface GenericModel {
    /** 商品 ID */
    productId?: string;
    /**
     * 扩展属性
     * @vm-type Attributes
     */
    attributes: { [key: string]: string };
}

/**
 * 更新通用设计对象信息
 * @internal
 */
interface ModelUpdateInfo {
    elementId: IDPCommon.DB.Types.ElementId;
    /**
     * 覆盖源扩展属性
     * @vm-type Attributes
     */
    attributes: { [key: string]: string };
}

declare namespace IDP {
    namespace DB {
        namespace Methods {
            /**
             * 根据 ID 获取通用设计对象信息
             * @param elementId 通用设计对象 ID
             * @internal
             */
            function getGenericModel(elementId: IDPCommon.DB.Types.ElementId): GenericModel | undefined;

            /**
             * 根据 ID 更新通用设计对象信息
             * @param modelUpdateInfo 需要更新的通用设计对象信息
             * @internal
             */
            function updateGenericModel(modelUpdateInfo: ModelUpdateInfo): void;
        }
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
