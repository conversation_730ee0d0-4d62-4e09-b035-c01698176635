import type { IDP as Floorplan } from '@qunhe/idp-floorplan';
import { KLineSegment2dImpl, KGeomFace2dImpl, KVector3dImpl, KPlaneImpl, KPoint2dImpl, KVector2dImpl } from '@qunhe/math-apaas-impl';
import { LineSeg2d, Point2d, Plane, Vector2d, Point3d, LineSeg3d, CompositeCurve3d, GeomFace2d, GeomLoop2d, Vector3d, CCS3d } from '@qunhe/math';

/**
 * 由于跑单测时node_modules/@qunhe/math-apaas-impl/apiType/KAllType.js
 * 没有实现math的静态类型，在生成的时候只有ObjectType，所以需要跑序列化单测一律使用plainObject做对比
 * @returns
 */
function serializeMathObject(): Object {
    return {} as any;
}

/**
 * 生成所有类型的构件
 * @returns
 */
const buildFullTypeElements = (): Floorplan.DB.Types.Floorplan.FloorplanElement[] => [
    // curve wall
    {
        etp: 'wall' as Floorplan.DB.Types.Floorplan.ElementType.Wall,
        id: '1',
        geometricRepresentation: {
            gtp: 'boundedAxis' as const,
            curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(0, 0), new Point2d(1000, 1000))),
            thickness: 240,
            height: 2800,
            offGround: 0,
        },
        wallType: 'normal' as Floorplan.DB.Types.Floorplan.WallType.NORMAL,
        isBearing: true,
    },
    // shaped wall
    {
        etp: 'wall' as Floorplan.DB.Types.Floorplan.ElementType.Wall,
        id: '2',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid' as const,
            extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
            plane: new KPlaneImpl(new Plane(new CCS3d())),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(new GeomLoop2d([
                    new LineSeg2d(new Point2d(0, 0), new Point2d(1000, 1000)),
                    new LineSeg2d(new Point2d(1000, 1000), new Point2d(1000, 0)),
                    new LineSeg2d(new Point2d(1000, 0), new Point2d(0, 0)),
                ]))),
            },
        },
        wallType: 'normal' as Floorplan.DB.Types.Floorplan.WallType.NORMAL,
        isBearing: true,
    },
    // beam
    {
        etp: 'beam' as Floorplan.DB.Types.Floorplan.ElementType.Beam,
        id: '3',
        geometricRepresentation: {
            gtp: 'boundedAxis' as const,
            curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(0, 0), new Point2d(1000, 1000))),
            thickness: 240,
            height: 2800,
            offGround: 0,
        },
        offgroundType: 'FLOOR' as Floorplan.DB.Types.Floorplan.OffgroundType,
    },
    // pillar
    {
        etp: 'pillar' as Floorplan.DB.Types.Floorplan.ElementType.Pillar,
        id: '4',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid' as const,
            extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
            plane: new KPlaneImpl(new Plane(new CCS3d())),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(new GeomLoop2d([]))),
            },
        },
    },
    // room
    {
        etp: 'room' as Floorplan.DB.Types.Floorplan.ElementType.Room,
        id: '5',
        location: new KPoint2dImpl(new Point2d(0, 0)),
        name: 'room1',
        roomType: 17 as Floorplan.DB.Types.Floorplan.RoomType,
        roomTypeName: 'normal',
        hasCeiling: true,
    },
    // room separator
    {
        etp: 'room_separator' as Floorplan.DB.Types.Floorplan.ElementType.RoomSeparator,
        id: '6',
        curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(0, 0), new Point2d(1000, 1000))),
    },
    // floor opening
    {
        etp: 'floor_opening' as Floorplan.DB.Types.Floorplan.ElementType.FloorOpening,
        id: '7',
        offgroundType: 'FLOOR' as Floorplan.DB.Types.Floorplan.OffgroundType,
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid' as const,
            extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
            plane: new KPlaneImpl(new Plane(new CCS3d())),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(new GeomLoop2d([]))),
            },
        },
    },
    // opening
    {
        etp: 'opening' as Floorplan.DB.Types.Floorplan.ElementType.Opening,
        id: '8',
        hostIds: ['1', '2'],
        openingType: 'OPENING' as Floorplan.DB.Types.Floorplan.OpeningType,
        geometricRepresentation: {
            gtp: 'boundedAxis' as const,
            curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(0, 0), new Point2d(1000, 1000))),
            thickness: 240,
            height: 2800,
            offGround: 0,
        },
    },
    // chimney
    {
        etp: 'chimney' as Floorplan.DB.Types.Floorplan.ElementType.Chimney,
        id: '9',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid' as const,
            extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
            plane: new KPlaneImpl(new Plane(new CCS3d())),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(new GeomLoop2d([]))),
            },
        },
    },
];

const serializedElements: Object[] = [
    {
        etp: 'wall',
        id: '1',
        geometricRepresentation: {
            gtp: 'boundedAxis',
            height: 2800,
            thickness: 240,
            offGround: 0,
            curve: serializeMathObject(),
        },
        isBearing: true,
        wallType: 'normal',
    },
    {
        etp: 'wall',
        id: '2',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid',
            extrudeDirection: serializeMathObject(),
            plane: serializeMathObject(),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: serializeMathObject(),
            },
        },
        wallType: 'normal',
        isBearing: true,
    },
    {
        etp: 'beam',
        id: '3',
        geometricRepresentation: {
            gtp: 'boundedAxis',
            height: 2800,
            thickness: 240,
            offGround: 0,
            curve: serializeMathObject(),
        },
        offgroundType: 'FLOOR',
    },
    {
        etp: 'pillar',
        id: '4',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid',
            extrudeDirection: serializeMathObject(),
            plane: serializeMathObject(),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: serializeMathObject(),
            },
        },
    },
    {
        etp: 'room',
        id: '5',
        location: serializeMathObject(),
        name: 'room1',
        roomType: 17,
        roomTypeName: 'normal',
        hasCeiling: true,
    },
    {
        etp: 'room_separator',
        id: '6',
        curve: serializeMathObject(),
    },
    {
        etp: 'floor_opening',
        id: '7',
        offgroundType: 'FLOOR',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid',
            extrudeDirection: serializeMathObject(),
            plane: serializeMathObject(),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: serializeMathObject(),
            },
        }
    },
    {
        etp: 'opening',
        id: '8',
        hostIds: ['1', '2'],
        openingType: 'OPENING',
        geometricRepresentation: {
            gtp: 'boundedAxis',
            curve: serializeMathObject(),
            thickness: 240,
            height: 2800,
            offGround: 0,
        },
    },
    {
        etp: 'chimney',
        id: '9',
        geometricRepresentation: {
            gtp: 'extrudeAreaSolid',
            extrudeDirection: serializeMathObject(),
            plane: serializeMathObject(),
            depth: 2800,
            profile: {
                ptp: 'arbitraryClosedProfile',
                faceOnPlane: serializeMathObject(),
            },
        },
    }
]

const buildBatchCreateRequest = (): Floorplan.DB.Types.Floorplan.FloorplanDocumentBatchUpdateRequest => {
    return {
        batchRequests: [
            {
                rtp: 'wall_create',
                geometricRepresentation: {
                    gtp: 'boundedAxis',
                    height: 2800,
                    thickness: 500,
                    offGround: 0,
                    curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(2000, 2000), new Point2d(5000, 5000))),
                },
                openingCreateRequests: [
                    // 矩形窗洞
                    {
                        rtp: 'opening_create',
                        hostId: '',
                        openingType: 'WINDOW' as Floorplan.DB.Types.Floorplan.OpeningType,
                        geometricRepresentation: {
                            gtp: 'boundedAxis',
                            height: 1200,
                            thickness: 500,
                            offGround: 600,
                            curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(2500, 2500), new Point2d(2500, 2500).added(new Vector2d(1, 1).normalized().multiplied(800)))),
                        },
                    },
                    // 矩形门洞
                    {
                        rtp: 'opening_create',
                        hostId: '',
                        openingType: 'DOOR' as Floorplan.DB.Types.Floorplan.OpeningType,
                        geometricRepresentation: {
                            gtp: 'boundedAxis',
                            height: 1500,
                            thickness: 500,
                            offGround: 0,
                            curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(4000, 4000), new Point2d(4000, 4000).added(new Vector2d(1, 1).normalized().multiplied(1200)))),
                        },
                    },
                ],
            },
            {
                rtp: 'wall_create',
                geometricRepresentation: {
                    gtp: 'boundedAxis',
                    height: 2800,
                    thickness: 240,
                    offGround: 0,
                    curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(5000, 5000), new Point2d(12000, 5000))),
                },
                openingCreateRequests: [
                    // 矩形门洞
                    {
                        rtp: 'opening_create',
                        hostId: '',
                        openingType: 'OPENING' as Floorplan.DB.Types.Floorplan.OpeningType,
                        geometricRepresentation: {
                            gtp: 'boundedAxis',
                            height: 2000,
                            thickness: 240,
                            offGround: 200,
                            curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(10000, 5000), new Point2d(11200, 5000))),
                        },
                    },
                    // 非矩形门洞
                    (() => {
                        const curve3ds = [
                            new LineSeg3d(new Point3d(7000, 5000, 0), new Point3d(8200, 5000, 0)),
                            new LineSeg3d(new Point3d(8200, 5000, 0), new Point3d(9000, 5000, 1500)),
                            new LineSeg3d(new Point3d(9000, 5000, 1500), new Point3d((7000 + 8200) / 2, 5000, 2300)),
                            new LineSeg3d(new Point3d((7000 + 8200) / 2, 5000, 2300), new Point3d(6200, 5000, 1500)),
                            new LineSeg3d(new Point3d(6200, 5000, 1500), new Point3d(7000, 5000, 0)),
                        ];

                        const cc3d = new CompositeCurve3d();
                        cc3d.setCurves(curve3ds);

                        const ccPlane = Plane.makeFromCompositeCurve(cc3d)!;
                        const normal = cc3d.getNormalOfCompositeCurve()!;
                        const origin = ccPlane.getClosestPoint(new Point3d(0, 0, 0));
                        const plane = Plane.makePlaneByPointNormal(origin, normal);
                        const uvFace = plane.faceToUV({
                            contour: curve3ds.concat(),
                            holes: [],
                        });

                        return {
                            rtp: 'opening_create',
                            hostId: '',
                            openingType: 'DOOR' as string as Floorplan.DB.Types.Floorplan.OpeningType,
                            geometricRepresentation: {
                                gtp: 'extrudeAreaSolid',
                                extrudeDirection: new KVector3dImpl(normal),
                                plane: new KPlaneImpl(plane),
                                depth: 240,
                                profile: {
                                    ptp: 'arbitraryClosedProfile',
                                    faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(
                                        new GeomLoop2d(uvFace.contour)
                                    )),
                                },
                            },
                        };
                    })(),
                ],
            },
            {
                rtp: 'wall_create',
                geometricRepresentation: {
                    gtp: 'boundedAxis',
                    height: 2800,
                    thickness: 240,
                    offGround: 0,
                    curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(12000, 5000), new Point2d(12000, 2000))),
                },
            },
            {
                rtp: 'wall_create',
                geometricRepresentation: {
                    gtp: 'boundedAxis',
                    height: 2800,
                    thickness: 240,
                    offGround: 0,
                    curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(12000, 2000), new Point2d(2000, 2000))),
                },
            },
            {
                rtp: 'wall_create',
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
                    plane: new KPlaneImpl(new Plane(new CCS3d())),
                    depth: 2800,
                    profile: {
                        ptp: 'circleProfile',
                        center: new KPoint2dImpl(new Point2d(-3000, -2000)),
                        radius: 500,
                    },
                },
            },

            {
                rtp: 'wall_create',
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
                    plane: new KPlaneImpl(new Plane(new CCS3d())),
                    depth: 2800,
                    profile: {
                        ptp: 'arbitraryClosedProfile',
                        faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(
                            new GeomLoop2d([
                                new LineSeg2d(new Point2d(-8000, -8000), new Point2d(-12000, -8000)),
                                new LineSeg2d(new Point2d(-12000, -8000), new Point2d(-12000, -6000)),
                                new LineSeg2d(new Point2d(-12000, -6000), new Point2d(-8000, -8000)),
                            ])
                        )),
                    },
                },
            },
            {
                rtp: 'beam_create',
                offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
                geometricRepresentation: {
                    gtp: 'boundedAxis',
                    height: 600,
                    thickness: 500,
                    offGround: 0,
                    curve: new KLineSegment2dImpl(new LineSeg2d(new Point2d(5000, 0), new Point2d(5000, 5000))),
                },
            },
            {
                rtp: 'pillar_create',
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
                    plane: new KPlaneImpl(new Plane(new CCS3d())),
                    depth: 2800,
                    profile: {
                        ptp: 'rectangleProfile',
                        center: new KPoint2dImpl(new Point2d(9999, 1222)),
                        length: 500,
                        width: 600,
                        xDirection: new KVector2dImpl(new Vector2d(-5, -7).normalized()),
                    },
                },
            },
            {
                rtp: 'pillar_create',
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
                    plane: new KPlaneImpl(new Plane(new CCS3d())),
                    depth: 2800,
                    profile: {
                        ptp: 'circleProfile',
                        center: new KPoint2dImpl(new Point2d(0, 2000)),
                        radius: 500,
                    },
                },
            },
            {
                rtp: 'pillar_create',
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
                    plane: new KPlaneImpl(new Plane(new CCS3d())),
                    depth: 2800,
                    profile: {
                        ptp: 'arbitraryClosedProfile',
                        faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(
                            new GeomLoop2d([
                                new LineSeg2d(new Point2d(8000, 8000), new Point2d(12000, 8000)),
                                new LineSeg2d(new Point2d(12000, 8000), new Point2d(12000, 6000)),
                                new LineSeg2d(new Point2d(12000, 6000), new Point2d(8000, 8000)),
                            ])
                        )),
                    },
                },
            },
            {
                rtp: 'room_separator_create',
                curve: new KLineSegment2dImpl(
                    new LineSeg2d(new Point2d(2000, 2000), new Point2d(2000, 6000))
                ),
            },
            {
                rtp: 'room_separator_create',
                curve: new KLineSegment2dImpl(
                    new LineSeg2d(new Point2d(2000, 6000), new Point2d(5000, 5000))
                ),
            },
            {
                rtp: 'floor_opening_create',
                name: '?-floor',
                offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxis),
                    plane: new KPlaneImpl(new Plane(new CCS3d(new Point3d(0, 0, 0)))),
                    depth: 250,
                    profile: {
                        ptp: 'arbitraryClosedProfile',
                        faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(
                            new GeomLoop2d([
                                new LineSeg2d(new Point2d(5000, 3000), new Point2d(8000, 3000)),
                                new LineSeg2d(new Point2d(8000, 3000), new Point2d(8000, 4000)),
                                new LineSeg2d(new Point2d(8000, 4000), new Point2d(5000, 4000)),
                                new LineSeg2d(new Point2d(5000, 4000), new Point2d(5000, 3000)),
                            ])
                        )),
                    },
                },
            },
            {
                rtp: 'floor_opening_create',
                name: '?-ceiling',
                offGroundType: 'CEILING' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
                geometricRepresentation: {
                    gtp: 'extrudeAreaSolid',
                    extrudeDirection: new KVector3dImpl(Vector3d.zAxisMinus),
                    plane: new KPlaneImpl(new Plane(new CCS3d(new Point3d(0, 0, 2800)))),
                    depth: 250,
                    profile: {
                        ptp: 'arbitraryClosedProfile',
                        faceOnPlane: new KGeomFace2dImpl(new GeomFace2d(
                            new GeomLoop2d([
                                new LineSeg2d(new Point2d(5000, 3000), new Point2d(8000, 3000)),
                                new LineSeg2d(new Point2d(8000, 3000), new Point2d(8000, 4000)),
                                new LineSeg2d(new Point2d(8000, 4000), new Point2d(5000, 4000)),
                                new LineSeg2d(new Point2d(5000, 4000), new Point2d(5000, 3000)),
                            ])
                        )),
                    },
                },
            },
        ]
    }
}

const serializedBatchCreateRequest: Object = {
    batchRequests: [
        {
            rtp: 'wall_create',
            geometricRepresentation: {
                gtp: 'boundedAxis',
                height: 2800,
                thickness: 500,
                offGround: 0,
                curve: serializeMathObject(),
            },
            openingCreateRequests: [
                {
                    rtp: 'opening_create',
                    hostId: '',
                    openingType: 'WINDOW' as Floorplan.DB.Types.Floorplan.OpeningType,
                    geometricRepresentation: {
                        gtp: 'boundedAxis',
                        height: 1200,
                        thickness: 500,
                        offGround: 600,
                        curve: serializeMathObject(),
                    },
                },
                {
                    rtp: 'opening_create',
                    hostId: '',
                    openingType: 'DOOR' as Floorplan.DB.Types.Floorplan.OpeningType,
                    geometricRepresentation: {
                        gtp: 'boundedAxis',
                        height: 1500,
                        thickness: 500,
                        offGround: 0,
                        curve: serializeMathObject(),
                    },
                },
            ]
        },
        {
            rtp: 'wall_create',
            geometricRepresentation: {
                gtp: 'boundedAxis',
                height: 2800,
                thickness: 240,
                offGround: 0,
                curve: serializeMathObject(),
            },
            openingCreateRequests: [
                {
                    rtp: 'opening_create',
                    hostId: '',
                    openingType: 'OPENING' as Floorplan.DB.Types.Floorplan.OpeningType,
                    geometricRepresentation: {
                        gtp: 'boundedAxis',
                        height: 2000,
                        thickness: 240,
                        offGround: 200,
                        curve: serializeMathObject(),
                    },
                },
                {
                    rtp: 'opening_create',
                    hostId: '',
                    openingType: 'DOOR' as Floorplan.DB.Types.Floorplan.OpeningType,
                    geometricRepresentation: {
                        gtp: 'extrudeAreaSolid',
                        extrudeDirection: serializeMathObject(),
                        plane: serializeMathObject(),
                        depth: 240,
                        profile: {
                            ptp: 'arbitraryClosedProfile',
                            faceOnPlane: serializeMathObject(),
                        },
                    },
                },
            ]
        },
        {
            rtp: 'wall_create',
            geometricRepresentation: {
                gtp: 'boundedAxis',
                height: 2800,
                thickness: 240,
                offGround: 0,
                curve: serializeMathObject(),
            }
        },
        {
            rtp: 'wall_create',
            geometricRepresentation: {
                gtp: 'boundedAxis',
                height: 2800,
                thickness: 240,
                offGround: 0,
                curve: serializeMathObject(),
            }
        },
        {
            rtp: 'wall_create',
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 2800,
                profile: {
                    ptp: 'circleProfile',
                    center: serializeMathObject(),
                    radius: 500,
                },
            }
        },
        {
            rtp: 'wall_create',
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 2800,
                profile: {
                    ptp: 'arbitraryClosedProfile',
                    faceOnPlane: serializeMathObject(),
                },
            }
        },
        {
            rtp: 'beam_create',
            offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
            geometricRepresentation: {
                gtp: 'boundedAxis',
                height: 600,
                thickness: 500,
                offGround: 0,
                curve: serializeMathObject(),
            },
        },
        {
            rtp: 'pillar_create',
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 2800,
                profile: {
                    ptp: 'rectangleProfile',
                    center: serializeMathObject(),
                    length: 500,
                    width: 600,
                    xDirection: serializeMathObject(),
                },
            }
        },
        {
            rtp: 'pillar_create',
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 2800,
                profile: {
                    ptp: 'circleProfile',
                    center: serializeMathObject(),
                    radius: 500,
                },
            }
        },
        {
            rtp: 'pillar_create',
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 2800,
                profile: {
                    ptp: 'arbitraryClosedProfile',
                    faceOnPlane: serializeMathObject(),
                },
            }
        },
        {
            rtp: 'room_separator_create',
            curve: serializeMathObject(),
        },
        {
            rtp: 'room_separator_create',
            curve: serializeMathObject(),
        },
        {
            rtp: 'floor_opening_create',
            name: '?-floor',
            offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 250,
                profile: {
                    ptp: 'arbitraryClosedProfile',
                    faceOnPlane: serializeMathObject(),
                },
            }
        },
        {
            rtp: 'floor_opening_create',
            name: '?-ceiling',
            offGroundType: 'CEILING' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
            geometricRepresentation: {
                gtp: 'extrudeAreaSolid',
                extrudeDirection: serializeMathObject(),
                plane: serializeMathObject(),
                depth: 250,
                profile: {
                    ptp: 'arbitraryClosedProfile',
                    faceOnPlane: serializeMathObject(),
                },
            }
        },
    ]
}

const buildBatchDeleteRequest = (): Floorplan.DB.Types.Floorplan.FloorplanDocumentBatchUpdateRequest => {
    return {
        view: 'BASIC' as Floorplan.DB.Types.Floorplan.DocumentView,
        batchRequests: Array.from({ length: 10 }).fill('').map((_, i) => ({
            rtp: 'element_delete',
            id: `${i + 1}`,
        }))
    }
}

const serializedBatchDeleteRequest: Object = {
    view: 'BASIC' as Floorplan.DB.Types.Floorplan.DocumentView,
    batchRequests: Array.from({ length: 10 }).fill('').map((_, i) => ({
        rtp: 'element_delete',
        id: `${i + 1}`,
    })),
}

const buildBatchUpdateRequest = (): Floorplan.DB.Types.Floorplan.FloorplanDocumentBatchUpdateRequest => {
    return {
        view: 'BASIC' as Floorplan.DB.Types.Floorplan.DocumentView,
        batchRequests: [
            {
                rtp: 'beam_update',
                id: '10',
                offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
            },
            {
                rtp: 'floor_opening_update',
                id: '11',
                offGroundType: 'CEILING' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
            },
            {
                rtp: 'floor_opening_update',
                id: '12',
                offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
            },
        ],
    }
}

const serializedBatchUpdateRequest: Object = {
    view: 'BASIC' as Floorplan.DB.Types.Floorplan.DocumentView,
    batchRequests: [
        {
            rtp: 'beam_update',
            id: '10',
            offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
        },
        {
            rtp: 'floor_opening_update',
            id: '11',
            offGroundType: 'CEILING' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
        },
        {
            rtp: 'floor_opening_update',
            id: '12',
            offGroundType: 'FLOOR' as unknown as Floorplan.DB.Types.Floorplan.OffgroundType,
        },
    ],
}

export const mockUtils = {
    buildFullTypeElements,
    serializedElements,
    buildBatchCreateRequest,
    serializedBatchCreateRequest,
    buildBatchDeleteRequest,
    serializedBatchDeleteRequest,
    buildBatchUpdateRequest,
    serializedBatchUpdateRequest,
}
