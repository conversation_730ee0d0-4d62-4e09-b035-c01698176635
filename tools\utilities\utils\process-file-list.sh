#!/bin/bash

# 文件列表处理工具函数
# 用于处理带引号的空格分隔文件列表

# 函数：处理文件列表
# 参数：带引号的文件列表字符串
# 输出：每行一个文件路径
process_file_list() {
    local file_list="$1"
    
    if [ -z "$file_list" ]; then
        return 0
    fi
    
    # 使用 eval 正确展开带引号的变量
    eval "files_array=($file_list)"
    
    # 输出每个文件路径
    for file in "${files_array[@]}"; do
        if [ -n "$file" ]; then
            echo "$file"
        fi
    done
}

# 函数：验证文件是否存在
# 参数：文件路径
# 返回：0 存在，1 不存在
validate_file_exists() {
    local file="$1"
    
    if [ -f "$file" ]; then
        return 0
    else
        echo "⚠️ 文件不存在: $file" >&2
        return 1
    fi
}

# 函数：过滤存在的文件
# 参数：带引号的文件列表字符串
# 输出：存在的文件路径，每行一个
filter_existing_files() {
    local file_list="$1"
    
    process_file_list "$file_list" | while read file; do
        if validate_file_exists "$file" 2>/dev/null; then
            echo "$file"
        fi
    done
}

# 函数：计算文件数量
# 参数：带引号的文件列表字符串
# 输出：文件数量
count_files() {
    local file_list="$1"
    
    if [ -z "$file_list" ]; then
        echo "0"
        return
    fi
    
    process_file_list "$file_list" | wc -l
} 