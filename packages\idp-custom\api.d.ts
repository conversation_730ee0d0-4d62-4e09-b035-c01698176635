import {
    IBatchGetFittingDataByAuxiliaryOption as BatchGetFittingDataByAuxiliaryOption,
    BomDetectionProblem,
    BomDetectionRule,
    IBomDetectionRule as BomDetectionRuleV1,
    IBomGroupEditable as BomGroupEditable,
    IBomGroupFull as Bom<PERSON><PERSON>Full,
    IBomGroupPropertiesClearOption as BomGroupPropertiesClearOption,
    IBomPropertiesClearOption as BomPropertiesClearOption,
    ICheckBomResult as CheckBomResult,
    BomDetectionOption,
    BomDetectionOptionInternal,
    ICheckBomsOptionV1 as CheckBomsOptionV1,
    ICheckOrderModePermissionOption as CheckOrderModePermissionOption,
    ICreateAfterSaleOrderFromDesignOption as CreateAfterSaleOrderFromDesignOption,
    ICreateCustomModelSimpleOption as CreateCustomModelSimpleOption,
    ICreateCustomerOption as CreateCustomerOption,
    ICreateCustomerResult as CreateCustomerResult,
    ICreateOrderOption as C<PERSON><PERSON>rderO<PERSON>,
    ICreateOrderResult as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ICreateOrdersResult as CreateOrdersResult,
    ECustomAppMode as CustomAppMode,
    ICustomEnterAppMode as CustomEnterAppMode,
    ICustomEnterAppModeAsync as CustomEnterAppModeAsync,
    ICustomModelLiteInfo as CustomModelLiteInfo,
    CustomModelSimpleData,
    CustomModelSimple as CustomModelSimpleModel,
    IDeleteAttachmentOption as DeleteAttachmentOption,
    DeleteFinishedProductOption,
    DeleteMoldingsOption,
    IDeleteOrderRemarkOption as DeleteOrderRemarkOption,
    IDesignExportDataParams as DesignExportDataParams,
    IDesignExportDataResponse as DesignExportDataResponse,
    IDesignExportUrlDataResponse as DesignExportUrlDataResponse,
    IDesignExportXmlDataParams as DesignExportXmlDataParams,
    IDesignOrderListOption as DesignOrderListOption,
    IDesignOrderListResult as DesignOrderListResult,
    IDesignOwnerDeatils as DesignOwnerDeatils,
    IDetectItem as DetectItem,
    IDetectOption as DetectOption,
    IDetectResult as DetectResult,
    IDoorCoveringHardwareMode as DoorCoveringHardwareMode,
    IDrawingExportParams as DrawingExportParams,
    IDrawingExportResponse as DrawingExportResponse,
    EBomCurveType,
    EBomPlankHoleType,
    EBomPlankType,
    EBzPropertyType,
    EClockWise,
    ECustomerQueryType,
    ECustomerSource,
    EDesignExportCode,
    EDesignType,
    EDetectType,
    EDivisionType,
    EDrawingFileType,
    EFace,
    EFieldEnum,
    EIntersectModelType,
    EIntersectedInfoType,
    ELineType,
    ENavigatorKey,
    EOrderFieldType,
    EParamModelType,
    EParamType,
    EPointType,
    EProductDirection,
    EToolType,
    EUploadFileBucket,
    ElementId,
    IEvent as Event,
    IExportParamModelData as ExportModelData,
    IFindAuditedModelResult as FindAuditedModelResult,
    FindFinishedProductOption,
    FindMoldingsOption,
    IFindOrdersOption as FindOrdersOption,
    IFindOrdersResult as FindOrdersResult,
    IListTopModelsOptions as FindTopModelsOptions,
    FindTopModelsSimpleOption,
    IFittingDesignData as FittingDesignData,
    IFittingDesignDataV2 as FittingDesignDataV2,
    IFittingDesignOption as FittingDesignOption,
    GenerateReportPageOption,
    IGetAppUidResult as GetAppUidResult,
    IGetCustomerListOption as GetCustomerListOption,
    IGetCustomerListResult as GetCustomerListResult,
    IGetFittingDataByAuxiliaryOption as GetFittingDataByAuxiliaryOption,
    IGetFittingDataByAuxiliaryResult as GetFittingDataByAuxiliaryResult,
    IGetIntersectedOption as GetIntersectedOption,
    IGetMergeTaskReportFileOption as GetMergeTaskReportFileOption,
    IGetModelJsonOption as GetModelJsonOption,
    IGetOrderReportFileOption as GetOrderReportFileOption,
    IGetPreviewImgOption as GetPreviewImgOption,
    IGetStoreInfoResult as GetStoreResult,
    IGetTopModelsLiteInfoOption as GetTopModelsLiteInfoOption,
    IGroupData as GroupData,
    IAddOrderStateAttachmentOption,
    IAddOrderStateRemarkOption,
    IBomFinishedProduct,
    IBomFinishedProductEditable,
    IBomFinishedProductEditableByModelId,
    IBomFinishedProductEditableByProductId,
    IBomFinishedProductUpdate,
    IBomGroup,
    IBomMolding,
    IBomMoldingEditable,
    IBomPlankEditable,
    IBomPlankFull,
    IBomPlankGroove,
    IBomPlankGrooveEditable,
    IBomPlankGrooveWithId,
    IBomPlankHole,
    IBomPlankHoleEditable,
    IBomPlankHoleWithId,
    IBomPlankLayoutArrange,
    IBomPlankLayoutProcessAttribute,
    IBomPlankUpdate,
    IBomPlankWithId,
    IBomRawPlank,
    IBomRawPlankEditable,
    IBomRawPlankLayoutResult,
    IBomSurplusPlank,
    IBomSurplusPlankEditable,
    ICategory,
    ICategoryAttribute,
    ICheckOperatePermissionOption,
    IParamModelLite as ICustomModel,
    IParamModelLiteParam as ICustomModelLiteParam,
    IBzParamModelLiteProperty as ICustomModelLiteProperty,
    ICustomerOrderDetail,
    IDeleteOrderStateAttachmentOption,
    IDetectBaseResult,
    IDetectConfig,
    IEngravingMachineCuttingResponse,
    IEngravingMachineCuttingSearchResult,
    IEngravingMachineCuttingTaskCreate,
    IEngravingMachineCuttingTaskCreateErrResponse,
    IEngravingMachineCuttingTaskCreateResponse,
    IExecuteOrderOperationOption,
    IExportAssemblyInfo,
    IExportModelInfo,
    IExportRes,
    IFieldEnumResult,
    IFindCustomFoldersOptions,
    IFindCustomProductsOptions,
    IFindPlankListOption,
    IFolder,
    IGetOrderAttachmentsOption,
    IGetOrderAttachmentsResult,
    IGetOrderFieldsResult,
    IGetOrderRemarksOption,
    IGetOrderRemarksResult,
    IIntersectResult,
    IOrderEventData,
    IOrderOperationData,
    IOrderStateData,
    IProduct,
    IRuleResult,
    IUpdateOrderStateRemarkOption,
    IUserDetails,
    IValidityResult,
    IImportModel as ImportModel,
    IImportModelResult as ImportModelResult,
    IInnerSpaceData as InnerSpaceData,
    IInstallData as InstallData,
    IIntersectCheckOption as IntersectCheckOption,
    IIntersectCheckResult as IntersectCheckResult,
    IIntersectedData as IntersectedData,
    IModelIsAssociatedOrderOption as ModelIsAssociatedOrderOption,
    IModelIsAssociatedOrderResult as ModelIsAssociatedOrderResult,
    IModelPackage as ModelPackage,
    Number3,
    IOrderData as OrderData,
    IPlankDrawingResult as PlankDrawingResult,
    IPreviewImgInfo as PreviewImgInfo,
    IPreviewPlankDrawingOption as PreviewPlankDrawingOption,
    IProductCodeInOrderInfo as ProductCodeInOrderInfo,
    IQuotationOption as QuotationOption,
    IQuotationReportTemplateParam as QuotationReportTemplate,
    IQuotationReportTemplateItem as QuotationReportTemplateItem,
    IQuotationResult as QuotationResult,
    IRefModelLite as RefModelLite,
    ISaveFittingDesignOption as SaveFittingDesignOption,
    SetSelectedLibraryPositionOption,
    ISplitDesignParams as SplitDesignParams,
    ISplitDesignResponse as SplitDesignResponse,
    IStorageGetKeysOption as StorageGetKeysOption,
    IStorageGetKeysResult as StorageGetKeysResult,
    IStorageItem as StorageItem,
    IStoreInfo as StoreInfo,
    IUnlockAuditedModelOption as UnlockAuditedModelOption,
    IUpdateCustomModelSimpleOption as UpdateCustomModelSimpleOption,
    IUpdateOrderModel as UpdateOrderModel,
    IUploadFileOption as UploadFileOption,
    IUploadFileResult as UploadFileResult,
    IBomStructureEditable,
    IBomStructure,
    IPreviewStructureDrawingOption as PreviewStructureDrawingOption,
    IStructureDrawingResult as StructureDrawingResult,
    IUpdateOrderOption as UpdateOrderOption,
    IUpdateCustomerOption as UpdateCustomerOption,
    IOrderModel as OrderModel,
    IBomStructureUpdate as BomStructureUpdate,
    IModelLockOption as ModelLockOption,
    IPreviewMaterialDrawingsOption as PreviewMaterialDrawingsOption,
    IPreviewMaterialDrawingsResult as PreviewMaterialDrawingsResult,
    ISwitchOrderOption as SwitchOrderOption,
    IInnerSpaceWithBorders as InnerSpaceWithBorders,
    ICreateAfterSaleOrderFromBomOption as CreateAfterSaleOrderFromBomOption,
    IGetProductDefaultBuildOption as GetProductDefaultBuildOption,
    IRefModelTemplate as RefModelTemplate,
    ICreateTemplateOption as CreateTemplateOption,
    IRefModel as RefModel,
    IAddRefModelOption as AddRefModelOption,
    IUpdateRefModelOption as UpdateRefModelOption,
    IIsolateRefModelResult as IsolateRefModelResult,
    IFindTemplatesOption as FindTemplatesOption,
    IFindRefModelsOption as FindRefModelsOption,
    ITriggerBoolEffectResult as TriggerBoolEffectResult,
    ILeftPanelStyleExtensionOptions as LeftPanelStyleExtensionOptions,
    IGetCustomerOrderOption as GetCustomerOrderOption,
    IRelatedOrderResult as RelatedOrderResult,
    IAuditOrderResult as AuditOrderResult,
} from '@qunhe/custom-apass-api';
import { IDP as IDPCommon } from '@qunhe/idp-common';

/**
 * Promise 结果范型
 */
interface Result<T> {
    /**
     * 状态码
     *   - 0：成功
     *   - 1：取消
     *   - 2：失败
     */
    code: number;
    /** 错误信息描述 */
    errorMessage?: string;
    /** 数据 */
    data?: T;
}

/**
 * 拖拽创建定制设计对象返回的 Promise 结果类型
 * @vm-type DragCustomProductPromiseResult
 */
type DragCustomProductPromiseResult = Result<IDPCommon.DB.Types.ElementId[]>;

/** 检测规则类型 */
declare enum DetectionRuleType {
    /** 有效性检测 */
    VALID = 1,
    /** 规则检测 */
    RULE,
    /** 干涉检测 */
    INTERSECT
}

interface MixGroup {
    /** 混组elementId */
    id: IDPCommon.DB.Types.ElementId;
    /** 位置 */
    position: Number3;
    /** 尺寸 */
    size: Number3;
    /** 角度 */
    rotation: Number3;
    /** 混组子模型 */
    subElements: IDPCommon.DB.Types.ElementId[];
}

interface CheckModelSplitOrderResult {
    /** 模型id */
    modelId: string;
    /** 模型是否拆单 */
    isSplitOrder: boolean;
}

interface DesignUnlockModelResult {
    /** 模型id */
    modelId: string;
    /** 导致解锁的事件key */
    eventKey: string;
}

interface GetModelJsonOptionV2 {
    /**
     * 模型id
     * 只支持同一工具线的模型同时查询
     */
    modelIds: string | string[];
    /**
     * 楼层Id
     * 默认当前所在楼层
     */
    levelId?: string;
    /**
     * 超时时间，单位（s）
     * 默认最大超时时间（10min）
     */
    timeout?: number;
    /**
     * 模板ID
     */
    templateId?: string;
}

interface GetModelJsonResultV2 {
    /**
     * 模型ID
     */
    modelId: string;
    /**
     * JSON url地址
     */
    url: string;
}

declare namespace IDP {
    /* eslint-disable no-shadow */
    namespace Design {
        /**
         * 通过elementId获取一个混组
         * @param elementId
         * @internal
         */
        function getMixGroupAsync(
            elementId: IDPCommon.DB.Types.ElementId
        ): Promise<MixGroup | undefined>;
        /**
         * 混组列表
         * @internal
         */
        function findMixGroupsAsync(): Promise<MixGroup[]>;
        /**
         * 通过商品、位置信息创建混组
         * @internal
         */
        function createMixGroupAsync(data: {
            product: {
                brandGoodId: number;
                obsBrandGoodId: string;
            };
            position: Number3;
            rotation?: Number3;
        }): Promise<MixGroup>;
        /**
         * 获取当前方案的Owner用户信息
         * @internal
         */
        function getOwnerDetailsAsync(): Promise<DesignOwnerDeatils>;
        /**
         * 删除混组
         * @internal
         */
        function deleteMixGroup(id: string): void;
        /**
         * 更新混组
         * @internal
         */
        function updateMixGroup(data: {
            id: string;
            position?: Number3;
            rotation?: Number3;
        }): void;

        /**
         * 复制当前方案
         * 由于方案复制为异步接口，最迟在接口返回后5秒左右完成复制
         * @param option
         */
        function copyAsync(): Promise<{
            /** 复制之后的方案ID */
            designId: string;
        }>;
    }

    interface EventTypes {
        /**
         * 进入定制子模式下触发的事件
         */
        'IDP.Custom.Mode.Enter': CustomAppMode;
        /**
         * 退出定制子模式下触发的事件
         */
        'IDP.Custom.Mode.Exit': CustomAppMode;
        /**
         * 掩门五金模式，选项配置发生变化时触发
         */
        'IDP.Custom.DoorCoveringHardware.Mode': DoorCoveringHardwareMode;
        /**
         * 掩门五金模式下，模型选中事件
         */
        'IDP.Custom.DoorCoveringHardware.SelectedElementsChange': IDPCommon.DB.Types.ElementId[];
        /**
         * 定制模式下，模型更新事件
         */
        'IDP.Custom.Design.CustomModel.Update': IDPCommon.DB.Types.ElementId[];
        /**
         * 订单模式下，自动更新模型范围变更时触发
         */
        'IDP.Integration.FOP.OrderMode.OrderModel.Update': Pick<
            FindAuditedModelResult,
            'modelIds' | 'subModels'
        >;
        /**
         * 装配——监听装配环境进入离开
         * 进入时派发当前激活的行业线
         * 离开时发送undefined
         * @internal
         */
        'IDP.Custom.Kada.ActiveKadaMode': EToolType | undefined;
        /**
         * 订单模式下，自动更新模型范围变更失败
         * @internal
         */
        'IDP.Integration.FOP.OrderMode.OrderModel.Update.Failed': void;
        /**
         * 订单模式下，模型范围同步状态变更
         * @internal
         */
        'IDP.Integration.FOP.OrderMode.OrderModel.SyncStatusChange': void;
        /**
         * 小程序控制渲染左侧栏时，导航item数组变更时发送
         * @internal
         */
        'IDP.Custom.LeftPanel.Navigator.Update': undefined;
        /**
         * 定制模型更新事件
         * 模型对象是最顶层模型，与设计工具资源管理器顶层节点一致
         */
        'IDP.Custom.Design.CustomModel.UpdateV2': IDPCommon.DB.Types.ElementId[];
        /**
         * 定制模型新增事件
         * 模型对象是最顶层模型，与设计工具资源管理器顶层节点一致
         */
        'IDP.Custom.Design.CustomModel.AddV2': IDPCommon.DB.Types.ElementId[];
        /**
         * 定制模型删除事件
         * 模型对象是最顶层模型，与设计工具资源管理器顶层节点一致
         */
        'IDP.Custom.Design.CustomModel.DeleteV2': IDPCommon.DB.Types.ElementId[];
        /**
         * 混组更新后的回调
         * @internal
         */
        'IDP.MixGroup.Update': string[];
        /**
         * 风格面板选中商品事件
         */
        'IDP.Custom.LeftPanel.Style.Selected': IProduct;
    }

    /**
     * @internal
     */
    namespace Common {
        /**
         * lms埋点上报
         * 自动上报 `userId` `rootAccountId` 数据
         * @internal
         * @param data 上报数据
         * @vm-type KioLog
         */
        function kioLog(
            /** 上报数据 */
            data: {
                /** 埋点标识符 */
                behavior: string;
                [key: string]: any;
            }
        ): void;
    }

    /**
     * 定制模式（包含厨卫/家居工具线）下的 API 能力
     */
    namespace Custom {
        /**
         * 定制下的一些通用变量或通用函数存放位置
         */
        namespace Common {
            interface OpenOrCloseOption {
                /**
                 * 调用clientId
                 */
                clientId?: string;
                /**
                 * 超时时间，默认为30s
                 */
                timeout?: number;
            }

            /**
             * 定制的ElementId
             */
            type CustomElementId = ElementId;

            /**
             * 定制工具线内部行业线区分
             */
            const ToolType: typeof EToolType;

            /**
             * 方案类型
             */
            const DesignType: typeof EDesignType;
            /**
             * 打开对接2.0通信通道，当前此调用此方法，需要先挂载，并且仅支持挂载到main节点
             * 如果需要打开多条通道时，需要指定clientId，否则会出现异常
             *
             * @remarks 打开通道
             * ```typescript
             * IDP.Miniapp.view.defaultFrame.mount(IDP.Miniapp.view.mountPoints.main);
             * ID.Custom.Common.openTerminalAsync().catch(() => {
             *    IDP.Miniapp.view.defaultFrame.unmount();
             *    IDP.Custom.Common.closeTerminalAsync();
             * });
             * ```
             *
             */
            function openTerminalAsync(
                option?: OpenOrCloseOption
            ): Promise<void>;

            /**
             * 关闭对接2.0通信通道（在小程序退出时，会自动关闭已打开的通道）
             *
             */
            function closeTerminalAsync(
                option?: OpenOrCloseOption
            ): Promise<void>;
            /**
             * 获取当前所在的行业线
             */
            function getCurrentToolType(): EToolType | undefined;
            /**
             * 获取当前方案类型
             */
            function getDesignType(): EDesignType | undefined;
            /**
             * 获取审核/生产方案ID
             */
            function getOrderDesignId(): string | undefined;
            /**
             * 获取定制数据是否加载完成
             */
            function isDesignLoaded(): boolean;
            /**
             * 判断分桶开关是否为优化版本流量
             * 分桶应用：https://pub.qunhequnhe.com/newbucket#/exp/custom
             * @warn 用于减少存量代码迁移，增量功能迭代不应该调用
             * @param key 试验key
             * @internal
             * @deprecated 推荐API `IDP.Platform.getPubInjectionAsync`
             */
            function isBucketKeyOptimizedAsync(key: string): Promise<boolean>;

            /**
             * 获取saasConfig配置
             * @internal
             * @param key 配置key
             * @returns Error 获取失败
             *
             *  @vm-type AsyncFunctionType
             */
            function getSaasConfigAsync<T>(key: string): Promise<T>;
        }
        /**
         * 提供内空相关能力
         */
        namespace InnerSpace {
            /**
             * 内空的朝向信息
             */
            const Face: typeof EFace;

            /**
             * 监听主工具当中，选中的内空信息
             *
             * @remarks 监听单次选中的内空信息
             * ```typescript
             * const cancel = IDP.Custom.InnerSpace.selection.once((inner) => {
             *   // will log inner
             * });
             * // 手动取消监听
             * cancel();
             * ```
             *
             * @remarks 监听内空变化触发函数
             * ```typescript
             * const cancel = IDP.Custom.InnerSpace.selection.on((inner) => {
             *
             * });
             * // 手动取消监听
             * cancel();
             * ```
             * @vm-type ISelectionEvtType
             */
            const selection: Event<InnerSpaceData | undefined>;

            /**
             * 获取当前选中的内空信息
             *
             * @remarks
             * ```typescript
             * const innerData = IDP.Custom.InnerSpace.getSelected();
             * ```
             */
            function getSelected(): InnerSpaceData | undefined;
        }

        /**
         * 掩门五金模式
         */
        namespace DoorCoveringHardware {
            /**
             * 设置商品配置信息
             */
            export interface SetOption {
                /**
                 * 商品ID
                 */
                productId: string;
            }

            /**
             * 获取掩门五金模式下的配置状态
             */
            function getOption(): DoorCoveringHardwareMode;
            /**
             * 设置掩门五金模式配置
             * @param option
             */
            function setOption(option: DoorCoveringHardwareMode): void;

            /**
             * 设置掩门五金模式下的中间件商品ID
             * @param option
             */
            function setProductId(option: SetOption): void;

            /**
             * 在掩门五金模式下，获取当前选中的模型
             */
            function getSelectedElements(): IDPCommon.DB.Types.ElementId[];
        }

        /**
         * 提供定制模式切换能力
         */
        namespace Mode {
            /**
             * 定制子模式枚举
             * @remarks
             * ```typescript
             * // 内空模式
             * IDP.Custom.Mode.AppMode.INNER_SPACE
             * ```
             */
            const AppMode: typeof CustomAppMode;

            /**
             * 在定制制模式下，获取定制子模式 如：内空, 生成等
             * @remarks
             * ```typescript
             * const mode = IDP.Custom.Mode.current();
             * ```
             */
            function current(): CustomAppMode | null;

            /**
             * 进入定制子模式
             * @remarks 进入内空模式
             * ```typescript
             * IDP.Custom.Mode.enter({
             *     mode: IDP.Custom.Mode.AppMode.INNER_SPACE,
             * });
             * ```
             * @param mode 定制子模式
             * @param {string} mode.orderId 进入订单模式时需传递订单ID (orderId)，否则无法进入
             *
             * @remarks 进入订单模式
             * ```typescript
             * IDP.Custom.Mode.enter({
             *     mode: IDP.Custom.Mode.AppMode.FOP_ORDER,
             *     orderId: 'xxxx'
             * });
             * ```
             */
            function enter(mode: CustomEnterAppMode): void;

            /**
             * 进入定制子模式，异步版本
             * @param mode 定制子模式
             * @param {string} mode.orderId 进入FOP订单模式时需传递订单ID (orderId)，否则无法进入
             *
             * @remarks 进入订单模式
             * ```typescript
             * IDP.Custom.Mode.enterAsync({
             *     mode: IDP.Custom.Mode.AppMode.FOP_ORDER,
             *     orderId: 'xxxx'
             * });
             * ```
             */
            function enterAsync(mode: CustomEnterAppModeAsync): Promise<void>;

            /**
             * 退出当前定制子模式
             * @remarks
             * ```typescript
             * IDP.Custom.Mode.exit();
             * ```
             */
            function exit(): void;
        }

        /**
         * 提供方案相关能力
         */
        namespace Design {
            /**
             * 提供方案输出能力
             */
            namespace Export {
                const DesignExportCode: typeof EDesignExportCode;
                const PointType: typeof EPointType;
                const ClockWise: typeof EClockWise;
                const LineType: typeof ELineType;
                /**
                 * 交接对象类型
                 */
                const IntersectModel: typeof EIntersectModelType;
                /**
                 * 商品建模方向
                 */
                const ProductDirection: typeof EProductDirection;
                /**
                 * 输出的交接类型
                 */
                const IntersectedInfoType: typeof EIntersectedInfoType;

                /**
                 * json输出数据
                 */
                interface ModelJson {
                    /**
                     * 模型列表
                     */
                    paramModel: ExportModelData[];
                }
                /**
                 * 获取当前方案导出JSON数据
                 *
                 * 获取厨卫工具线的方案JSON数据
                 * @remarks
                 * ```typescript
                 * IDP.Custom.Design.Export.getDesignJson({toolType: IDP.Custom.Common.ToolType.Cabinet}).then(res => {
                 *   log(res);
                 * });
                 * ```
                 * 附带超时时间
                 * @remarks
                 * ```typescript
                 * IDP.Custom.Design.Export.getDesignJson({toolType: IDP.Custom.Common.ToolType.Cabinet,timeout: 120}).then(res => {
                 *   log(res);
                 * });
                 * ```
                 * 获取指定房间方案输出
                 * @remarks
                 * ```typescript
                 * IDP.Custom.Design.Export.getDesignJson({roomId: '10250'}).then(res => {
                 *    log(res);
                 * })
                 * ```
                 *
                 * @param option 相关参数
                 */
                function getDesignJsonAsync(
                    option?: DesignExportDataParams
                ): Promise<DesignExportDataResponse>;

                /**
                 * 获取当前方案完整JSON数据
                 * @param option 相关参数
                 */
                function getDesignFullJsonAsync(
                    option?: DesignExportDataParams
                ): Promise<DesignExportDataResponse>;

                /**
                 * 获取模型json数据，json数据同孔槽方案关联，重新获取清空孔槽方案
                 * @deprecated
                 * WARNING: This API is deprecated. Please use the `getModelJsonAsyncV2` instead.
                 */
                function getModelJsonAsync(
                    option: GetModelJsonOption
                ): Promise<IDP.Custom.Design.Export.ModelJson | null>;
                /**
                 * 获取当前方案导出JSON数据下载URL地址
                 *
                 * @param option
                 */
                function getDesignJsonUrlAsync(
                    option?: DesignExportDataParams
                ): Promise<DesignExportDataResponse<{ url: string }>>;

                /**
                 * 获取当前方案完整JSON数据数据的下载URL地址
                 * @param option
                 */
                function getDesignFullJsonUrlAsync(
                    option?: DesignExportDataParams
                ): Promise<DesignExportDataResponse<{ url: string }>>;

                /**
                 * 获取当前方案导出XML的数据下载URL地址
                 * @param option DesignExportXmlDataParams
                 */
                function getDesignXmlUrlAsync(
                    option?: DesignExportXmlDataParams
                ): Promise<DesignExportUrlDataResponse>;

                /**
                 * 获取渲染报价清单
                 * @param {QuotationOption} option
                 */
                function getQuotation(option: QuotationOption): QuotationResult;

                /**
                 * 获取报价模板
                 */
                function getQuotationTemplateAsync(
                    option?: QuotationReportTemplate
                ): Promise<{ result: QuotationReportTemplateItem[] }>;

                /**
                 * 获取当前方案内可导出的私库单体模型
                 * @internal
                 */
                function getPrivateTopModelList(): IExportRes<
                    IExportModelInfo[]
                >;

                /**
                 * 获取当前方案内可导出的私库组合模型list
                 * @internal
                 */
                function getPrivateAssemblyListAsync(): Promise<
                    IExportRes<IExportAssemblyInfo[]>
                >;
                /**
                 * 获取模型json数据
                 * 按照模型维度生成json数据
                 */
                function getModelJsonAsyncV2(
                    option: GetModelJsonOptionV2
                ): Promise<GetModelJsonResultV2 | GetModelJsonResultV2[]>;

                /**
                 * 获取辅助结构定义的孔槽
                 * 坐标系原点为关联模型的左后下
                 * @remarks
                 * ```typescript
                 * const fittingDataList = await IDP.Custom.Design.Export.getFittingDataFromAuxiliaryAsync({modelId: '17EB6C45-A02E-4660-9058-5A8A9E1D1094'});
                 * ```
                 */
                function getFittingDataFromAuxiliaryAsync(
                    option: GetFittingDataByAuxiliaryOption
                ): Promise<GetFittingDataByAuxiliaryResult[]>;

                /**
                 * 批量获取辅助结构定义的孔槽
                 * 坐标系原点为关联模型的左后下
                 * @remarks
                 * ```typescript
                 * const fittingDataList = await IDP.Custom.Design.Export.getFittingDataFromAuxiliaryAsync({modelIds: ['17EB6C45-A02E-4660-9058-5A8A9E1D1094']});
                 * ```
                 */
                function getFittingDatasFromAuxiliaryAsync(
                    option: BatchGetFittingDataByAuxiliaryOption
                ): Promise<GetFittingDataByAuxiliaryResult[][]>;

                /**
                 * 获取当前方案楼层模型交接数据
                 * 坐标系原点为关联模型的左后下
                 * @remarks 获取模型的交接数据
                 * ```typescript
                 * const intersectedData = await IDP.Custom.Design.Export.getIntersectedDataAsync({modelId: '17EB6C45-A02E-4660-9058-5A8A9E1D1094'});
                 * ```
                 */
                function getIntersectedDataAsync(
                    option: GetIntersectedOption
                ): Promise<IntersectedData>;
            }

            /**
             * 提供参数化模型增删改查能力
             */
            // eslint-disable-next-line no-shadow
            namespace CustomModel {
                /**
                 * 参数类型
                 */
                const ParamType: typeof EParamType;
                /**
                 * 定制模型类别
                 */
                const ParamModelType: typeof EParamModelType;
                /**
                 * 自定议参数类型
                 */
                const BzPropertyType: typeof EBzPropertyType;
                /**
                 * 输出CustomModel类型
                 */
                type CustomModel = ICustomModel;
                /**
                 * 定制参数化模型类型
                 */
                type CustomModelParam<T = any> = ICustomModelLiteParam<T>;
                /**
                 * 用户自定议参数
                 */
                type CustomModelProperty<
                    T extends number | string | boolean = any
                > = ICustomModelLiteProperty<T>;

                /**
                 * 通过真分类创建一个CustomModel对象
                 * @param category 真分类ID
                 *
                 * @vm-type NewCustomModelByCategoryAsync
                 */
                function newCustomModelByCategoryAsync(
                    category: number
                ): Promise<CustomModel | undefined>;

                /**
                 * 通过商品ID创建一个CustomModel对象
                 * @param productId 商品ID
                 *
                 * @vm-type NewCustomModelByProductIdAsync
                 */
                function newCustomModelByProductIdAsync(
                    productId: string
                ): Promise<CustomModel | undefined>;

                /**
                 * 根据模型的的ID，获取方案中的模型信息
                 * @param id 模型ID
                 *
                 * @vm-type GetCustomModelByModelIdAsync
                 */
                function getCustomModelByModelIdAsync(
                    id: string
                ): Promise<CustomModel | undefined>;

                /**
                 * 根据模型的的ID，获取方案中的模型信息
                 * @param id 模型ID
                 * @internal
                 *
                 * @vm-type GetCustomModelByModelIdAsync
                 */
                function getCustomModelInternalByModelIdAsync(
                    id: string
                ): Promise<CustomModel | undefined>;

                /**
                 * 获取当前场景中的顶层模型
                 * @param options CustomModel对象数组
                 *
                 * @vm-type FindTopModelsAsync
                 */
                function findTopModelsAsync(
                    options?: FindTopModelsOptions
                ): Promise<CustomModel[]>;

                /**
                 * 获取当前场景中的顶层模型
                 *
                 * @internal
                 *
                 * @vm-type FindTopModelsAsync
                 */
                function findTopModelsInternalAsync(
                    options?: FindTopModelsOptions
                ): Promise<CustomModel[]>;

                /**
                 * 向方案中插入顶层节点
                 * @param options CustomModel对象数组
                 *
                 * @vm-type UpdateCustomModelAsync
                 */
                function insertAsync(options: {
                    models: CustomModel[];
                }): Promise<void>;

                /**
                 * 更新CustomModel模型
                 * @param options
                 *
                 * @vm-type UpdateCustomModelAsync
                 */
                function updateAsync(options: {
                    models: CustomModel[];
                }): Promise<void>;

                /**
                 * 删除指定顶层模型
                 * @param options
                 *
                 * @vm-type DeleteTopModelsAsync
                 */
                function deleteTopModelsAsync(options: {
                    models: CustomModel[];
                }): Promise<void>;

                /**
                 * 根据模型id获取模型的缩略图信息
                 * @param option.modelId 模型ID
                 * @param option.ignoreCategory 按真分类ID过滤缩略图内部分模型图像
                 */
                function getPreviewImgAsync(
                    option: GetPreviewImgOption
                ): Promise<PreviewImgInfo>;
                /**
                 * 查询定制商品所有的行业线，如果非定制商品，在返回对象中，将不包含对应行业线
                 * @param options
                 *
                 * @internal
                 */
                function getCustomModelToolTypeByProductIdAsync(options: {
                    productIds: string[];
                }): Promise<Record<string, EToolType[]>>;

                /**
                 * 分页获取顶层模型轻量信息
                 */
                function getTopModelsLiteInfoAsync(
                    option: GetTopModelsLiteInfoOption
                ): Promise<{
                    /**
                     * 当前查询总数
                     */
                    count: number;
                    /**
                     * 当前页码
                     */
                    page: number;
                    /**
                     * 分页数量
                     */
                    size: number;
                    /**
                     * 模型数据
                     */
                    data: CustomModelLiteInfo[];
                }>;
                /*
                 * 模型或组件高亮
                 * @internal
                 */
                function highlightModels(option: { modelIds: string[] }): void;

                /**
                 * 获取customModel的商品包(modelPackage)信息
                 * @param id 模型id数组，返回对象的key为模型id，value为商品包信息，若该id查找模型失败，则value为undefined
                 * @internal
                 */
                function getModelPackageById(
                    ids: string[]
                ): Record<string, ModelPackage>;

                /**
                 * 对子模型进行风格替换，不支持并行调用，前面的替换结束之前发起的新的调用会被丢弃
                 * @param option.modelIds 需要替换的子模型id，必须是同真分类且顶层模型是同一个
                 * @param option.productId 替换的目标商品，必须和被替换的子模型真分类相同
                 * @internal
                 */
                function replaceModelStyleAsync(option: {
                    modelIds: string[];
                    productId: string;
                }): Promise<void>;
                /**
                 * 查询一批模型数据
                 * @param option
                 */
                function findTopModelsSimpleAsync(
                    option?: FindTopModelsSimpleOption
                ): Promise<CustomModelSimpleData[]>;
                /**
                 * 锁定模型（模型在工具中将会禁用所有交互功能，无法再编辑模型数据）
                 * 注意：该接口仅是前端临时锁定，锁定效果不会进行持久化存储，且都会按照顶层模型进行锁定；
                 * 业务标识(businessId)需联系酷家乐工作人员注册之后方能使用，否则无法进行锁定
                 * @param option
                 */
                function lockModel(option: ModelLockOption): void;
                /**
                 * 解锁模型（解除模型在工具中所禁用的交互功能）
                 * 注意：该接口仅是前端临时解锁，解锁效果不会进行持久化存储，且都会按照顶层模型进行解锁；
                 * 调用该接口并不保证模型会被解锁，如果模型还被其他业务锁定，将无法解锁；
                 * 业务标识(businessId)需联系酷家乐工作人员注册之后方能使用，否则无法进行解锁
                 * @param option
                 */
                function unlockModel(option: ModelLockOption): void;
                /**
                 * 获取模型的所有内空数据，包含边界信息
                 * @param {string} id 模型id, 只允许传入顶层柜体的id
                 * * 异常处理：
                 * 1. 传入id找不到模型: throw new Error('the model does not exist')
                 * 2. 传入的id是组合模型id: throw new Error('expect paramModel, the model is paramModelAssembly');
                 * 3. 传入的id是组件id：throw new Error('expect root model, the model is child model');
                 */
                function computeInnerSpacesAsync(
                    id: string
                ): Promise<InnerSpaceWithBorders[]>;
                /**
                 * 创建引用模型母版
                 * @param option CreateTemplateOption
                 * 可基于方案中已有的模型id创建，也可以基于CustomModel直接创建
                 *
                 * @internal
                 */
                function createTemplateAsync(
                    option: CreateTemplateOption
                ): Promise<RefModelTemplate>;
                /**
                 * 查询引用模型母版
                 * @param id 母板id
                 *
                 * @internal
                 */
                function getTemplate(id: string): RefModelTemplate | undefined;

                /**
                 * 创建引用模型
                 * @param option
                 *
                 * @internal
                 */
                function createRefModel(
                    option: AddRefModelOption[]
                ): RefModel[];
                /**
                 * 查询引用模型信息
                 * @param option
                 *
                 * @internal
                 */
                function getRefModel(id: string): RefModel | undefined;
                /**
                 * 在方案中删除引用模型
                 * @param option
                 *
                 * @internal
                 */
                function deleteRefModel(id: string): void;
                /**
                 * 从母版中脱离
                 * @param option
                 *
                 * @internal
                 */
                function isolateRefModelFromTemplate(
                    id: string
                ): IsolateRefModelResult;
                /**
                 * 更新引用模型信息
                 * @param option
                 *
                 * @internal
                 */
                function updateRefModel(option: UpdateRefModelOption[]): void;
                /**
                 * 获取模版列表
                 * @param option 获取模版列表的筛选项
                 *
                 * @internal
                 */
                function findTemplates(option: FindTemplatesOption): RefModelTemplate[];
                /**
                 * 获取引用模型列表
                 * @param option 获取引用模型列表的筛选项
                 *
                 * @internal
                 */
                function findRefModels(option: FindRefModelsOption): RefModel[];
            }

            /**
             * 创建分单方案
             * @param {SplitDesignParams} option
             */
            function splitDesignByElementsAsync(
                option: SplitDesignParams
            ): Promise<SplitDesignResponse>;
        }

        namespace Product {
            /**
             * 拖拽创建定制设计对象到场景
             * @param productId 商品 ID
             * @param option.isAccessory 是否是组件
             */
            function startDragProductAsync(
                productId: string,
                option?: { isAccessory?: boolean }
            ): Promise<DragCustomProductPromiseResult>;

            /**
             * 判断模型是否是企业库模型
             * @param {string} id 模型id
             * @returns {*}  {boolean}
             */
            function isEnterpriseLibraryModel(id: string): boolean;

            /**
             * 反向导入模型
             * 详细介绍及使用可参考 https://open.kujiale.com/open/apps/2/docs?doc_id=653&tab_id=api&path=0_536_651_653
             * @param {ImportModel} option
             */
            function importProductAsync(
                option: ImportModel
            ): Promise<ImportModelResult>;

            /**
             * 获取商品信息
             * @param productId
             * @internal
             * @deprecated Product是一个更通用的概念，不应该放在Custom下，该接口即将迁移到IDP.Product下
             */
            function getProductInfoAsync(productId: string): Promise<IProduct>;

            /**
             * 设置当前选中的商品库的库节点
             * @param option
             * @internal
             */
            function setSelectedLibraryPosition(
                option: SetSelectedLibraryPositionOption
            ): void;

            /**
             * 设置默认选中的商品库的库节点
             * @param option
             * @internal
             */
            function setDefaultSelectedLibraryPosition(
                option: SetSelectedLibraryPositionOption
            ): void;
        }

        /**
         * 图纸
         */
        namespace Drawing {
            /**
             * 出图方式
             */
            const DivisionType: typeof EDivisionType;

            /**
             * 出图文件类型
             */
            const FileType: typeof EDrawingFileType;

            /**
             * 获取模型施工图
             * @param {DrawingExportParams} option
             */
            function getConstructionDrawingByElementsAsync(
                option: DrawingExportParams
            ): Promise<DrawingExportResponse[]>;

            /**
             * 进入企业自定义的出图环境
             * @param configId 自定义出图配置ID
             */
            function enterCustomDrawingAsync(configId: string): Promise<void>;
        }

        /**
         * 孔槽方案相关能力
         * @deprecated 接口已废弃，计划于`2024年9月30日`完全下线，请尽快使用`FittingDesignV2`代替，解锁更丰富的孔槽类型
         */
        namespace FittingDesign {
            /**
             * 交接对象类型
             * @deprecated 接口已废弃, 请使用`IDP.Custom.Design.Export.IntersectModel`代替
             */
            const IntersectModel: typeof EIntersectModelType;
            /**
             * 商品建模方向
             * @deprecated 接口已废弃, 请使用`IDP.Custom.Design.Export.ProductDirection`代替
             */
            const ProductDirection: typeof EProductDirection;
            /**
             * 输出的交接类型
             * @deprecated 接口已废弃, 请使用`IDP.Custom.Design.Export.IntersectedInfoType`代替
             */
            const IntersectedInfoType: typeof EIntersectedInfoType;
            /**
             * 获取当前方案楼层模型交接数据
             * @deprecated 接口已废弃, 请使用`IDP.Custom.Design.Export.getIntersectedDataAsync`代替
             */
            function getIntersectedDataAsync(
                option: GetIntersectedOption
            ): Promise<IntersectedData>;
            /**
             * 获取当前方案楼层孔槽方案
             */
            function getDesignDataAsync(
                option: FittingDesignOption
            ): Promise<FittingDesignData | null>;
            /**
             * 保存或更新当前方案楼层孔槽方案
             * >保存孔槽方案前确认已初始化模型json: `IDP.Custom.Design.Export.getModelJsonAsync`
             */
            function saveDesignDataAsync(
                option: SaveFittingDesignOption
            ): Promise<void>;
            /**
             * 删除当前方案楼层孔槽方案
             */
            function deleteDesignDataAsync(
                option: FittingDesignOption
            ): Promise<void>;
            /**
             * 批量获取模型孔槽方案
             * @param ids 模型id集合
             */
            function findDesignDataAsync(
                ids: string[]
            ): Promise<FittingDesignData[]>;
            /**
             * 批量写入模型孔槽方案
             */
            function saveDesignDatasAsync(
                option: SaveFittingDesignOption[]
            ): Promise<void>;
            /**
             * 批量删除模型孔槽方案
             * @param ids 模型id集合
             */
            function deleteDesignDatasAsync(ids: string[]): Promise<void>;
        }

        /**
         * 孔槽方案V2
         * 支持复杂工艺的孔槽：比如圆角方槽、路径槽
         * 描述孔槽空间关系的坐标系原点为孔槽关联模型的中心点
         */
        namespace FittingDesignV2 {
            /**
             * 批量保存或更新孔槽方案
             * @remark 限制最大批量更新值为`20`
             * ```typescript
             * const fittingDesigns = await IDP.Custom.FittingDesignV2.putDesignDataAsync([{id:'xxx', fittingDesign: [{modelId: 'xxx', holes: [], grooves: []}]}])
             * ```
             */
            function putDesignDataAsync(
                option: FittingDesignDataV2<false>[]
            ): Promise<FittingDesignDataV2<true>[]>;
            /**
             * 批量获取孔槽方案
             * @param ids 孔槽方案关联的顶层模型id
             * @remark 限制最大批量查找值为`20`
             * ```typescript
             * const fittingDesigns = await IDP.Custom.FittingDesignV2.findDesignDataAsync(['xxx1', 'xxx2'])
             * ```
             */
            function findDesignDataAsync(
                ids: string[]
            ): Promise<FittingDesignDataV2<true>[]>;
            /**
             * 批量删除孔槽方案
             * @param ids 孔槽方案关联的顶层模型id
             * @remark 限制最大批量删除值为`20`
             * ```typescript
             * await IDP.Custom.FittingDesignV2.deleteDesignDataAsync(['xxx1', 'xxx2'])
             * ```
             */
            function deleteDesignDataAsync(ids: string[]): Promise<void>;
        }

        /**
         * 检测相关能力
         */
        namespace Detection {
            /**
             * 检测类型
             */
            const DetectType: typeof EDetectType;

            /**
             * 获取指定模型的干涉检测结果
             * 默认会检测方案所有的模型
             *
             * @param {IntersectCheckOption} [option]
             */
            function checkIntersectionAsync(
                option?: IntersectCheckOption
            ): Promise<IntersectCheckResult[]>;

            /**
             * 集成的检测接口
             * 调用后会打开检测面板，执行config中等于true的检测，并返回检测后的结果
             * 注意：该检测只针对当前场景中的房间，建议配合IDP.Interaction.getSelectedRooms使用
             * @param  config 检测的类型
             * @param  option 检测的选项 toolType默认当前所在工具线
             * @return  检测的结果
             */
            function detectAsync(
                config: DetectItem<boolean>,
                option?: DetectOption
            ): Promise<DetectItem<DetectResult>>;
            /**
             * 按模型ids批量检测
             * @param config 检测类型
             * @param modelIds 模型ids
             */
            function detectByModelIdAsync(
                config: DetectItem<boolean>,
                modelIds: string[]
            ): Promise<DetectItem<DetectResult>>;
            /**
             * 获取各工具线支持的检测规则
             */
            function getDetectConfig(): IDetectConfig;
            /**
             * 按模型ids查询有效性检测结果
             * @param option.modelIds 模型id
             * @internal
             */
            function checkValidityByModelIdsAsync(option: {
                modelIds: string[];
            }): Promise<IDetectBaseResult<IValidityResult[]>>;
            /**
             * 按模型ids查询规则检测结果
             * @param option.modelIds 模型id
             * @internal
             */
            function checkRuleByModelIdsAsync(option: {
                modelIds: string[];
            }): Promise<IDetectBaseResult<IRuleResult[]>>;
            /**
             * 按模型ids查询干涉检测结果
             * @param option.modelIds 模型id
             * @internal
             */
            function checkIntersectionByModelIdsAsync(option: {
                modelIds: string[];
            }): Promise<IDetectBaseResult<IIntersectResult[]>>;
            /**
             * 是否拥有检测的权限（FOP平台权限）
             * @param eventKey 传了key代表订单流转时的权限
             * @internal
             */
            function checkFopAuthAsync(eventKey?: string): Promise<boolean>;
        }

        /**
         * 订单相关
         */
        namespace Order {
            /**
             * 订单详情
             */
            type CustomerOrderDetail = ICustomerOrderDetail;

            /**
             * 获取订单方案关联的订单信息
             * @param option
             * @returns 订单信息
             */
            function getRelatedOrderAsync(orderDesignId: string): Promise<RelatedOrderResult>;
            /**
             * 获取客户订单详情
             * @param option
             * @returns 客户订单详情
             */
            function getCustomerOrderAsync(option: GetCustomerOrderOption): Promise<CustomerOrderDetail | undefined>;
            /**
             * 获取审核订单详情
             * @param option
             * @returns 审核订单详情
             */
            function getAuditOrderAsync(orderId: string): Promise<AuditOrderResult>;
            /**
             * 打开订单详情面板
             * @param orderReadableId 订单ID
             * @remarks 打开订单详情面板
             * ```typescript
             * IDP.Custom.Order.openOrderDetailPanel('xxxx');
             * ```
             */
            function openOrderDetailPanel(orderReadableId: string): void;
        }

        /**
         * 分组相关
         */
        namespace Group {
            /**
             * 获取方案中的分组数据
             */
            function getGroupDataAsync(): Promise<GroupData | undefined>;

            /**
             * 更新分组数据
             * @param updateData
             */
            function updateGroupDataAsync(
                updateData: GroupData
            ): Promise<undefined>;
        }

        /**
         * 安装相关
         */
        namespace InstallCode {
            /**
             * 附加安装信息
             * @param installData
             */
            function attachInstallDataAsync(
                installData: InstallData
            ): Promise<undefined>;

            /**
             * 生成安装编码
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function getInstallCodeAsync(option: {
                /**
                 * 工具线，当不传时，为当前所在的默认工具线
                 */
                toolType?: EToolType;
                /**
                 * 超时时间；默认为50s
                 * 单位：秒
                 */
                timeout?: number;
            }): Promise<Record<string, string>>;
        }

        /**
         * @internal
         */
        namespace KA {
            /**
             * 欧派
             * @internal
             */
            namespace OP {
                /**
                 * 获取柜号
                 * @internal
                 * @param option.modelIds 顶层模型id集合
                 */
                function getInstallationCodeAsync(option: {
                    modelIds?: string | string[];
                }): Promise<
                    Array<{ modelId: string; installationCode: string }>
                >;
            }

            /**
             * 志邦
             * @internal
             */
            namespace ZBOM {
                /**
                 * 获取方案xml url的下载地址
                 * @internal
                 * @param option.toolType 工具线，默认当前所在工具线
                 */
                function getDesignXmlUrlAsync(option?: {
                    toolType?: EToolType;
                }): Promise<{ url: string }>;
            }
        }

        /**
         * 报价相关
         * @internal
         */
        namespace Quotation {
            /**
             * 打开报价清单导出弹窗
             * @param toolType
             * @internal
             */
            function openQuotationModal(toolType: EToolType): void;
        }

        /**
         * 左侧栏命名空间
         */
        namespace LeftPanel {
            /**
             * 渲染左侧栏
             * @param containerId 用于挂载DOM id
             * @param navigatorKey 用于控制左侧栏渲染对应的库
             * @param hiddenExpand 是否隐藏缩放icon
             * @param expanded 控制左侧栏是否展开状态
             * @param account coohom品牌库的商品信息
             * @internal
             */
            function renderCustomLeftPanelContent(
                containerId: string,
                navigatorKey: ENavigatorKey,
                options?: {
                    hiddenExpand?: boolean;
                    expanded?: boolean;
                    account?: string;
                }
            ): void;

            /**
             * 清空左侧栏渲染配置
             * @internal
             */
            function unmountCustomLeftPanelContent(): void;

            /**
             * 获取左侧栏导航栏item数组
             * @internal
             */
            function getCustomLeftPanelNavigatorTabs(): ENavigatorKey[];
        }

        /**
         * 布尔造型
         */
        namespace BoolModeling {
            /**
             * 触发一次定制布尔模型的副作用
             * @param modelId 模型ID
             * @returns { TriggerBoolEffectResult } success:成功/失败  code:业务描述
             */
            function triggerBoolEffectAsync(
                modelId: string
            ): Promise<TriggerBoolEffectResult>;
        }

        /**
         * 定制UI相关
         */
        namespace UI {
            /** 打开定制商品风格面板 */
            function openStyleExtensionAsync(
                option: LeftPanelStyleExtensionOptions
            ): Promise<void>;
        }
    }

    namespace Product {
        /**
         * 获取商品信息
         * @param productId
         */
        function getProductAsync(productId: string): Promise<IProduct>;

        /**
         * 批量获取商品信息
         * 当前批量获取时，不支持获取`customFields`字段
         * @param productIds
         * @return 返回值为一个对象，对象中key为productId，而对应值为商品信息
         *
         * @vm-type AsyncFunctionType
         */
        function getProductsAsync(
            productIds: string[]
        ): Promise<Record<string, IProduct>>;

        /** 查询定制产品包商品类目 */
        function findCustomFoldersAsync(
            options: IFindCustomFoldersOptions
        ): Promise<IFolder[]>;

        /** 查询定制产品包商品 */
        function findCustomProductsAsync(
            options: IFindCustomProductsOptions
        ): Promise<{ list: IProduct[]; totalCount: number }>;

        /** 获取商品json数据 */
        function getProductDefaultBuildAsync(
            options: GetProductDefaultBuildOption
        ): Promise<string>;
    }

    namespace DB {
        namespace Types {
            /**
             * 定制组合对象
             */
            interface CustomGroup {
                /** ID */
                elementId: IDPCommon.DB.Types.ElementId;
                /** 子对象 */
                subElements: IDPCommon.DB.Types.ElementId[];
            }
        }

        namespace Methods {
            /**
             * 获取定制组合对象
             * @param eid
             */
            function getCustomGroup(
                eid: IDPCommon.DB.Types.ElementId
            ): IDP.DB.Types.CustomGroup | undefined;

            /**
             * 在非定制环境下，创建一个定制厨卫模型到方案当中
             */
            function createCabinetAsync(
                option: CreateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId>;

            /**
             * 在非定制环境下，更新定制厨卫模型相关参数
             * @param option
             */
            function updateCabinetAsync(
                option: UpdateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId | undefined>;

            /**
             * 通过elementId，来删除一下定制厨卫模型（当找不到模型时，会返回false）
             *
             * @param elementId
             */
            function deleteCabinetAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<boolean>;

            /**
             * 通过elementId来获取一个定制厨卫模型数据
             * @param elementId
             */
            function getCabinetAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<CustomModelSimpleModel | undefined>;

            /**
             * 定制厨卫模型数据列表
             */
            function findCabinetListAsync(): Promise<CustomModelSimpleModel[]>;

            /**
             * 定制厨卫引用模型数据列表
             *
             * @vm-type AsyncFunctionType
             */
            function findCabinetRefListAsync(): Promise<RefModelLite[]>;

            /**
             * 在非定制环境下，创建一个定制厨卫副本模型到方案当中
             */
            function createCabinetCopyAsync(
                option: CreateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId>;

            /**
             * 在非定制环境下，更新定制厨卫副本模型相关参数
             * @param option
             */
            function updateCabinetCopyAsync(
                option: UpdateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId | undefined>;

            /**
             * 通过elementId，来删除一下定制厨卫副本模型（当找不到模型时，会返回false）
             *
             * @param elementId
             */
            function deleteCabinetCopyAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<boolean>;

            /**
             * 通过elementId来获取一个定制厨卫副本模型数据
             * @param elementId
             */
            function getCabinetCopyAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<CustomModelSimpleModel | void>;

            /**
             * 定制厨卫副本模型数据列表
             */
            function findCabinetCopyListAsync(): Promise<
                CustomModelSimpleModel[]
            >;

            /**
             * 定制厨卫副本引用模型数据列表
             *
             * @vm-type AsyncFunctionType
             */
            function findCabinetCopyRefListAsync(): Promise<RefModelLite[]>;

            /**
             * 在非定制环境下，创建一个定制家居模型到方案当中
             */
            function createWardrobeAsync(
                option: CreateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId>;

            /**
             * 在非定制环境下，更新定制家居模型相关参数
             * @param option
             */
            function updateWardrobeAsync(
                option: UpdateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId | void>;

            /**
             * 通过elementId，来删除一下定制家居模型（当找不到模型时，会返回false）
             *
             * @param elementId
             */
            function deleteWardrobeAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<boolean>;

            /**
             * 通过elementId来获取一个定制家居模型数据
             * @param elementId
             */
            function getWardrobeAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<CustomModelSimpleModel | void>;

            /**
             * 批量获取定制家居模型参数信息
             */
            function findWardrobeListAsync(): Promise<CustomModelSimpleModel[]>;

            /**
             * 在非定制环境下，创建一个定制家居副本模型到方案当中
             */
            function createWardrobeCopyAsync(
                option: CreateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId>;

            /**
             * 在非定制环境下，更新定制家居副本模型相关参数
             * @param option
             */
            function updateWardrobeCopyAsync(
                option: UpdateCustomModelSimpleOption
            ): Promise<IDPCommon.DB.Types.ElementId | void>;

            /**
             * 通过elementId，来删除一下定制家居副本模型（当找不到模型时，会返回false）
             *
             * @param elementId
             */
            function deleteWardrobeCopyAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<boolean>;

            /**
             * 通过elementId来获取一个定制家居副本模型数据
             * @param elementId
             */
            function getWardrobeCopyAsync(
                elementId: IDPCommon.DB.Types.ElementId
            ): Promise<CustomModelSimpleModel | void>;

            /**
             * 批量获取定制家居副本模型参数信息
             */
            function findWardrobeCopyListAsync(): Promise<
                CustomModelSimpleModel[]
            >;

            /**
             * 获取定制全屋家具的引用模型数据
             *
             * @vm-type AsyncFunctionType
             */
            function findWardrobeRefListAsync(): Promise<RefModelLite[]>;

            /**
             * 获取定制全屋家具的引用模型数据
             *
             * @vm-type AsyncFunctionType
             */
            function findWardrobeCopyRefListAsync(): Promise<RefModelLite[]>;
        }
    }

    namespace Integration {
        namespace FOP {
            type GetOrderFieldsResult = IGetOrderFieldsResult;

            /**
             * 客户来源
             */
            const CustomerSource: typeof ECustomerSource;
            /**
             * 客户信息查询字段类型
             */
            const CustomerQueryType: typeof ECustomerQueryType;
            /**
             * 订单字段类型
             * @internal
             */
            const OrderFieldType: typeof EOrderFieldType;

            /**
             * 添加附件
             */
            type AddOrderStateAttachmentOption = IAddOrderStateAttachmentOption;
            /**
             * 删除附件
             */
            type DeleteOrderStateAttachmentOption = IDeleteOrderStateAttachmentOption;
            /**
             * 更新备注
             */
            type UpdateOrderStateRemarkOption = IUpdateOrderStateRemarkOption;
            /**
             * 添加备注
             */
            type AddOrderStateRemarkOption = IAddOrderStateRemarkOption;
            /**
             * 查询订单附件参数
             */
            type GetOrderAttachmentsOption = IGetOrderAttachmentsOption;
            /**
             * 查询订单附件结果
             */
            type GetOrderAttachmentsResult = IGetOrderAttachmentsResult;
            /**
             * 查询订单备注参数
             */
            type GetOrderRemarksOption = IGetOrderRemarksOption;
            /**
             * 查询订单备注结果
             */
            type GetOrderRemarksResult = IGetOrderRemarksResult;
            /**
             * 订单状态定义
             */
            type OrderStateData = IOrderStateData;
            /**
             * 订单事件定义
             */
            type OrderEventData = IOrderEventData;
            /**
             * 订单操作定义
             */
            type OrderOperationData = IOrderOperationData;
            /**
             * 执行订单操作参数
             */
            type ExecuteOrderOperationOption = IExecuteOrderOperationOption;
            /**
             * 检查是否具有某个订单的操作权限参数
             */
            type CheckOperatePermissionOption = ICheckOperatePermissionOption;

            /**
             * 创建订单
             * @param {CreateOrderOption} option
             */
            function createOrderAsync(
                option: CreateOrderOption
            ): Promise<CreateOrderResult>;

            /**
             * 批量创建订单（目前是串行）
             * @param {CreateOrderOption} option
             * @internal
             */
            function createOrdersAsync(
                option: CreateOrderOption[]
            ): Promise<CreateOrdersResult>;

            /**
             * 获取订单数据
             * @param orderId
             */
            function getOrderAsync(orderId: string): Promise<OrderData>;
            /**
             * 新增订单附件
             * @param {AddOrderStateAttachmentOption} option
             * @internal
             *
             * @vm-type AsyncFunctionType
             */
            function addOrderAttachmentAsync(
                option: AddOrderStateAttachmentOption
            ): Promise<void>;
            /**
             * 添加订单附件
             * @param {AddOrderStateAttachmentOption} option
             *
             * @vm-type AsyncFunctionType
             */
            function addAttachmentAsync(
                option: AddOrderStateAttachmentOption
            ): Promise<void>;

            /**
             * 删除附件
             * @param {DeleteOrderStateAttachmentOption} option
             * @internal
             *
             * @vm-type AsyncFunctionType
             */
            function deleteOrderAttachmentAsync(
                option: DeleteOrderStateAttachmentOption
            ): Promise<void>;

            /**
             * 删除附件
             * @param {DeleteAttachmentOption} option
             *
             * @vm-type AsyncFunctionType
             */
            function deleteAttachmentAsync(
                option: DeleteAttachmentOption
            ): Promise<void>;

            /**
             * 修改订单备注
             * @param {UpdateOrderStateRemarkOption} option
             * @internal
             *
             * @vm-type AsyncFunctionType
             */
            function updateOrderRemarkAsync(
                option: UpdateOrderStateRemarkOption
            ): Promise<void>;

            /**
             * 修改订单备注
             * @param {UpdateOrderStateRemarkOption} option
             *
             * @vm-type AsyncFunctionType
             */
            function updateRemarkAsync(
                option: UpdateOrderStateRemarkOption
            ): Promise<void>;

            /**
             * 新增订单备注
             * @param {AddOrderStateRemarkOption} option
             * @internal
             *
             * @vm-type AsyncFunctionType
             */
            function addOrderRemarkAsync(
                option: AddOrderStateRemarkOption
            ): Promise<void>;

            /**
             * 新增订单备注
             * @param {AddOrderStateRemarkOption} option
             *
             * @vm-type AsyncFunctionType
             */
            function addRemarkAsync(
                option: AddOrderStateRemarkOption
            ): Promise<void>;

            /**
             * 删除订单备注
             * @param {DeleteOrderRemarkOption} option
             *
             * @internal
             */
            function deleteOrderRemarkAsync(
                option: DeleteOrderRemarkOption
            ): Promise<void>;
            /**
             * 删除订单备注
             * @param {DeleteOrderRemarkOption} option
             */
            function deleteRemarkAsync(
                option: DeleteOrderRemarkOption
            ): Promise<void>;

            /**
             * 获取方案关联的指定状态的全部订单列表
             * @param {DesignOrderListOption} option
             * @vm-type AsyncFunctionType
             */
            function getDesignOrderListAsync(
                option: DesignOrderListOption
            ): Promise<DesignOrderListResult[]>;

            /**
             * 判断模型是否关联未作废的订单
             * @param {ModelIsAssociatedOrderResult} option
             */
            function getModelAssociatedOrderAsync(
                option: ModelIsAssociatedOrderOption
            ): Promise<ModelIsAssociatedOrderResult[]>;

            /**
             * 解除提审模型锁定，解除后模型可编辑
             * @param {UnlockAuditedModelOption} option
             * @internal
             */
            function unlockAuditedModel(option: UnlockAuditedModelOption): void;
            /**
             * 执行订单操作
             * @param option
             */
            function executeOrderOperationAsync(
                option: ExecuteOrderOperationOption
            ): Promise<boolean>;
            /**
             * 校验是否有创建订单的权限
             * @internal
             */
            function checkCreateOrderAuthAsync(): Promise<boolean>;
            /**
             * 校验是否具有某个订单的操作权限
             */
            function checkOperatePermissionAsync(
                option: CheckOperatePermissionOption
            ): Promise<boolean>;
            /**
             * 获取当前用户可见的订单字段
             * @internal
             * @deprecated 可使用`getOrderFieldsAsync`代替
             */
            function getOrderVisibleFieldsAsync(): Promise<string[]>;
            /**
             * 创建客户信息
             * @param option
             */
            function createCustomerAsync(
                option: CreateCustomerOption
            ): Promise<CreateCustomerResult>;
            /**
             * 查询客户信息列表
             * @param option
             * @internal
             */
            function getCustomerListAsync(
                option: GetCustomerListOption
            ): Promise<GetCustomerListResult>;
            /**
             * 查询客户信息列表
             * @param option
             */
            function findCustomerListAsync(
                option: GetCustomerListOption
            ): Promise<GetCustomerListResult>;
            /**
             * 获取当前用户所在的门店信息，如果没有门店则返回null
             */
            function getStoreAsync(): Promise<GetStoreResult | null>;
            /**
             * 获取订单字段配置详情
             * @param option.orderId 订单ID，返回创建订单时账号下的字段配置，不传返回最新的字段配置
             * @return {Promise<GetOrderFieldsResult>}
             *
             * @vm-type AsyncFunctionType
             */
            function getOrderFieldsAsync(option?: {
                orderId?: string;
            }): Promise<GetOrderFieldsResult>;
            /**
             * 根据筛选条件查询当前方案的订单
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function findOrdersAsync(
                option?: FindOrdersOption
            ): Promise<FindOrdersResult>;

            /**
             * 查询附件
             * @param {GetOrderAttachmentsOption} option
             * @throws Error 报错即表示查询失败
             * @internal
             *
             * @vm-type AsyncFunctionType
             */
            function getOrderAttachmentsAsync(
                option: GetOrderAttachmentsOption
            ): Promise<GetOrderAttachmentsResult>;
            /**
             * 查询附件
             * @param {GetOrderAttachmentsOption} option
             * @throws Error 报错即表示查询失败
             *
             * @vm-type AsyncFunctionType
             */
            function getAttachmentsAsync(
                option: GetOrderAttachmentsOption
            ): Promise<GetOrderAttachmentsResult>;

            /**
             * 查询备注
             * @param {GetOrderRemarksOption} option
             * @throws Error 报错即表示查询失败
             * @internal
             *
             * @vm-type AsyncFunctionType
             */
            function getOrderRemarksAsync(
                option: GetOrderRemarksOption
            ): Promise<GetOrderRemarksResult[]>;

            /**
             * 查询备注
             * @param {GetOrderRemarksOption} option
             * @throws Error 报错即表示查询失败
             *
             * @vm-type AsyncFunctionType
             */
            function getRemarksAsync(
                option: GetOrderRemarksOption
            ): Promise<GetOrderRemarksResult[]>;
            /**
             * 创建售后单
             * @param {CreateAfterSaleOrderFromDesignOption} option
             */
            function createAfterSaleOrderFromDesignAsync(
                option: CreateAfterSaleOrderFromDesignOption
            ): Promise<CreateOrderResult>;
            /**
             * 查询订单提审模型信息
             * @param {orderId} option
             * @throws Error 报错即表示查询失败
             * @deprecated 可使用`findOrderModelsAsync`平替
             */
            function findAuditedModelAsync(option: {
                orderId: string;
            }): Promise<FindAuditedModelResult>;
            /**
             * 查询当前用户可见的所有门店信息
             */
            function getStoresAsync(): Promise<StoreInfo[]>;
            /**
             * 获取订单流程状态列表
             */
            function getStatesAsync(): Promise<{
                /**
                 * 系统订单流程状态
                 */
                sysStates: OrderStateData[];
                /**
                 * 商家自定义订单流程状态
                 */
                customStates: OrderStateData[];
            }>;

            /**
             * 获取订单流程操作列表
             */
            function getOperationsAsync(): Promise<{
                /**
                 * 系统内置订单操作
                 */
                sysOperations: OrderOperationData[];
                /**
                 * 商家自定义订单操作
                 */
                customOperations: OrderOperationData[];
            }>;
            /**
             * 更新订单绑定的模型
             * 具体的定义说明可以参考：https://open.kujiale.com/pub/saas/open-platform/doc-detail?app_id=15&node_id=4642&node_type=1&language=zh&tree_tab=a&doc_tab=doc
             * @param option
             */
            function updateOrderModelsAsync(
                option: UpdateOrderModel
            ): Promise<void>;
            /**
             * 查询订单绑定的模型
             * @param {orderId} option
             */
            function findOrderModelsAsync(option: {
                orderId: string;
            }): Promise<OrderModel>;
            /**
             * 获取用户可用的FOP配置的检测项
             * @internal
             * @deprecated
             * @throws Error 获取失败
             */
            function getDetectionsAsync(): Promise<DetectionRuleType[]>;
            /**
             * 校验模型是否拆单
             * @param modelIds
             * @internal
             */
            function checkModelSplitOrderAsync(
                modelIds: string[]
            ): Promise<CheckModelSplitOrderResult[]>;
            /**
             * 获取当前方案中提审后被解锁的模型列表
             * @internal
             */
            function getDesignUnlockModelListAsync(option?: {
                modelIds?: string[];
            }): Promise<DesignUnlockModelResult[]>;
            /**
             * 获取报表文件地址，格式：PDF
             * @throws Error 报错即表示获取失败
             */
            function getReportFileAsync(
                option: GetOrderReportFileOption | GetMergeTaskReportFileOption
            ): Promise<{ url: string }>;
            /**
             * 检查一批模型在可提审库中
             * - 子模型是否可提审：子模型归属顶层模型是否可提审
             * @param models 模型ID，不限制模型层级
             * @internal
             */
            function checkModelsInAllowedLibraryAsync(
                models: string[]
            ): Promise<Record<string, boolean>>;
            /**
             * 检测一批模型是否存在错误参数
             * - 子模型是否有错误参数：子模型归属顶层模型是否有错误参数
             * @param models 模型ID，不限制模型层级
             * @internal
             */
            function checkModelsHasErrParamAsync(
                models: string[]
            ): Promise<Record<string, boolean>>;
            /**
             * 获取FOP配置的可提审库
             * @internal
             */
            function getAllowedProductLibrariesAsync(): Promise<
                | {
                    /** 库渠道 */
                    channel: string;
                    /** 工具线 */
                    toolType: EToolType;
                    /** 库名称 */
                    libraryName: string;
                }[]
                | null
            >;

            /**
             * 查询整个订单的图纸
             * 返回所有板件图纸的zip文件链接
             * @param orderId 订单ID
             */
            function getAllPlankDrawingAsync(
                orderId: string
            ): Promise<{ url: string }>;
            /**
             * 查询某个板件的图纸
             * 返回单个板件的pdf图纸文件链接
             * @param option
             */
            function getPlankDrawingAsync(option: {
                orderId: string;
                code: string;
            }): Promise<{ url: string }>;
            /**
             * 批量查询板件的图纸
             * 返回每块板件的pdf图纸文件链接
             * @param option
             */
            function getPlankDrawingsAsync(option: {
                orderId: string;
                codes: string[];
            }): Promise<Array<{ code: string; url: string }>>;
            /**
             * 更新订单
             * @param UpdateOrderOption 更新订单参数
             * 入参参考订单详情返回字段，并根据字段是否可编辑性进行传参
             * 不可编辑的字段无法更新
             */
            function updateOrderAsync(option: UpdateOrderOption): Promise<void>;

            /**
             * 更新客户信息
             * @param UpdateCustomerOption 更新客户信息参数
             */
            function updateCustomerAsync(
                option: UpdateCustomerOption
            ): Promise<void>;

            /**
             * 根据订单id查询售后单
             * @param option
             */
            function findAfterSaleOrderListAsync(option: {
                parentOrderId: string;
            }): Promise<DesignOrderListResult[]>;

            /**
             * 从bom创建售后单
             * @param CreateAfterSaleOrderFromBomOption 从bom创建售后单参数
             */
            function createAfterSaleOrderFromBomAsync(
                option: CreateAfterSaleOrderFromBomOption
            ): Promise<CreateOrderResult>;

            /**
             * 根据订单id删除订单
             * @param option
             */
            function deleteOrderAsync(option: {
                orderId: string | string[];
            }): Promise<void>;

            /**
             * 订单模式相关
             */
            namespace OrderMode {
                /**
                 * 获取订单模式是否可编辑状态
                 * 该API仅限订单模式下使用
                 * @default false
                 * @returns {Boolean} 是否可编辑
                 */
                function getEditable(): boolean;

                /**
                 * 设置订单模式的编辑状态
                 * 该API仅限订单模式下使用
                 * @param editable
                 * @default false
                 */
                function setEditable(editable: boolean): void;

                /**
                 * 获取订单模式当前订单ID
                 * 如果不在订单模式则获取不到订单ID
                 * @returns {string | undefined} 订单ID | 未获取到订单ID
                 */
                function getOrderId(): string | undefined;

                /**
                 * 切换订单模式下的当前订单
                 * 该API仅限订单模式下使用
                 * @internal
                 * @param {Object} option
                 */
                function switchOrderAsync(
                    option: SwitchOrderOption
                ): Promise<void>;

                /**
                 * 暂停方案保存时自动更新订单模型范围
                 * 该API仅限订单模式下使用
                 */
                function pauseAutoUpdateOrderModel(): void;

                /**
                 * 恢复方案保存时自动更新订单模型范围
                 * 该API仅限订单模式下使用
                 */
                function resumeAutoUpdateOrderModel(): void;
                /**
                 * 触发自动更新模型范围（仅在订单模式下有效）
                 * 不触发方案保存
                 * 不受pauseAutoUpdateOrderModel api影响
                 * @internal
                 */
                function emitAutoUpdateOrderModelAsync(): Promise<boolean>;

                /**
                 * 获取自动更新模型范围是否启用
                 * 仅二方使用
                 * @internal
                 */
                function getAutoUpdateOrderModelEnabled(): boolean;
                /**
                 * 校验订单在订单模式下的权限
                 * @param {CheckOrderModePermissionOption} option
                 * @internal
                 */
                function checkOrderPermissionAsync(
                    option: CheckOrderModePermissionOption
                ): Promise<boolean>;
            }

            /**
             * 配置相关
             */
            namespace Config {
                /**
                 * 自定义配置字段枚举
                 */
                const FieldEnum: typeof EFieldEnum;
                /**
                 * 获取自定义配置返回结果
                 */
                type FieldEnumResult = IFieldEnumResult;

                interface GetDetectionConfigRes {
                    /** 检测范围 */
                    rules: DetectionRuleType[];
                    /** 触发检测的订单操作项 */
                    operations: string[];
                }
                /**
                 * 获取自定义配置枚举
                 * @param option
                 */
                function getEnumAsync(option: {
                    field: EFieldEnum;
                }): Promise<FieldEnumResult[]>;
                /**
                 * 获取用户的检测配置：检测范围、触发检测的订单操作项
                 * @internal
                 * @throws Error 获取失败
                 */
                function getDetectionConfigAsync(): Promise<
                    GetDetectionConfigRes
                >;
            }

            /**
             * 安装分享相关
             */
            namespace InstallationSharing {
                /**
                 * 生成分享数据
                 */
                function createShareDataAsync(option: {
                    orderId: string;
                }): Promise<{ expirationTime: number }>;
            }
        }

        namespace Upload {
            /**
             * 上传文件
             * @param {IUploadFileOption} option
             * @internal
             */
            function uploadFileAsync(
                option: UploadFileOption
            ): Promise<UploadFileResult[]>;

            /**
             * 上传文件业务存储空间
             * @internal
             */
            const Bucket: typeof EUploadFileBucket;
        }
        /**
         * 华为云排版算法
         */
        namespace EngravingMachineCutting {
            /**
             * 排版任务详情
             */
            type EngravingMachineCuttingResponse = IEngravingMachineCuttingResponse;
            /**
             * 任务检索结果
             * @internal
             */
            type EngravingMachineCuttingSearchResult = IEngravingMachineCuttingSearchResult;

            /**
             * 创建排版任务入参
             */
            type EngravingMachineCuttingCreateTask = IEngravingMachineCuttingTaskCreate;

            /**
             * 创建排版任务响应参数
             */
            type EngravingMachineCuttingTaskCreateResponse = IEngravingMachineCuttingTaskCreateResponse;
            /**
             * 创建排版任务入参数校验不能过时，响应
             */
            type EngravingMachineCuttingTaskCreateErrResponse = IEngravingMachineCuttingTaskCreateErrResponse;
            /**
             * 删除/查询任务
             */
            type FindOrDeleteTaskOption = Record<'task_id', string>;
            /**
             * 获取所有排版任务
             * @internal
             */
            function getTaskListAsync(): Promise<
                EngravingMachineCuttingSearchResult
            >;
            /**
             * 创建新的排版任务
             * @param {EngravingMachineCuttingCreateTask} option
             */
            function createTaskAsync(
                option: EngravingMachineCuttingCreateTask
            ): Promise<
                | EngravingMachineCuttingTaskCreateResponse
                | EngravingMachineCuttingTaskCreateErrResponse
            >;
            /**
             * 查询排版任务详情
             * @param option
             */
            function findTaskAsync(
                option: FindOrDeleteTaskOption
            ): Promise<EngravingMachineCuttingResponse>;
            /**
             * 删除排版任务
             * @param option
             */
            function deleteTaskAsync(
                option: FindOrDeleteTaskOption
            ): Promise<void>;
        }

        /**
         * BOM数据
         */
        namespace Bom {
            /**
             * 新增板件时入参
             */
            type BomPlankEditable = IBomPlankEditable;

            /**
             * 更新板件时的入参
             */
            type BoomPlank = IBomPlankUpdate;

            /**
             * 板件附带订单的完整信息
             */
            type BomPlankFull = IBomPlankFull;

            /**
             * 删除板件后的入参
             */
            type BoomPlankWithId = IBomPlankWithId;

            /**
             * 板件类型
             */
            const BomPlankType: typeof EBomPlankType;

            /**
             * 线段类型
             */
            const BomCurveType: typeof EBomCurveType;

            /**
             * 孔/槽类型
             */
            const BomPlankHoleType: typeof EBomPlankHoleType;

            /**
             * 查找板件入参
             */
            type FindPlankListOption = IFindPlankListOption;

            /**
             * 创建物料参数
             *
             * @vm-type UnknownType
             */
            interface CreateMaterialsOption {
                /**
                 * 线条
                 */
                moldings?: BomMoldingEditable[];
                /**
                 * 板件
                 */
                planks?: BomPlankEditable[];
                /**
                 * 成品五金
                 */
                finishedProducts?: BomFinishedProductEditable[];
                /**
                 * 分组
                 */
                groups?: BomGroup[];
                /**
                 * 装配件
                 */
                structures?: BomStructureEditable[];
                /**
                 * 产品id
                 */
                productId: string;
            }
            /** 批量查询物料结果 */
            interface FindMaterialsResult {
                /**
                 * 线条
                 */
                moldings?: BomMolding<any>[];
                /**
                 * 板件
                 */
                planks?: BomPlankFull[];
                /**
                 * 成品五金
                 */
                finishedProducts?: BomFinishedProduct[];
                /**
                 * 分组
                 */
                groups?: BomGroupFull[];
                /**
                 * 装配件
                 */
                structures?: BomStructure[];
            }
            /** 批量查询物料 */
            interface FindMaterialsOption {
                /** 订单id */
                orderId: string;
                /** 产品id */
                productIds?: string[];
            }

            interface CreateMaterialsResult {
                /**
                 * 线条
                 */
                moldingIds?: string[];
                /**
                 * 板件
                 */
                plankIds?: string[];
                /**
                 * 成品五金
                 */
                finishedProductIds?: string[];
                /**
                 * 分组
                 */
                rootIds?: string[];
                /**
                 * 装配件
                 */
                structureIds?: string[];
            }

            /**
             * 删除物料参数
             */
            interface DeleteMaterialsOption {
                /**
                 * 订单ID
                 */
                orderId: string;
                /**
                 * 产品ID
                 */
                productIds: string[];
            }

            interface DeleteMaterialsResult {
                productMaterials: Array<{
                    /**
                     * 物料ID
                     */
                    bomIds: string[];
                    /**
                     * 产品ID
                     */
                    productId: string;
                    /**
                     * 分组ID
                     */
                    groupIds: string[];
                }>;
            }

            interface DeleteMaterialsByOrderIdResult {
                orderMaterial: {
                    /**
                     * 物料ID
                     */
                    bomIds: string[];
                    /**
                     * 分组ID
                     */
                    groupIds: string[];
                };
            }

            /**
             * 批量创建物料
             * @param option
             */
            function createMaterialsAsync(
                option: CreateMaterialsOption
            ): Promise<CreateMaterialsResult>;

            /**
             * 通过订单ID及产品ID，批量删除物料
             * @param option
             */
            function deleteMaterialsAsync(
                option: DeleteMaterialsOption
            ): Promise<DeleteMaterialsResult>;

            /**
             * 通过订单ID删除物料
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function deleteMaterialsByOrderIdAsync(
                option: Omit<DeleteMaterialsOption, 'productIds'>
            ): Promise<DeleteMaterialsByOrderIdResult>;

            /**
             * 通过物料ID批量解包物料
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function unpackMaterialsByBomIdAsync(option: {
                bomIds: string[];
            }): Promise<{
                /**
                 * 物料ID
                 */
                bomIds: string[];
                /**
                 * 分组ID
                 */
                groupIds: string[];
            }>;

            /**
             * 通过包裹ID,批量解包物料
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function unpackMaterialsByPackageIdAsync(option: {
                packageIds: string[];
            }): Promise<{
                /**
                 * 物料ID
                 */
                bomIds: string[];
                /**
                 * 分组ID
                 */
                groupIds: string[];
            }>;

            /**
             * 通过订单ID批量解包物料
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function unpackMaterialsByOrderIdAsync(option: {
                orderIds: string[];
            }): Promise<{
                /**
                 * 物料ID
                 */
                bomIds: string[];
                /**
                 * 分组ID
                 */
                groupIds: string[];
            }>;

            /**
             * 查询板件列表
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function findPlankListAsync(
                option: FindPlankListOption
            ): Promise<{ result: BomPlankFull[] }>;

            /**
             * 创建板件接口
             * @param plank
             *
             * @vm-type AsyncFunctionType
             */
            function createPlankAsync(
                plank: BomPlankEditable
            ): Promise<BoomPlankWithId>;

            /**
             * 更新板件接口
             * @throws Error 如果报错就表示更新失败
             * @param plank
             *
             * @vm-type AsyncFunctionType
             */
            function updatePlankAsync(plank: BoomPlank): Promise<void>;

            /**
             * 删除板件
             * @throws {Error} 报错即表示删除失败
             * @param plankId
             */
            function deletePlankAsync(plankId: string): Promise<void>;

            /**
             * 批量创建板件接口
             * @throws {Error} 报错即表示创建失败
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function createPlanksAsync(option: {
                planks: BomPlankEditable[];
            }): Promise<{ plankIds: string[] }>;

            /**
             * 批量更新创建板件接口
             * @throws {Error} 报错即表示创建失败
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function updatePlanksAsync(option: {
                planks: BoomPlank[];
            }): Promise<void>;

            /**
             * 批量删除板件接口
             * @throws {Error} 报错即表示删除失败
             * @param option
             */
            function deletePlanksAsync(option: {
                plankIds: string[];
            }): Promise<{ plankIds: string[] }>;

            /**
             * 通过产品ID 批量删除产品和这个产品下的所有子部件（包含孔槽）
             * @throws {Error} 报错即表示删除失败
             * @param option
             */
            function deletePlanksByProductIdsAsync(option: {
                orderId: string;
                productIds: string[];
            }): Promise<{ plankIds: string[] }>;

            /**
             * 通过订单ID 批量删除整个订单和这个订单下的所有子部件（包含孔槽）
             * @throws {Error} 报错即表示删除失败
             * @param option
             */
            function deletePlanksByOrderIdsAsync(option: {
                orderIds: string[];
            }): Promise<{ plankIds: string[] }>;

            /**
             * 板中的局部操作逻辑
             */
            namespace Plank {
                /**
                 * 孔附带ID
                 */
                type BoomPlankHoleWithId = IBomPlankHoleWithId;
                /**
                 * 孔可被编辑的信息
                 */
                type BomPlankHoleEditable = IBomPlankHoleEditable;
                /**
                 * 孔的完整信息
                 */
                type BomPlankHole = IBomPlankHole;

                /**
                 * 板面上，创建孔
                 * @param hole 孔信息
                 *
                 * @vm-type AsyncFunctionType
                 */
                function createHolesAsync(option: {
                    holes: Array<BomPlankHoleEditable & BoomPlankWithId>;
                }): Promise<void>;

                /**
                 * 更新孔
                 * @param hole 孔信息
                 *
                 * @vm-type AsyncFunctionType
                 */
                function updateHoleAsync(
                    hole: BomPlankHole & BoomPlankWithId
                ): Promise<void>;

                /**
                 * 删除孔
                 * @param holeId 孔Id
                 *
                 * @vm-type AsyncFunctionType
                 */
                function deleteHoleAsync(holeId: string): Promise<void>;

                /**
                 * 孔附带ID
                 */
                type BoomPlankGrooveWithId = IBomPlankGrooveWithId;
                /**
                 * 孔可被编辑的信息
                 */
                type BomPlankGrooveEditable = IBomPlankGrooveEditable;
                /**
                 * 孔的完整信息
                 */
                type BomPlankGroove = IBomPlankGroove;

                /**
                 * 板面上，创建槽
                 * @param option 槽信息
                 *
                 * @vm-type AsyncFunctionType
                 */
                function createGroovesAsync(option: {
                    grooves: Array<BomPlankGrooveEditable & BoomPlankWithId>;
                }): Promise<void>;

                /**
                 * 更新槽
                 * @param groove 槽信息
                 *
                 * @vm-type AsyncFunctionType
                 */
                function updateGrooveAsync(
                    groove: BomPlankGroove & BoomPlankWithId
                ): Promise<void>;

                /**
                 * 删除槽
                 * @param grooveId 槽Id
                 *
                 * @vm-type AsyncFunctionType
                 */
                function deleteGrooveAsync(grooveId: string): Promise<void>;
            }

            /**
             * 线条相关API
             */
            type BomMoldingEditable = IBomMoldingEditable;

            /**
             * 完整的线条数据
             */
            type BomMolding<T = any> = IBomMolding<T>;

            /**
             * 创建线条
             * @param option 线条信息
             *
             * @vm-type AsyncFunctionType
             */
            function createMoldingsAsync(option: {
                moldings: BomMoldingEditable[];
            }): Promise<{ moldingIds: string[] }>;

            /**
             * 更新线条
             * @param option 线条信息
             *
             * @vm-type AsyncFunctionType
             */
            function updateMoldingsAsync(option: {
                moldings: BomMoldingEditable[];
            }): Promise<void>;

            /**
             * 删除线条
             * @param option
             *
             * @vm-type AsyncFunctionType
             */
            function deleteMoldingsAsync(
                option: DeleteMoldingsOption
            ): Promise<void>;

            /**
             * 查询线条
             * @param option 查询条件
             *
             * @vm-type AsyncFunctionType
             */
            function findMoldingsAsync(
                option: FindMoldingsOption
            ): Promise<{ moldings: BomMolding<any>[] }>;

            /**
             * 通过模型顶层ID创建成品
             */
            type BomFinishedProductEditableByProductId = IBomFinishedProductEditableByProductId;
            /**
             * 通过方案中模型ID创建成品ID
             */
            type BomFinishedProductEditableByModelId = IBomFinishedProductEditableByModelId;
            /**
             * 成品五金相关API
             */
            type BomFinishedProductEditable = IBomFinishedProductEditable;
            /**
             * 装配件
             */
            type BomStructureEditable = IBomStructureEditable;
            /**
             * 装配件完整信息
             */
            type BomStructure = IBomStructure;

            /**
             * 完整的成品信息
             */
            type BomFinishedProduct = IBomFinishedProduct;

            /**
             * 更新成品配置信息
             */
            type BomFinishedProductUpdate = IBomFinishedProductUpdate;

            /**
             * 批量创建成品五金
             * @param option 五金信息
             *
             * @vm-type AsyncFunctionType
             */
            function createFinishedProductsAsync(option: {
                finishedProducts: BomFinishedProductEditable[];
            }): Promise<{ finishedProductIds: string[] }>;

            /**
             * 批量更新成品五金
             * @param option 五金信息
             *
             * @vm-type AsyncFunctionType
             */
            function updateFinishedProductsAsync(option: {
                finishedProducts: BomFinishedProductUpdate[];
            }): Promise<void>;

            /**
             * 批量删除成品五金
             * @param option 五金删除参数
             *
             * @vm-type AsyncFunctionType
             */
            function deleteFinishedProductsAsync(
                option: DeleteFinishedProductOption
            ): Promise<{ finishedProductIds: string[] }>;

            /**
             * 查询成品五金列表
             * @param option 查询信息
             *
             * @vm-type AsyncFunctionType
             */
            function findFinishedProductsAsync(
                option: FindFinishedProductOption
            ): Promise<{ finishedProducts: BomFinishedProduct[] }>;

            /**
             * 分组管理
             */
            /**
             * BomGroup信息
             */
            type BomGroup<T = any> = IBomGroup<T>;

            interface BomGroupDeleteOption {
                rootIds: string[];
            }

            interface BomGroupDeleteByProductIdsOption {
                /**
                 * 订单ID
                 */
                orderId: string;
                /**
                 * 商品ID
                 */
                productIds: string[];
            }

            interface BomGroupDeleteByProductIdsResult {
                groups: Array<{
                    /**
                     * 产品ID
                     */
                    productId: string;
                    /**
                     * 产品下的分组ID
                     */
                    groupIds: string[];
                }>;
            }

            /**
             * 创建分组
             * @param option 分组信息
             *
             * @vm-type AsyncFunctionType
             */
            function createGroupsAsync(option: {
                groups: BomGroup<string>[];
            }): Promise<{
                rootIds: string[];
            }>;

            /**
             * 更新分组
             * @param option 分组信息
             *
             * @vm-type AsyncFunctionType
             */
            function updateGroupsAsync(option: {
                groups: BomGroupEditable[];
            }): Promise<{ groupIds: string[] }>;

            /**
             * 批量删除
             * @param option 删除参数
             *
             * @vm-type AsyncFunctionType
             */
            function deleteGroupsAsync(
                option: BomGroupDeleteOption
            ): Promise<BomGroupDeleteOption>;

            /**
             * 通过产品ID，批量删除分组
             * @param option 删除参数
             */
            function deleteGroupsByProductIdAsync(
                option: BomGroupDeleteByProductIdsOption
            ): Promise<BomGroupDeleteByProductIdsResult>;

            /**
             * 根据订单号，重新构建分组层级关系
             *
             * @param option
             */
            function generateGroupRelationsAsync(option: {
                /**
                 * 是否重新构建，当regenerate为false，会自动判断当前订单层级是否需要构建；而为true时，强制重新构建
                 * @default false
                 */
                regenerate?: boolean;
                /**
                 * 订单号
                 */
                orderId: string;
            }): Promise<void>;

            /**
             * 通过关联的分组ID查询Group分组信息
             *
             * @param option 分组Id
             *
             * @vm-type AsyncFunctionType
             */
            function findGroupsByRootIdAsync(option: {
                /**
                 * 分组ID
                 */
                rootIds: string[];
            }): Promise<{ groups: BomGroupFull[] }>;
            /**
             * 通过叶子节点对应的物料ID查询Group分组信息
             * @param option 物料Id
             *
             * @vm-type AsyncFunctionType
             */
            function findGroupsByBomIdAsync(option: {
                /**
                 * 叶子节点对应的物料ID
                 */
                bomIds: string[];
            }): Promise<{ groups: BomGroupFull[] }>;
            /**
             * 通过关联的订单ID查询Group分组信息
             * @param option 订单Id
             *
             * @vm-type AsyncFunctionType
             */
            function findGroupsByOrderIdAsync(option: {
                /**
                 * 订单ID
                 */
                orderIds: string[];
            }): Promise<{ groups: BomGroupFull[] }>;

            /**
             * 通过产品ID查询分组信息
             * @param option 查询信息
             *
             * @vm-type AsyncFunctionType
             */
            function findGroupsByProductIdAsync(option: {
                /**
                 * 订单ID
                 */
                orderId: string;
                /**
                 * 产品ID
                 */
                productIds: string[];
            }): Promise<{ groups: BomGroupFull[] }>;

            /**
             * 排版加工
             */
            type BomRawPlankEditable = IBomRawPlankEditable;

            /**
             * 完成排版信息
             */
            type BomRawPlank = IBomRawPlank;

            interface FindBomRawPlankOption {
                /**
                 * 合并批次ID
                 */
                mergeIds?: string[];
                /**
                 * 大板ID
                 */
                rawPlankIds?: string[];
            }

            /**
             * 批量创建大板
             * @param option 大板信息
             *
             * @vm-type AsyncFunctionType
             */
            function createRawPlanksAsync(option: {
                rawPlanks: BomRawPlankEditable[];
            }): Promise<{ rawPlankIds: string[] }>;

            /**
             * 批量删除大板
             * @param option 大板Id集合
             *
             * @vm-type AsyncFunctionType
             */
            function deleteRawPlanksAsync(option: {
                rawPlankIds: string[];
            }): Promise<{ rawPlankIds: string[] }>;

            /**
             * 批量更新大板信息
             * @param option 大板信息
             *
             * @vm-type AsyncFunctionType
             */
            function updateRawPlanksAsync(option: {
                rawPlanks: Array<Omit<BomRawPlank, 'mergeId'>>;
            }): Promise<void>;

            /**
             * 查询大板信息
             * @param option 查询条件
             *
             * @vm-type AsyncFunctionType
             */
            function findRawPlanksAsync(
                option: FindBomRawPlankOption
            ): Promise<{ rawPlanks: BomRawPlank[] }>;

            /**
             * Bom创建排版信息配置
             */
            type BomPlankLayoutArrange = IBomPlankLayoutArrange;

            /**
             * 排版结果
             */
            type BomRawPlankLayoutResult = IBomRawPlankLayoutResult;

            /**
             * 设置大板加工工艺参数
             */
            type BomPlankLayoutProcessAttribute = IBomPlankLayoutProcessAttribute;

            /**
             * 将一块板件排版到一块大板
             * @param option 排版信息配置
             *
             * @vm-type AsyncFunctionType
             */
            function arrangePlanksAsync(option: {
                layouts: BomPlankLayoutArrange[];
            }): Promise<{ layoutIds: string[] }>;

            /**
             * 设置大板加工工艺
             * @param option
             */
            function updateRawPlankProcessingAttributesAsync(option: {
                attributes: BomPlankLayoutProcessAttribute[];
            }): Promise<undefined>;

            /**
             * 查询排版结果
             *
             * @vm-type AsyncFunctionType
             * @param option 查询条件
             */
            function findLayoutAsync(option: {
                mergeId: string;
            }): Promise<BomRawPlankLayoutResult>;

            /**
             * 批量删除排版记录
             * @param option 排版ID集合
             *
             * @vm-type AsyncFunctionType
             */
            function deleteLayoutsAsync(option: {
                layoutIds: string[];
            }): Promise<{ layoutIds: string[] }>;

            /**
             * 通过板件id 批量删除排版记录
             * @param option 板件ID集合
             *
             * @vm-type AsyncFunctionType
             */
            function deleteLayoutsByPlankIdAsync(option: {
                plankIds: string[];
            }): Promise<{ layoutIds: string[] }>;

            /**
             * 余料
             */
            type BomSurplusPlankEditable = IBomSurplusPlankEditable;

            /**
             * 完成余料信息
             */
            type BomSurplusPlank = IBomSurplusPlank;

            interface FindSurplusPlanksOption {
                /**
                 * 从哪个批次剩余的合批ID
                 */
                fromMergeIds?: string[];
                /**
                 * 用于哪个批次的合批ID
                 */
                usedMergeIds?: string[];
                /**
                 * 对应的大板IDs
                 */
                surplusPlankIds?: string[];
            }

            /**
             * 批量创建余料
             * @param option 余料信息
             *
             * @vm-type AsyncFunctionType
             */
            function createSurplusPlanksAsync(option: {
                surplusPlanks: BomSurplusPlankEditable[];
            }): Promise<{ surplusPlankIds: string[] }>;

            /**
             * 批量删除余料
             * @param option 余料Id集合
             *
             * @vm-type AsyncFunctionType
             */
            function deleteSurplusPlanksAsync(option: {
                surplusPlankIds: string[];
            }): Promise<{ surplusPlankIds: string[] }>;

            /**
             * 批量更新余料
             * @param option 余料信息
             *
             * @vm-type AsyncFunctionType
             */
            function updateSurplusPlanksAsync(option: {
                surplusPlanks: BomSurplusPlank[];
            }): Promise<void>;

            /**
             * 查询余料信息
             * @param option 查询条件
             *
             * @vm-type AsyncFunctionType
             */
            function findSurplusPlanksAsync(
                option: FindSurplusPlanksOption
            ): Promise<{ surplusPlanks: BomSurplusPlank[] }>;

            /**
             * 查询品类与属性配置信息
             */
            interface FindCategoryAttrOption {
                categoryCodes: string[];
            }

            /**
             * 扩展属性
             */
            type CategoryAttribute = ICategoryAttribute;
            /**
             * 品类
             */
            type Category = ICategory;

            interface FindCategoryAttrResult {
                catAttrs: Array<{
                    /**
                     * 扩展属性
                     */
                    attributes: CategoryAttribute[];
                    /**
                     * 品类
                     */
                    category: Category;
                }>;
            }

            /**
             * 查询品类和属性对应关系
             * @param option
             */
            function findCategoryAttrsAsync(
                option: FindCategoryAttrOption
            ): Promise<FindCategoryAttrResult>;

            /**
             * 清除物料的属性
             */
            function clearMaterialPropertiesAsync(
                option: BomPropertiesClearOption
            ): Promise<void>;

            /**
             * 清除分组节点的属性
             */
            function clearGroupPropertiesAsync(
                option: BomGroupPropertiesClearOption
            ): Promise<void>;

            interface ProductCodeInOrder {
                products?: ProductCodeInOrderInfo[];
            }

            /** 更新产品编号 */
            interface UpdateProductsOption extends ProductCodeInOrder {
                /** 订单ID */
                orderId: string;
            }

            /**
             * 自动生成订单下产品的产品编码
             * @param option 订单ID
             */
            function refreshProductCodeInOrderAsync(option: {
                orderId: string;
            }): Promise<ProductCodeInOrder>;

            /**
             * 更新订单下产品编号信息
             * @param option
             */
            function updateProductsAsync(
                option: UpdateProductsOption
            ): Promise<void>;

            /**
             * 自动生成部件编号
             * @param option.orderId 订单id
             */
            function refreshCodeAsync(option: {
                orderId: string;
            }): Promise<void>;
            /**
             * 触发BOM检测
             * @param option
             * @return
             * @internal
             * @deprecated
             */
            function checkBomsAsync(
                option: CheckBomsOptionV1
            ): Promise<CheckBomResult[]>;
            /**
             * BOM检测规则枚举项
             * @return
             * @internal
             * @deprecated
             */
            function getBomDetectionRuleEnumAsync(): Promise<
                BomDetectionRuleV1[]
            >;
            /**
             * 是否拥有bom检测的权限
             * @param eventKey 传了key代表订单流转时的权限
             * @internal
             * @deprecated
             */
            function checkBomDetectionAuthAsync(
                eventKey?: string
            ): Promise<boolean>;
            /**
             * 批量查询物料
             * @param option
             * @vm-type AsyncFunctionType
             */
            function findMaterialsAsync(
                option: FindMaterialsOption
            ): Promise<FindMaterialsResult>;
            /**
             * 预览板件图纸
             * @param option 板件Bom数据
             * @vm-type AsyncFunctionType
             */
            function previewPlankDrawingAsync(
                option: PreviewPlankDrawingOption
            ): Promise<PlankDrawingResult>;
            /**
             * 查询已写入BOM物料的产品列表
             * @param orderId 订单ID
             */
            function findProductsAsync(
                orderId: string
            ): Promise<Array<{ productId: string }>>;
            /**
             * 预览商品图纸
             * @param option 商品Bom数据
             * @vm-type AsyncFunctionType
             */
            function previewStructureDrawingAsync(
                option: PreviewStructureDrawingOption
            ): Promise<StructureDrawingResult>;
            /**
             * 更新装配件信息
             * @param structure
             * @vm-type AsyncFunctionType
             */
            function updateStructureAsync(
                structure: BomStructureUpdate
            ): Promise<void>;

            /**
             * 批量预览BOM物料图纸
             * 最多支持传入200个板件或者装配件
             * @param option
             */
            function previewMaterialDrawingsAsync(
                option: PreviewMaterialDrawingsOption
            ): Promise<PreviewMaterialDrawingsResult>;

            /**
             * BOM检测
             */
            namespace Detection {
                /**
                 * 触发BOM检测
                 * @param option
                 * @return
                 * @throws Error 报错即表示检测失败
                 * @vm-type AsyncFunctionType
                 */
                function detectAsync(
                    option: BomDetectionOption
                ): Promise<{ bomDetectionProblem: BomDetectionProblem[] }>;
                /**
                 * 查询用户应用的BOM检测规则列表
                 * @param option
                 * @vm-type AsyncFunctionType
                 */
                function findRuleListAsync(): Promise<BomDetectionRule[]>;
                /**
                 * 触发BOM检测(内部二方)
                 * @param option
                 * @return
                 * @throws Error 报错即表示检测失败
                 * @internal
                 * @vm-type AsyncFunctionType
                 */
                function detectInternalAsync(
                    option: BomDetectionOptionInternal
                ): Promise<{ bomDetectionProblem: BomDetectionProblem[] }>;
                /**
                 * 是否拥有bom检测的权限
                 * @param eventKey 传了key代表订单流转时的权限
                 * @internal
                 */
                function checkAuthAsync(eventKey?: string): Promise<boolean>;
            }
        }

        namespace Report {
            /**
             * 获取清单预览地址
             * @param option
             * @return url 清单预览地址
             * @throws Error 报错即表示获取失败
             */
            function generateReportPageAsync(
                option: GenerateReportPageOption
            ): Promise<string>;
        }
    }

    namespace User {
        type UserDetails = IUserDetails;

        /**
         * 获取商家appUid
         *  - 没有appUid返回null
         */
        function getAppUidAsync(): Promise<GetAppUidResult>;
        /**
         * 获取用户名称
         * @internal
         */
        function getUserName(): string;

        /**
         * 获取用户信息
         */
        function getUserDetailsAsync(): Promise<UserDetails>;
    }

    /**
     * 通用存储能力
     */
    namespace Storage {
        /**
         * 跨方案的通用存储（用户账号维度隔离）
         * 同一小程序数据可在不同方案进行数据存储
         */
        namespace Common {
            /**
             * 创建/更新数据，
             * 如果传入的key不存在则新建数据
             * 如果传入的key存在则更新数据（直接覆盖原有数据）
             * @throws {Error} 报异常则表示创建/更新失败
             * @param option
             */
            function putItemAsync(option: StorageItem): Promise<void>;
            /**
             * 查询单个数据
             * 没有查到则返回undefined
             * @param key
             */
            function getItemAsync(
                key: string
            ): Promise<Record<string, any> | undefined>;
            /**
             * 批量查询数据
             * @param keys
             */
            function getItemListAsync(keys: string[]): Promise<StorageItem[]>;
            /**
             * 删除数据
             * @param key
             * @throws {Error} 报异常则表示删除失败
             */
            function deleteItemAsync(key: string): Promise<void>;
            /**
             * 查询所有的key
             * 默认会查询第一页10条数据
             * @param option
             */
            function getKeysAsync(
                option?: StorageGetKeysOption
            ): Promise<StorageGetKeysResult>;
        }

        /**
         * 方案隔离存储（用户账号维度隔离）
         * 同一小程序数据仅在对应的方案进行数据存储，无法跨方案进行操作
         */
        namespace Design {
            /**
             * 创建/更新数据，
             * 如果传入的key不存在则新建数据
             * 如果传入的key存在则更新数据（直接覆盖原有数据）
             * @throws {Error} 报异常则表示创建/更新失败
             * @param option
             */
            function putItemAsync(option: StorageItem): Promise<void>;
            /**
             * 查询单个数据
             * 没有查到则返回undefined
             * @param key
             */
            function getItemAsync(
                key: string
            ): Promise<Record<string, any> | undefined>;
            /**
             * 批量查询数据
             * @param keys
             */
            function getItemListAsync(keys: string[]): Promise<StorageItem[]>;
            /**
             * 删除数据
             * @param key
             * @throws {Error} 报异常则表示删除失败
             */
            function deleteItemAsync(key: string): Promise<void>;
            /**
             * 查询所有的key
             * 默认会查询第一页10条数据
             * @param option
             */
            function getKeysAsync(
                option?: StorageGetKeysOption
            ): Promise<StorageGetKeysResult>;
        }

        /**
         * 方案存储
         * 小程序数据跟随方案进行数据存储，可随方案进行复制
         */
        namespace DesignV2 {
            /**
             * 创建/更新数据
             * 如果传入的key不存在则新建数据
             * 如果传入的key存在则更新数据（直接覆盖原有数据）
             * @throws {Error} 报异常则表示创建/更新失败
             * @param option
             */
            function putItemAsync(option: StorageItem): Promise<void>;
            /**
             * 查询单个数据
             * 没有查到则返回undefined
             * @param key
             */
            function getItemAsync(
                key: string
            ): Promise<Record<string, any> | undefined>;
            /**
             * 删除数据
             * @param key
             * @throws {Error} 报异常则表示删除失败
             */
            function deleteItemAsync(key: string): Promise<void>;
        }

        /**
         * 虚体维度的通用存储
         * 同一小程序数据可在不同方案进行数据存储
         * 同时数据可在同一个商家账号下子账号之间互通
         */
        namespace Enterprise {
            /**
             * 创建/更新数据，
             * 如果传入的key不存在则新建数据
             * 如果传入的key存在则更新数据（直接覆盖原有数据）
             * @throws {Error} 报异常则表示创建/更新失败
             * @param option
             */
            function putItemAsync(option: StorageItem): Promise<void>;
            /**
             * 查询单个数据
             * 没有查到则返回undefined
             * @param key
             */
            function getItemAsync(
                key: string
            ): Promise<Record<string, any> | undefined>;
            /**
             * 批量查询数据
             * @param keys
             */
            function getItemListAsync(keys: string[]): Promise<StorageItem[]>;
            /**
             * 删除数据
             * @param key
             * @throws {Error} 报异常则表示删除失败
             */
            function deleteItemAsync(key: string): Promise<void>;
            /**
             * 查询所有的key
             * 默认会查询第一页10条数据
             * @param option
             */
            function getKeysAsync(
                option?: StorageGetKeysOption
            ): Promise<StorageGetKeysResult>;
        }
    }

    namespace Interaction {
        /**
         * 获取当前选中的对象列表
         */
        function getSelectedElements(): IDPCommon.DB.Types.ElementId[];
        /**
         * 设置当前选中的对象列表
         * 目前支持下面几种 ElementType:：Wardrobe、WardrobeCopy、Cabinet、CabinetCopy、DoorWindow、DoorWindowCopy、CustomGroup;
         * @param option
         * @returns 返回已选中的对象列表，没有选中的对象则返回[];
         */
        function setSelectedElements(
            option: IDPCommon.DB.Types.ElementId[]
        ): IDPCommon.DB.Types.ElementId[];
        /**
         * 获取当前选中的房间列表
         * @returns 返回已选中的房间列表，未单独选中房间则返回[]
         */
        function getSelectedRooms(): { roomId: string; roomName: string }[];
    }

    namespace Platform {
        /**
         * 进入定制模式
         * @default { EToolType.Wardrobe } 默认进入【家居定制】行业线
         * @param toolType 定制行业线
         */
        function enterCustomModeAsync(toolType?: EToolType): Promise<void>;
    }
}

export { IDP };
// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export { };
