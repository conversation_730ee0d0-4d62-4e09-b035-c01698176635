#!/bin/bash

# 本地评审脚本
# 模拟 GitLab CI 中的 MR 评审流程
# 重构自 local-mr-review.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 设置项目根目录
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
cd "$PROJECT_ROOT" || {
    log_error "无法切换到项目根目录: $PROJECT_ROOT"
    exit 1
}

# 本地评审配置
DOCKER_IMAGE="${DOCKER_IMAGE:-registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest}"
CONTAINER_NAME="local-mr-review-$(date +%s)"
USE_DOCKER="${USE_DOCKER:-true}"
CLEANUP_ON_EXIT="${CLEANUP_ON_EXIT:-true}"

# 设置模拟 CI 环境变量
setup_mock_ci_environment() {
    log_step "1" "设置模拟 CI 环境"
    
    # 基本 CI 环境变量
    export CI=true
    export CI_COMMIT_REF_NAME="local-test"
    export CI_COMMIT_SHORT_SHA="localsha"
    export CI_MERGE_REQUEST_IID="123"
    export CI_PROJECT_DIR="$(pwd)"
    
    # 获取当前分支
    local current_branch=$(git branch --show-current 2>/dev/null || echo 'local-test')
    export CI_COMMIT_BRANCH="$current_branch"
    
    # 模拟其他环境变量
    export GITLAB_CI=true
    export CI_PIPELINE_ID="local-$(date +%s)"
    export CI_JOB_ID="local-job-$(date +%s)"
    
    log_info "模拟 CI 环境变量设置:"
    echo "  CI_COMMIT_BRANCH: $CI_COMMIT_BRANCH"
    echo "  CI_COMMIT_REF_NAME: $CI_COMMIT_REF_NAME"
    echo "  CI_COMMIT_SHORT_SHA: $CI_COMMIT_SHORT_SHA"
    echo "  CI_MERGE_REQUEST_IID: $CI_MERGE_REQUEST_IID"
    echo "  CI_PROJECT_DIR: $CI_PROJECT_DIR"
    
    log_success "模拟 CI 环境设置完成"
    return 0
}

# 检查 Docker 环境
check_docker_environment() {
    log_step "2" "检查 Docker 环境"
    
    if [ "$USE_DOCKER" != "true" ]; then
        log_info "跳过 Docker 环境检查 (USE_DOCKER=false)"
        return 0
    fi
    
    # 检查 Docker 是否可用
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装或不可用"
        log_info "请安装 Docker 或设置 USE_DOCKER=false 使用本地环境"
        return 1
    fi
    
    # 检查 Docker 服务状态
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行"
        log_info "请启动 Docker 服务或设置 USE_DOCKER=false 使用本地环境"
        return 1
    fi
    
    # 检查 Docker 镜像
    log_info "检查 Docker 镜像: $DOCKER_IMAGE"
    if docker image inspect "$DOCKER_IMAGE" >/dev/null 2>&1; then
        log_success "Docker 镜像已存在"
    else
        log_info "Docker 镜像不存在，尝试拉取..."
        if docker pull "$DOCKER_IMAGE"; then
            log_success "Docker 镜像拉取成功"
        else
            log_error "Docker 镜像拉取失败"
            log_info "请检查网络连接或设置 USE_DOCKER=false 使用本地环境"
            return 1
        fi
    fi
    
    log_success "Docker 环境检查通过"
    return 0
}

# 执行本地环境检查
run_local_environment_check() {
    log_step "3" "本地环境检查"

    if [ "$USE_DOCKER" = "true" ]; then
        log_info "在 Docker 容器中执行环境检查..."

        # 在 Docker 容器中运行环境检查
        local docker_cmd="docker run --rm"
        docker_cmd="$docker_cmd -v $(pwd):/workspace"
        docker_cmd="$docker_cmd -w /workspace"
        docker_cmd="$docker_cmd $DOCKER_IMAGE"
        docker_cmd="$docker_cmd bash -c 'tools/cicd/core/environment.sh'"

        if eval "$docker_cmd"; then
            log_success "Docker 环境检查通过"
            increment_counter "local_env_check_success"
            return 0
        else
            log_error "Docker 环境检查失败"
            increment_counter "local_env_check_failed"
            return 1
        fi
    else
        # 本地环境检查
        local env_script="$SCRIPT_DIR/../core/environment.sh"

        if [ ! -f "$env_script" ]; then
            log_error "环境检查脚本不存在: $env_script"
            return 1
        fi

        log_info "执行本地环境检查..."
        if "$env_script"; then
            log_success "本地环境检查通过"
            increment_counter "local_env_check_success"
            return 0
        else
            log_error "本地环境检查失败"
            increment_counter "local_env_check_failed"
            return 1
        fi
    fi
}

# 执行 API 变更检测
run_local_api_detection() {
    log_step "4" "API 变更检测"

    # API 变更检测需要访问 Git 历史，在本地环境运行更可靠
    local api_changes_script="$SCRIPT_DIR/../core/api-changes.sh"

    if [ ! -f "$api_changes_script" ]; then
        log_error "API 变更检测脚本不存在: $api_changes_script"
        return 1
    fi

    log_info "执行 API 变更检测..."

    # 执行 API 变更检测脚本并捕获输出
    local output
    if output=$("$api_changes_script" 2>&1); then
        log_success "API 变更检测完成"

        # 从输出中提取环境变量
        local has_changes=$(echo "$output" | grep "有 API 变更:" | sed 's/.*有 API 变更: //')
        local changed_files=$(echo "$output" | grep "变更文件: specifications" | sed 's/.*变更文件: //')
        local changed_services=$(echo "$output" | grep "变更服务:" | sed 's/.*变更服务: //')

        # 设置环境变量
        export HAS_API_CHANGES="${has_changes:-false}"
        export CHANGED_API_FILES="${changed_files:-}"
        export CHANGED_SERVICES="${changed_services:-}"

        # 显示检测结果
        log_info "API 变更检测结果:"
        echo "  HAS_API_CHANGES: ${HAS_API_CHANGES}"
        echo "  CHANGED_API_FILES: ${CHANGED_API_FILES:-'(无)'}"
        echo "  CHANGED_SERVICES: ${CHANGED_SERVICES:-'(无)'}"

        increment_counter "local_api_detection_success"
        return 0
    else
        log_error "API 变更检测失败"
        log_debug "错误输出: $output"
        increment_counter "local_api_detection_failed"
        return 1
    fi
}

# 执行 OpenAPI 验证
run_local_validation() {
    log_step "5" "OpenAPI 验证"

    if [ "$USE_DOCKER" = "true" ]; then
        log_info "在 Docker 容器中执行 OpenAPI 验证..."

        # 在 Docker 容器中运行 OpenAPI 验证
        local docker_cmd="docker run --rm"
        docker_cmd="$docker_cmd -v $(pwd):/workspace"
        docker_cmd="$docker_cmd -w /workspace"
        docker_cmd="$docker_cmd -e HAS_API_CHANGES=${HAS_API_CHANGES:-false}"
        docker_cmd="$docker_cmd -e CHANGED_API_FILES='$CHANGED_API_FILES'"
        docker_cmd="$docker_cmd -e CHANGED_SERVICES='$CHANGED_SERVICES'"
        docker_cmd="$docker_cmd $DOCKER_IMAGE"
        docker_cmd="$docker_cmd bash -c 'tools/cicd/core/validation.sh changed'"

        if eval "$docker_cmd"; then
            log_success "Docker OpenAPI 验证通过"
            increment_counter "local_validation_success"
            return 0
        else
            log_error "Docker OpenAPI 验证失败"
            increment_counter "local_validation_failed"
            return 1
        fi
    else
        # 本地环境验证
        local validation_script="$SCRIPT_DIR/../core/validation.sh"

        if [ ! -f "$validation_script" ]; then
            log_error "OpenAPI 验证脚本不存在: $validation_script"
            return 1
        fi

        log_info "执行本地 OpenAPI 验证..."
        if "$validation_script" "changed"; then
            log_success "OpenAPI 验证通过"
            increment_counter "local_validation_success"
            return 0
        else
            log_error "OpenAPI 验证失败"
            increment_counter "local_validation_failed"
            return 1
        fi
    fi
}

# 执行 SDK 生成测试
run_local_sdk_test() {
    log_step "6" "SDK 生成测试"
    
    # 检查是否有 API 变更
    if [ "${HAS_API_CHANGES:-false}" != "true" ]; then
        log_info "⚠️ 没有 API 变更，跳过 SDK 生成测试"
        return 0
    fi
    
    local sdk_dir="$SCRIPT_DIR/../sdk"
    
    # 只测试 SDK 生成，不执行构建和测试（本地环境可能没有完整的构建环境）
    log_info "执行 SDK 生成测试..."
    if [ -f "$sdk_dir/generate.sh" ]; then
        if "$sdk_dir/generate.sh" "changed"; then
            log_success "SDK 生成测试完成"
            increment_counter "local_sdk_test_success"
            return 0
        else
            log_error "SDK 生成测试失败"
            increment_counter "local_sdk_test_failed"
            return 1
        fi
    else
        log_error "SDK 生成脚本不存在: $sdk_dir/generate.sh"
        return 1
    fi
}

# 执行文档预览测试
run_local_docs_test() {
    log_step "7" "文档预览测试"
    
    local docs_script="$SCRIPT_DIR/../docs/preview.sh"
    
    if [ ! -f "$docs_script" ]; then
        log_error "文档预览脚本不存在: $docs_script"
        return 1
    fi
    
    log_info "执行文档预览测试..."
    if "$docs_script" "normal"; then
        log_success "文档预览测试完成"
        increment_counter "local_docs_test_success"
        return 0
    else
        log_error "文档预览测试失败"
        increment_counter "local_docs_test_failed"
        return 1
    fi
}

# 在 Docker 容器中执行评审
run_docker_review() {
    log_step "8" "Docker 容器评审"
    
    if [ "$USE_DOCKER" != "true" ]; then
        log_info "跳过 Docker 容器评审 (USE_DOCKER=false)"
        return 0
    fi
    
    local workdir="$(pwd)"
    
    log_info "在 Docker 容器中执行评审流程..."
    log_info "容器名称: $CONTAINER_NAME"
    log_info "工作目录: $workdir"
    
    # 构建 Docker 运行命令
    local docker_cmd="docker run --rm"
    docker_cmd="$docker_cmd --name $CONTAINER_NAME"
    docker_cmd="$docker_cmd -v $workdir:/workspace"
    docker_cmd="$docker_cmd -w /workspace"
    docker_cmd="$docker_cmd -e CI=true"
    docker_cmd="$docker_cmd -e CI_COMMIT_BRANCH=$CI_COMMIT_BRANCH"
    docker_cmd="$docker_cmd -e HAS_API_CHANGES=${HAS_API_CHANGES:-false}"
    docker_cmd="$docker_cmd -e CHANGED_API_FILES='$CHANGED_API_FILES'"
    docker_cmd="$docker_cmd -e CHANGED_SERVICES='$CHANGED_SERVICES'"
    docker_cmd="$docker_cmd $DOCKER_IMAGE"
    docker_cmd="$docker_cmd bash -c 'tools/cicd/workflows/mr-pipeline.sh'"
    
    log_debug "Docker 命令: $docker_cmd"
    
    if $docker_cmd; then
        log_success "Docker 容器评审完成"
        increment_counter "docker_review_success"
        return 0
    else
        log_error "Docker 容器评审失败"
        increment_counter "docker_review_failed"
        return 1
    fi
}

# 清理资源
cleanup_resources() {
    if [ "$CLEANUP_ON_EXIT" = "true" ] && [ "$USE_DOCKER" = "true" ]; then
        log_info "清理 Docker 资源..."
        
        # 停止并删除容器（如果存在）
        if docker ps -a --format "table {{.Names}}" | grep -q "$CONTAINER_NAME"; then
            docker rm -f "$CONTAINER_NAME" >/dev/null 2>&1 || true
            log_info "Docker 容器已清理"
        fi
    fi
}

# 显示本地评审统计
show_local_review_stats() {
    log_step "9" "显示本地评审统计"
    
    echo ""
    log_info "🔍 本地评审执行统计:"
    
    local env_check=$(get_counter "local_env_check_success")
    local api_detection=$(get_counter "local_api_detection_success")
    local validation=$(get_counter "local_validation_success")
    local sdk_test=$(get_counter "local_sdk_test_success")
    local docs_test=$(get_counter "local_docs_test_success")
    local docker_review=$(get_counter "docker_review_success")
    
    echo "  本地环境检查: $([ $env_check -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  API 变更检测: $([ $api_detection -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  OpenAPI 验证: $([ $validation -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
    echo "  SDK 生成测试: $([ $sdk_test -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    echo "  文档预览测试: $([ $docs_test -gt 0 ] && echo "✅ 完成" || echo "❌ 失败")"
    echo "  Docker 容器评审: $([ $docker_review -gt 0 ] && echo "✅ 完成" || echo "⚠️ 跳过/失败")"
    
    if [ "${HAS_API_CHANGES:-false}" = "true" ]; then
        echo "  变更服务: ${CHANGED_SERVICES:-'(无)'}"
        echo "  变更文件数: $(echo "$CHANGED_API_FILES" | wc -w)"
    fi
    
    show_stats "本地评审统计"
}

# 主函数
main() {
    init_script "本地评审" "模拟 GitLab CI 中的 MR 评审流程"
    
    # 设置清理陷阱
    trap cleanup_resources EXIT
    
    log_info "==============================================="
    log_info "本地 MR 评审流程模拟脚本"
    log_info "==============================================="
    log_info "模拟 GitLab CI 中的以下流程："
    log_info "1. 环境检查"
    log_info "2. API 变更检测"
    log_info "3. OpenAPI 验证"
    log_info "4. SDK 生成测试"
    log_info "5. 文档预览测试"
    log_info "6. Docker 容器评审（可选）"
    log_info "==============================================="
    
    local review_status=0
    
    # 执行本地评审流程
    if ! setup_mock_ci_environment; then
        review_status=1
    elif ! check_docker_environment; then
        review_status=1
    elif ! run_local_environment_check; then
        review_status=1
    elif ! run_local_api_detection; then
        review_status=1
    elif ! run_local_validation; then
        review_status=1
    elif ! run_local_sdk_test; then
        review_status=1
    elif ! run_local_docs_test; then
        review_status=1
    fi
    
    # Docker 容器评审（可选，不影响整体结果）
    if [ $review_status -eq 0 ] && [ "$USE_DOCKER" = "true" ]; then
        run_docker_review || true
    fi
    
    show_local_review_stats
    
    if [ $review_status -eq 0 ]; then
        log_success "🎉 本地评审完成！"
        log_info "💡 提示: 如果所有检查都通过，您的代码应该能够通过 GitLab CI"
        finish_script "本地评审" "true"
        return 0
    else
        log_error "❌ 本地评审失败，请修复问题后重试"
        log_info "💡 提示: 请根据错误信息修复问题，然后重新运行本地评审"
        finish_script "本地评审" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "本地评审模块已加载 ✅"
