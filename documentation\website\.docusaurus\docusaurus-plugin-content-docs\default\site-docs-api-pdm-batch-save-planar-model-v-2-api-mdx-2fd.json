{"id": "api/pdm/batch-save-planar-model-v-2", "title": "批量保存平面造型数据", "description": "批量保存平面造型数据", "source": "@site/docs/api/pdm/batch-save-planar-model-v-2.api.mdx", "sourceDirName": "api/pdm", "slug": "/api/pdm/batch-save-planar-model-v-2", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-save-planar-model-v-2", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "frontMatter": {"id": "batch-save-planar-model-v-2", "title": "批量保存平面造型数据", "description": "批量保存平面造型数据", "sidebar_label": "批量保存平面造型数据", "hide_title": true, "hide_table_of_contents": true, "api": {"tags": ["平面造型设计管理接口"], "description": "批量保存平面造型数据", "operationId": "batchSavePlanarModelV2", "parameters": [{"name": "designId", "in": "path", "description": "设计ID", "required": true, "schema": {"type": "string"}}, {"name": "levelId", "in": "path", "description": "楼层ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "批量平面造型数据请求体", "content": {"application/json": {"schema": {"type": "object", "properties": {"requestId": {"type": "string"}, "operationId": {"type": "string"}, "batchRequests": {"type": "array", "items": {"required": ["planarModel"], "type": "object", "properties": {"requestId": {"type": "string"}, "planarModel": {"required": ["archFaces", "edgeAttributes", "faceAttributes", "id", "model", "stripLightMode"], "type": "object", "properties": {"id": {"type": "string", "description": "平面造型ID", "example": "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"}, "archFaces": {"type": "array", "description": "所在的建筑面数据列表", "items": {"type": "object", "properties": {"faceId": {"type": "string", "description": "面ID"}}, "description": "面数据基础类", "title": "FaceLod100Data"}}, "model": {"type": "object", "title": "Geometric2dDesign", "description": "二维平面设计模型。", "allOf": [{"type": "object", "title": "BodyBase", "description": "所有几何体（模型）的基类。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"modelType": {"readOnly": true, "type": "string", "title": "ModelTypes", "description": "模型的分类。", "enum": ["GEOMETRIC_2D_DESIGN", "GEOMETRIC_3D_BODY", "TRI_MESH_MODEL"]}}}], "properties": {"fs": {"type": "array", "items": {"type": "object", "title": "Design2dFace", "description": "二维平面设计模型的一个区域（面片）。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"elp": {"type": "array", "items": {"type": "array", "items": {"type": "object", "title": "Design2dEdge", "description": "二维平面设计模型的边。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"cv": {"description": "边所依赖的曲线。", "type": "object", "title": "Curve2d", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": {"propertyName": "tp", "mapping": {"L2": {"type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "LS2": {"type": "object", "title": "LineSegment2d", "description": "二维直线段。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["LS2"]}, "p0": {"description": "直线段的起点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "p1": {"description": "直线段的终点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "line": {"readOnly": true, "type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}}}, "RAY2": {"type": "object", "title": "Ray2d", "description": "二维射线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["RAY2"]}, "p0": {"description": "二维射线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "二维射线的方向（单位向量）。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "ARC2": {"type": "object", "title": "Arc2d", "description": "二维圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["ARC2"]}, "circle": {"description": "二维圆弧所在的圆。", "type": "object", "title": "Circle2d", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "s": {"type": "number", "format": "double", "description": "二维圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "二维圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "圆弧的转向：1 = 逆时针，-1 = 顺时针。"}}}, "CIR2": {"type": "object", "title": "Circle2d", "description": "二维圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "ELP2": {"type": "object", "title": "Ellipse2d", "description": "二维椭圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["ELP2"]}, "ccs2d": {"description": "二维椭圆的局部坐标系（圆心，参数域x轴，参数域y轴）。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径，也就是x轴方向的半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径，也就是y轴方向的半径。"}, "c": {"description": "椭圆圆心坐标，为了兼容老版的Math2的json格式。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "EARC2": {"type": "object", "title": "EllipticalArc2d", "description": "二维椭圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["EARC2"]}, "ccs2d": {"description": "二维椭圆弧所在的椭圆的局部坐标系。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径。"}, "s": {"type": "number", "format": "double", "description": "椭圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "椭圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "1 = 逆时针，-1 = 顺时针。"}}}, "PLY2": {"type": "object", "title": "Polyline2d", "description": "二维折线段。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["PLY2"]}, "pts": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "构成折线段的有序点列。"}, "tangents": {"type": "array", "items": {"type": "object", "title": "Vector2d", "description": "二维向量。", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}, "description": "折线段每点处的切向量；允许为空。"}}}, "BEZ2": {"type": "object", "title": "BezierCurve2d", "description": "二维贝塞尔曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["BEZ2"]}, "cv": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "贝塞尔曲线的控制顶点。"}, "itv": {"description": "贝塞尔曲线的参数区间。", "type": "object", "title": "Interval", "properties": {"s": {"type": "number", "format": "double", "description": "参数区间的起点。"}, "e": {"type": "number", "format": "double", "description": "参数区间的终点。"}}}}}, "NBSC2": {"type": "object", "title": "NurbsCurve2d", "description": "二维的非均匀有理B样条曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["NBSC2"]}, "d": {"type": "integer", "description": "nurbs曲线的次数。"}, "ks": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线的节点向量。"}, "cvs": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "nurbs曲线的控制顶点。"}, "ws": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线控制顶点的权重。"}}}}}, "required": ["tp"]}, "sn": {"type": "boolean", "description": "边的方向（从起点到终点）是否和所依赖的曲线的方向一致。"}, "vs": {"type": "array", "items": {"type": "object", "title": "Design2dVertex", "description": "二维平面设计模型的顶点。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"pt": {"description": "顶点的坐标位置。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "description": "边的两个顶点。"}}}}, "description": "面片的边界。"}, "esn": {"type": "array", "items": {"type": "array", "items": {"type": "boolean"}}, "description": "边界环本身的转向和其中每条边的方向是否一致。"}}}, "description": "2D设计的区域。"}, "es": {"type": "array", "items": {"type": "object", "title": "Design2dEdge", "description": "二维平面设计模型的边。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"cv": {"description": "边所依赖的曲线。", "type": "object", "title": "Curve2d", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": {"propertyName": "tp", "mapping": {"L2": {"type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "LS2": {"type": "object", "title": "LineSegment2d", "description": "二维直线段。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["LS2"]}, "p0": {"description": "直线段的起点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "p1": {"description": "直线段的终点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "line": {"readOnly": true, "type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}}}, "RAY2": {"type": "object", "title": "Ray2d", "description": "二维射线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["RAY2"]}, "p0": {"description": "二维射线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "二维射线的方向（单位向量）。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "ARC2": {"type": "object", "title": "Arc2d", "description": "二维圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["ARC2"]}, "circle": {"description": "二维圆弧所在的圆。", "type": "object", "title": "Circle2d", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "s": {"type": "number", "format": "double", "description": "二维圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "二维圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "圆弧的转向：1 = 逆时针，-1 = 顺时针。"}}}, "CIR2": {"type": "object", "title": "Circle2d", "description": "二维圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "ELP2": {"type": "object", "title": "Ellipse2d", "description": "二维椭圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["ELP2"]}, "ccs2d": {"description": "二维椭圆的局部坐标系（圆心，参数域x轴，参数域y轴）。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径，也就是x轴方向的半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径，也就是y轴方向的半径。"}, "c": {"description": "椭圆圆心坐标，为了兼容老版的Math2的json格式。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "EARC2": {"type": "object", "title": "EllipticalArc2d", "description": "二维椭圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["EARC2"]}, "ccs2d": {"description": "二维椭圆弧所在的椭圆的局部坐标系。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径。"}, "s": {"type": "number", "format": "double", "description": "椭圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "椭圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "1 = 逆时针，-1 = 顺时针。"}}}, "PLY2": {"type": "object", "title": "Polyline2d", "description": "二维折线段。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["PLY2"]}, "pts": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "构成折线段的有序点列。"}, "tangents": {"type": "array", "items": {"type": "object", "title": "Vector2d", "description": "二维向量。", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}, "description": "折线段每点处的切向量；允许为空。"}}}, "BEZ2": {"type": "object", "title": "BezierCurve2d", "description": "二维贝塞尔曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["BEZ2"]}, "cv": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "贝塞尔曲线的控制顶点。"}, "itv": {"description": "贝塞尔曲线的参数区间。", "type": "object", "title": "Interval", "properties": {"s": {"type": "number", "format": "double", "description": "参数区间的起点。"}, "e": {"type": "number", "format": "double", "description": "参数区间的终点。"}}}}}, "NBSC2": {"type": "object", "title": "NurbsCurve2d", "description": "二维的非均匀有理B样条曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["NBSC2"]}, "d": {"type": "integer", "description": "nurbs曲线的次数。"}, "ks": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线的节点向量。"}, "cvs": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "nurbs曲线的控制顶点。"}, "ws": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线控制顶点的权重。"}}}}}, "required": ["tp"]}, "sn": {"type": "boolean", "description": "边的方向（从起点到终点）是否和所依赖的曲线的方向一致。"}, "vs": {"type": "array", "items": {"type": "object", "title": "Design2dVertex", "description": "二维平面设计模型的顶点。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"pt": {"description": "顶点的坐标位置。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "description": "边的两个顶点。"}}}, "description": "模型的所有边。"}, "vs": {"type": "array", "items": {"type": "object", "title": "Design2dVertex", "description": "二维平面设计模型的顶点。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"pt": {"description": "顶点的坐标位置。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "description": "模型的所有顶点。"}}}, "stripLightMode": {"type": "string", "description": "灯带模式", "example": "INNER", "enum": ["MODE_TYPE_UNSPECIFIED", "INNER", "OUTER"]}, "edgeAttributes": {"type": "array", "description": "平面造型边属性", "items": {"required": ["edgeId", "stripLight"], "type": "object", "properties": {"edgeId": {"type": "string", "description": "边id", "example": "123"}, "stripLight": {"required": ["stripLightProfileData"], "type": "object", "properties": {"stripLightProfileData": {"required": ["height", "horizontalFaceThickness", "verticalFaceThickness", "width"], "type": "object", "properties": {"width": {"type": "number", "description": "宽度", "format": "double", "example": 500}, "height": {"type": "number", "description": "高度", "format": "double", "example": 200}, "verticalFaceThickness": {"type": "number", "description": "垂直面厚度", "format": "double", "example": 10}, "horizontalFaceThickness": {"type": "number", "description": "水平面厚度", "format": "double", "example": 10}}, "description": "灯带轮廓数据", "title": "StripLightProfile"}}, "description": "灯带数据", "title": "StripLight"}}, "description": "平面造型边的属性", "title": "EdgeAttribute"}}, "faceAttributes": {"type": "array", "description": "平面造型区域属性", "items": {"required": ["faceId", "height"], "type": "object", "properties": {"faceId": {"type": "string", "description": "面id", "example": "123"}, "height": {"type": "number", "description": "区域抬高高度", "format": "double", "example": 100}}, "description": "平面造型区域的属性", "title": "FaceAttribute"}}}, "description": "平面造型数据", "title": "PlanarModel"}}, "description": "保存平面造型数据请求体", "title": "SavePlanarModelLod100DataRequestV2"}}}, "description": "批量保存平面造型数据请求体", "title": "BatchSavePlanarModelLod100DataRequestV2"}}}, "required": true}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"elements": {"type": "array", "items": {"required": ["archFaces", "edgeAttributes", "faceAttributes", "id", "model", "stripLightMode"], "type": "object", "properties": {"id": {"type": "string", "description": "平面造型ID", "example": "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"}, "archFaces": {"type": "array", "description": "所在的建筑面数据列表", "items": {"type": "object", "properties": {"faceId": {"type": "string", "description": "面ID"}}, "description": "面数据基础类", "title": "FaceLod100Data"}}, "model": {"type": "object", "title": "Geometric2dDesign", "description": "二维平面设计模型。", "allOf": [{"type": "object", "title": "BodyBase", "description": "所有几何体（模型）的基类。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"modelType": {"readOnly": true, "type": "string", "title": "ModelTypes", "description": "模型的分类。", "enum": ["GEOMETRIC_2D_DESIGN", "GEOMETRIC_3D_BODY", "TRI_MESH_MODEL"]}}}], "properties": {"fs": {"type": "array", "items": {"type": "object", "title": "Design2dFace", "description": "二维平面设计模型的一个区域（面片）。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"elp": {"type": "array", "items": {"type": "array", "items": {"type": "object", "title": "Design2dEdge", "description": "二维平面设计模型的边。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"cv": {"description": "边所依赖的曲线。", "type": "object", "title": "Curve2d", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": {"propertyName": "tp", "mapping": {"L2": {"type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "LS2": {"type": "object", "title": "LineSegment2d", "description": "二维直线段。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["LS2"]}, "p0": {"description": "直线段的起点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "p1": {"description": "直线段的终点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "line": {"readOnly": true, "type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}}}, "RAY2": {"type": "object", "title": "Ray2d", "description": "二维射线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["RAY2"]}, "p0": {"description": "二维射线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "二维射线的方向（单位向量）。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "ARC2": {"type": "object", "title": "Arc2d", "description": "二维圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["ARC2"]}, "circle": {"description": "二维圆弧所在的圆。", "type": "object", "title": "Circle2d", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "s": {"type": "number", "format": "double", "description": "二维圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "二维圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "圆弧的转向：1 = 逆时针，-1 = 顺时针。"}}}, "CIR2": {"type": "object", "title": "Circle2d", "description": "二维圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "ELP2": {"type": "object", "title": "Ellipse2d", "description": "二维椭圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["ELP2"]}, "ccs2d": {"description": "二维椭圆的局部坐标系（圆心，参数域x轴，参数域y轴）。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径，也就是x轴方向的半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径，也就是y轴方向的半径。"}, "c": {"description": "椭圆圆心坐标，为了兼容老版的Math2的json格式。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "EARC2": {"type": "object", "title": "EllipticalArc2d", "description": "二维椭圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["EARC2"]}, "ccs2d": {"description": "二维椭圆弧所在的椭圆的局部坐标系。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径。"}, "s": {"type": "number", "format": "double", "description": "椭圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "椭圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "1 = 逆时针，-1 = 顺时针。"}}}, "PLY2": {"type": "object", "title": "Polyline2d", "description": "二维折线段。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["PLY2"]}, "pts": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "构成折线段的有序点列。"}, "tangents": {"type": "array", "items": {"type": "object", "title": "Vector2d", "description": "二维向量。", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}, "description": "折线段每点处的切向量；允许为空。"}}}, "BEZ2": {"type": "object", "title": "BezierCurve2d", "description": "二维贝塞尔曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["BEZ2"]}, "cv": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "贝塞尔曲线的控制顶点。"}, "itv": {"description": "贝塞尔曲线的参数区间。", "type": "object", "title": "Interval", "properties": {"s": {"type": "number", "format": "double", "description": "参数区间的起点。"}, "e": {"type": "number", "format": "double", "description": "参数区间的终点。"}}}}}, "NBSC2": {"type": "object", "title": "NurbsCurve2d", "description": "二维的非均匀有理B样条曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["NBSC2"]}, "d": {"type": "integer", "description": "nurbs曲线的次数。"}, "ks": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线的节点向量。"}, "cvs": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "nurbs曲线的控制顶点。"}, "ws": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线控制顶点的权重。"}}}}}, "required": ["tp"]}, "sn": {"type": "boolean", "description": "边的方向（从起点到终点）是否和所依赖的曲线的方向一致。"}, "vs": {"type": "array", "items": {"type": "object", "title": "Design2dVertex", "description": "二维平面设计模型的顶点。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"pt": {"description": "顶点的坐标位置。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "description": "边的两个顶点。"}}}}, "description": "面片的边界。"}, "esn": {"type": "array", "items": {"type": "array", "items": {"type": "boolean"}}, "description": "边界环本身的转向和其中每条边的方向是否一致。"}}}, "description": "2D设计的区域。"}, "es": {"type": "array", "items": {"type": "object", "title": "Design2dEdge", "description": "二维平面设计模型的边。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"cv": {"description": "边所依赖的曲线。", "type": "object", "title": "Curve2d", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": {"propertyName": "tp", "mapping": {"L2": {"type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "LS2": {"type": "object", "title": "LineSegment2d", "description": "二维直线段。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["LS2"]}, "p0": {"description": "直线段的起点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "p1": {"description": "直线段的终点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "line": {"readOnly": true, "type": "object", "title": "Line2d", "description": "二维直线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["L2"]}, "p0": {"description": "直线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "直线的方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}}}, "RAY2": {"type": "object", "title": "Ray2d", "description": "二维射线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["RAY2"]}, "p0": {"description": "二维射线的原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "v": {"description": "二维射线的方向（单位向量）。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "ARC2": {"type": "object", "title": "Arc2d", "description": "二维圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["ARC2"]}, "circle": {"description": "二维圆弧所在的圆。", "type": "object", "title": "Circle2d", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "s": {"type": "number", "format": "double", "description": "二维圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "二维圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "圆弧的转向：1 = 逆时针，-1 = 顺时针。"}}}, "CIR2": {"type": "object", "title": "Circle2d", "description": "二维圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["CIR2"]}, "c": {"description": "圆的圆心。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "r": {"type": "number", "format": "double", "description": "圆的半径。"}}}, "ELP2": {"type": "object", "title": "Ellipse2d", "description": "二维椭圆。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["ELP2"]}, "ccs2d": {"description": "二维椭圆的局部坐标系（圆心，参数域x轴，参数域y轴）。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径，也就是x轴方向的半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径，也就是y轴方向的半径。"}, "c": {"description": "椭圆圆心坐标，为了兼容老版的Math2的json格式。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "EARC2": {"type": "object", "title": "EllipticalArc2d", "description": "二维椭圆弧。", "allOf": [{"type": "object", "title": "TrimmedCurve2d", "description": "二维裁剪曲线（有界曲线）。", "x-abstract": true, "x-internal": true, "x-circular": true, "x-circular-ref": ["Curve2d", {"description": "TrimmedCurve2d继承自Curve2d，其子类可能通过属性引用回Curve2d的其他子类。"}], "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"startParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的起点参数。"}, "endParam": {"type": "number", "format": "double", "readOnly": true, "description": "获取和设置裁剪曲线的终点参数。"}}}], "properties": {"tp": {"type": "string", "enum": ["EARC2"]}, "ccs2d": {"description": "二维椭圆弧所在的椭圆的局部坐标系。", "type": "object", "title": "CCS2d", "properties": {"o": {"description": "坐标系原点。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "dx": {"description": "坐标系x轴方向。", "type": "object", "title": "Vector2d", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}}}, "a": {"type": "number", "format": "double", "description": "椭圆长轴半径。"}, "b": {"type": "number", "format": "double", "description": "椭圆短轴半径。"}, "s": {"type": "number", "format": "double", "description": "椭圆弧的参数起点（弧度值）。"}, "e": {"type": "number", "format": "double", "description": "椭圆弧的参数终点（弧度值）。"}, "cs": {"type": "integer", "description": "1 = 逆时针，-1 = 顺时针。"}}}, "PLY2": {"type": "object", "title": "Polyline2d", "description": "二维折线段。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["PLY2"]}, "pts": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "构成折线段的有序点列。"}, "tangents": {"type": "array", "items": {"type": "object", "title": "Vector2d", "description": "二维向量。", "properties": {"x": {"type": "number", "format": "double", "description": "二维向量的x坐标。"}, "y": {"type": "number", "format": "double", "description": "二维向量的y坐标。"}}}, "description": "折线段每点处的切向量；允许为空。"}}}, "BEZ2": {"type": "object", "title": "BezierCurve2d", "description": "二维贝塞尔曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["BEZ2"]}, "cv": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "贝塞尔曲线的控制顶点。"}, "itv": {"description": "贝塞尔曲线的参数区间。", "type": "object", "title": "Interval", "properties": {"s": {"type": "number", "format": "double", "description": "参数区间的起点。"}, "e": {"type": "number", "format": "double", "description": "参数区间的终点。"}}}}}, "NBSC2": {"type": "object", "title": "NurbsCurve2d", "description": "二维的非均匀有理B样条曲线。", "allOf": [{"type": "object", "title": "Curve2d", "description": "二维曲线的基类。", "allOf": [{"type": "object", "title": "Geometry", "description": "所有几何数据的基类。注意，所有几何数据都是SubType。"}], "discriminator": "circular()", "required": ["tp"]}], "properties": {"tp": {"type": "string", "enum": ["NBSC2"]}, "d": {"type": "integer", "description": "nurbs曲线的次数。"}, "ks": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线的节点向量。"}, "cvs": {"type": "array", "items": {"type": "object", "title": "Point2d", "description": "二维点。", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}, "description": "nurbs曲线的控制顶点。"}, "ws": {"type": "array", "items": {"type": "number", "format": "double"}, "description": "nurbs曲线控制顶点的权重。"}}}}}, "required": ["tp"]}, "sn": {"type": "boolean", "description": "边的方向（从起点到终点）是否和所依赖的曲线的方向一致。"}, "vs": {"type": "array", "items": {"type": "object", "title": "Design2dVertex", "description": "二维平面设计模型的顶点。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"pt": {"description": "顶点的坐标位置。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "description": "边的两个顶点。"}}}, "description": "模型的所有边。"}, "vs": {"type": "array", "items": {"type": "object", "title": "Design2dVertex", "description": "二维平面设计模型的顶点。", "allOf": [{"type": "object", "title": "Topology", "description": "所有拓扑对象的基类。", "properties": {"n": {"description": "拓扑对象的持久命名。", "type": "object", "title": "Name", "discriminator": {"propertyName": "subType", "mapping": {"TN": {"type": "object", "title": "TextName", "description": "文本式名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["TN"]}, "n": {"type": "string", "description": "对象的名字。"}}}, "STN": {"type": "object", "title": "StructuredName", "description": "结构化名字。", "allOf": [{"type": "object", "title": "Name", "description": "名字的基类。", "discriminator": "circular()", "required": ["subType"]}], "properties": {"subType": {"type": "string", "enum": ["STN"]}, "et": {"type": "string", "description": "被命名对象的类型，例如V，E，F分别代表顶点，边，面。"}, "pg": {"type": "array", "items": {"type": "string"}, "description": "当前对象的生成源名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "ac": {"type": "array", "items": {"type": "string"}, "description": "辅助关联对象名字列表（字符串形式，避免循环引用）。", "maxItems": 50}, "pf": {"type": "string", "description": "对象的额外编号或者额外类型信息。"}}}}}, "required": ["subType"]}, "p": {"type": "object", "additionalProperties": true, "description": "挂载在该对象上的非标数据（自定义属性），业务方自定义设置、解析和使用。"}, "id": {"type": "string", "description": "该对象在内部系统中的ID。"}}}], "properties": {"pt": {"description": "顶点的坐标位置。", "type": "object", "title": "Point2d", "properties": {"x": {"type": "number", "format": "double", "description": "x坐标。"}, "y": {"type": "number", "format": "double", "description": "y坐标。"}}}}}, "description": "模型的所有顶点。"}}}, "stripLightMode": {"type": "string", "description": "灯带模式", "example": "INNER", "enum": ["MODE_TYPE_UNSPECIFIED", "INNER", "OUTER"]}, "edgeAttributes": {"type": "array", "description": "平面造型边属性", "items": {"required": ["edgeId", "stripLight"], "type": "object", "properties": {"edgeId": {"type": "string", "description": "边id", "example": "123"}, "stripLight": {"required": ["stripLightProfileData"], "type": "object", "properties": {"stripLightProfileData": {"required": ["height", "horizontalFaceThickness", "verticalFaceThickness", "width"], "type": "object", "properties": {"width": {"type": "number", "description": "宽度", "format": "double", "example": 500}, "height": {"type": "number", "description": "高度", "format": "double", "example": 200}, "verticalFaceThickness": {"type": "number", "description": "垂直面厚度", "format": "double", "example": 10}, "horizontalFaceThickness": {"type": "number", "description": "水平面厚度", "format": "double", "example": 10}}, "description": "灯带轮廓数据", "title": "StripLightProfile"}}, "description": "灯带数据", "title": "StripLight"}}, "description": "平面造型边的属性", "title": "EdgeAttribute"}}, "faceAttributes": {"type": "array", "description": "平面造型区域属性", "items": {"required": ["faceId", "height"], "type": "object", "properties": {"faceId": {"type": "string", "description": "面id", "example": "123"}, "height": {"type": "number", "description": "区域抬高高度", "format": "double", "example": 100}}, "description": "平面造型区域的属性", "title": "FaceAttribute"}}}, "description": "平面造型数据", "title": "PlanarModel"}}}, "description": "批量保存平面造型响应体", "title": "BatchSavePlanarModelResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "title": "ApiError", "description": "REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范", "required": ["code", "message", "status", "details"], "properties": {"code": {"type": "integer", "description": "HTTP 状态码（数字），例如 403、404、500等", "example": 403, "minimum": 100, "maximum": 599}, "message": {"type": "string", "description": "面向开发者的错误消息，应该使用英文", "example": "Permission denied", "minLength": 1, "maxLength": 1000}, "status": {"description": "RestAPI 对应的状态码枚举值", "type": "string", "title": "Code", "enum": ["OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "PARTIAL_ELEMENT_UPDATE_FAILED"], "x-module": "error", "x-category": "错误处理"}, "details": {"description": "错误的详细信息", "type": "object", "title": "ErrorDetails", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "错误原因，标识错误的直接原因。格式为大写蛇形命名", "example": "INVALID_REQUEST_FORMAT", "pattern": "^[A-Z][A-Z0-9_]*[A-Z0-9]$", "maxLength": 63}, "message": {"type": "string", "description": "针对此错误发生的人类可读的解释说明", "example": "请求参数格式不正确，缺少必需的字段 'name'", "maxLength": 500}, "domain": {"type": "string", "description": "错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称", "example": "deco.manycoreapis.com", "maxLength": 100}, "metaData": {"type": "object", "description": "关于此错误的附加结构化详细信息，键名限制为64个字符", "additionalProperties": {"type": "string", "maxLength": 200}, "example": {"maxAllowedSize": "1000", "actualSize": "1500"}}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "localizedMessage": {"description": "错误的本地化消息，可选字段", "type": "object", "title": "LocalizedMessage", "required": ["locale", "message"], "properties": {"locale": {"type": "string", "description": "消息所使用的语言环境", "example": "zh-CN", "pattern": "^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"}, "message": {"type": "string", "description": "本地化的消息内容", "example": "权限不足，无法访问该资源", "minLength": 1, "maxLength": 1000}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "help": {"description": "错误的帮助信息，可选字段", "type": "object", "title": "Help", "required": ["desc", "url"], "properties": {"desc": {"type": "string", "description": "链接描述，说明该链接提供的帮助内容", "example": "查看 API 使用指南", "minLength": 1, "maxLength": 200}, "url": {"type": "string", "description": "帮助链接的 URL 地址", "example": "https://docs.example.com/api-guide", "format": "uri", "maxLength": 500}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "title": "ApiError", "description": "REST API 错误响应的主要数据结构，遵循 Google API 错误处理规范", "required": ["code", "message", "status", "details"], "properties": {"code": {"type": "integer", "description": "HTTP 状态码（数字），例如 403、404、500等", "example": 403, "minimum": 100, "maximum": 599}, "message": {"type": "string", "description": "面向开发者的错误消息，应该使用英文", "example": "Permission denied", "minLength": 1, "maxLength": 1000}, "status": {"description": "RestAPI 对应的状态码枚举值", "type": "string", "title": "Code", "enum": ["OK", "CANCELLED", "UNKNOWN", "INVALID_ARGUMENT", "DEADLINE_EXCEEDED", "NOT_FOUND", "ALREADY_EXISTS", "PERMISSION_DENIED", "UNAUTHENTICATED", "RESOURCE_EXHAUSTED", "FAILED_PRECONDITION", "ABORTED", "OUT_OF_RANGE", "UNIMPLEMENTED", "INTERNAL", "UNAVAILABLE", "DATA_LOSS", "PARTIAL_ELEMENT_UPDATE_FAILED"], "x-module": "error", "x-category": "错误处理"}, "details": {"description": "错误的详细信息", "type": "object", "title": "ErrorDetails", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "错误原因，标识错误的直接原因。格式为大写蛇形命名", "example": "INVALID_REQUEST_FORMAT", "pattern": "^[A-Z][A-Z0-9_]*[A-Z0-9]$", "maxLength": 63}, "message": {"type": "string", "description": "针对此错误发生的人类可读的解释说明", "example": "请求参数格式不正确，缺少必需的字段 'name'", "maxLength": 500}, "domain": {"type": "string", "description": "错误域，表示错误原因所属的逻辑分组，通常是生成错误的服务名称", "example": "deco.manycoreapis.com", "maxLength": 100}, "metaData": {"type": "object", "description": "关于此错误的附加结构化详细信息，键名限制为64个字符", "additionalProperties": {"type": "string", "maxLength": 200}, "example": {"maxAllowedSize": "1000", "actualSize": "1500"}}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "localizedMessage": {"description": "错误的本地化消息，可选字段", "type": "object", "title": "LocalizedMessage", "required": ["locale", "message"], "properties": {"locale": {"type": "string", "description": "消息所使用的语言环境", "example": "zh-CN", "pattern": "^[a-z]{2,3}(-[A-Z]{2})?(-[a-z0-9]{5,8})?(-[a-z0-9]{1,8})*$"}, "message": {"type": "string", "description": "本地化的消息内容", "example": "权限不足，无法访问该资源", "minLength": 1, "maxLength": 1000}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}, "help": {"description": "错误的帮助信息，可选字段", "type": "object", "title": "Help", "required": ["desc", "url"], "properties": {"desc": {"type": "string", "description": "链接描述，说明该链接提供的帮助内容", "example": "查看 API 使用指南", "minLength": 1, "maxLength": 200}, "url": {"type": "string", "description": "帮助链接的 URL 地址", "example": "https://docs.example.com/api-guide", "format": "uri", "maxLength": 500}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}, "additionalProperties": false, "x-module": "error", "x-category": "错误处理"}}}}}, "x-return-extra": {"is-operation": "true"}, "extensions": [{"key": "x-return-extra", "value": {"is-operation": "true"}}], "method": "post", "path": "/deco/api/v2/rest/designs/{designId}/levels/{levelId}/planarmodel:batchcreate", "servers": [{"url": "http://localhost:8083", "description": "本地开发环境"}, {"url": "https://api-dev.qunhe.com", "description": "开发测试环境"}, {"url": "https://api.qunhe.com", "description": "生产环境"}], "jsonRequestBodyExample": {"requestId": "string", "operationId": "string", "batchRequests": [{"requestId": "string", "planarModel": {"id": "PDM-NBXDRSAKTJGHUAABAAAAADY8-123", "archFaces": [{"faceId": "string"}], "model": {"n": {}, "p": {}, "id": "string", "fs": [{"n": {}, "p": {}, "id": "string", "elp": [{"n": {}, "p": {}, "id": "string", "cv": {}, "sn": true, "vs": [{"n": {}, "p": {}, "id": "string", "pt": {"x": 0, "y": 0}}]}], "esn": [true]}], "es": [{"n": {}, "p": {}, "id": "string", "cv": {}, "sn": true, "vs": [{"n": {}, "p": {}, "id": "string", "pt": {"x": 0, "y": 0}}]}], "vs": [{"n": {}, "p": {}, "id": "string", "pt": {"x": 0, "y": 0}}]}, "stripLightMode": "INNER", "edgeAttributes": [{"edgeId": "123", "stripLight": {"stripLightProfileData": {"width": 500, "height": 200, "verticalFaceThickness": 10, "horizontalFaceThickness": 10}}}], "faceAttributes": [{"faceId": "123", "height": 100}]}}]}, "info": {"title": "平面造型设计API", "description": "平面造型设计REST API\n\n    ## 功能特性\n    - 平面造型的 CRUD 操作\n    - 批量操作支持（创建、更新、删除、查询）\n    - 分页查询支持\n    \n    ## 数据模型\n    - **平面造型**: 包含平面造型所在的面、平面造型形状信息\n    \n    ## 分页机制\n    使用基于页码的分页，支持自定义页面大小", "contact": {"name": "群核科技开发团队", "url": "https://wiki.manycore.com/furniture-design", "email": "<EMAIL>"}, "license": {"name": "群核科技专有许可证", "url": "https://manycore.com/license"}, "version": "1.0.0"}, "postman": {"name": "批量保存平面造型数据", "description": {"content": "批量保存平面造型数据", "type": "text/plain"}, "url": {"path": ["deco", "api", "v2", "rest", "designs", ":designId", "levels", ":levelId", "planarmodel:batchcreate"], "host": ["{{baseUrl}}"], "query": [], "variable": [{"disabled": false, "description": {"content": "(Required) 设计ID", "type": "text/plain"}, "type": "any", "value": "", "key": "designId"}, {"disabled": false, "description": {"content": "(Required) 楼层ID", "type": "text/plain"}, "type": "any", "value": "", "key": "levelId"}]}, "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}}}, "sidebar_class_name": "post api-method", "info_path": "docs/api/pdm/平面造型设计api", "custom_edit_url": null}, "sidebar": "pdmSidebar", "previous": {"title": "批量删除平面造型数据", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/batch-delete-planar-model"}, "next": {"title": "根据平面造型ID删除单个平面造型数据", "permalink": "/manycoreapi-demo/0.0.4/docs/api/pdm/delete-planar-model"}}