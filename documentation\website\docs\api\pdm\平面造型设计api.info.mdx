---
id: 平面造型设计api
title: "平面造型设计API"
description: "平面造型设计REST API"
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import ApiLogo from "@theme/ApiLogo";
import Heading from "@theme/Heading";
import SchemaTabs from "@theme/SchemaTabs";
import TabItem from "@theme/TabItem";
import Export from "@theme/ApiExplorer/Export";

<span
  className={"theme-doc-version-badge badge badge--secondary"}
  children={"Version: 1.0.0"}
>
</span>

<Export
  url={"/specifications/services/pdm/openapi.yaml"}
  proxy={undefined}
>
  
</Export>

<Heading
  as={"h1"}
  className={"openapi__heading"}
  children={"平面造型设计API"}
>
</Heading>



平面造型设计REST API

    ## 功能特性
    - 平面造型的 CRUD 操作
    - 批量操作支持（创建、更新、删除、查询）
    - 分页查询支持
    
    ## 数据模型
    - **平面造型**: 包含平面造型所在的面、平面造型形状信息
    
    ## 分页机制
    使用基于页码的分页，支持自定义页面大小

<div
  style={{"display":"flex","flexDirection":"column","marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    Contact
  </h3><span>
    群核科技开发团队: [<EMAIL>](mailto:<EMAIL>)
  </span><span>
    URL: [https://wiki.manycore.com/furniture-design](https://wiki.manycore.com/furniture-design)
  </span>
</div><div
  style={{"marginBottom":"var(--ifm-paragraph-margin-bottom)"}}
>
  <h3
    style={{"marginBottom":"0.25rem"}}
  >
    License
  </h3><a
    href={"https://manycore.com/license"}
  >
    群核科技专有许可证
  </a>
</div>
      