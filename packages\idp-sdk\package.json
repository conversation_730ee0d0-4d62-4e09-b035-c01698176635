{"name": "@qunhe/idp-sdk", "version": "1.70.1", "description": "idp sdk typings", "maintainers": ["da<PERSON>o"], "author": {"name": "da<PERSON>o", "email": "<EMAIL>"}, "homepage": "", "license": "ISC", "publishConfig": {"registry": "hrcp://npm-registry.qunhequnhe.com/"}, "release": {"scripts": {"pre-public": "yarn prepare-public-package", "pre-release": "yarn build-package"}, "publishRoot": "./build"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/IDP.Math.git"}, "scripts": {"build": "ts-node ../../scripts/build-idp-sdk --trim-internal-api", "build-package": "npm run build && node ../../scripts/publish/prepare-sdk-package", "prepare-public-package": "cross-env PUBLIC_SCOPE=manycore node ../../scripts/publish/prepare-sdk-package"}, "kjl-template": {"plugins": {"manual": "@qunhe/kjl-plugin_manual"}}, "docSiteConfig": {"title": "小程序平台", "favicon": "../../assets/kujialeLogo.ico", "navbarLogo": "../../assets/kujialeLogo.svg"}, "dependencies": {"@qunhe/idp-app-core": "1.70.0", "@qunhe/idp-common": "1.70.0", "@qunhe/idp-custom": "1.70.0", "@qunhe/idp-custom-doorwindow": "1.70.0", "@qunhe/idp-decoration": "1.70.0", "@qunhe/idp-drawing": "1.70.0", "@qunhe/idp-element-label": "1.70.0", "@qunhe/idp-floorplan": "1.70.1", "@qunhe/idp-intelligent": "1.70.0", "@qunhe/idp-layout-planner": "1.70.0", "@qunhe/idp-math": "1.70.0", "@qunhe/idp-param-ceiling": "1.70.0", "@qunhe/idp-payment": "1.70.0", "@qunhe/idp-spatial-zone": "1.70.0", "@qunhe/idp-yundesign": "1.70.0", "@qunhe/idp-yunrender": "1.70.0"}, "devDependencies": {"cross-env": "^7.0.3", "ts-node": "^10.3.0"}, "manual": "b4621b36-d023-43fd-8996-c3349306dd47"}