# 本地 SDK 管理工具

本目录包含用于本地开发和测试 SDK 的脚本工具，支持通过 Docker 容器进行 SDK 的生成、构建、测试和发布操作。

## 🚀 快速开始

### 1. 前置要求

- Docker 已安装并运行
- 具有项目 Docker 镜像的访问权限
- 项目根目录包含必要的配置文件（`ci-settings.xml`、OpenAPI 规范文件等）

### 2. 快速使用

使用 `quick-sdk.sh` 脚本进行快速操作：

```bash
# 生成所有服务的 SDK
./scripts/local/quick-sdk.sh gen

# 生成特定服务的 SDK
./scripts/local/quick-sdk.sh gen doorwindow
./scripts/local/quick-sdk.sh gen furniture

# 执行完整流程（生成 + 构建 + 测试）
./scripts/local/quick-sdk.sh full

# 构建 SDK
./scripts/local/quick-sdk.sh build

# 测试 SDK
./scripts/local/quick-sdk.sh test

# 部署快照版本
./scripts/local/quick-sdk.sh snap

# 预览操作（不实际执行）
./scripts/local/quick-sdk.sh dry
```

## 📋 详细使用说明

### 主要脚本

#### 1. `local-sdk-manager.sh` - 主要管理脚本

完整功能的 SDK 管理脚本，支持所有操作：

```bash
# 基本用法
./scripts/local/local-sdk-manager.sh <操作> [服务名]

# 可用操作
generate    # 生成 SDK
build       # 构建 SDK
test        # 测试 SDK
deploy-snap # 部署快照版本
deploy-rel  # 部署正式版本
all         # 执行完整流程

# 可选参数
--pull      # 拉取最新 Docker 镜像
--dry-run   # 预览命令，不实际执行
--help      # 显示帮助信息
```

#### 2. `quick-sdk.sh` - 快速操作脚本

简化的快捷命令脚本：

```bash
# 快速命令
gen [service]     # 生成 SDK
build [service]   # 构建 SDK  
test [service]    # 测试 SDK
full [service]    # 完整流程
snap [service]    # 部署快照
```

#### 3. `fix-openapi-issue.sh` - OpenAPI 修复脚本

修复 OpenAPI 规范文件中的常见问题：

```bash
# 修复所有问题
./scripts/local/fix-openapi-issue.sh

# 修复并测试特定服务
./scripts/local/fix-openapi-issue.sh doorwindow
```

#### 4. `debug-openapi.sh` - OpenAPI 调试脚本

验证和调试 OpenAPI 规范：

```bash
# 调试特定服务
./scripts/local/debug-openapi.sh doorwindow

# 验证 OpenAPI 规范格式
./scripts/local/debug-openapi.sh furniture
```

#### 5. `demo-add-service.sh` - 新服务演示脚本

演示如何添加新服务：

```bash
# 运行交互式演示
./scripts/local/demo-add-service.sh
```

### 支持的服务

脚本**自动发现** `openapi/` 目录下的所有服务目录。一个有效的服务目录必须包含：
- `restapi.yaml` - OpenAPI 规范文件
- `config-java.yaml` - Java SDK 生成配置文件

**当前发现的服务**：
- **doorwindow**: 门窗服务 SDK
- **furniture**: 家具设计服务 SDK
- **all**: 所有服务（默认）

**添加新服务**：
1. 在 `openapi/` 目录下创建新的服务目录
2. 添加必需的 `restapi.yaml` 和 `config-java.yaml` 文件
3. 脚本会自动识别并支持新服务

**跳过的目录**：
- `common/` - 公共 API 定义
- `shared/` - 共享配置文件

## 🔧 配置说明

### 环境配置

配置文件 `config.env` 包含了各种环境变量设置：

```bash
# Docker 镜像
DOCKER_IMAGE=registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest

# 服务配置
DOORWINDOW_RESTAPI=openapi/doorwindow/restapi.yaml
FURNITURE_RESTAPI=openapi/furniture/restapi.yaml

# 输出目录
DOORWINDOW_OUTPUT_JAVA=output/doorwindow/java
FURNITURE_OUTPUT_JAVA=output/furniture/java
```

### 发布认证配置

我们提供了几种更便捷的发布方式，避免每次都要手动输入长私钥：

#### 方式一：本地 GPG 密钥环（推荐）

**如果你已经有 GPG 密钥**，导入现有密钥：

```bash
# 1. 导入现有 GPG 密钥
./scripts/local/import-existing-gpg.sh

# 2. 编辑生成的配置文件
nano ~/.manycore-sdk-config

# 3. 加载配置并发布
source ~/.manycore-sdk-config
./scripts/local/publish.sh doorwindow
```

**如果需要生成新密钥**：

```bash
# 1. 生成新的 GPG 密钥
./scripts/local/setup-local-gpg.sh

# 2. 编辑生成的配置文件
nano ~/.manycore-sdk-config

# 3. 加载配置并发布
source ~/.manycore-sdk-config
./scripts/local/publish.sh doorwindow
```

#### 方式二：使用配置文件

```bash
# 1. 复制配置模板
cp scripts/local/config.env.template scripts/local/config.env

# 2. 编辑配置文件
nano scripts/local/config.env

# 3. 加载配置并发布
source scripts/local/config.env
./scripts/local/local-sdk-manager.sh deploy-rel doorwindow
```

#### 方式三：直接设置环境变量（不推荐）

用于对 JAR 文件进行数字签名：

```bash
export GPG_KEY_NAME="1234567890ABCDEF"      # GPG 密钥 ID
export GPG_PASSPHRASE="your-gpg-passphrase" # GPG 密钥密码
```

用于上传到 Maven Central Portal：

```bash
export CENTRAL_USERNAME="your-central-username"  # Portal 用户名
export CENTRAL_PASSWORD="your-central-token"     # Portal 密码/令牌
```

> **📖 详细设置指南**：请参考 [MAVEN_CENTRAL_SETUP.md](./MAVEN_CENTRAL_SETUP.md) 了解如何获取和配置这些认证信息。
> 
> **🔒 安全提醒**：配置文件包含敏感信息，已自动添加到 `.gitignore`，请妥善保管。

### 项目结构

```
backend-api-sdk/
├── openapi/                    # OpenAPI 规范文件
│   ├── doorwindow/
│   │   ├── restapi.yaml
│   │   └── config-java.yaml
│   └── furniture/
│       ├── restapi.yaml
│       └── config-java.yaml
├── output/                     # 生成的 SDK 输出目录
│   ├── doorwindow/
│   └── furniture/
├── ci-settings.xml            # Maven 配置文件
└── scripts/
    └── local/                 # 本地开发脚本
        ├── local-sdk-manager.sh
        ├── quick-sdk.sh
        ├── config.env
        └── README.md
```

## 💡 使用示例

### 动态服务发现

```bash
# 查看当前支持的所有服务
./scripts/local/local-sdk-manager.sh --help

# 列出发现的服务（通过错误信息查看）
./scripts/local/local-sdk-manager.sh generate invalid-service
```

### 添加新服务

```bash
# 1. 创建新服务目录
mkdir -p openapi/new-service

# 2. 添加必需文件
cp openapi/doorwindow/restapi.yaml openapi/new-service/
cp openapi/doorwindow/config-java.yaml openapi/new-service/

# 3. 编辑文件内容适配新服务...

# 4. 脚本会自动识别新服务
./scripts/local/quick-sdk.sh gen new-service
```

### 开发新功能时的典型工作流

```bash
# 1. 修改 OpenAPI 规范文件后，生成新的 SDK
./scripts/local/quick-sdk.sh gen doorwindow

# 2. 构建 SDK 确保没有编译错误
./scripts/local/quick-sdk.sh build doorwindow

# 3. 运行测试
./scripts/local/quick-sdk.sh test doorwindow

# 4. 如果一切正常，部署快照版本供测试使用
./scripts/local/quick-sdk.sh snap doorwindow
```

### 发布新版本

```bash
# 1. 执行完整的测试流程
./scripts/local/quick-sdk.sh full

# 2. 设置认证信息
export GPG_KEY_NAME="1234567890ABCDEF"
export GPG_PASSPHRASE="your-gpg-passphrase"
export CENTRAL_USERNAME="your-central-username"
export CENTRAL_PASSWORD="your-central-token"

# 3. 部署正式版本到 Maven Central
./scripts/local/local-sdk-manager.sh deploy-rel all

# 或者一行命令
GPG_KEY_NAME="1234567890ABCDEF" \
GPG_PASSPHRASE="your-passphrase" \
CENTRAL_USERNAME="your-username" \
CENTRAL_PASSWORD="your-token" \
./scripts/local/local-sdk-manager.sh deploy-rel all
```

### 预览操作

```bash
# 预览将要执行的 Docker 命令，不实际执行
./scripts/local/local-sdk-manager.sh --dry-run generate doorwindow
```

## 🐛 故障排除

### 常见问题

1. **OpenAPI 生成错误（ApiError 类型转换问题）**
   ```bash
   # 修复 OpenAPI 规范文件中的类型定义问题
   ./scripts/local/fix-openapi-issue.sh
   
   # 或者针对特定服务
   ./scripts/local/fix-openapi-issue.sh doorwindow
   ```

2. **Docker 镜像拉取失败**
   ```bash
   # 手动拉取镜像
   docker pull registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest
   ```

3. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x scripts/local/*.sh
   ```

4. **输出目录权限问题**
   ```bash
   # 清理输出目录
   rm -rf output/
   mkdir -p output/{doorwindow,furniture}/java
   ```

5. **Maven 配置问题**
   - 确保 `ci-settings.xml` 文件存在且配置正确
   - 检查 Maven 仓库访问权限

6. **OpenAPI 规范验证**
   ```bash
   # 使用调试脚本验证和调试 OpenAPI 规范
   ./scripts/local/debug-openapi.sh doorwindow
   ```

### 调试模式

使用 `--dry-run` 参数可以预览将要执行的命令：

```bash
./scripts/local/local-sdk-manager.sh --dry-run generate doorwindow
```

## 📝 注意事项

1. 首次使用时会自动拉取 Docker 镜像，可能需要一些时间
2. 生成的 SDK 会保存在 `output/` 目录中
3. 每次生成前会自动创建必要的输出目录
4. 使用 `--pull` 参数可以确保使用最新的 Docker 镜像
5. 部署操作需要正确的 Maven 仓库访问权限
6. 如果遇到 OpenAPI 生成错误，首先运行 `fix-openapi-issue.sh` 修复规范文件
7. 新增服务时确保 `restapi.yaml` 和 `config-java.yaml` 文件格式正确

## 🔗 相关文档

- [OpenAPI Generator 文档](https://openapi-generator.tech/)
- [Maven 部署指南](../ci/deployment/)
- [项目 CI/CD 流程](../../.gitlab-ci.yml) 