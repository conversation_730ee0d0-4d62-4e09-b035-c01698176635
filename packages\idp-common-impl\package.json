{"name": "@qunhe/idp-common-impl", "version": "1.70.1", "description": "> TODO: description", "maintainers": ["da<PERSON>o"], "author": {"name": "da<PERSON>o", "email": "<EMAIL>"}, "homepage": "", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/IDP.Math.git"}, "scripts": {"build": "node ../../scripts/build-package/build-impl", "build-package": "npm run build && tsc index.ts --outDir build --declaration && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "dependencies": {"@qunhe/idp-common": "1.70.0", "@qunhe/kls-abstraction": "~1.1.15", "@qunhe/kls-runtime": "~1.1.15", "lodash": "4.x"}, "devDependencies": {"copyfiles": "^2.4.1", "typescript": "^4.2.3"}}