{"id": "api/camera/camera-infrastructure-api", "title": "Camera Infrastructure API", "description": "相机基础设施 REST API", "source": "@site/docs/api/camera/camera-infrastructure-api.info.mdx", "sourceDirName": "api/camera", "slug": "/api/camera/camera-infrastructure-api", "permalink": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure-api", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "sidebarPosition": 0, "frontMatter": {"id": "camera-infrastructure-api", "title": "Camera Infrastructure API", "description": "相机基础设施 REST API", "sidebar_label": "Introduction", "sidebar_position": 0, "hide_title": true, "custom_edit_url": null}, "sidebar": "cameraSidebar", "next": {"title": "Camera Infrastructure", "permalink": "/manycoreapi-demo/0.0.4/docs/api/camera/camera-infrastructure"}}