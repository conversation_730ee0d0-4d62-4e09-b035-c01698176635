<!-- ci-settings.xml -->
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <servers>
    <!-- Server for Sonatype OSSRH (if needed) -->
    <server>
      <id>ossrh</id>
      <username>${env.SONATYPE_USERNAME}</username> <!-- Optional: if deploying snapshots to OSSRH -->
      <password>${env.SONATYPE_PASSWORD}</password> <!-- Optional: if deploying snapshots to OSSRH -->
    </server>
    <!-- Server for Central Portal API Token -->
    <server>
      <id>central</id>
      <!-- Use environment variables for Central Portal Token -->
      <username>${env.CENTRAL_USERNAME}</username>
      <password>${env.CENTRAL_PASSWORD}</password>
    </server>
  </servers>

   <!-- Proxy configuration - Commented out as it often interferes with Central publishing -->
   <!-- If you require a proxy, uncomment and configure appropriately for central.sonatype.com -->
   
   <proxies>
    <proxy>
      <id>clash-proxy</id>
      <active>true</active>
      <protocol>http</protocol>
      <host>127.0.0.1</host>
      <port>7890</port>
      <nonProxyHosts>localhost|*.local|*.qunhequnhe.com|central.sonatype.com|s01.oss.sonatype.org</nonProxyHosts>
    </proxy>
  </proxies>
  

  <mirrors>
    <!-- This mirror allows HTTP access specifically to your Nexus instance -->
    <mirror>
      <id>nexus-http-unblocker</id>
      <mirrorOf>qunhe-releases,qunhe-snapshots</mirrorOf> <!-- Match the repository IDs -->
      <name>Allow HTTP for internal Nexus</name>
      <url>http://nexus.qunhequnhe.com/repository/maven-public/</url> <!-- Use the public group URL -->

    </mirror>
    <!-- Optional: Add Central mirror if needed, but usually not required when deploying to Sonatype -->
    <!-- 
    <mirror>
      <id>maven-central-mirror</id>
      <mirrorOf>central</mirrorOf>
      <name>Maven Central Mirror (e.g., Aliyun)</name>
      <url>https://maven.aliyun.com/repository/central</url> 
    </mirror>
    -->
  </mirrors>

  <profiles>
    <!-- Profile to provide GPG passphrase -->
    <profile>
      <id>gpg</id>
      <properties>
        <gpg.passphrase>${env.GPG_KEYNAME}</gpg.passphrase>
        <gpg.passphrase>${env.GPG_PASSPHRASE}</gpg.passphrase>
      </properties>
    </profile>
    <profile>
      <id>nexus-repos</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <repositories>
        <repository>
          <id>qunhe-releases</id>
          <url>http://nexus.qunhequnhe.com/repository/maven-releases/</url>
          <releases><enabled>true</enabled></releases>
          <snapshots><enabled>false</enabled></snapshots>
        </repository>
        <repository>
          <id>qunhe-snapshots</id>
          <url>http://nexus.qunhequnhe.com/repository/maven-snapshots/</url>
          <releases><enabled>false</enabled></releases>
          <snapshots><enabled>true</enabled></snapshots>
        </repository>
      </repositories>
       <pluginRepositories>
         <pluginRepository>
           <id>qunhe-releases</id>
           <url>http://nexus.qunhequnhe.com/repository/maven-releases/</url>
           <releases><enabled>true</enabled></releases>
           <snapshots><enabled>false</enabled></snapshots>
         </pluginRepository>
         <pluginRepository>
           <id>qunhe-snapshots</id>
           <url>http://nexus.qunhequnhe.com/repository/maven-snapshots/</url>
           <releases><enabled>false</enabled></releases>
           <snapshots><enabled>true</enabled></snapshots>
         </pluginRepository>
       </pluginRepositories>
    </profile>
  </profiles>
</settings>