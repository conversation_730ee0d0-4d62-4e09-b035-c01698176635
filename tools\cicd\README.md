# Backend API SDK CI/CD 脚本

## 📋 概述

这是 Backend API SDK 项目的新一代 CI/CD 脚本集合，提供了模块化、可维护和高效的自动化构建、测试和部署解决方案。

## 🏗️ 架构设计

### 设计原则
- **模块化**：按功能分类，职责清晰
- **可复用**：通用函数库，减少重复代码
- **可配置**：环境变量驱动，灵活配置
- **可观测**：统一日志，完整监控
- **可扩展**：标准接口，易于扩展

### 目录结构
```
tools/cicd/
├── core/                       # 核心功能脚本
│   ├── environment.sh          # 环境检查和设置
│   ├── api-changes.sh          # API变更检测
│   └── validation.sh           # 各种验证功能
├── sdk/                        # SDK相关脚本
│   ├── generate.sh             # SDK生成
│   ├── build.sh               # SDK构建
│   ├── test.sh                # SDK测试
│   └── deploy.sh              # SDK部署
├── docs/                       # 文档相关脚本
│   ├── build.sh               # 文档构建
│   ├── preview.sh             # 文档预览
│   └── deploy.sh              # 文档部署
├── workflows/                  # 工作流编排脚本
│   ├── mr-pipeline.sh         # MR流程
│   ├── main-pipeline.sh       # 主分支流程
│   └── local-review.sh        # 本地评审
├── utils/                      # 工具脚本
│   ├── common.sh              # 通用函数库
│   ├── gitlab-api.sh          # GitLab API操作
│   └── docker-utils.sh        # Docker 工具
├── config/                     # 配置文件
│   ├── environments.conf      # 环境配置
│   └── sdk-config.conf        # SDK 配置
├── README.md                   # 本文档
├── MIGRATION_GUIDE.md          # 迁移指南
├── API.md                      # API 文档
└── CHANGELOG.md                # 变更日志
```

## 🎯 设计原则

### 1. 功能职责分离
- **core/**: 核心基础功能，被其他模块依赖
- **sdk/**: SDK 生命周期管理（生成→构建→测试→部署）
- **docs/**: 文档生命周期管理（构建→预览→部署）
- **workflows/**: 业务流程编排，组合调用其他模块
- **utils/**: 通用工具和函数库

### 2. 统一命名规范
- 使用 `动词.sh` 格式命名脚本
- 文件名简洁明了，避免过长
- 功能相似的脚本命名保持一致

### 3. 标准化接口
- 所有脚本都加载 `utils/common.sh`
- 统一的错误处理和日志输出
- 标准化的环境变量和参数传递

## 🔧 使用指南

### 基本用法

1. **加载通用函数库**
```bash
# 在脚本开头加载
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"
```

2. **初始化脚本**
```bash
init_script "脚本名称" "脚本描述"
```

3. **使用日志函数**
```bash
log_info "信息日志"
log_success "成功日志"
log_warning "警告日志"
log_error "错误日志"
```

4. **完成脚本**
```bash
finish_script "脚本名称" "true"  # 成功
finish_script "脚本名称" "false" # 失败
```

### 环境变量

#### 通用环境变量
- `DEBUG`: 设置为 `true` 启用调试模式
- `LOG_LEVEL`: 设置日志级别 (0=DEBUG, 1=INFO, 2=WARN, 3=ERROR)

#### GitLab CI 环境变量
- `CI_PROJECT_ID`: GitLab 项目 ID
- `CI_MERGE_REQUEST_IID`: MR 编号
- `GITLAB_TOKEN`: GitLab API Token
- `CI_COMMIT_BRANCH`: 当前分支
- `CI_COMMIT_TAG`: 当前标签

#### 服务相关环境变量
支持的服务: `camera`, `doorwindow`, `furniture`, `koolux`, `layout`, `pdm`

每个服务的环境变量格式：
- `{SERVICE}_RESTAPI`: OpenAPI 规范文件路径
- `{SERVICE}_CONFIG_JAVA`: Java SDK 配置文件路径
- `{SERVICE}_OUTPUT_JAVA`: Java SDK 输出路径

## 📋 脚本详细说明

### core/ 核心功能

#### environment.sh
- **功能**: 环境检查和设置
- **输入**: 无
- **输出**: 环境检查结果
- **用法**: `./core/environment.sh`

#### api-changes.sh
- **功能**: 检测 API 变更
- **输入**: Git 变更信息
- **输出**: 设置环境变量 `HAS_API_CHANGES`, `CHANGED_API_FILES`
- **用法**: `source ./core/api-changes.sh`

#### validation.sh
- **功能**: 验证 OpenAPI 规范
- **输入**: OpenAPI 文件路径
- **输出**: 验证结果
- **用法**: `./core/validation.sh [service_name]`

### sdk/ SDK 管理

#### generate.sh
- **功能**: 生成 SDK 代码
- **输入**: 服务名称或 "all"
- **输出**: 生成的 SDK 代码
- **用法**: `./sdk/generate.sh [service_name|all]`

#### build.sh
- **功能**: 构建 SDK
- **输入**: 服务名称或 "all"
- **输出**: 构建的 SDK 包
- **用法**: `./sdk/build.sh [service_name|all]`

#### test.sh
- **功能**: 测试 SDK
- **输入**: 服务名称或 "all"
- **输出**: 测试结果
- **用法**: `./sdk/test.sh [service_name|all]`

#### deploy.sh
- **功能**: 部署 SDK
- **输入**: 部署类型 (releases|snapshots)
- **输出**: 部署结果
- **用法**: `./sdk/deploy.sh [releases|snapshots]`

### docs/ 文档管理

#### build.sh
- **功能**: 构建文档
- **输入**: 构建模式 (normal|optimized)
- **输出**: 构建的文档
- **用法**: `./docs/build.sh [normal|optimized]`

#### preview.sh
- **功能**: 预览文档
- **输入**: 预览模式 (normal|optimized)
- **输出**: 预览服务器
- **用法**: `./docs/preview.sh [normal|optimized]`

#### deploy.sh
- **功能**: 部署文档
- **输入**: 部署环境
- **输出**: 部署结果
- **用法**: `./docs/deploy.sh [manual|production]`

### workflows/ 工作流

#### mr-pipeline.sh
- **功能**: MR 完整流程
- **输入**: MR 相关环境变量
- **输出**: MR 处理结果
- **用法**: `./workflows/mr-pipeline.sh`

#### main-pipeline.sh
- **功能**: 主分支流程
- **输入**: 主分支环境变量
- **输出**: 主分支处理结果
- **用法**: `./workflows/main-pipeline.sh`

#### local-review.sh
- **功能**: 本地评审模拟
- **输入**: 本地环境
- **输出**: 评审结果
- **用法**: `./workflows/local-review.sh`

### utils/ 工具脚本

#### common.sh
- **功能**: 通用函数库
- **包含**: 日志函数、环境检查、文件操作等
- **用法**: `source ./utils/common.sh`

#### logging.sh
- **功能**: 高级日志功能
- **包含**: 文件日志、进度条、统计信息等
- **用法**: `source ./utils/logging.sh`

#### gitlab-api.sh
- **功能**: GitLab API 操作
- **包含**: MR 评论、API 请求等
- **用法**: `./utils/gitlab-api.sh` 或 `source ./utils/gitlab-api.sh`

## 🚀 开发指南

### 新增脚本

1. **确定功能归属**
   - 核心功能 → `core/`
   - SDK 相关 → `sdk/`
   - 文档相关 → `docs/`
   - 工作流程 → `workflows/`
   - 通用工具 → `utils/`

2. **使用脚本模板**
```bash
#!/bin/bash

# 脚本描述
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 主函数
main() {
    init_script "脚本名称" "脚本描述"
    
    # 脚本逻辑
    log_info "开始执行..."
    
    # 错误处理
    if ! some_operation; then
        finish_script "脚本名称" "false"
        exit 1
    fi
    
    finish_script "脚本名称" "true"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

3. **遵循命名规范**
   - 文件名: `动词.sh`
   - 函数名: `snake_case`
   - 变量名: `UPPER_CASE` (环境变量) 或 `lower_case` (局部变量)

### 测试脚本

1. **单元测试**
```bash
# 测试单个脚本
./core/environment.sh
```

2. **集成测试**
```bash
# 测试完整工作流
./workflows/mr-pipeline.sh
```

3. **调试模式**
```bash
# 启用调试模式
DEBUG=true ./sdk/generate.sh
```

## 🔄 迁移指南

### 从旧结构迁移

1. **更新脚本引用**
   - 旧: `tools/ci-cd/ci/deployment/generate-sdks.sh`
   - 新: `tools/cicd/sdk/generate.sh`

2. **更新 GitLab CI 配置**
```yaml
# 旧配置
script:
  - chmod +x tools/ci-cd/ci/deployment/generate-sdks.sh
  - ./tools/ci-cd/ci/deployment/generate-sdks.sh

# 新配置
script:
  - chmod +x tools/cicd/sdk/generate.sh
  - ./tools/cicd/sdk/generate.sh
```

3. **更新环境变量**
   - 确保所有必需的环境变量已设置
   - 使用新的标准化变量名

## 📞 支持

如有问题或建议，请联系 Backend API Team 或在项目中创建 Issue。
