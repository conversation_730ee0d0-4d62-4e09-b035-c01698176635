'!include': '../../shared/shared-data-mappings.yaml'
generatorName: "java"
inputSpec: "specifications/services/pdm/openapi.yaml"
outputDir: "build/sdks/pdm/java"
templateDir: "generators/sdks/templates/java"
additionalProperties:
  groupId: "com.manycore"
  artifactId: "pdm-rest-client"
  artifactVersion: "0.0.1-SNAPSHOT"
  modelPackage: "com.manycore.pdm.client.model"
  apiPackage: "com.manycore.pdm.client.api"
  invokerPackage: "com.manycore.pdm.client"
  java8: true
  dateLibrary: "java8"
  library: "resttemplate"
  sourceFolder: "src/main/java"
  hideGenerationTimestamp: true
  serializationLibrary: "jackson"
  useRuntimeException: false

    

