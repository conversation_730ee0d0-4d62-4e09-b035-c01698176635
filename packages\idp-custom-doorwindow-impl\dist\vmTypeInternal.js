var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_IParamModelLite = injections.packages["@qunhe/custom-apass-api"]["IParamModelLite"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_Custom = {};
    var var_DoorWindow = {};
    var var_findDWModelByOpeningIds = {};
    var var_stringType_Array = {};
    var var_stringType = {};
    var var_DWModelData_Array = {};
    var var_DWModelData = {};
    var var_replaceDWModelAsync = {};
    var var_DWReplaceModelInfo_Array = {};
    var var_DWReplaceModelInfo = {};
    var var_DWReplaceModelInfo_options_objectLiteral = {};
    var var_DWReplaceResponse_Array_Promise = {};
    var var_DWReplaceResponse_Array_Promise_then = {};
    var var_DWReplaceResponse_Array_Promise_then_onresolve = {};
    var var_DWReplaceResponse_Array = {};
    var var_DWReplaceResponse = {};
    var var_booleanType = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_replaceDWModelV2Async = {};
    var var_DWReplaceModelInfoV2_Array = {};
    var var_DWReplaceModelInfoV2 = {};
    var var_DWReplaceModelInfoV2_options_objectLiteral = {};
    var var_DWReplaceModelInfoV2_options_params_objectLiteral_Array = {};
    var var_DWReplaceModelInfoV2_options_params_objectLiteral = {};
    var var_DWReplaceResponseV2_Array_Promise = {};
    var var_DWReplaceResponseV2_Array_Promise_then = {};
    var var_DWReplaceResponseV2_Array_Promise_then_onresolve = {};
    var var_DWReplaceResponseV2_Array = {};
    var var_DWReplaceResponseV2 = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_IParamModelLite, packageName: "@qunhe/custom-apass-api", exportName: "IParamModelLite" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "Custom": var_Custom,
    };
    var_Custom.type = BasicType.Object;
    var_Custom.properties = {
        "DoorWindow": var_DoorWindow,
    };
    var_DoorWindow.type = BasicType.Object;
    var_DoorWindow.properties = {
        "findDWModelByOpeningIds": var_findDWModelByOpeningIds,
        "replaceDWModelAsync": var_replaceDWModelAsync,
        "replaceDWModelV2Async": var_replaceDWModelV2Async,
    };
    var_findDWModelByOpeningIds.type = BasicType.Function;
    var_findDWModelByOpeningIds.name = "findDWModelByOpeningIds";
    var_findDWModelByOpeningIds.varying = false;
    var_findDWModelByOpeningIds.keepArgsHandle = false;
    var_findDWModelByOpeningIds.args = [var_stringType_Array];
    var_findDWModelByOpeningIds.return = var_DWModelData_Array;
    var_stringType_Array.type = BasicType.Array;
    var_stringType_Array.value = var_stringType;
    var_stringType.type = BasicType.String;
    var_DWModelData_Array.type = BasicType.Array;
    var_DWModelData_Array.value = var_DWModelData;
    var_DWModelData.type = BasicType.Object;
    var_DWModelData.properties = {
        "model": var_injection_IParamModelLite,
        "openingIds": var_stringType_Array,
    };
    var_replaceDWModelAsync.type = BasicType.Function;
    var_replaceDWModelAsync.name = "replaceDWModelAsync";
    var_replaceDWModelAsync.varying = false;
    var_replaceDWModelAsync.keepArgsHandle = false;
    var_replaceDWModelAsync.args = [var_DWReplaceModelInfo_Array];
    var_replaceDWModelAsync.return = var_DWReplaceResponse_Array_Promise;
    var_DWReplaceModelInfo_Array.type = BasicType.Array;
    var_DWReplaceModelInfo_Array.value = var_DWReplaceModelInfo;
    var_DWReplaceModelInfo.type = BasicType.Object;
    var_DWReplaceModelInfo.properties = {
        "modelId": var_stringType,
        "brandGoodId": var_stringType,
        "options": var_DWReplaceModelInfo_options_objectLiteral,
    };
    var_DWReplaceModelInfo_options_objectLiteral.type = BasicType.Object;
    var_DWReplaceModelInfo_options_objectLiteral.properties = {
        "lock": var_stringType,
        "material": var_stringType,
    };
    var_DWReplaceResponse_Array_Promise.type = BasicType.Object;
    var_DWReplaceResponse_Array_Promise.properties = {
        "then": var_DWReplaceResponse_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_DWReplaceResponse_Array_Promise_then.type = BasicType.Function;
    var_DWReplaceResponse_Array_Promise_then.name = "";
    var_DWReplaceResponse_Array_Promise_then.varying = false;
    var_DWReplaceResponse_Array_Promise_then.keepArgsHandle = true;
    var_DWReplaceResponse_Array_Promise_then.args = [var_DWReplaceResponse_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_DWReplaceResponse_Array_Promise_then.return = var_undefinedType;
    var_DWReplaceResponse_Array_Promise_then_onresolve.type = BasicType.Function;
    var_DWReplaceResponse_Array_Promise_then_onresolve.name = "";
    var_DWReplaceResponse_Array_Promise_then_onresolve.varying = false;
    var_DWReplaceResponse_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_DWReplaceResponse_Array_Promise_then_onresolve.args = [var_DWReplaceResponse_Array];
    var_DWReplaceResponse_Array_Promise_then_onresolve.return = var_undefinedType;
    var_DWReplaceResponse_Array.type = BasicType.Array;
    var_DWReplaceResponse_Array.value = var_DWReplaceResponse;
    var_DWReplaceResponse.type = BasicType.Object;
    var_DWReplaceResponse.properties = {
        "result": var_booleanType,
        "message": var_stringType,
    };
    var_booleanType.type = BasicType.Boolean;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_replaceDWModelV2Async.type = BasicType.Function;
    var_replaceDWModelV2Async.name = "replaceDWModelV2Async";
    var_replaceDWModelV2Async.varying = false;
    var_replaceDWModelV2Async.keepArgsHandle = false;
    var_replaceDWModelV2Async.args = [var_DWReplaceModelInfoV2_Array];
    var_replaceDWModelV2Async.return = var_DWReplaceResponseV2_Array_Promise;
    var_DWReplaceModelInfoV2_Array.type = BasicType.Array;
    var_DWReplaceModelInfoV2_Array.value = var_DWReplaceModelInfoV2;
    var_DWReplaceModelInfoV2.type = BasicType.Object;
    var_DWReplaceModelInfoV2.properties = {
        "modelIdPath": var_stringType_Array,
        "productId": var_stringType,
        "unInheritParams": var_stringType_Array,
        "options": var_DWReplaceModelInfoV2_options_objectLiteral,
    };
    var_DWReplaceModelInfoV2_options_objectLiteral.type = BasicType.Object;
    var_DWReplaceModelInfoV2_options_objectLiteral.properties = {
        "lock": var_stringType,
        "material": var_stringType,
        "params": var_DWReplaceModelInfoV2_options_params_objectLiteral_Array,
    };
    var_DWReplaceModelInfoV2_options_params_objectLiteral_Array.type = BasicType.Array;
    var_DWReplaceModelInfoV2_options_params_objectLiteral_Array.value = var_DWReplaceModelInfoV2_options_params_objectLiteral;
    var_DWReplaceModelInfoV2_options_params_objectLiteral.type = BasicType.Object;
    var_DWReplaceModelInfoV2_options_params_objectLiteral.properties = {
        "key": var_stringType,
        "value": var_stringType,
    };
    var_DWReplaceResponseV2_Array_Promise.type = BasicType.Object;
    var_DWReplaceResponseV2_Array_Promise.properties = {
        "then": var_DWReplaceResponseV2_Array_Promise_then,
        "catch": var_Promise_catch,
    };
    var_DWReplaceResponseV2_Array_Promise_then.type = BasicType.Function;
    var_DWReplaceResponseV2_Array_Promise_then.name = "";
    var_DWReplaceResponseV2_Array_Promise_then.varying = false;
    var_DWReplaceResponseV2_Array_Promise_then.keepArgsHandle = true;
    var_DWReplaceResponseV2_Array_Promise_then.args = [var_DWReplaceResponseV2_Array_Promise_then_onresolve, var_Promise_then_onreject];
    var_DWReplaceResponseV2_Array_Promise_then.return = var_undefinedType;
    var_DWReplaceResponseV2_Array_Promise_then_onresolve.type = BasicType.Function;
    var_DWReplaceResponseV2_Array_Promise_then_onresolve.name = "";
    var_DWReplaceResponseV2_Array_Promise_then_onresolve.varying = false;
    var_DWReplaceResponseV2_Array_Promise_then_onresolve.keepArgsHandle = false;
    var_DWReplaceResponseV2_Array_Promise_then_onresolve.args = [var_DWReplaceResponseV2_Array];
    var_DWReplaceResponseV2_Array_Promise_then_onresolve.return = var_undefinedType;
    var_DWReplaceResponseV2_Array.type = BasicType.Array;
    var_DWReplaceResponseV2_Array.value = var_DWReplaceResponseV2;
    var_DWReplaceResponseV2.type = BasicType.Object;
    var_DWReplaceResponseV2.properties = {
        "result": var_booleanType,
        "message": var_stringType,
    };
    var_DWReplaceResponse.properties = Object.assign({}, var_DWReplaceModelInfo.properties, var_DWReplaceResponse.properties);
    var_DWReplaceResponseV2.properties = Object.assign({}, var_DWReplaceModelInfoV2.properties, var_DWReplaceResponseV2.properties);
    
    return var_sourceFile;
};
