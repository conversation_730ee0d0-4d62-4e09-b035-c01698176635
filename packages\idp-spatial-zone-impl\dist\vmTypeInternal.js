var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_KFace3d = injections.packages["@qunhe/math-apaas-api"]["KFace3d"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Types = {};
    var var_Methods = {};
    var var_getAllZoneList = {};
    var var_Zone_Array = {};
    var var_Zone = {};
    var var_ElementId = {};
    var var_stringType = {};
    var var_Interaction = {};
    var var_openZoneAsync = {};
    var var_undefinedType_Promise = {};
    var var_undefinedType_Promise_then = {};
    var var_undefinedType_Promise_then_onresolve = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_changeZoneStyle = {};
    var var_ZoneStyle_Array = {};
    var var_ZoneStyle = {};
    var var_numberType = {};
    var var_ZoneStyle_labelStyle_objectLiteral = {};
    var var_ZoneStyle_labelStyle_position_objectLiteral = {};
    var var_ZoneStyle_labelStyle_anchor_objectLiteral = {};
    var var_numberType_Array = {};
    var var_startDrawZone = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_KFace3d, packageName: "@qunhe/math-apaas-api", exportName: "KFace3d" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
        "Interaction": var_Interaction,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
    };
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "getAllZoneList": var_getAllZoneList,
    };
    var_getAllZoneList.type = BasicType.Function;
    var_getAllZoneList.name = "getAllZoneList";
    var_getAllZoneList.varying = false;
    var_getAllZoneList.keepArgsHandle = false;
    var_getAllZoneList.args = [];
    var_getAllZoneList.return = var_Zone_Array;
    var_Zone_Array.type = BasicType.Array;
    var_Zone_Array.value = var_Zone;
    var_Zone.type = BasicType.Object;
    var_Zone.properties = {
        "id": var_ElementId,
        "profile": var_injection_KFace3d,
    };
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_Interaction.type = BasicType.Object;
    var_Interaction.properties = {
        "openZoneAsync": var_openZoneAsync,
        "changeZoneStyle": var_changeZoneStyle,
        "startDrawZone": var_startDrawZone,
    };
    var_openZoneAsync.type = BasicType.Function;
    var_openZoneAsync.name = "openZoneAsync";
    var_openZoneAsync.varying = false;
    var_openZoneAsync.keepArgsHandle = false;
    var_openZoneAsync.args = [];
    var_openZoneAsync.return = var_undefinedType_Promise;
    var_undefinedType_Promise.type = BasicType.Object;
    var_undefinedType_Promise.properties = {
        "then": var_undefinedType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_undefinedType_Promise_then.type = BasicType.Function;
    var_undefinedType_Promise_then.name = "";
    var_undefinedType_Promise_then.varying = false;
    var_undefinedType_Promise_then.keepArgsHandle = true;
    var_undefinedType_Promise_then.args = [var_undefinedType_Promise_then_onresolve, var_Promise_then_onreject];
    var_undefinedType_Promise_then.return = var_undefinedType;
    var_undefinedType_Promise_then_onresolve.type = BasicType.Function;
    var_undefinedType_Promise_then_onresolve.name = "";
    var_undefinedType_Promise_then_onresolve.varying = false;
    var_undefinedType_Promise_then_onresolve.keepArgsHandle = false;
    var_undefinedType_Promise_then_onresolve.args = [var_undefinedType];
    var_undefinedType_Promise_then_onresolve.return = var_undefinedType;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_changeZoneStyle.type = BasicType.Function;
    var_changeZoneStyle.name = "changeZoneStyle";
    var_changeZoneStyle.varying = false;
    var_changeZoneStyle.keepArgsHandle = false;
    var_changeZoneStyle.args = [var_ZoneStyle_Array];
    var_changeZoneStyle.return = var_undefinedType;
    var_ZoneStyle_Array.type = BasicType.Array;
    var_ZoneStyle_Array.value = var_ZoneStyle;
    var_ZoneStyle.type = BasicType.Object;
    var_ZoneStyle.properties = {
        "id": var_ElementId,
        "color": var_numberType,
        "labelStyle": var_ZoneStyle_labelStyle_objectLiteral,
    };
    var_numberType.type = BasicType.Number;
    var_ZoneStyle_labelStyle_objectLiteral.type = BasicType.Object;
    var_ZoneStyle_labelStyle_objectLiteral.properties = {
        "content": var_stringType,
        "position": var_ZoneStyle_labelStyle_position_objectLiteral,
        "rotation": var_numberType,
        "fontFamily": var_stringType,
        "fontSize": var_numberType,
        "color": var_numberType,
        "anchor": var_ZoneStyle_labelStyle_anchor_objectLiteral,
        "padding": var_numberType_Array,
        "backgroundColor": var_numberType,
    };
    var_ZoneStyle_labelStyle_position_objectLiteral.type = BasicType.Object;
    var_ZoneStyle_labelStyle_position_objectLiteral.properties = {
        "x": var_numberType,
        "y": var_numberType,
    };
    var_ZoneStyle_labelStyle_anchor_objectLiteral.type = BasicType.Object;
    var_ZoneStyle_labelStyle_anchor_objectLiteral.properties = {
        "x": var_numberType,
        "y": var_numberType,
    };
    var_numberType_Array.type = BasicType.Array;
    var_numberType_Array.value = var_numberType;
    var_startDrawZone.type = BasicType.Function;
    var_startDrawZone.name = "startDrawZone";
    var_startDrawZone.varying = false;
    var_startDrawZone.keepArgsHandle = false;
    var_startDrawZone.args = [];
    var_startDrawZone.return = var_undefinedType;
    
    return var_sourceFile;
};
