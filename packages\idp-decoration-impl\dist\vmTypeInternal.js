var BasicType = require('@qunhe/kls-abstraction').BasicType;
module.exports.createVMBindingType = function createVMBindingType(injections) {
    injections = injections || {};
    injections.types = injections.types || {};
    injections.packages = injections.packages || {};
    
    // pre-define all injection type variables
    var var_injection_KBoundedCurve3d = injections.packages["@qunhe/math-apaas-api"]["KBoundedCurve3d"];
    var var_injection_KSurface = injections.packages["@qunhe/math-apaas-api"]["KSurface"];
    
    // pre-define all (non-injection) type variables
    var var_sourceFile = {};
    var var_IDP = {};
    var var_DB = {};
    var var_Types = {};
    var var_PlanarModelingDesignType = {};
    var var_stringType = {};
    var var_Methods = {};
    var var_updateDecorationByTaskIdAsync = {};
    var var_booleanType_Promise = {};
    var var_booleanType_Promise_then = {};
    var var_booleanType_Promise_then_onresolve = {};
    var var_booleanType = {};
    var var_undefinedType = {};
    var var_Promise_then_onreject = {};
    var var_unknownType = {};
    var var_Promise_catch = {};
    var var_Promise_catch_onreject = {};
    var var_deleteMoldingById = {};
    var var_ElementId = {};
    var var_isProductInDecorationDesignAsync = {};
    var var_replaceDecorationProductAsync = {};
    var var_isDecorationProductAsync = {};
    var var_getFloorModelingExtrusionFaces = {};
    var var_DecorationFace3d_Array = {};
    var var_DecorationFace3d = {};
    var var_injection_KBoundedCurve3d_Array = {};
    var var_injection_KBoundedCurve3d_Array_Array = {};
    var var_getPlanarModelingDesignExtrusionFaces = {};
    
    // verify that all injections are correctly provided.
    var actualInjections = [
        { value: var_injection_KBoundedCurve3d, packageName: "@qunhe/math-apaas-api", exportName: "KBoundedCurve3d" },
        { value: var_injection_KSurface, packageName: "@qunhe/math-apaas-api", exportName: "KSurface" },
    ];
    var missingInjections = actualInjections.filter(item => !item.value);
    if (missingInjections.length) {
      throw new Error('missing vm type injections: ' + missingInjections.map(item => (item.packageName ? item.packageName + '.' : '') + item.exportName).join(', '));
    }
    var usedInjectionNamesFromTsdocTag = new Set(
      actualInjections
        .filter(item => !item.packageName)
        .map(item => item.exportName)
    );
    var unusedInjectionNamesFromTsdocTag = Object.keys(injections.types).filter(name => !usedInjectionNamesFromTsdocTag.has(name));
    if (unusedInjectionNamesFromTsdocTag.length) {
      throw new Error('unused vm type injections: ' + unusedInjectionNamesFromTsdocTag.join(', '));
    }
    
    // actually assign value to type variables
    var_sourceFile.type = BasicType.Object;
    var_sourceFile.properties = {
        "IDP": var_IDP,
    };
    var_IDP.type = BasicType.Object;
    var_IDP.properties = {
        "DB": var_DB,
    };
    var_DB.type = BasicType.Object;
    var_DB.properties = {
        "Types": var_Types,
        "Methods": var_Methods,
    };
    var_Types.type = BasicType.Object;
    var_Types.properties = {
        "PlanarModelingDesignType": var_PlanarModelingDesignType,
    };
    var_PlanarModelingDesignType.type = BasicType.Object;
    var_PlanarModelingDesignType.properties = {
        "Floor": var_stringType,
        "Ceiling": var_stringType,
    };
    var_stringType.type = BasicType.String;
    var_Methods.type = BasicType.Object;
    var_Methods.properties = {
        "updateDecorationByTaskIdAsync": var_updateDecorationByTaskIdAsync,
        "deleteMoldingById": var_deleteMoldingById,
        "isProductInDecorationDesignAsync": var_isProductInDecorationDesignAsync,
        "replaceDecorationProductAsync": var_replaceDecorationProductAsync,
        "isDecorationProductAsync": var_isDecorationProductAsync,
        "getFloorModelingExtrusionFaces": var_getFloorModelingExtrusionFaces,
        "getPlanarModelingDesignExtrusionFaces": var_getPlanarModelingDesignExtrusionFaces,
    };
    var_updateDecorationByTaskIdAsync.type = BasicType.Function;
    var_updateDecorationByTaskIdAsync.name = "updateDecorationByTaskIdAsync";
    var_updateDecorationByTaskIdAsync.varying = false;
    var_updateDecorationByTaskIdAsync.keepArgsHandle = false;
    var_updateDecorationByTaskIdAsync.args = [var_stringType];
    var_updateDecorationByTaskIdAsync.return = var_booleanType_Promise;
    var_booleanType_Promise.type = BasicType.Object;
    var_booleanType_Promise.properties = {
        "then": var_booleanType_Promise_then,
        "catch": var_Promise_catch,
    };
    var_booleanType_Promise_then.type = BasicType.Function;
    var_booleanType_Promise_then.name = "";
    var_booleanType_Promise_then.varying = false;
    var_booleanType_Promise_then.keepArgsHandle = true;
    var_booleanType_Promise_then.args = [var_booleanType_Promise_then_onresolve, var_Promise_then_onreject];
    var_booleanType_Promise_then.return = var_undefinedType;
    var_booleanType_Promise_then_onresolve.type = BasicType.Function;
    var_booleanType_Promise_then_onresolve.name = "";
    var_booleanType_Promise_then_onresolve.varying = false;
    var_booleanType_Promise_then_onresolve.keepArgsHandle = false;
    var_booleanType_Promise_then_onresolve.args = [var_booleanType];
    var_booleanType_Promise_then_onresolve.return = var_undefinedType;
    var_booleanType.type = BasicType.Boolean;
    var_undefinedType.type = BasicType.Undefined;
    var_Promise_then_onreject.type = BasicType.Function;
    var_Promise_then_onreject.name = "";
    var_Promise_then_onreject.varying = false;
    var_Promise_then_onreject.keepArgsHandle = false;
    var_Promise_then_onreject.args = [var_unknownType];
    var_Promise_then_onreject.return = var_undefinedType;
    var_unknownType.type = BasicType.Unknown;
    var_Promise_catch.type = BasicType.Function;
    var_Promise_catch.name = "";
    var_Promise_catch.varying = false;
    var_Promise_catch.keepArgsHandle = false;
    var_Promise_catch.args = [var_Promise_catch_onreject];
    var_Promise_catch.return = var_undefinedType;
    var_Promise_catch_onreject.type = BasicType.Function;
    var_Promise_catch_onreject.name = "";
    var_Promise_catch_onreject.varying = false;
    var_Promise_catch_onreject.keepArgsHandle = false;
    var_Promise_catch_onreject.args = [var_unknownType];
    var_Promise_catch_onreject.return = var_undefinedType;
    var_deleteMoldingById.type = BasicType.Function;
    var_deleteMoldingById.name = "deleteMoldingById";
    var_deleteMoldingById.varying = false;
    var_deleteMoldingById.keepArgsHandle = false;
    var_deleteMoldingById.args = [var_ElementId];
    var_deleteMoldingById.return = var_booleanType;
    var_ElementId.type = BasicType.Object;
    var_ElementId.properties = {
        "id": var_stringType,
        "type": var_stringType,
    };
    var_isProductInDecorationDesignAsync.type = BasicType.Function;
    var_isProductInDecorationDesignAsync.name = "isProductInDecorationDesignAsync";
    var_isProductInDecorationDesignAsync.varying = false;
    var_isProductInDecorationDesignAsync.keepArgsHandle = false;
    var_isProductInDecorationDesignAsync.args = [var_stringType];
    var_isProductInDecorationDesignAsync.return = var_booleanType_Promise;
    var_replaceDecorationProductAsync.type = BasicType.Function;
    var_replaceDecorationProductAsync.name = "replaceDecorationProductAsync";
    var_replaceDecorationProductAsync.varying = false;
    var_replaceDecorationProductAsync.keepArgsHandle = false;
    var_replaceDecorationProductAsync.args = [var_stringType, var_stringType];
    var_replaceDecorationProductAsync.return = var_booleanType_Promise;
    var_isDecorationProductAsync.type = BasicType.Function;
    var_isDecorationProductAsync.name = "isDecorationProductAsync";
    var_isDecorationProductAsync.varying = false;
    var_isDecorationProductAsync.keepArgsHandle = false;
    var_isDecorationProductAsync.args = [var_stringType];
    var_isDecorationProductAsync.return = var_booleanType_Promise;
    var_getFloorModelingExtrusionFaces.type = BasicType.Function;
    var_getFloorModelingExtrusionFaces.name = "getFloorModelingExtrusionFaces";
    var_getFloorModelingExtrusionFaces.varying = false;
    var_getFloorModelingExtrusionFaces.keepArgsHandle = false;
    var_getFloorModelingExtrusionFaces.args = [];
    var_getFloorModelingExtrusionFaces.return = var_DecorationFace3d_Array;
    var_DecorationFace3d_Array.type = BasicType.Array;
    var_DecorationFace3d_Array.value = var_DecorationFace3d;
    var_DecorationFace3d.type = BasicType.Object;
    var_DecorationFace3d.properties = {
        "id": var_stringType,
        "contour": var_injection_KBoundedCurve3d_Array,
        "holes": var_injection_KBoundedCurve3d_Array_Array,
        "surface": var_injection_KSurface,
    };
    var_injection_KBoundedCurve3d_Array.type = BasicType.Array;
    var_injection_KBoundedCurve3d_Array.value = var_injection_KBoundedCurve3d;
    var_injection_KBoundedCurve3d_Array_Array.type = BasicType.Array;
    var_injection_KBoundedCurve3d_Array_Array.value = var_injection_KBoundedCurve3d_Array;
    var_getPlanarModelingDesignExtrusionFaces.type = BasicType.Function;
    var_getPlanarModelingDesignExtrusionFaces.name = "getPlanarModelingDesignExtrusionFaces";
    var_getPlanarModelingDesignExtrusionFaces.varying = false;
    var_getPlanarModelingDesignExtrusionFaces.keepArgsHandle = false;
    var_getPlanarModelingDesignExtrusionFaces.args = [var_stringType];
    var_getPlanarModelingDesignExtrusionFaces.return = var_DecorationFace3d_Array;
    
    return var_sourceFile;
};
