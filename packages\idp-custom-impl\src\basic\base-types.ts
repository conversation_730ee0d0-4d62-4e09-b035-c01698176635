import { BasicType } from '@qunhe/kls-abstraction';

/**
 * 目前存在一些ts类型无法自动解析vm-type，先手动指定vm-type
 * - 未支持的类型，暂无
 * - 处理为unknownType，但是有函数成员的类型(函数无法序列化，unknownType无法根据值推导vm-type)，例如：泛型
 */

export const StringType = {
    type: BasicType.String,
};

export const BooleanType = {
    type: BasicType.Boolean,
};

export const NumberType = {
    type: BasicType.Number,
};

export const UnknownType = {
    type: BasicType.Unknown,
};

export const UndefinedType = {
    type: BasicType.Undefined,
};
