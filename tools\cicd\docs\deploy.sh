#!/bin/bash

# 文档部署脚本
# 部署文档到群核科技 Manual 网站
# 重构自 deploy-to-manual.sh
# 作者: Backend API Team

# 加载通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/common.sh"

# 部署配置
DOCS_CONFIG_FILE="${DOCS_CONFIG_FILE:-documentation/website/docusaurus.config.ts}"
DOCS_LEGACY_CONFIG="${DOCS_LEGACY_CONFIG:-docs-site/docusaurus.config.ts}"
NPM_REGISTRY="${NPM_REGISTRY:-http://npm-registry.qunhequnhe.com}"

# 验证部署环境
verify_deploy_environment() {
    log_step "1" "验证部署环境"
    
    # 检查项目根目录
    if [ ! -f "$DOCS_CONFIG_FILE" ]; then
        if [ -f "$DOCS_LEGACY_CONFIG" ]; then
            log_warning "发现旧路径配置文件: $DOCS_LEGACY_CONFIG"
            log_warning "建议更新到新路径: $DOCS_CONFIG_FILE"
            DOCS_CONFIG_FILE="$DOCS_LEGACY_CONFIG"
        else
            log_error "未找到 Docusaurus 配置文件"
            log_error "查找路径: $DOCS_CONFIG_FILE"
            log_error "当前目录: $(pwd)"
            return 1
        fi
    fi
    
    # 检查 npm
    if ! command -v npm >/dev/null 2>&1; then
        log_error "npm 未安装"
        return 1
    fi
    
    # 检查 kjl 工具
    if ! command -v kjl >/dev/null 2>&1; then
        log_error "kjl 工具未找到，请检查 Docker 镜像配置"
        return 1
    fi
    
    local kjl_version=$(kjl --version 2>/dev/null || echo "unknown")
    log_success "kjl 工具验证成功: $kjl_version"
    
    log_success "部署环境验证通过"
    return 0
}

# 配置部署参数
configure_deployment() {
    log_step "2" "配置部署参数"
    
    # 获取部署环境
    DEPLOY_ENV="${MANUAL_ENV:-dev}"
    DEPLOY_VERSION="${MANUAL_VERSION:-}"
    
    # 从 Git 信息生成版本号
    if [ -z "$DEPLOY_VERSION" ]; then
        if [ -n "$CI_COMMIT_TAG" ]; then
            # 如果是 tag 构建，使用 tag 作为版本号
            DEPLOY_VERSION="$CI_COMMIT_TAG"
            DEPLOY_ENV="prod"
            log_info "检测到 Git Tag: $CI_COMMIT_TAG"
        elif [[ "$CI_COMMIT_BRANCH" == "master" ]] || [[ "$CI_COMMIT_BRANCH" == "main" ]]; then
            # 主分支使用时间戳版本
            DEPLOY_VERSION="1.0.0-$(date +%Y%m%d%H%M%S)"
            DEPLOY_ENV="staging"
            log_info "检测到主分支构建"
        else
            # 其他分支使用分支名和 commit 信息
            local branch_name=$(echo "${CI_COMMIT_BRANCH:-dev}" | sed 's/[^a-zA-Z0-9.-]/-/g')
            local commit_short="${CI_COMMIT_SHORT_SHA:-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')}"
            DEPLOY_VERSION="0.0.1-${branch_name}-${commit_short}"
            DEPLOY_ENV="dev"
            log_info "检测到功能分支构建"
        fi
    fi
    
    # 显示 CI 环境信息
    log_info "CI 环境变量:"
    echo "  CI_COMMIT_BRANCH: ${CI_COMMIT_BRANCH:-'(未设置)'}"
    echo "  CI_COMMIT_TAG: ${CI_COMMIT_TAG:-'(未设置)'}"
    echo "  CI_COMMIT_SHORT_SHA: ${CI_COMMIT_SHORT_SHA:-'(未设置)'}"
    echo "  CI_MERGE_REQUEST_IID: ${CI_MERGE_REQUEST_IID:-'(未设置)'}"
    
    log_info "部署配置:"
    echo "  版本: $DEPLOY_VERSION"
    echo "  环境: $DEPLOY_ENV"
    
    log_success "部署参数配置完成"
    return 0
}

# 配置 npm registry
configure_npm_registry() {
    log_step "3" "配置 npm registry"
    
    log_info "配置群核科技私有 npm registry: $NPM_REGISTRY"
    
    if npm config set @qunhe:registry "$NPM_REGISTRY"; then
        log_success "npm registry 配置成功"
        increment_counter "npm_configured"
        return 0
    else
        log_error "npm registry 配置失败"
        return 1
    fi
}

# kjl 工具认证
authenticate_kjl() {
    log_step "4" "kjl 工具认证"
    
    # 检查是否在 CI 环境中
    if [ -z "$CI" ]; then
        log_info "非 CI 环境，跳过自动认证"
        log_warning "请手动执行 'kjl login' 进行认证"
        return 0
    fi
    
    # 检查认证信息
    if [ -n "$KJL_USERNAME" ] && [ -n "$KJL_PASSWORD" ]; then
        log_info "使用环境变量中的认证信息进行 kjl 自动登录"
        log_info "用户名: $KJL_USERNAME"
        
        # 执行登录
        log_info "执行 kjl 登录..."
        if kjl login -u "$KJL_USERNAME" -p "$KJL_PASSWORD" >/dev/null 2>&1; then
            log_success "kjl 登录成功"
            
            # 短暂等待登录完成
            sleep 2
            
            # 验证登录状态
            log_info "验证登录状态和 manual 权限..."
            if kjl manual:upload --help >/dev/null 2>&1; then
                log_success "kjl 登录验证成功，manual:upload 权限可用"
                increment_counter "kjl_authenticated"
                return 0
            else
                log_error "kjl 登录验证失败，manual:upload 权限不可用"
                return 1
            fi
        else
            log_error "kjl 登录失败，请检查用户名和密码"
            return 1
        fi
    elif [ -n "$KJL_TOKEN" ]; then
        log_error "当前脚本版本暂不支持 Token 认证，请使用用户名密码方式"
        log_error "请在 GitLab CI Variables 中设置："
        log_error "  KJL_USERNAME = 你的LDAP用户名"
        log_error "  KJL_PASSWORD = 你的LDAP密码 (设置为Masked)"
        return 1
    else
        log_error "CI 环境中缺少认证信息"
        log_error "请在 GitLab CI Variables 中设置以下环境变量："
        log_error "  KJL_USERNAME = 你的LDAP用户名"
        log_error "  KJL_PASSWORD = 你的LDAP密码 (设置为Masked)"
        log_error ""
        log_error "设置步骤："
        log_error "1. 访问 GitLab 项目 > Settings > CI/CD > Variables"
        log_error "2. 添加变量 KJL_USERNAME (值: 你的LDAP用户名)"
        log_error "3. 添加变量 KJL_PASSWORD (值: 你的LDAP密码, 设置为Protected和Masked)"
        return 1
    fi
}

# 构建文档
build_documentation() {
    log_step "5" "构建文档"
    
    # 调用文档构建脚本
    local build_script="$SCRIPT_DIR/build.sh"
    
    if [ ! -f "$build_script" ]; then
        log_error "文档构建脚本不存在: $build_script"
        return 1
    fi
    
    log_info "调用文档构建脚本..."
    if "$build_script" "optimized"; then
        log_success "文档构建完成"
        increment_counter "build_success"
        return 0
    else
        log_error "文档构建失败"
        increment_counter "build_failed"
        return 1
    fi
}

# 部署文档
deploy_documentation() {
    log_step "6" "部署文档"
    
    local docs_dir="${DOCS_DIR:-docs-site}"
    local build_dir="$docs_dir/build"
    
    # 检查构建输出
    if [ ! -d "$build_dir" ]; then
        log_error "构建输出目录不存在: $build_dir"
        return 1
    fi
    
    # 执行部署
    log_info "开始部署文档到 Manual 网站..."
    log_info "部署版本: $DEPLOY_VERSION"
    log_info "部署环境: $DEPLOY_ENV"
    
    local start_time=$(get_timestamp)
    
    # 构建 kjl 部署命令
    local deploy_cmd="kjl manual:upload"
    deploy_cmd="$deploy_cmd --source $build_dir"
    deploy_cmd="$deploy_cmd --version $DEPLOY_VERSION"
    deploy_cmd="$deploy_cmd --env $DEPLOY_ENV"
    
    log_debug "执行命令: $deploy_cmd"
    
    if $deploy_cmd; then
        local end_time=$(get_timestamp)
        local duration=$((end_time - start_time))
        
        log_success "文档部署成功 (耗时: ${duration}s)"
        increment_counter "deploy_success"
        
        # 生成访问链接
        local access_url=""
        case "$DEPLOY_ENV" in
            "prod")
                access_url="https://manual.qunhequnhe.com/manycoreapi/"
                ;;
            "staging")
                access_url="https://manual.qunhequnhe.com/manycoreapi-staging/"
                ;;
            "dev")
                access_url="https://manual.qunhequnhe.com/manycoreapi-dev/"
                ;;
        esac
        
        if [ -n "$access_url" ]; then
            log_success "文档访问地址: $access_url"
        fi
        
        return 0
    else
        log_error "文档部署失败"
        increment_counter "deploy_failed"
        
        # 显示错误详情
        log_info "错误详情:"
        $deploy_cmd 2>&1 | tail -10
        return 1
    fi
}

# 显示部署统计
show_deploy_stats() {
    log_step "7" "显示部署统计"
    
    echo ""
    log_info "📚 文档部署结果统计:"
    
    local npm_configured=$(get_counter "npm_configured")
    local kjl_authenticated=$(get_counter "kjl_authenticated")
    local build_success=$(get_counter "build_success")
    local build_failed=$(get_counter "build_failed")
    local deploy_success=$(get_counter "deploy_success")
    local deploy_failed=$(get_counter "deploy_failed")
    
    echo "  部署版本: $DEPLOY_VERSION"
    echo "  部署环境: $DEPLOY_ENV"
    echo "  npm 配置: $([ $npm_configured -gt 0 ] && echo "成功" || echo "失败")"
    echo "  kjl 认证: $([ $kjl_authenticated -gt 0 ] && echo "成功" || echo "跳过")"
    echo "  文档构建: $([ $build_success -gt 0 ] && echo "成功" || echo "失败")"
    echo "  文档部署: $([ $deploy_success -gt 0 ] && echo "成功" || echo "失败")"
    echo "  部署时间: $(date)"
    
    show_stats "文档部署统计"
}

# 主函数
main() {
    init_script "文档部署" "部署文档到群核科技 Manual 网站"
    
    local deploy_status=0
    
    # 执行部署流程
    if ! verify_deploy_environment; then
        deploy_status=1
    elif ! configure_deployment; then
        deploy_status=1
    elif ! configure_npm_registry; then
        deploy_status=1
    elif ! authenticate_kjl; then
        deploy_status=1
    elif ! build_documentation; then
        deploy_status=1
    elif ! deploy_documentation; then
        deploy_status=1
    fi
    
    show_deploy_stats
    
    if [ $deploy_status -eq 0 ]; then
        log_success "🎉 文档部署完成！"
        finish_script "文档部署" "true"
        return 0
    else
        log_error "❌ 文档部署失败，请检查错误信息"
        finish_script "文档部署" "false"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

log_info "文档部署模块已加载 ✅"
