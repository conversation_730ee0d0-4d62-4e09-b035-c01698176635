#!/bin/bash

# 简化的发布脚本 - 直接从 config.env 读取配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 显示帮助信息
show_help() {
    cat << EOF
🚀 ManyCore SDK 简化发布工具

用法: $0 [选项] <服务名>

服务名:
  - doorwindow
  - furniture  
  - all (所有服务)

选项:
  --dry-run    预览操作，不实际执行
  --help       显示此帮助信息

示例:
  $0 doorwindow              # 发布 doorwindow 服务
  $0 all                     # 发布所有服务
  $0 --dry-run furniture     # 预览发布 furniture 服务

配置文件: scripts/local/config.env

EOF
}

# 检查配置文件
check_config() {
    local config_file="$SCRIPT_DIR/config.env"
    
    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        echo ""
        print_status "请先创建配置文件："
        echo "1. 复制模板: cp $SCRIPT_DIR/config.env.template $config_file"
        echo "2. 编辑配置: nano $config_file"
        echo "3. 填写GPG私钥和Maven Central认证信息"
        exit 1
    fi
    
    # 加载配置
    source "$config_file"
    
    # 验证必要的配置
    local missing=0
    
    if [ -z "$GPG_KEY_NAME" ]; then
        print_error "GPG_KEY_NAME 未设置"
        missing=1
    fi
    
    if [ -z "$GPG_PASSPHRASE" ]; then
        print_error "GPG_PASSPHRASE 未设置"
        missing=1
    fi
    
    if [ -z "$GPG_PRIVATE_KEY" ]; then
        print_error "GPG_PRIVATE_KEY 未设置"
        missing=1
    fi
    
    if [ -z "$CENTRAL_USERNAME" ]; then
        print_error "CENTRAL_USERNAME 未设置"
        missing=1
    fi
    
    if [ -z "$CENTRAL_PASSWORD" ]; then
        print_error "CENTRAL_PASSWORD 未设置"
        missing=1
    fi
    
    if [ $missing -eq 1 ]; then
        print_error "配置不完整，请编辑 $config_file"
        exit 1
    fi
    
    print_success "配置验证通过"
}

# 显示配置摘要
show_config_summary() {
    echo ""
    print_status "当前配置摘要:"
    echo "  GPG Key: $GPG_KEY_NAME"
    echo "  GPG Passphrase: $([ -n "$GPG_PASSPHRASE" ] && echo "已设置" || echo "未设置")"
    echo "  GPG Private Key: $([ -n "$GPG_PRIVATE_KEY" ] && echo "${GPG_PRIVATE_KEY:0:20}..." || echo "未设置")"
    echo "  Central User: $CENTRAL_USERNAME"
    echo "  Central Password: $([ -n "$CENTRAL_PASSWORD" ] && echo "${CENTRAL_PASSWORD:0:8}..." || echo "未设置")"
    echo ""
}

# 主函数
main() {
    local service=""
    local dry_run=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run="--dry-run"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$service" ]; then
                    service="$1"
                else
                    print_error "只能指定一个服务名"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查服务名参数
    if [ -z "$service" ]; then
        print_error "请指定要发布的服务名"
        show_help
        exit 1
    fi
    
    echo "🚀 ManyCore SDK 简化发布工具"
    echo "=============================="
    echo ""
    
    # 检查和加载配置
    check_config
    
    # 显示配置摘要
    show_config_summary
    
    # 确认发布
    if [ -z "$dry_run" ]; then
        echo "🎯 准备发布服务: $service"
        read -p "确认继续？(y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_warning "发布已取消"
            exit 0
        fi
        echo ""
    fi
    
    # 执行发布
    print_status "开始发布流程..."
    cd "$PROJECT_ROOT"
    
    # 导出环境变量供 local-sdk-manager.sh 使用
    export GPG_KEY_NAME
    export GPG_PASSPHRASE  
    export GPG_PRIVATE_KEY
    export CENTRAL_USERNAME
    export CENTRAL_PASSWORD
    
    if ! "$SCRIPT_DIR/local-sdk-manager.sh" $dry_run deploy-rel "$service"; then
        print_error "发布失败"
        exit 1
    fi
    
    if [ -z "$dry_run" ]; then
        print_success "🎉 发布完成！"
        echo ""
        print_status "后续操作："
        echo "  - 检查 Maven Central Portal: https://central.sonatype.com/"
        echo "  - 等待同步到 Maven Central Repository (通常需要10-30分钟)"
        echo "  - 验证发布: mvn dependency:get -Dartifact=com.manycoreapis:$service-sdk:版本号"
    else
        print_success "预览完成"
    fi
}

main "$@" 