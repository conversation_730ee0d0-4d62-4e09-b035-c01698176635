- 小程序开发文档参考 [酷家乐开放平台 - 小程序](https://open.kujiale.com/pub/saas/open-platform/doc-detail?app_id=15&doc_tab=doc&tab_id=15&node_id=1282&node_type=1&tree_tab=a)，这些功能对二方、三方均适用
    > 注：小程序接口区分了三方（酷家乐外部）和二方（酷家乐内部），此文档是基于三方开发者视角。
    > 对于二方开发者，额外事项：
    >   - 将各处的 `@manycore/idp-sdk` 替换为 `@qunhe/idp-sdk-internal`（注意同时更新 package 版本，选择正式版或 rc 预发版的最新版本）
- 此外，针对二方小程序还有一些特殊支持，参考 [二方小程序补充开发文档](./二方小程序补充开发文档/README.md)
