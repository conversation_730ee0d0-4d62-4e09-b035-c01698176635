在工具中，需要对小程序 API （函数）的调用进行鉴权与埋点统计。

## API 标识

为了明确地知道哪些 API 被禁用、哪些 API 被调用，我们需要 API 标识。目前，框架会尽可能地为各个业务方注册的 API 生成标识。部分无法自动推断的孤立节点，需要对应业务方在对应根节点添加自定义标识，延续框架自动生成标识逻辑。

> 框架会跳过缺少标识的 API 的鉴权与埋点统计逻辑

### 标识生成规则

简单概括：
- 当前节点默认标识 = `${父节点标识}.${属性名}`
- 业务方可以为指定节点添加自定义标识，覆盖默认标识
- IDP 节点的标识为`IDP`

最终效果类似路径调用，如 [getDesignId](API_LINK:IDP.Design.getDesignId) 标识为`IDP.Design.getDesignId`。

### 添加自定义标识

以 IDP 为根节点的 API，框架能够自动为其生成合理的标识，但对于孤立节点，框架无法自动为它以及它的属性、方法生成**合理**的标识。

以 Math 提供的 API [createArc2dByArcAngle](API_LINK:IDP.Math.KGeomLib.createArc2dByArcAngle) 为例，它的标识为 `IDP.Math.KGeomLib.createArc2dByArcAngle`，调用它创建的 [KArc2d](API_LINK:UNEXPORTED.KArc2d) 是孤立节点。如果不做任何处理，其成员方法 [getTangent](API_LINK:UNEXPORTED.KArc2d.getTangent) 将被框架跳过鉴权与埋点统计。正确的接入方法是：业务方手动为 [KArc2d](API_LINK:UNEXPORTED.KArc2d) 节点添加标识`KArc2d`，这样框架就能继续遍历拼接，为 [getTangent](API_LINK:UNEXPORTED.KArc2d.getTangent) 生成标识`KArc2d.getTangent`。

> 通常，位于 [UNEXPORTED](API_LINK:UNEXPORTED) 下的就是孤立节点，其主要使用场景是返回值。

当然，不是所有孤立节点都值得“标识”，目前仅推荐：
- 当类作为返回值时，比如上面 Math 的例子
- 当重要函数作为返回值时，比如 disposer

API 标识位于 VmType 上，VmType 有手写和自动两种生成方式，框架均提供了相应的添加自定义标识支持。

#### 自动生成 VmType

支持对 interface、function 添加注释 `@vm-id xxx`：

```ts
/**
 * @vm-id A
 */
interface A {
    getA(): number;
}

declare namespace Test {
    function createA(): A;
}
```

#### 手动书写 VmType

`ObjectTypeBuilder`、`FunctionTypeBuilder`提供`withId`方法：
```ts
manager.registerApi(() => {
    return {
        namespace: 'Test',
        value: {
            createA: () => new A()
        },
        type: new ObjectTypeBuilder()
            .addFunction('createA', fn => fn
                .withName('createA')
                .withObjectReturn((builder => builder
                    .withId('A') // <----添加自定义标识
                    .addFunction('getA', fnA => fnA.withName('getA').withNumberReturn())
                ))
            )
            .build()
    };
});
```

`Type`新增可选属性`id`：
```ts
manager.registerApi(() => {
    return {
        namespace: 'Test',
        value: {
            createA: () => new A()
        },
        type: {
            type: BasicType.Object,
            properties: {
                createA: {
                    name: 'createA',
                    varying: false,
                    keepArgsHandle: false,
                    type: BasicType.Function,
                    args: [],
                    return: {
                        type: BasicType.Object,
                        id: 'A', // <----添加自定义标识
                        properties: {
                            getA: {
                                name: 'getA',
                                varying: false,
                                keepArgsHandle: false,
                                type: BasicType.Function,
                                args: [],
                                return: {
                                    type: BasicType.Number
                                }
                            } as FunctionType
                        }
                    }
                } as FunctionType
            }
        }
    };
});
```

## 埋点

### 验证埋点

触发 API 函数调用，Network 过滤 appCoreMiniappLifecycle：

![验证埋点](./assets/api.png)

### 数据分析

tesseract 事件分析选择工具小程序使用埋点：

![数据分析](./assets/log.png)

## 鉴权

对于高阶、核心 API，我们需要进行适当的权限管理（配合售卖）。

### 录入 API

权限点的生成、分配由开放平台控制，我们需要将待鉴权的 API 的**标识**提供给开放平台（沙皇）进行 API 录入。

如何获取 API 标识，参考标识生成规则，或通过观察埋点请求，获取精确的标识。

### 调试鉴权

API 鉴权使用黑名单模式，对于新录入的 API，已有的普通小程序均无调用权限（需要在开放平台为该小程序开通对应权限）。

#### 禁止调用测试

> **工具测试小程序** 是普通小程序，无新录入的 API 的权限
>
> **playground** 是通过 pub 配置特殊注入的小程序，拥有所有权限

1. 启动工具测试小程序

![启动工具测试小程序](./assets/forbidden1.png)

2. 输入对应 API，运行

![输入对应 API](./assets/forbidden2.png)

### Deprecated

[API 权限对接（Draft）](https://cf.qunhequnhe.com/pages/viewpage.action?pageId=80351165297) 提供了一种手动鉴权的方式，目前已被自动鉴权覆盖，废弃。

旧逻辑仍将持续运行，之前对接过手动鉴权的业务方，可以自行安排移除时间。
