#!/bin/bash

# 本地 Maven 发布脚本 - 无需 Docker，支持加速镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 服务配置
declare -A SERVICE_CONFIG=(
    ["doorwindow"]="openapi/doorwindow/config-java.yaml"
    ["furniture"]="openapi/furniture/config-java.yaml"
)

declare -A SERVICE_OUTPUT=(
    ["doorwindow"]="output/doorwindow/java"
    ["furniture"]="output/furniture/java"
)

# 显示帮助信息
show_help() {
    cat << EOF
🚀 本地 Maven 发布工具 (无需 Docker)

用法: $0 [选项] <服务名>

服务名:
  - doorwindow
  - furniture  
  - all (所有服务)

选项:
  --dry-run    预览操作，不实际执行
  --help       显示此帮助信息
  --no-mirror  不使用加速镜像
  --skip-gen   跳过 SDK 生成步骤

示例:
  $0 doorwindow              # 发布 doorwindow 服务
  $0 all                     # 发布所有服务
  $0 --dry-run furniture     # 预览发布 furniture 服务

配置文件: scripts/local/config.env

EOF
}

# 检查配置文件
check_config() {
    local config_file="$SCRIPT_DIR/config.env"
    
    if [ ! -f "$config_file" ]; then
        print_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 加载配置
    source "$config_file"
    
    # 验证必要的配置
    local missing=0
    local required_vars=("GPG_KEY_NAME" "GPG_PASSPHRASE" "CENTRAL_USERNAME" "CENTRAL_PASSWORD")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_error "$var 未设置"
            missing=1
        fi
    done
    
    if [ $missing -eq 1 ]; then
        print_error "配置不完整，请编辑 $config_file"
        exit 1
    fi
    
    print_success "配置验证通过"
}

# 创建加速的 Maven settings.xml
create_maven_settings() {
    local use_mirror=${1:-true}
    local settings_dir="$HOME/.m2"
    local settings_file="$settings_dir/settings.xml"
    
    mkdir -p "$settings_dir"
    
    print_status "创建 Maven settings.xml 文件..."
    
    cat > "$settings_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">
    
    <servers>
        <server>
            <id>central</id>
            <username>${CENTRAL_USERNAME}</username>
            <password>${CENTRAL_PASSWORD}</password>
        </server>
    </servers>
    
    <profiles>
        <profile>
            <id>gpg</id>
            <properties>
                <gpg.keyname>${GPG_KEY_NAME}</gpg.keyname>
                <gpg.passphrase>${GPG_PASSPHRASE}</gpg.passphrase>
            </properties>
        </profile>
    </profiles>
    
    <activeProfiles>
        <activeProfile>gpg</activeProfile>
    </activeProfiles>
EOF

    if [ "$use_mirror" = "true" ]; then
        cat >> "$settings_file" << EOF
    
    <mirrors>
        <!-- 阿里云 Maven 镜像 -->
        <mirror>
            <id>aliyun-maven</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Maven Mirror</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
        <!-- 腾讯云 Maven 镜像 -->
        <mirror>
            <id>tencent-maven</id>
            <mirrorOf>*,!central.sonatype.com</mirrorOf>
            <name>Tencent Maven Mirror</name>
            <url>https://mirrors.tencent.com/nexus/repository/maven-public/</url>
        </mirror>
    </mirrors>
EOF
        print_success "已配置加速镜像 (阿里云 + 腾讯云)"
    else
        cat >> "$settings_file" << EOF
    
    <!-- 镜像配置已禁用 -->
EOF
        print_warning "未配置加速镜像"
    fi
    
    cat >> "$settings_file" << EOF
    
</settings>
EOF
    
    print_success "Maven settings.xml 创建成功: $settings_file"
}

# 生成 SDK (本地方式)
generate_sdk_local() {
    local service=$1
    local config_file="${SERVICE_CONFIG[$service]}"
    local output_dir="${SERVICE_OUTPUT[$service]}"
    
    if [ -z "$config_file" ]; then
        print_error "不支持的服务: $service"
        return 1
    fi
    
    print_status "生成 $service SDK (本地方式)..."
    
    # 检查是否有 OpenAPI Generator JAR
    local generator_jar="$HOME/.openapi-generator/openapi-generator-cli-7.12.0.jar"
    
    if [ ! -f "$generator_jar" ]; then
        print_status "下载 OpenAPI Generator..."
        mkdir -p "$HOME/.openapi-generator"
        curl -L "https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.12.0/openapi-generator-cli-7.12.0.jar" \
            -o "$generator_jar"
        print_success "OpenAPI Generator 下载完成"
    fi
    
    # 生成 SDK
    java -jar "$generator_jar" generate \
        -i "$config_file" \
        -g java \
        -o "$output_dir" \
        --config "$config_file"
    
    print_success "$service SDK 生成完成"
}

# 构建和发布
build_and_deploy() {
    local service=$1
    local output_dir="${SERVICE_OUTPUT[$service]}"
    local dry_run=${2:-false}
    
    print_status "构建和发布 $service 服务..."
    
    if [ ! -d "$output_dir" ]; then
        print_error "输出目录不存在: $output_dir"
        return 1
    fi
    
    cd "$output_dir"
    
    # 清理
    print_status "清理构建目录..."
    mvn clean
    
    # 构建
    print_status "构建 artifacts..."
    mvn package -DskipTests
    
    # 发布
    if [ "$dry_run" = "false" ]; then
        print_status "发布到 Maven Central..."
        mvn deploy -Pcentral-release -DskipTests
        print_success "$service 发布完成"
    else
        print_status "试运行模式：跳过实际发布"
    fi
    
    cd "$PROJECT_ROOT"
}

# 主函数
main() {
    local service=""
    local dry_run="false"
    local use_mirror="true"
    local skip_gen="false"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                dry_run="true"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            --no-mirror)
                use_mirror="false"
                shift
                ;;
            --skip-gen)
                skip_gen="true"
                shift
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$service" ]; then
                    service="$1"
                else
                    print_error "只能指定一个服务名"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查服务名参数
    if [ -z "$service" ]; then
        print_error "请指定要发布的服务名"
        show_help
        exit 1
    fi
    
    echo "🚀 本地 Maven 发布工具"
    echo "======================="
    echo ""
    
    # 检查和加载配置
    check_config
    
    # 创建 Maven 配置
    create_maven_settings "$use_mirror"
    
    # 确认发布
    if [ "$dry_run" = "false" ]; then
        echo "🎯 准备发布服务: $service"
        read -p "确认继续？(y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_warning "发布已取消"
            exit 0
        fi
        echo ""
    fi
    
    # 执行发布
    print_status "开始发布流程..."
    cd "$PROJECT_ROOT"
    
    if [ "$service" = "all" ]; then
        for svc in "${!SERVICE_CONFIG[@]}"; do
            print_status "处理服务: $svc"
            
            if [ "$skip_gen" = "false" ]; then
                generate_sdk_local "$svc"
            fi
            
            build_and_deploy "$svc" "$dry_run"
        done
    else
        if [ -z "${SERVICE_CONFIG[$service]}" ]; then
            print_error "不支持的服务: $service"
            print_error "支持的服务: ${!SERVICE_CONFIG[*]}"
            exit 1
        fi
        
        if [ "$skip_gen" = "false" ]; then
            generate_sdk_local "$service"
        fi
        
        build_and_deploy "$service" "$dry_run"
    fi
    
    if [ "$dry_run" = "false" ]; then
        print_success "🎉 发布完成！"
        echo ""
        print_status "后续操作："
        echo "  - 检查 Maven Central Portal: https://central.sonatype.com/"
        echo "  - 等待同步到 Maven Central Repository (通常需要10-30分钟)"
    else
        print_success "预览完成"
    fi
}

main "$@" 