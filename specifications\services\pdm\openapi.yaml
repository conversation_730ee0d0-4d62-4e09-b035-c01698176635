---
openapi: "3.1.0"
info:
  title: "平面造型设计API"
  description: "平面造型设计REST API\n\n    ## 功能特性\n    - 平面造型的 CRUD 操作\n    - 批量操作支持（创\
    建、更新、删除、查询）\n    - 分页查询支持\n    \n    ## 数据模型\n    - **平面造型**: 包含平面造型所在的面、平面造型形\
    状信息\n    \n    ## 分页机制\n    使用基于页码的分页，支持自定义页面大小"
  contact:
    name: "群核科技开发团队"
    url: "https://wiki.manycore.com/furniture-design"
    email: "<EMAIL>"
  license:
    name: "群核科技专有许可证"
    url: "https://manycore.com/license"
  version: "1.0.0"
servers:
  - url: "http://localhost:8083"
    description: "本地开发环境"
  - url: "https://api-dev.qunhe.com"
    description: "开发测试环境"
  - url: "https://api.qunhe.com"
    description: "生产环境"
tags:
  - name: "平面造型设计管理接口"
    description: "提供平面造型设计数据的完整生命周期管理，包括CRUD操作、批量处理、分页查询等功能。支持不同数据视图以优化性能。"
paths:
  /deco/api/v2/rest/designs/{designId}/levels/{levelId}/planarmodel:
    post:
      tags:
        - "平面造型设计管理接口"
      summary: "保存单个平面造型数据"
      description: "保存单个平面造型数据"
      operationId: "savePlanarModelV2"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
      requestBody:
        description: "保存单个平面造型数据请求体"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SavePlanarModelLod100DataRequestV2"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PlanarModel"
      x-return-extra:
        is-operation: "true"
  /deco/api/v2/rest/designs/{designId}/levels/{levelId}/planarmodel:batchdelete:
    post:
      tags:
        - "平面造型设计管理接口"
      summary: "批量删除平面造型数据"
      description: "根据平面造型ID批量删除平面造型数据"
      operationId: "batchDeletePlanarModel"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
      requestBody:
        description: "批量删除平面造型数据请求体"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchDeletePlanarModelRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchDeletePlanarModelResponse"
      x-return-extra:
        is-operation: "true"
  /deco/api/v2/rest/designs/{designId}/levels/{levelId}/planarmodel:batchcreate:
    post:
      tags:
        - "平面造型设计管理接口"
      summary: "批量保存平面造型数据"
      description: "批量保存平面造型数据"
      operationId: "batchSavePlanarModelV2"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
      requestBody:
        description: "批量平面造型数据请求体"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchSavePlanarModelLod100DataRequestV2"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchSavePlanarModelResponse"
      x-return-extra:
        is-operation: "true"
  /deco/api/v2/rest/designs/{designId}/levels/{levelId}/planarmodel/{modelId}:
    delete:
      tags:
        - "平面造型设计管理接口"
      summary: "根据平面造型ID删除单个平面造型数据"
      description: "根据平面造型ID删除单个平面造型数据"
      operationId: "deletePlanarModel"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
        - name: "modelId"
          in: "path"
          description: "平面造型ID"
          required: true
          schema:
            type: "string"
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                type: "boolean"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
      x-return-extra:
        is-operation: "true"
  /deco/api/v1/rest/designs/{designId}/levels/{levelId}/planarmodels:
    get:
      tags:
        - "平面造型设计管理接口"
      summary: "分页获取平面造型数据列表"
      description: "分页获取平面造型数据列表"
      operationId: "fetchPlanarModelList"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
        - name: "pageSize"
          in: "query"
          description: "单页大小"
          required: true
          schema:
            type: "integer"
            format: "int32"
        - name: "pageNum"
          in: "query"
          description: "页码"
          required: true
          schema:
            type: "integer"
            format: "int32"
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FetchPlanarModelListPageResponse"
      x-return-extra:
        is-operation: "true"
  /deco/api/v1/rest/designs/{designId}/levels/{levelId}/planarmodel:buildgeom:
    post:
      tags:
        - "平面造型设计管理接口"
      summary: "批量获取平面造型建模结果"
      description: "根据请求体中的平面造型ID批量获取平面造型的建模结果"
      operationId: "batchFetchPlanarModelBuildResult"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
      requestBody:
        description: "批量获取平面造型建模结果请求体"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchGetPlanarModelRequest"
        required: true
      responses:
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchFetchPlanarModelBuildResultResponse"
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
      x-return-extra:
        is-operation: "true"
  /deco/api/v1/rest/designs/{designId}/levels/{levelId}/planarmodel:batchget:
    post:
      tags:
        - "平面造型设计管理接口"
      summary: "批量获取平面造型数据"
      description: "根据请求体中的平面造型ID批量获取平面造型数据"
      operationId: "batchFetchPlanarModel"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
      requestBody:
        description: "批量获取平面造型数据请求体"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BatchGetPlanarModelRequest"
        required: true
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BatchFetchPlanarModelResponse"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
      x-return-extra:
        is-operation: "true"
  /deco/api/v1/rest/designs/{designId}/levels/{levelId}/planarmodel/{modelId}:
    get:
      tags:
        - "平面造型设计管理接口"
      summary: "获取平面造型数据"
      description: "根据平面造型ID获取单个平面造型数据"
      operationId: "fetchPlanarModel"
      parameters:
        - name: "designId"
          in: "path"
          description: "设计ID"
          required: true
          schema:
            type: "string"
        - name: "levelId"
          in: "path"
          description: "楼层ID"
          required: true
          schema:
            type: "string"
        - name: "modelId"
          in: "path"
          description: "平面造型ID"
          required: true
          schema:
            type: "string"
      responses:
        500:
          description: "服务器内部错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        400:
          description: "请求参数错误"
          content:
            application/json:
              schema:
                $ref: "../../common/restapi-sdk-data-schemas.yaml#/components/schemas/ApiError"
        200:
          description: "成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PlanarModel"
      x-return-extra:
        is-operation: "true"
components:
  schemas:
    EdgeAttribute:
      required:
        - "edgeId"
        - "stripLight"
      type: "object"
      properties:
        edgeId:
          type: "string"
          description: "边id"
          example: "123"
        stripLight:
          $ref: "#/components/schemas/StripLight"
      description: "平面造型边的属性"
    FaceAttribute:
      required:
        - "faceId"
        - "height"
      type: "object"
      properties:
        faceId:
          type: "string"
          description: "面id"
          example: "123"
        height:
          type: "number"
          description: "区域抬高高度"
          format: "double"
          example: 100
      description: "平面造型区域的属性"
    FaceLod100Data:
      type: "object"
      properties:
        faceId:
          type: "string"
          description: "面ID"
      description: "面数据基础类"
    PlanarModel:
      required:
        - "archFaces"
        - "edgeAttributes"
        - "faceAttributes"
        - "id"
        - "model"
        - "stripLightMode"
      type: "object"
      properties:
        id:
          type: "string"
          description: "平面造型ID"
          example: "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"
        archFaces:
          type: "array"
          description: "所在的建筑面数据列表"
          items:
            $ref: "#/components/schemas/FaceLod100Data"
        model:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Geometric2dDesign"
        stripLightMode:
          type: "string"
          description: "灯带模式"
          example: "INNER"
          enum:
            - "MODE_TYPE_UNSPECIFIED"
            - "INNER"
            - "OUTER"
        edgeAttributes:
          type: "array"
          description: "平面造型边属性"
          items:
            $ref: "#/components/schemas/EdgeAttribute"
        faceAttributes:
          type: "array"
          description: "平面造型区域属性"
          items:
            $ref: "#/components/schemas/FaceAttribute"
      description: "平面造型数据"
    SavePlanarModelLod100DataRequestV2:
      required:
        - "planarModel"
      type: "object"
      properties:
        requestId:
          type: "string"
        planarModel:
          $ref: "#/components/schemas/PlanarModel"
      description: "保存平面造型数据请求体"
    StripLight:
      required:
        - "stripLightProfileData"
      type: "object"
      properties:
        stripLightProfileData:
          $ref: "#/components/schemas/StripLightProfile"
      description: "灯带数据"
    StripLightProfile:
      required:
        - "height"
        - "horizontalFaceThickness"
        - "verticalFaceThickness"
        - "width"
      type: "object"
      properties:
        width:
          type: "number"
          description: "宽度"
          format: "double"
          example: 500
        height:
          type: "number"
          description: "高度"
          format: "double"
          example: 200
        verticalFaceThickness:
          type: "number"
          description: "垂直面厚度"
          format: "double"
          example: 10
        horizontalFaceThickness:
          type: "number"
          description: "水平面厚度"
          format: "double"
          example: 10
      description: "灯带轮廓数据"
    BatchDeletePlanarModelRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/PlanarModelId"
      description: "批量删除平面造型请求体"
    PlanarModelId:
      required:
        - "planarModelId"
      type: "object"
      properties:
        planarModelId:
          type: "string"
          description: "平面造型id"
          example: "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"
      description: "平面造型id"
    BatchDeletePlanarModelResponse:
      type: "object"
      properties:
        elements:
          type: "array"
          items:
            $ref: "#/components/schemas/PlanarModelId"
      description: "批量删除平面造型数据响应体"
    BatchSavePlanarModelLod100DataRequestV2:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/SavePlanarModelLod100DataRequestV2"
      description: "批量保存平面造型数据请求体"
    BatchSavePlanarModelResponse:
      type: "object"
      properties:
        elements:
          type: "array"
          items:
            $ref: "#/components/schemas/PlanarModel"
      description: "批量保存平面造型响应体"
    FetchPlanarModelListPageResponse:
      type: "object"
      properties:
        data:
          type: "array"
          description: "数据列表"
          items:
            $ref: "#/components/schemas/PlanarModel"
        more:
          type: "boolean"
          description: "是否还有更多数据"
      description: "分页获取平面造型数据响应体"
    BatchGetPlanarModelRequest:
      type: "object"
      properties:
        requestId:
          type: "string"
        operationId:
          type: "string"
        batchRequests:
          type: "array"
          items:
            $ref: "#/components/schemas/PlanarModelId"
      description: "批量获取平面造型数据请求体"
    BatchFetchPlanarModelBuildResultResponse:
      type: "object"
      properties:
        elements:
          type: "array"
          items:
            $ref: "#/components/schemas/PlanarModelBuildResult"
      description: "批量获取平面造型数据建模结果响应体"
    PlanarModelBuildResult:
      required:
        - "archFaces"
        - "id"
        - "planarModelFaces"
        - "stripLightMode"
      type: "object"
      properties:
        id:
          type: "string"
          description: "平面造型ID"
          example: "PDM-NBXDRSAKTJGHUAABAAAAADY8-123"
        archFaces:
          type: "array"
          description: "所在的建筑面数据列表"
          items:
            $ref: "#/components/schemas/FaceLod100Data"
        planarModelFaces:
          type: "array"
          description: "平面造型面数据列表"
          items:
            $ref: "#/components/schemas/PlanarModelBuildResultFace"
        stripLightMode:
          type: "string"
          description: "灯带模式"
          example: "INNER"
          enum:
            - "MODE_TYPE_UNSPECIFIED"
            - "INNER"
            - "OUTER"
      description: "平面造型建模结果数据"
    PlanarModelBuildResultFace:
      required:
        - "face"
        - "faceId"
      type: "object"
      properties:
        faceId:
          type: "string"
          description: "平面造型面id"
          example: "PDM-NBXDRSAKTJGHUAABAAAAADY8-123:00ff00:00"
        face:
          $ref: "../../common/geom-data-exchange-schemas.yaml#/components/schemas/Face"
      description: "平面造型建模结果面数据"
    BatchFetchPlanarModelResponse:
      type: "object"
      properties:
        elements:
          type: "array"
          items:
            $ref: "#/components/schemas/PlanarModel"
      description: "批量获取平面造型数据响应体"
