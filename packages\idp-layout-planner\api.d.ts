import { IDP as IDPCommon } from '@qunhe/idp-common';

declare namespace IDP {
    namespace DB {
        namespace Types {
            interface FurnitureLegend {
                readonly id: IDPCommon.DB.Types.ElementId;
                readonly area: number;
            }

            interface ParamLegend {
                readonly id: IDPCommon.DB.Types.ElementId;
                readonly area: number;
            }

            interface LegendGroup {
                readonly id: IDPCommon.DB.Types.ElementId;
                readonly subElements: IDPCommon.DB.Types.ElementId[];
            }
        }

        namespace Methods {
            /**
             * 获取所有家具图例
             */
            function getAllFurnitureLegendList(): IDP.DB.Types.FurnitureLegend[];

            /**
             * 获取所有参数化图例
             */
            function getAllParamLegendList(): IDP.DB.Types.ParamLegend[];

            /**
             * 获取所有图例组合
             */
            function getAllLegendGroupList(): IDP.DB.Types.LegendGroup[];
        }
    }
}

export { IDP };

// 空的 export 声明，用于避免 ts 某个 bug 导致的未声明导出的类型也会被导出
export {};
