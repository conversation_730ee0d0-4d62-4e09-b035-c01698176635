// 编译 package 使其合法，比如生成 vmType 生成函数、全局类型声明定义

const child_process = require('child_process');
const path = require('path');
const fse = require('fs-extra');

const argv = require('yargs')
    .strict()
    .option('exclude-packages', { type: 'array', default: [] })
    .option('keep-args-handle', { type: 'boolean', default: false })
    .option('enable-unknown-type', { type: 'boolean', default: false })
    .argv;

function execSync(cmd) {
    console.log(`$ ${cmd}`);
    child_process.execSync(cmd, { stdio: 'inherit' });
}

// 清空旧文件
fse.removeSync('dist');

// 分别生成正式版和internal版接口的 vm types
const generateVMTypesArgs = argv['exclude-packages'].length ? `--exclude-packages ${argv['exclude-packages'].join(' ')}` : '';
const keepArgsHandle = argv['keep-args-handle'] ? '--keep-args-handle' : '';
const enableUnknownType = argv['enable-unknown-type'] ? '--enable-unknown-type' : '';
const packageDirName = path.basename(process.cwd());
const apiDtsFile = `../${packageDirName.replace(/-impl$/, '')}/api.d.ts`;
const apaasTypeGeneratorScript = path.resolve(__dirname, '../apaas-type-generator.js');
execSync(`node ${apaasTypeGeneratorScript} generate-vm-types --api-dts-file ${apiDtsFile} --output-js-file dist/vmTypePublic.js --trim-internal-api ${generateVMTypesArgs} ${keepArgsHandle} ${enableUnknownType}`);
execSync(`node ${apaasTypeGeneratorScript} generate-vm-types --api-dts-file ${apiDtsFile} --output-js-file dist/vmTypeInternal.js ${generateVMTypesArgs} ${keepArgsHandle} ${enableUnknownType}`);
