#!/bin/bash

# 本地 SDK 管理器
# 支持通过 Docker 容器生成、构建、测试、发布 SDK
# 可以指定特定服务进行操作

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DOCKER_IMAGE="registry-qunhe02.qunhequnhe.com/display/openapi-generator:latest"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 动态发现服务列表
discover_services() {
    local services=()
    local openapi_dir="$PROJECT_ROOT/openapi"
    
    if [ ! -d "$openapi_dir" ]; then
        print_error "OpenAPI 目录不存在: $openapi_dir"
        return 1
    fi
    
    # 扫描 openapi 目录下的所有子目录
    for service_dir in "$openapi_dir"/*; do
        if [ -d "$service_dir" ]; then
            local service_name=$(basename "$service_dir")
            
            # 跳过公共目录
            if [[ "$service_name" == "common" || "$service_name" == "shared" ]]; then
                continue
            fi
            
            # 检查是否包含必要的文件
            if [ -f "$service_dir/restapi.yaml" ] && [ -f "$service_dir/config-java.yaml" ]; then
                services+=("$service_name")
            else
                print_warning "服务目录 $service_name 缺少必要文件，跳过"
            fi
        fi
    done
    
    if [ ${#services[@]} -eq 0 ]; then
        print_error "未发现任何有效的服务目录"
        return 1
    fi
    
    printf '%s\n' "${services[@]}"
}

# 获取支持的服务列表
get_supported_services() {
    discover_services
}

# 获取服务配置
get_service_config() {
    local service=$1
    local config_type=$2  # restapi, config, output
    
    case $config_type in
        "restapi")
            echo "openapi/$service/restapi.yaml"
            ;;
        "config")
            echo "openapi/$service/config-java.yaml"
            ;;
        "output")
            echo "output/$service/java"
            ;;
        *)
            print_error "未知的配置类型: $config_type"
            return 1
            ;;
    esac
}

# 打印帮助信息
print_help() {
    local services_list
    services_list=$(get_supported_services 2>/dev/null | tr '\n' ' ')
    
    echo -e "${BLUE}用法: $0 [选项] <操作> [服务名]${NC}"
    echo ""
    echo -e "${YELLOW}操作:${NC}"
    echo "  generate      生成 SDK"
    echo "  build         构建 SDK"
    echo "  test          测试 SDK"
    echo "  deploy-snap   部署快照版本到内部仓库"
    echo "  deploy-rel    部署正式版本到 Maven Central"
    echo "  deploy-internal 部署到内部仓库（带签名）"
    echo "  all           执行完整流程 (generate + build + test)"
    echo ""
    echo -e "${YELLOW}服务名 (可选):${NC}"
    if [ -n "$services_list" ]; then
        for service in $services_list; do
            echo "  $service     $service 服务 SDK"
        done
    fi
    echo "  all         所有服务 (默认)"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo "  -h, --help    显示帮助信息"
    echo "  --pull        拉取最新 Docker 镜像"
    echo "  --no-cache    不使用缓存构建"
    echo "  --dry-run     预览将要执行的命令，不实际执行"
    echo ""
    echo -e "${YELLOW}环境变量 (用于发布):${NC}"
    echo "  GPG_KEY_NAME        GPG 密钥 ID（16位十六进制，如：1234567890ABCDEF）"
    echo "  GPG_PASSPHRASE      GPG 密钥密码"
    echo "  GPG_PRIVATE_KEY     GPG 私钥（Base64 编码，可选）"
    echo "  CENTRAL_USERNAME    Maven Central Portal 用户名"
    echo "  CENTRAL_PASSWORD    Maven Central Portal 密码/令牌"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    if [ -n "$services_list" ]; then
        local first_service=$(echo $services_list | awk '{print $1}')
        local second_service=$(echo $services_list | awk '{print $2}')
        [ -n "$first_service" ] && echo "  $0 generate $first_service        # 生成 $first_service 服务 SDK"
        [ -n "$second_service" ] && echo "  $0 all $second_service            # 对 $second_service 服务执行完整流程"
    fi
    echo "  $0 build                      # 构建所有服务 SDK"
    echo "  $0 --pull generate all        # 拉取最新镜像后生成所有 SDK"
    echo "  $0 deploy-snap doorwindow     # 部署快照版本到内部仓库"
    echo "  # 发布到 Maven Central（需要所有认证信息）"
    echo "  GPG_KEY_NAME=xxx GPG_PASSPHRASE=yyy CENTRAL_USERNAME=abc CENTRAL_PASSWORD=token $0 deploy-rel all"
}

# 打印状态信息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 打印成功信息
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 打印警告信息
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 打印错误信息
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装或不可用"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "无法连接到 Docker daemon"
        exit 1
    fi
}

# 拉取 Docker 镜像
pull_docker_image() {
    print_status "拉取 Docker 镜像: $DOCKER_IMAGE"
    if ! docker pull "$DOCKER_IMAGE"; then
        print_error "拉取 Docker 镜像失败"
        exit 1
    fi
    print_success "Docker 镜像拉取完成"
}

# 创建输出目录
create_output_dirs() {
    local service=$1
    
    if [ "$service" = "all" ]; then
        # 为所有服务创建输出目录
        local services
        services=$(get_supported_services)
        for svc in $services; do
            local output_dir=$(get_service_config "$svc" "output")
            mkdir -p "$PROJECT_ROOT/$output_dir"
            print_status "创建输出目录: $output_dir"
        done
    else
        # 为特定服务创建输出目录
        local output_dir=$(get_service_config "$service" "output")
        mkdir -p "$PROJECT_ROOT/$output_dir"
        print_status "创建输出目录: $output_dir"
    fi
}

# 构建环境变量
build_env_vars() {
    local env_vars=""
    local services
    services=$(get_supported_services)
    
    for service in $services; do
        local restapi_path=$(get_service_config "$service" "restapi")
        local config_path=$(get_service_config "$service" "config")
        local output_path=$(get_service_config "$service" "output")
        
        # 构建环境变量名（大写）
        local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
        
        env_vars="$env_vars -e ${service_upper}_RESTAPI=$restapi_path"
        env_vars="$env_vars -e ${service_upper}_CONFIG_JAVA=$config_path"
        env_vars="$env_vars -e ${service_upper}_OUTPUT_JAVA=$output_path"
    done
    
    # 兼容性环境变量（保持旧的变量名）
    env_vars="$env_vars -e DIYMODELDW_RESTAPI=$(get_service_config "doorwindow" "restapi" 2>/dev/null || echo "openapi/doorwindow/restapi.yaml")"
    env_vars="$env_vars -e DIYMODELDW_CONFIG_JAVA=$(get_service_config "doorwindow" "config" 2>/dev/null || echo "openapi/doorwindow/config-java.yaml")"
    env_vars="$env_vars -e DIYMODELDW_OUTPUT_JAVA=$(get_service_config "doorwindow" "output" 2>/dev/null || echo "output/doorwindow/java")"
    env_vars="$env_vars -e FURNITURE_DESIGN_RESTAPI=$(get_service_config "furniture" "restapi" 2>/dev/null || echo "openapi/furniture/restapi.yaml")"
    env_vars="$env_vars -e FURNITURE_DESIGN_CONFIG_JAVA=$(get_service_config "furniture" "config" 2>/dev/null || echo "openapi/furniture/config-java.yaml")"
    env_vars="$env_vars -e FURNITURE_DESIGN_OUTPUT_JAVA=$(get_service_config "furniture" "output" 2>/dev/null || echo "output/furniture/java")"
    
    # GPG 相关环境变量（用于发布）
    if [ -n "$GPG_KEY_NAME" ]; then
        env_vars="$env_vars -e GPG_KEY_NAME=$GPG_KEY_NAME"
    fi
    if [ -n "$GPG_PASSPHRASE" ]; then
        env_vars="$env_vars -e GPG_PASSPHRASE=$GPG_PASSPHRASE"
    fi
    if [ -n "$GPG_PRIVATE_KEY" ]; then
        env_vars="$env_vars -e GPG_PRIVATE_KEY=$GPG_PRIVATE_KEY"
    fi
    
    # Maven Central 认证环境变量
    if [ -n "$CENTRAL_USERNAME" ]; then
        env_vars="$env_vars -e CENTRAL_USERNAME=$CENTRAL_USERNAME"
    fi
    if [ -n "$CENTRAL_PASSWORD" ]; then
        env_vars="$env_vars -e CENTRAL_PASSWORD=$CENTRAL_PASSWORD"
    fi
    
    echo "$env_vars"
}

# 运行 Docker 容器执行命令
run_docker_command() {
    local cmd=$1
    local volumes="-v $PROJECT_ROOT:/app"
    local env_vars
    env_vars=$(build_env_vars)
    
    if [ "$DRY_RUN" = "true" ]; then
        print_status "预览命令:"
        echo "docker run --rm $volumes $env_vars -w /app $DOCKER_IMAGE bash -c \"$cmd\""
        return 0
    fi
    
    print_status "执行命令: $cmd"
    if ! docker run --rm $volumes $env_vars -w /app "$DOCKER_IMAGE" bash -c "$cmd"; then
        print_error "命令执行失败: $cmd"
        return 1
    fi
}

# 生成单个服务的 SDK
generate_single_sdk() {
    local service=$1
    local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
    
    local cmd="echo '🔧 生成 $service SDK...' && java -jar /opt/openapi-generator/openapi-generator-cli-7.12.0.jar generate -i \${${service_upper}_RESTAPI} -g java -o \${${service_upper}_OUTPUT_JAVA} --config \${${service_upper}_CONFIG_JAVA} && echo '✅ $service SDK 生成成功'"
    
    run_docker_command "$cmd"
}

# 生成 SDK
generate_sdk() {
    local service=$1
    print_status "开始生成 SDK: $service"
    
    create_output_dirs "$service"
    
    if [ "$service" = "all" ]; then
        # 如果有现成的generate-sdks.sh脚本，优先使用
        if [ -f "$PROJECT_ROOT/scripts/ci/deployment/generate-sdks.sh" ]; then
            local cmd="chmod +x scripts/ci/deployment/generate-sdks.sh && ./scripts/ci/deployment/generate-sdks.sh"
            run_docker_command "$cmd"
        else
            # 否则遍历所有服务
            local services
            services=$(get_supported_services)
            for svc in $services; do
                print_status "生成 $svc 服务 SDK"
                generate_single_sdk "$svc"
            done
        fi
    else
        generate_single_sdk "$service"
    fi
    
    print_success "SDK 生成完成: $service"
}

# 构建单个服务的 SDK
build_single_sdk() {
    local service=$1
    local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
    
    local cmd="echo '📦 构建 $service SDK...' && cd \${${service_upper}_OUTPUT_JAVA} && mvn compile && echo '✅ $service SDK 构建成功'"
    
    run_docker_command "$cmd"
}

# 构建 SDK
build_sdk() {
    local service=$1
    print_status "开始构建 SDK: $service"
    
    if [ "$service" = "all" ]; then
        # 如果有现成的build-sdks.sh脚本，优先使用
        if [ -f "$PROJECT_ROOT/scripts/ci/deployment/build-sdks.sh" ]; then
            local cmd="chmod +x scripts/ci/deployment/build-sdks.sh && ./scripts/ci/deployment/build-sdks.sh"
            run_docker_command "$cmd"
        else
            # 否则遍历所有服务
            local services
            services=$(get_supported_services)
            for svc in $services; do
                print_status "构建 $svc 服务 SDK"
                build_single_sdk "$svc"
            done
        fi
    else
        build_single_sdk "$service"
    fi
    
    print_success "SDK 构建完成: $service"
}

# 测试单个服务的 SDK
test_single_sdk() {
    local service=$1
    local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
    
    local cmd="echo '🧪 测试 $service SDK...' && cd \${${service_upper}_OUTPUT_JAVA} && mvn test && echo '✅ $service SDK 测试通过'"
    
    run_docker_command "$cmd"
}

# 测试 SDK
test_sdk() {
    local service=$1
    print_status "开始测试 SDK: $service"
    
    if [ "$service" = "all" ]; then
        # 如果有现成的test-sdks.sh脚本，优先使用
        if [ -f "$PROJECT_ROOT/scripts/ci/deployment/test-sdks.sh" ]; then
            local cmd="chmod +x scripts/ci/deployment/test-sdks.sh && ./scripts/ci/deployment/test-sdks.sh"
            run_docker_command "$cmd"
        else
            # 否则遍历所有服务
            local services
            services=$(get_supported_services)
            for svc in $services; do
                print_status "测试 $svc 服务 SDK"
                test_single_sdk "$svc"
            done
        fi
    else
        test_single_sdk "$service"
    fi
    
    print_success "SDK 测试完成: $service"
}

# 部署单个服务的快照版本
deploy_single_snapshot() {
    local service=$1
    local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
    
    local cmd="echo '🚀 部署 $service SNAPSHOT...' && cd \${${service_upper}_OUTPUT_JAVA} && mvn deploy -DskipTests && echo '✅ $service SNAPSHOT 部署成功'"
    
    run_docker_command "$cmd"
}

# 部署单个服务的正式版本  
deploy_single_release() {
    local service=$1
    local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
    
    local cmd="echo '🎯 部署 $service 正式版本...' && \\
    if [ -n \"\${GPG_PRIVATE_KEY}\" ]; then \\
        echo '🔑 导入 GPG 私钥（Docker模式）...' && \\
        echo \"\${GPG_PRIVATE_KEY}\" | base64 -d | gpg --batch --yes --allow-secret-key-import --import && \\
        echo '✅ GPG 私钥导入成功'; \\
    elif gpg --list-secret-keys \"\${GPG_KEY_NAME}\" >/dev/null 2>&1; then \\
        echo '🔑 使用本地 GPG 密钥环...'; \\
    else \\
        echo '❌ 错误：未找到 GPG 私钥，请确保:' && \\
        echo '  1. 本地 GPG 密钥环中有对应密钥，或' && \\
        echo '  2. 设置 GPG_PRIVATE_KEY 环境变量' && \\
        exit 1; \\
    fi && \\
    echo '🔧 创建 Maven settings.xml 文件...' && \\
    mkdir -p ~/.m2 && \\
    cat > ~/.m2/settings.xml << EOF
<?xml version=\"1.0\" encoding=\"UTF-8\"?>
<settings xmlns=\"http://maven.apache.org/SETTINGS/1.0.0\"
          xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"
          xsi:schemaLocation=\"http://maven.apache.org/SETTINGS/1.0.0
                              http://maven.apache.org/xsd/settings-1.0.0.xsd\">
    <servers>
        <server>
            <id>central</id>
            <username>\${CENTRAL_USERNAME}</username>
            <password>\${CENTRAL_PASSWORD}</password>
        </server>
    </servers>
</settings>
EOF
    echo '✅ Maven settings.xml 创建成功' && \\
    cd \${${service_upper}_OUTPUT_JAVA} && \\
    mvn deploy -Pcentral-release -DskipTests -Dgpg.keyname=\${GPG_KEY_NAME} -Dgpg.passphrase=\${GPG_PASSPHRASE} && \\
    echo '✅ $service 正式版本部署成功'"
    
    run_docker_command "$cmd"
}

# 部署快照版本
deploy_snapshot() {
    local service=$1
    print_status "开始部署快照版本: $service"
    
    if [ "$service" = "all" ]; then
        # 如果有现成的deploy-snapshots.sh脚本，优先使用
        if [ -f "$PROJECT_ROOT/scripts/ci/deployment/deploy-snapshots.sh" ]; then
            local cmd="chmod +x scripts/ci/deployment/deploy-snapshots.sh && ./scripts/ci/deployment/deploy-snapshots.sh"
            run_docker_command "$cmd"
        else
            # 否则遍历所有服务
            local services
            services=$(get_supported_services)
            for svc in $services; do
                print_status "部署 $svc 服务快照版本"
                deploy_single_snapshot "$svc"
            done
        fi
    else
        deploy_single_snapshot "$service"
    fi
    
    print_success "快照版本部署完成: $service"
}

# 部署正式版本
deploy_release() {
    local service=$1
    print_status "开始部署正式版本到 Maven Central: $service"
    
    if [ "$service" = "all" ]; then
        # 对于本地发布，直接遍历所有服务使用单个发布方法
        # deploy-releases.sh 脚本主要用于 CI/CD 环境
        local services
        services=$(get_supported_services)
        for svc in $services; do
            print_status "部署 $svc 服务正式版本到 Maven Central"
            deploy_single_release "$svc"
        done
    else
        deploy_single_release "$service"
    fi
    
    print_success "正式版本部署完成: $service"
}

# 部署单个服务到内部仓库（带签名）
deploy_single_internal() {
    local service=$1
    local service_upper=$(echo "$service" | tr '[:lower:]' '[:upper:]')
    
    local cmd="echo '🏢 部署 $service 到内部仓库...' && \\
    if [ -n \"\${GPG_PRIVATE_KEY}\" ]; then \\
        echo '🔑 导入 GPG 私钥...' && \\
        echo \"\${GPG_PRIVATE_KEY}\" | base64 -d | gpg --batch --yes --allow-secret-key-import --import && \\
        echo '✅ GPG 私钥导入成功'; \\
    fi && \\
    cd \${${service_upper}_OUTPUT_JAVA} && \\
    mvn deploy -Pinternal-release -DskipTests -Dgpg.keyname=\${GPG_KEY_NAME} -Dgpg.passphrase=\${GPG_PASSPHRASE} && \\
    echo '✅ $service 内部部署成功'"
    
    run_docker_command "$cmd"
}

# 部署到内部仓库
deploy_internal() {
    local service=$1
    print_status "开始部署到内部仓库: $service"
    
    if [ "$service" = "all" ]; then
        # 遍历所有服务
        local services
        services=$(get_supported_services)
        for svc in $services; do
            print_status "部署 $svc 服务到内部仓库"
            deploy_single_internal "$svc"
        done
    else
        deploy_single_internal "$service"
    fi
    
    print_success "内部仓库部署完成: $service"
}

# 执行完整流程
run_all() {
    local service=$1
    print_status "开始执行完整流程: $service"
    
    generate_sdk "$service"
    build_sdk "$service"
    test_sdk "$service"
    
    print_success "完整流程执行完成: $service"
}

# 验证服务名
validate_service() {
    local service=$1
    if [ "$service" = "all" ]; then
        return 0
    fi
    
    local services
    services=$(get_supported_services 2>/dev/null)
    
    if [ -z "$services" ]; then
        print_error "无法获取支持的服务列表"
        exit 1
    fi
    
    for supported in $services; do
        if [ "$supported" = "$service" ]; then
            return 0
        fi
    done
    
    print_error "不支持的服务: $service"
    print_error "支持的服务: $services all"
    exit 1
}

# 主函数
main() {
    # 解析参数
    PULL_IMAGE=false
    NO_CACHE=false
    DRY_RUN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                print_help
                exit 0
                ;;
            --pull)
                PULL_IMAGE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            *)
                break
                ;;
        esac
    done
    
    # 检查参数
    if [ $# -lt 1 ]; then
        print_error "缺少操作参数"
        print_help
        exit 1
    fi
    
    local action=$1
    local service=${2:-"all"}
    
    # 验证服务名
    validate_service "$service"
    
    # 检查 Docker (干运行模式下跳过)
    if [ "$DRY_RUN" != "true" ]; then
        check_docker
    fi
    
    # 拉取镜像
    if [ "$PULL_IMAGE" = "true" ]; then
        pull_docker_image
    fi
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 执行操作
    case $action in
        "generate")
            generate_sdk "$service"
            ;;
        "build")
            build_sdk "$service"
            ;;
        "test")
            test_sdk "$service"
            ;;
        "deploy-snap")
            deploy_snapshot "$service"
            ;;
        "deploy-rel")
            deploy_release "$service"
            ;;
        "deploy-internal")
            deploy_internal "$service"
            ;;
        "all")
            run_all "$service"
            ;;
        *)
            print_error "未知操作: $action"
            print_help
            exit 1
            ;;
    esac
    
    print_success "操作完成!"
}

# 运行主函数
main "$@" 