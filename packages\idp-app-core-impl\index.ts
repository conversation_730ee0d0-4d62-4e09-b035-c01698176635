import { BasicType } from '@qunhe/kls-abstraction';
import { once } from 'lodash';
import { createVMBindingType } from './dist/vmTypePublic';
import { createVMBindingType as createVMBindingTypeInternal } from './dist/vmTypeInternal';
import { injections } from '@qunhe/apaas-type-generator-lib';

type FirstArgument<T extends Function> = T extends (arg: infer Arg) => any ? Arg : never;
function getInjections(): FirstArgument<typeof createVMBindingTypeInternal> {
    return {
        types: {
            /**
             * TODO: 请注意当前不生成 Miniapp 类型，
             * 原因是需要去自定义 miniapp-types 里的 mountPoints 类型生成。
             *
             * 因而该部分类型不要使用
             */
            Miniapp: { type: BasicType.Unknown }
        },
        packages: {
            '@qunhe/math-apaas-api': injections['@qunhe/math-apaas-api'],
        }
    }
}

export const getVMBindingType = once(() => {
    return createVMBindingType(getInjections());
});

export const getVMBindingTypeInternal = once(() => {
    return createVMBindingTypeInternal(getInjections());
});
