#!/bin/bash

# GPG 设置辅助脚本
# 帮助获取正确的 GPG 密钥信息，用于 Maven Central 发布

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}GPG 设置辅助脚本${NC}"
echo "================="

# 检查 GPG 是否安装
if ! command -v gpg &> /dev/null; then
    echo -e "${RED}错误: GPG 未安装${NC}"
    echo "请先安装 GPG："
    echo "  Ubuntu/Debian: sudo apt-get install gnupg"
    echo "  CentOS/RHEL: sudo yum install gnupg"
    echo "  macOS: brew install gnupg"
    exit 1
fi

echo -e "${YELLOW}1. 当前可用的 GPG 密钥:${NC}"
gpg --list-secret-keys --keyid-format LONG

echo ""
echo -e "${YELLOW}2. 如果没有密钥，创建新密钥:${NC}"
echo "运行: gpg --gen-key"
echo "按照提示输入姓名、邮箱等信息"

echo ""
read -p "请输入要使用的密钥 ID (16位十六进制，如 1234567890ABCDEF): " key_id

if [ -z "$key_id" ]; then
    echo -e "${RED}错误: 密钥 ID 不能为空${NC}"
    exit 1
fi

# 验证密钥是否存在
if ! gpg --list-secret-keys "$key_id" &> /dev/null; then
    echo -e "${RED}错误: 找不到密钥 $key_id${NC}"
    echo "请检查密钥 ID 是否正确"
    exit 1
fi

echo -e "${GREEN}✅ 找到密钥: $key_id${NC}"

# 导出私钥
echo ""
echo -e "${YELLOW}3. 导出私钥 (Base64 编码):${NC}"
private_key_base64=$(gpg --armor --export-secret-keys "$key_id" | base64 -w 0)

echo ""
echo -e "${GREEN}✅ GPG 配置信息:${NC}"
echo "=========================="
echo "GPG_KEY_NAME=\"$key_id\""
echo ""
echo "GPG_PRIVATE_KEY=\"$private_key_base64\""
echo ""
read -s -p "请输入 GPG 密钥密码: " passphrase
echo ""
echo "GPG_PASSPHRASE=\"$passphrase\""

echo ""
echo -e "${YELLOW}4. 上传公钥到密钥服务器:${NC}"
echo "gpg --keyserver keyserver.ubuntu.com --send-keys $key_id"
echo "gpg --keyserver keys.openpgp.org --send-keys $key_id"

echo ""
echo -e "${YELLOW}5. 使用示例:${NC}"
echo "GPG_KEY_NAME=\"$key_id\" \\"
echo "GPG_PASSPHRASE=\"$passphrase\" \\"
echo "GPG_PRIVATE_KEY=\"$private_key_base64\" \\"
echo "CENTRAL_USERNAME=\"your-username\" \\"
echo "CENTRAL_PASSWORD=\"your-token\" \\"
echo "./scripts/local/local-sdk-manager.sh deploy-rel doorwindow"

echo ""
echo -e "${BLUE}注意事项:${NC}"
echo "- 请妥善保管 GPG_PRIVATE_KEY，不要泄露"
echo "- 建议使用环境变量或 .env 文件存储这些信息"
echo "- 确保已上传公钥到密钥服务器"
echo "- 在 Maven Central Portal 完成命名空间验证"

# 可选：保存到 .env 文件
echo ""
read -p "是否将配置保存到 .env 文件? (y/n): " save_env

if [[ "$save_env" =~ ^[Yy]$ ]]; then
    env_file="$(dirname "$0")/.env"
    echo "# GPG 配置 - $(date)" >> "$env_file"
    echo "GPG_KEY_NAME=\"$key_id\"" >> "$env_file"
    echo "GPG_PASSPHRASE=\"$passphrase\"" >> "$env_file"
    echo "GPG_PRIVATE_KEY=\"$private_key_base64\"" >> "$env_file"
    echo "# Maven Central 配置 - 请手动填写" >> "$env_file"
    echo "# CENTRAL_USERNAME=\"\"" >> "$env_file"
    echo "# CENTRAL_PASSWORD=\"\"" >> "$env_file"
    echo "" >> "$env_file"
    
    echo -e "${GREEN}✅ 配置已保存到: $env_file${NC}"
    echo "请编辑该文件，添加 CENTRAL_USERNAME 和 CENTRAL_PASSWORD"
    echo "使用方法: source $env_file"
fi

echo ""
echo -e "${GREEN}✅ 设置完成！${NC}" 