{"name": "@qunhe/idp-yundesign-impl", "version": "1.70.1", "description": "api typings for idp-yundesign", "keywords": [], "author": "yuanyan <<EMAIL>>", "license": "ISC", "publishConfig": {"registry": "http://npm-registry.qunhequnhe.com/"}, "repository": {"type": "git", "url": "*************************:tool-frontend/kaf-group/kls/idp-types.git"}, "scripts": {"build": "node ../../scripts/build-package/build-impl --exclude-packages @qunhe/math-apaas-api", "build-package": "npm run build && tsc index.ts --outDir build --declaration && copyfiles \"**/*.{d.ts,js,json,md}\" -e \"build/**/*.{d.ts,js,json,md}\" build"}, "dependencies": {"@qunhe/apaas-type-generator-lib": "~1.0.3", "@qunhe/idp-common-impl": "1.70.1", "@qunhe/kls-abstraction": "~1.1.15", "@qunhe/math-apaas-api": "^3.0.2", "lodash": "4.x"}, "release": {"scripts": {"pre-release": "yarn build-package"}, "publishRoot": "./build"}, "devDependencies": {"copyfiles": "^2.4.1", "typescript": "^4.2.3"}}