import { BasicType } from '@qunhe/kls-abstraction';

/**
 * ParamModelLitePlankPathTypes
 */
export const ParamModelLitePlankPathTypes = {
    type: BasicType.Object,
    properties: {
        ref: undefined!,
        data: {
            type: BasicType.Unknown,
        },
        getOriginOuterPath: {
            name: 'getOriginOuterPath',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        getOriginInnerHoles: {
            name: 'getOriginInnerHoles',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        getOuterPath: {
            name: 'getOuterPath',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        getInnerHoles: {
            name: 'getInnerHoles',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        setOuterPath: {
            name: 'setOuterPath',
            varying: false,
            keepArgsHandle: false,
            args: [{
                type: BasicType.Unknown,
            }],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        setInnerHoles: {
            name: 'setInnerHoles',
            varying: false,
            keepArgsHandle: false,
            args: [{
                type: BasicType.Unknown,
            }],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        setSlots: {
            name: 'setSlots',
            varying: false,
            keepArgsHandle: false,
            args: [{
                type: BasicType.Unknown,
            }],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        getSlots: {
            name: 'getSlots',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
        getOriginSlots: {
            name: 'getOriginSlots',
            varying: false,
            keepArgsHandle: false,
            args: [],
            type: BasicType.Function,
            return: {
                type: BasicType.Unknown,
            },
        },
    },
}
