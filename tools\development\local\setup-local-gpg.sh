#!/bin/bash

# 本地 GPG 密钥环设置脚本
# 一次性设置后，无需每次传递私钥

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查是否已有 GPG 密钥
check_existing_keys() {
    print_status "检查现有 GPG 密钥..."
    
    if gpg --list-secret-keys | grep -q "sec"; then
        print_success "发现现有 GPG 密钥:"
        gpg --list-secret-keys --keyid-format LONG
        echo ""
        read -p "是否要使用现有密钥？(y/n): " use_existing
        if [ "$use_existing" = "y" ] || [ "$use_existing" = "Y" ]; then
            return 0
        fi
    fi
    return 1
}

# 生成新的 GPG 密钥
generate_new_key() {
    print_status "生成新的 GPG 密钥..."
    
    cat > gpg_batch_config << EOF
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: ManyCore APIs
Name-Email: <EMAIL>
Expire-Date: 2y
Passphrase: Manycore666
%commit
EOF

    gpg --batch --generate-key gpg_batch_config
    rm -f gpg_batch_config
    
    print_success "GPG 密钥生成完成!"
}

# 上传公钥到密钥服务器
upload_public_key() {
    local key_id=$1
    print_status "上传公钥到密钥服务器..."
    
    gpg --keyserver keyserver.ubuntu.com --send-keys "$key_id" || print_warning "上传到 keyserver.ubuntu.com 失败"
    gpg --keyserver keys.openpgp.org --send-keys "$key_id" || print_warning "上传到 keys.openpgp.org 失败"
    gpg --keyserver pgp.mit.edu --send-keys "$key_id" || print_warning "上传到 pgp.mit.edu 失败"
    
    print_success "公钥上传完成（部分服务器可能失败，这是正常的）"
}

# 创建本地环境配置文件
create_local_config() {
    local key_id=$1
    local config_file="$HOME/.manycore-sdk-config"
    
    print_status "创建本地配置文件..."
    
    cat > "$config_file" << EOF
# ManyCore SDK 发布配置
# 此文件包含本地 GPG 设置，请妥善保管

# GPG 配置
export GPG_KEY_NAME="$key_id"
export GPG_PASSPHRASE="Manycore666"

# Maven Central 配置（需要手动填写）
export CENTRAL_USERNAME="你的Maven Central用户名"
export CENTRAL_PASSWORD="你的Maven Central令牌"

# 使用方法：
# source ~/.manycore-sdk-config
# scripts/local/local-sdk-manager.sh deploy-rel doorwindow
EOF

    chmod 600 "$config_file"
    print_success "配置文件已创建: $config_file"
    print_warning "请编辑该文件，填写你的 Maven Central 认证信息"
}

# 测试 GPG 配置
test_gpg_config() {
    local key_id=$1
    print_status "测试 GPG 配置..."
    
    echo "test content" | gpg --armor --local-user "$key_id" --sign > /tmp/test_sign.asc
    if gpg --verify /tmp/test_sign.asc > /dev/null 2>&1; then
        print_success "GPG 签名测试通过"
        rm -f /tmp/test_sign.asc
        return 0
    else
        print_error "GPG 签名测试失败"
        rm -f /tmp/test_sign.asc
        return 1
    fi
}

# 主函数
main() {
    echo "🔐 ManyCore SDK 本地 GPG 设置助手"
    echo "======================================"
    echo ""
    
    # 检查 GPG 是否安装
    if ! command -v gpg >/dev/null 2>&1; then
        print_error "GPG 未安装，请先安装 GPG"
        echo "Ubuntu/Debian: sudo apt-get install gnupg"
        echo "macOS: brew install gnupg"
        echo "Windows: 下载 GPG4Win"
        exit 1
    fi
    
    local key_id=""
    
    # 检查现有密钥或生成新密钥
    if check_existing_keys; then
        # 让用户选择要使用的密钥
        echo "请选择要使用的密钥 ID（长格式，例如：1234567890ABCDEF）:"
        read -p "密钥 ID: " key_id
        
        # 验证密钥 ID
        if ! gpg --list-secret-keys "$key_id" >/dev/null 2>&1; then
            print_error "无效的密钥 ID: $key_id"
            exit 1
        fi
    else
        generate_new_key
        
        # 获取新生成的密钥 ID
        key_id=$(gpg --list-secret-keys --keyid-format LONG | grep sec | head -1 | sed 's/.*\/\([A-F0-9]*\) .*/\1/')
        print_success "新密钥 ID: $key_id"
        
        # 上传公钥
        upload_public_key "$key_id"
    fi
    
    # 测试 GPG 配置
    if ! test_gpg_config "$key_id"; then
        print_error "GPG 配置测试失败，请检查密钥设置"
        exit 1
    fi
    
    # 创建本地配置
    create_local_config "$key_id"
    
    echo ""
    print_success "本地 GPG 设置完成！"
    echo ""
    print_status "下一步："
    echo "1. 编辑配置文件填写 Maven Central 认证信息:"
    echo "   nano ~/.manycore-sdk-config"
    echo ""
    echo "2. 加载配置并发布:"
    echo "   source ~/.manycore-sdk-config"
    echo "   scripts/local/local-sdk-manager.sh deploy-rel doorwindow"
    echo ""
    print_warning "注意：配置文件包含敏感信息，请确保文件权限安全"
}

main "$@" 